@media (min-width: 992px) {
    .aside {
        width: 90px !important;
    }

    .wrapper {
        padding-left: 90px !important;
    }

    .header-fixed[data-kt-sticky-header=on] .header {
        left: 90px !important;
    }
}

a.nav-link {
    cursor: pointer;
}

.special-card {
    background-color: #FFF6F0 !important;
}

.text-my-primary {
    color: #E4620C !important;
}

.my-primary {
    background-color: #E4620C !important;
}

.btn-my-primary {
    background-color: #E4620C !important;
    color: #fff !important;
}

.btn-my-primary:hover {
    background-color: #b9510b !important;
}

.btn-light-my-primary {
    background-color: #fff !important;
    color: #000 !important;
}

.btn-light-my-primary:hover {
    background-color: #FFF6F0 !important;
    color: #000 !important;
}

.btn.btn-light-my-primary:active,
.btn.btn-light-my-primary:active:not(.btn-active),
.btn.btn-light-my-primary.active {
    background-color: #E4620C !important;
    color: #fff !important;
}

/* .btn-active-dark {
    background-color: #E4620C !important;
} */

.my-white {
    background-color: white !important;
}

.my-grey {
    background-color: #f9fafb !important;
}

.nav-line-tabs .nav-item .nav-link.active,
.nav-line-tabs .nav-item .nav-link:hover:not(.disabled),
.nav-line-tabs .nav-item.show .nav-link {
    border-bottom: 3px solid #50cd89 !important;
}

.hover-elevate-up:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, .12), 0 4px 8px rgba(0, 0, 0, .06);
}

.accordion-item {
    margin-bottom: 5px !important;
}

.accordion-button:not(.collapsed) {
    /* color: var(--bs-accordion-active-color); */
    background-color: #E4620C !important;
    /* box-shadow: inset 0 calc(-1 * var(--bs-accordion-border-width)) 0 var(--bs-accordion-border-color); */
}

.accordion-button:not(.collapsed)>h3 {
    color: #fff;
}

.accordion-button:not(.collapsed)::after {
    color: #fff !important;
}

.accordion-button {
    background-color: #FFF6F0 !important;
}

/* 
.highlight:hover {
    background: #E4620C !important;
    color: #fff !important;
} */

.cardBoardItem:hover {
    border: 1px solid #007bff !important;
}


.container-slide-wrapper {
    position: relative;
}

.slide-in-out {
    transition: transform 0.4s ease, opacity 0.4s ease;
    width: 100%;
    height: 100%;
    position: relative;
}

.slide-in-out.show {
    transform: translateY(0%);
    opacity: 1;
    display: block;
    visibility: visible;
}

.slide-in-out.hide {
    transition: transform 0.4s ease, opacity 0.4s ease;
    visibility: hidden;
    transform: translateY(100%);
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-transform: translateY(100%);
    -moz-transform: translateY(100%);
    -ms-transform: translateY(100%);
    -o-transform: translateY(100%);
}