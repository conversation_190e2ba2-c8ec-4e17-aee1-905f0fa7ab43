<?php

use App\Models\Spk;
use App\Models\DraftHitungan;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CRMController;
use App\Http\Controllers\TTDController;
use App\Http\Controllers\DealController;
use App\Http\Controllers\LostController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Redirect;
use App\Http\Controllers\BahanController;
use App\Http\Controllers\MesinController;
use App\Http\Controllers\SalesController;
use App\Http\Controllers\ToolsController;
use App\Http\Controllers\DesignController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\SumberController;
use App\Http\Controllers\SurveyController;
use App\Http\Controllers\TargetController;
use App\Http\Controllers\VendorController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\GradingController;
use App\Http\Controllers\GramasiController;

use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\FollowUpController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\QuotationController;
use App\Http\Controllers\BoardSalesController;
use App\Http\Controllers\ProductionController;
use App\Http\Controllers\SuratJalanController;
use App\Http\Controllers\TipeProdukController;
use App\Http\Controllers\JenisKertasController;
use App\Http\Controllers\JenisOngkosController;
use App\Http\Controllers\KertasPlanoController;
use App\Http\Controllers\TipeInstansiController;
use App\Http\Controllers\DraftHitunganController;
use App\Http\Controllers\KategoriMasalahController;
use App\Http\Controllers\Price\OtherPriceController;
use App\Http\Controllers\Price\PaperPriceController;
use App\Http\Controllers\Price\CuttingPlatController;
use App\Http\Controllers\EngagementController;
use App\Http\Controllers\BankAccountController;
use SebastianBergmann\CodeCoverage\Report\Html\Dashboard;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('auth/login');
});

Route::get('logout', function () {
    auth()->logout();
    Session()->flush();

    return Redirect::to('/');
})->name('do.logout');

Route::middleware(['auth:sanctum', 'verified'])->group(function () {
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/get_sales_report', [DashboardController::class, 'get_sales_report'])->name('dashboard.get_sales_report');
    Route::get('/dashboard/get_sumber', [DashboardController::class, 'get_sumber'])->name('dashboard.get_sumber');
    Route::get('/dashboard/get_tipe_instansi', [DashboardController::class, 'get_tipe_instansi'])->name('dashboard.get_tipe_instansi');
    Route::get('/dashboard/get_tipe_produk', [DashboardController::class, 'get_tipe_instansi'])->name('dashboard.get_tipe_produk');
    Route::get('/dashboard/table_get_sales_repeat', [DashboardController::class, 'table_get_sales_repeat'])->name('dashboard.table_get_sales_repeat');
    Route::get('/dashboard/table_get_sales_lost', [DashboardController::class, 'table_get_sales_lost'])->name('dashboard.table_get_sales_lost');
    Route::get('/dashboard/get_dashboard_spadmin_sales', [DashboardController::class, 'get_dashboard_spadmin_sales'])->name('dashboard.get_dashboard_spadmin_sales');
    Route::get('/dashboard/get_production_report', [DashboardController::class, 'get_production_report'])->name('dashboard.get_production_report');
    Route::get('/dashboard/get_jenis_kertas', [DashboardController::class, 'get_jenis_kertas'])->name('dashboard.get_jenis_kertas');
    Route::get('/dashboard/table_get_produksi_berjalan', [DashboardController::class, 'table_get_produksi_berjalan'])->name('dashboard.table_get_produksi_berjalan');
    Route::get('/dashboard/table_get_omzet_online', [DashboardController::class, 'table_omzet_online'])->name('dashboard.table_omzet_online');
    
    Route::get('/draft_hitungan', [DraftHitunganController::class, 'index'])->name('draft_hitungan');
    Route::get('draft_hitungan/detail/{id}', [DraftHitunganController::class, 'detail'])->name('draft_hitungan.detail');
    Route::delete('draft_hitungan/destroy/{id}', [DraftHitunganController::class, 'destroy'])->name('draft_hitungan.destroy');
    Route::get('draft_hitungan/getOrderProduct', [DraftHitunganController::class, 'getOrderProduct'])->name('draft_hitungan.getOrderProduct');
    Route::get('draft_hitungan/getListDraft', [DraftHitunganController::class, 'getListDraft'])->name('draft_hitungan.getListDraft');
    Route::get('draft_hitungan/getDraft', [DraftHitunganController::class, 'getDraft'])->name('draft_hitungan.getDraft');
    Route::post('draft_hitungan/store', [DraftHitunganController::class, 'store'])->name('draft_hitungan.store');

    Route::get('/design', [DesignController::class, 'index'])->name('design');
    Route::get('/design/layout', [DesignController::class, 'layout'])->name('design.layout');
    Route::get('/design/add_new_layout', [DesignController::class, 'add_new_layout'])->name('design.add_new_layout');
    Route::get('/design/add_new', [DesignController::class, 'add_new'])->name('design.add_new');
    Route::get('/design/edit_layout/{id}', [DesignController::class, 'edit_layout'])->name('design.edit_layout');
    Route::get('/design/counting_cutting_paper', [DesignController::class, 'counting_cutting_paper'])->name('design.counting_cutting_paper');
    Route::get('/design/get_layout', [DesignController::class, 'get_layout'])->name('design.get_layout');
    Route::post('/design/store_layout', [DesignController::class, 'store_layout'])->name('design.store_layout');

    Route::post('/design/kategori_layout/store_kategori_layout', [DesignController::class, 'store_kategori_layout'])->name('design.kategori_layout.store');
    Route::get('/design/list_layout', [DesignController::class, 'list_layout'])->name('design.kategori_layout.list_layout');
    Route::get('/design/call_layout', [DesignController::class, 'call_layout'])->name('design.call_layout');
    Route::post('/design/calculating_price', [DesignController::class, 'calculating_price'])->name('design.calculating_price');
    Route::post('/design/save_draft', [DesignController::class, 'save_draft'])->name('design.save_draft');
    Route::get('/design/getBoxAtas', [DesignController::class, 'getBoxAtas'])->name('design.getBoxAtas');
    Route::get('/design/getHitungBoxAtas', [DesignController::class, 'getHitungBoxAtas'])->name('design.getHitungBoxAtas');
    Route::get('/design/list_mesin', [DesignController::class, 'list_mesin'])->name('design.list_mesin');

    Route::get('/crm/get_jenis_kertas', [CRMController::class, 'get_jenis_kertas'])->name('crm.get_jenis_kertas');
    Route::get('/crm/get_kertas_plano', [CRMController::class, 'get_kertas_plano'])->name('crm.get_kertas_plano');
    Route::get('/crm/get_gramasi', [CRMController::class, 'get_gramasi'])->name('crm.get_gramasi');
    Route::get('/crm/get_mesin', [CRMController::class, 'get_mesin'])->name('crm.get_mesin');
    Route::get('/crm/get_jenis_ongkos', [CRMController::class, 'get_jenis_ongkos'])->name('crm.get_jenis_ongkos');
    Route::get('/crm/get_tools', [CRMController::class, 'get_tools'])->name('crm.get_tools');
    Route::get('/crm/get_bahan', [CRMController::class, 'get_bahan'])->name('crm.get_bahan');
    Route::get('/crm/get_other_price', [CRMController::class, 'get_other_price'])->name('crm.get_other_price');
    Route::get('/crm/get_province', [CRMController::class, 'get_province'])->name('crm.get_province');
    Route::get('/crm/get_district', [CRMController::class, 'get_district'])->name('crm.get_district');
    Route::get('/crm/get_sub_district', [CRMController::class, 'get_sub_district'])->name('crm.get_sub_district');
    Route::get('/crm/get_village', [CRMController::class, 'get_village'])->name('crm.get_village');
    Route::get('/crm/getOrder', [CRMController::class, 'getOrder'])->name('crm.getOrder');

    Route::group(['middleware' => ['sales']], function () {
        Route::get('/crm', [CRMController::class, 'index'])->name('crm');
        Route::get('/crm/table_customer', [CRMController::class, 'table_customer'])->name('crm.table_customer');
        Route::post('/crm/store_new_cust', [CRMController::class, 'store_new_cust'])->name('crm.store_new_cust');
        Route::post('/crm/check-phone-number', [CRMController::class, 'checkPhoneNumber'])->name('checkPhoneNumber');
        Route::get('/crm/show_cust/{id}', [CRMController::class, 'show_cust'])->name('crm.show_cust');
        Route::post('/crm/edit_cust', [CRMController::class, 'edit_cust'])->name('crm.edit_cust');
        Route::get('/crm/company/{id}', [CRMController::class, 'show_cust_company'])->name('crm.show_cust_company');
        Route::post('/crm/edit_cust_comp', [CRMController::class, 'edit_cust_comp'])->name('crm.edit_cust_comp');

        Route::get('/crm/main_kode_order_get', [CRMController::class, 'main_kode_order_get'])->name('crm.main_kode_order_get');

        Route::get('/crm/detail_customer/{id}', [CRMController::class, 'detail_customer'])->name('crm.detail_customer');
        Route::get('/crm/table_transaksi_customer/{id}', [CRMController::class, 'table_transaksi_customer'])->name('crm.table_transaksi_customer');

        Route::get('crm/direct_order/{id}', [CRMController::class, 'direct_order'])->name('crm.direct_order');

        Route::get('/crm/detail_main_order/{param}', [CRMController::class, 'detail_main_order'])->name('crm.detail_main_order');
        Route::get('/crm/table_main_order/{param}', [CRMController::class, 'table_main_order'])->name('crm.table_main_order');

        Route::get('/crm/table_order', [CRMController::class, 'table_order'])->name('crm.table_order');
        Route::post('/crm/store_new_order', [CRMController::class, 'store_new_order'])->name('crm.store_new_order');
        Route::get('/crm/get_customer', [CRMController::class, 'get_customer'])->name('crm.get_customer');
        Route::get('/crm/get_pic', [CRMController::class, 'get_pic'])->name('crm.get_pic');
        Route::get('/crm/show_order/{id}', [CRMController::class, 'show_order'])->name('crm.show_order');
        Route::get('/crm/show_produk/{key}', [CRMController::class, 'show_produk'])->name('crm.show_produk')->where('key', '.*');
        Route::post('/crm/edit_order', [CRMController::class, 'edit_order'])->name('crm.edit_order');
        Route::post('/crm/edit_produk', [CRMController::class, 'edit_produk'])->name('crm.edit_produk');
        Route::get('/crm/detail_order/{param}', [CRMController::class, 'detail_order'])->name('crm.detail_order');

        Route::get('/crm/form_edit_order/{param}', [CRMController::class, 'form_edit_order'])->name('crm.form_edit_order');
        Route::get('/crm/form_edit_produk/{param}', [CRMController::class, 'form_edit_produk'])->name('crm.form_edit_produk');

        Route::get('/crm/table_harga_rinci', [CRMController::class, 'table_harga_rinci'])->name('crm.table_harga_rinci');

        Route::get('/crm/table_faw', [CRMController::class, 'table_faw'])->name('crm.table_faw');
        Route::get('/crm/form_add_faw/{param}', [CRMController::class, 'form_add_faw'])->name('crm.form_add_faw')->where('param', '.*');
        Route::post('/crm/store_faw', [CRMController::class, 'store_faw'])->name('crm.store_faw');
        Route::get('/crm/faw_preview/{id}', [CRMController::class, 'faw_preview'])->name('crm.faw_preview');
        Route::get('/crm/generate_faw/{param}', [CRMController::class, 'generate_faw'])->name('crm.generate_faw');
        Route::get('/crm/edit_faw/{param}', [CRMController::class, 'edit_faw'])->name('crm.edit_faw');
        Route::get('/crm/delete_lampiran/{param}', [CRMController::class, 'delete_lampiran'])->name('crm.delete_lampiran');
        Route::post('/crm/update_faw', [CRMController::class, 'update_faw'])->name('crm.update_faw');
        Route::post('/crm/approve_faw/{id}', [CRMController::class, 'approve_faw'])->name('crm.approve_faw');

        Route::get('/crm/table_kendala', [CRMController::class, 'table_kendala'])->name('crm.table_kendala');
        Route::get('/crm/show_fpm/{id}', [CRMController::class, 'show_fpm'])->name('crm.show_fpm');
        Route::post('/crm/form_fpm', [CRMController::class, 'form_fpm'])->name('crm.form_fpm');
        Route::get('/crm/form_solusi/', [CRMController::class, 'form_solusi'])->name('crm.form_solusi');
        Route::post('/crm/store_solusi', [CRMController::class, 'store_solusi'])->name('crm.store_solusi');
        Route::post('/crm/store_table_solusi', [CRMController::class, 'store_table_solusi'])->name('crm.store_table_solusi');

        Route::delete('/crm/delete_order/{param}', [CRMController::class, 'delete_order'])->name('crm.delete_order');

        Route::get('/crm/generate_invoice/{param}', [CRMController::class, 'generate_invoice'])->name('crm.generate_invoice');
        Route::get('/crm/generate_invoice_dp/{param}', [CRMController::class, 'generate_invoice_dp'])->name('crm.generate_invoice_dp');
        Route::get('/crm/generate_offering_letter/{param}', [CRMController::class, 'generate_offering_letter'])->name('crm.generate_offering_letter');
        Route::get('/crm/generate_surat_jalan/{param}', [CRMController::class, 'generate_surat_jalan'])->name('crm.generate_surat_jalan');

        Route::get('/price_null', [CRMController::class, 'price_null'])->name('crm.price_null');
        Route::get('/table_price_null', [CRMController::class, 'table_price_null'])->name('crm.table_price_null');
        Route::get('/prioritas', [CRMController::class, 'prioritas'])->name('crm.prioritas');
        Route::get('/table_prioritas', [CRMController::class, 'table_prioritas'])->name('crm.table_prioritas');

        Route::get('/target', [TargetController::class, 'index'])->name('target');
        Route::get('/target/add_target', [TargetController::class, 'add_target'])->name('target.add_target');
        Route::get('/target/get_current_target', [TargetController::class, 'get_current_target'])->name('target.get_current_target');
        Route::post('/target/store_target', [TargetController::class, 'store_target'])->name('target.store_target');
        Route::get('/target/get_target', [TargetController::class, 'get_target'])->name('target.get_target');
        Route::get('/target/view_detail', [TargetController::class, 'view_detail'])->name('target.view_detail');
        Route::get('/target/get_detail', [TargetController::class, 'get_detail'])->name('target.get_detail');
        Route::get('/target/edit_target', [TargetController::class, 'edit_target'])->name('target.edit_target');
        Route::post('/target/update_target', [TargetController::class, 'update_target'])->name('target.update_target');
        Route::get('/target/detail_delas', [TargetController::class, 'table_detail_deals'])->name('target.table_detail_deals');

        Route::get('/survey', [SurveyController::class, 'index'])->name('survey');
        Route::get('/survey/detail', [SurveyController::class, 'detail'])->name('survey.detail');
        Route::get('/survey/get-list', [SurveyController::class, 'get_survey'])->name('survey.get_list');
        Route::post('/survey/assign-survey/generate', [SurveyController::class, 'generateLink'])->name('survey.assign_survey.generate');
        Route::get('/survey/do-survey/{id_order}', [SurveyController::class, 'doSurvey'])->name('survey.do_survey');
        Route::get('/survey/getAssignSurvey', [SurveyController::class, 'getAssignSurvey'])->name('survey.getAssignSurvey');
        Route::get('/survey/getTable', [SurveyController::class, 'getDataTableSurvey'])->name('survey.get_table');
        Route::get('/survey/edit/{id}', [SurveyController::class, 'editSurvey'])->name('survey.edit');
        Route::post('/survey/store', [SurveyController::class, 'createSurvey'])->name('survey.store');
        Route::post('/survey/update/{id}', [SurveyController::class, 'updateSurvey'])->name('survey.update');
        Route::delete('/survey/destroy/{id}', [SurveyController::class, 'destroySurvey'])->name('survey.destroy');
        Route::get('/survey/question/getTable', [SurveyController::class, 'getDataTableQuestion'])->name('survey.question.get_table');
        Route::get('/survey/question/edit/{id}', [SurveyController::class, 'editQuestion'])->name('survey.question.edit');
        Route::post('/survey/question/store', [SurveyController::class, 'createQuestion'])->name('survey.question.store');
        Route::post('/survey/question/update/{id}', [SurveyController::class, 'updateQuestion'])->name('survey.question.update');
        Route::delete('/survey/question/destroy/{id}', [SurveyController::class, 'destroyQuestion'])->name('survey.question.destroy');
    });

    Route::middleware(['produksi'])->group(function () {
        Route::get('/production', [ProductionController::class, 'index'])->name('production');
        Route::get('/production/table_production', [ProductionController::class, 'table_production'])->name('production.table_production');
        Route::get('/production/table_production_bermasalah', [ProductionController::class, 'table_production_bermasalah'])->name('production.table_production_bermasalah');
        Route::get('/production/form_add_production', [ProductionController::class, 'form_add_production'])->name('production.form_add_production');
        Route::get('/production/kode_order_get', [ProductionController::class, 'kode_order_get'])->name('production.kode_order_get');
        Route::post('/production/store_new_production', [ProductionController::class, 'store_new_production'])->name('production.store_new_production');

        Route::get('/production/show_production/{param}', [ProductionController::class, 'show_production'])->name('production.show_production');

        Route::post('/production/update_production', [ProductionController::class, 'update_production'])->name('production.update_production');

        Route::get('/production/done', [ProductionController::class, 'done_page'])->name('production.done_page');
        Route::get('/production/table_done', [ProductionController::class, 'table_done'])->name('production.table_done');
        Route::get('/production/ongoing', [ProductionController::class, 'ongoing'])->name('production.ongoing');
        Route::get('/production/table_ongoing', [ProductionController::class, 'table_ongoing'])->name('production.table_ongoing');
        Route::get('/production/dummy', [ProductionController::class, 'dummy'])->name('production.dummy');
        Route::get('/production/table_dummy', [ProductionController::class, 'table_dummy'])->name('production.table_dummy');
        Route::get('/production/dummy_done', [ProductionController::class, 'dummy_done'])->name('production.dummy_done');
        Route::get('/production/table_dummy_done', [ProductionController::class, 'table_dummy_done'])->name('production.table_dummy_done');
        Route::get('/production/not_acc_faw', [ProductionController::class, 'not_acc_faw'])->name('production.not_acc_faw');
        Route::get('/production/table_not_acc_faw', [ProductionController::class, 'table_not_acc_faw'])->name('production.table_not_acc_faw');
        Route::get('/production/show_faw/{param}', [ProductionController::class, 'show_faw'])->name('production.show_faw');
        Route::post('/production/approve_faw/{id}', [ProductionController::class, 'approve_faw'])->name('production.approve_faw');
        Route::get('/production/acc_faw_not_spk', [ProductionController::class, 'acc_faw_not_spk'])->name('production.acc_faw_not_spk');
        Route::get('/production/table_acc_faw_not_spk', [ProductionController::class, 'table_acc_faw_not_spk'])->name('production.table_acc_faw_not_spk');

        Route::get('/production/export_spk/{param}', [ProductionController::class, 'export_spk'])->name('production.export_spk');

        // Route::get('/production/show_production/{param}', [ProductionController::class, 'save'])->name('production.save');

        Route::get('/production/generate_spk/{param}', [ProductionController::class, 'generate_spk'])->name('production.generate_spk');


        Route::delete('/production/delete_spk/{id}', [ProductionController::class, 'delete_spk'])->name('production.delete_spk');
    });

    Route::middleware(['salesuper'])->group(function () {
        Route::get('/crm/generate_faw/{param}', [CRMController::class, 'generate_faw'])->name('crm.generate_faw');
    });

    Route::middleware(['superadmin'])->group(function () {
        Route::resource('user', UserController::class)->only('index', 'create', 'store', 'edit');
        Route::get('/user/show/{id}', [UserController::class, 'show'])->name('user.show');
        Route::post('/user/update/{id}', [UserController::class, 'update'])->name('user.update');
        Route::delete('/user/destroy/{id}', [UserController::class, 'destroy'])->name('user.destroy');
        
        Route::resource('ttd', TTDController::class)->only('index', 'create', 'store', 'edit');

        Route::resource('vendor_produksi', VendorController::class);
        Route::get('vendor_produksi/show/{id}', [VendorController::class, 'show'])->name('vendor.show');
        Route::post('vendor_produksi/update', [VendorController::class, 'update'])->name('vendor.update');
        Route::delete('vendor/destroy/{id}', [VendorController::class, 'destroy'])->name('vendor.destroy');

        Route::resource('instansi', TipeInstansiController::class);
        Route::get('instansi/show/{id}', [TipeInstansiController::class, 'show']);
        Route::post('instansi/update', [TipeInstansiController::class, 'update']);
        Route::delete('instansi/destroy/{id}', [TipeInstansiController::class, 'destroy']);

        Route::resource('sumber', SumberController::class);
        Route::get('sumber/show/{id}', [SumberController::class, 'show']);
        Route::post('sumber/update', [SumberController::class, 'update']);
        Route::delete('sumber/destroy/{id}', [SumberController::class, 'destroy']);

        Route::resource('grading', GradingController::class);
        Route::get('grading/show/{id}', [GradingController::class, 'show']);
        Route::post('grading/update', [GradingController::class, 'update']);
        Route::delete('grading/destroy/{id}', [GradingController::class, 'destroy']);

        Route::resource('follow_up', FollowUpController::class);
        Route::get('follow_up/show/{id}', [FollowUpController::class, 'show']);
        Route::post('follow_up/update', [FollowUpController::class, 'update']);
        Route::delete('follow_up/destroy/{id}', [FollowUpController::class, 'destroy']);

        Route::resource('deal', DealController::class);
        Route::get('deal/show/{id}', [DealController::class, 'show']);
        Route::post('deal/update', [DealController::class, 'update']);
        Route::delete('deal/destroy/{id}', [DealController::class, 'destroy']);

        Route::resource('lost', LostController::class);
        Route::get('lost/show/{id}', [LostController::class, 'show']);
        Route::post('lost/update', [LostController::class, 'update']);
        Route::delete('lost/destroy/{id}', [LostController::class, 'destroy']);

        Route::resource('bahan', BahanController::class);
        Route::get('bahan/show/{id}', [BahanController::class, 'show']);
        Route::post('bahan/update', [BahanController::class, 'update']);
        Route::delete('bahan/destroy/{id}', [BahanController::class, 'destroy']);

        Route::resource('jenis_kertas', JenisKertasController::class);
        Route::get('jenis_kertas/show/{id}', [JenisKertasController::class, 'show']);
        Route::post('jenis_kertas/update', [JenisKertasController::class, 'update']);
        Route::delete('jenis_kertas/destroy/{id}', [JenisKertasController::class, 'destroy']);

        Route::resource('gramasi', GramasiController::class);
        Route::get('gramasi/show/{id}', [GramasiController::class, 'show']);
        Route::post('gramasi/update', [GramasiController::class, 'update']);
        Route::delete('gramasi/destroy/{id}', [GramasiController::class, 'destroy']);

        Route::resource('tipe_produk', TipeProdukController::class);
        Route::get('tipe_produk/show/{id}', [TipeProdukController::class, 'show']);
        Route::post('tipe_produk/update', [TipeProdukController::class, 'update']);
        Route::delete('tipe_produk/destroy/{id}', [TipeProdukController::class, 'destroy']);

        Route::resource('kertas_plano', KertasPlanoController::class);
        Route::get('kertas_plano/show/{id}', [KertasPlanoController::class, 'show']);
        Route::post('kertas_plano/update', [KertasPlanoController::class, 'update']);
        Route::delete('kertas_plano/destroy/{id}', [KertasPlanoController::class, 'destroy']);

        Route::resource('kategori_masalah', KategoriMasalahController::class);
        Route::get('kategori_masalah/show/{id}', [KategoriMasalahController::class, 'show']);
        Route::post('kategori_masalah/update', [KategoriMasalahController::class, 'update']);
        Route::delete('kertas_plano/destroy/{id}', [KategoriMasalahController::class, 'destroy']);

        Route::resource('jenis_ongkos', JenisOngkosController::class);
        Route::get('jenis_ongkos/show/{id}', [JenisOngkosController::class, 'show']);
        Route::post('jenis_ongkos/update', [JenisOngkosController::class, 'update']);
        Route::delete('jenis_ongkos/destroy/{id}', [JenisOngkosController::class, 'destroy']);

        Route::resource('tools', ToolsController::class);
        Route::get('tools/show/{id}', [ToolsController::class, 'show']);
        Route::post('tools/update', [ToolsController::class, 'update']);
        Route::delete('tools/destroy/{id}', [ToolsController::class, 'destroy']);

        Route::resource('mesin', MesinController::class);
        Route::get('mesin/show/{id}', [MesinController::class, 'show']);
        Route::post('mesin/update', [MesinController::class, 'update']);
        Route::delete('mesin/destroy/{id}', [MesinController::class, 'destroy']);

        Route::resource('paper_price', PaperPriceController::class);
        Route::get('paper_price/show/{id}', [PaperPriceController::class, 'show']);
        Route::post('paper_price/update', [PaperPriceController::class, 'update']);
        Route::delete('paper_price/destroy/{id}', [PaperPriceController::class, 'destroy']);

        Route::resource('cutting_plat_price', CuttingPlatController::class);
        Route::get('cutting_plat_price/show/{id}', [CuttingPlatController::class, 'show']);
        Route::post('cutting_plat_price/update', [CuttingPlatController::class, 'update']);
        Route::delete('cutting_plat_price/destroy/{id}', [CuttingPlatController::class, 'destroy']);

        Route::resource('other_price', OtherPriceController::class);
        Route::get('other_price/show/{id}', [OtherPriceController::class, 'show']);
        Route::post('other_price/update', [OtherPriceController::class, 'update']);
        Route::delete('other_price/destroy/{id}', [OtherPriceController::class, 'destroy']);

        Route::resource('bank_account', BankAccountController::class);
        Route::get('/bank_account/show/{id}', [BankAccountController::class, 'show'])->name('bank_account.show');
        Route::delete('/bank_account/destroy/{id}', [BankAccountController::class, 'destroy'])->name('bank_account.destroy');
    });
    
    //Company
    Route::get('/company/table_company', [CompanyController::class, 'table_company'])->name('company.table_company');
    Route::get('/company/edit-company/{id}', [CompanyController::class, 'edit_company'])->name('company.edit_company');
    Route::post('/company/store_company', [CompanyController::class, 'store_company'])->name('company.store_company');
    Route::get('/company/detail-company/{id}', [CompanyController::class, 'detail_company'])->name('company.detail_company');
    Route::get('/company/detail', [CompanyController::class, 'detail'])->name('company.detail');
    Route::post('/company/add_note', [CompanyController::class, 'add_note'])->name('company.add_note');
    Route::get('/company/get-customer', [CompanyController::class, 'getCustomer'])->name('company.get_customer');
    Route::post('/company/add_pic_cust', [CompanyController::class, 'add_pic_cust'])->name('company.add_pic_cust');
    Route::post('/company/remove_pic_cust', [CompanyController::class, 'remove_pic_cust'])->name('company.remove_pic_cust');
    Route::post('/company/add_engagement', [CompanyController::class, 'add_engagement'])->name('company.add_engagement');
    Route::get('/company/get-exist-company', [CompanyController::class, 'existCompany'])->name('company.exist_company');

    Route::group(['prefix' => 'sales', 'as' => 'sales.'], function () {
        Route::get('/', [SalesController::class, 'index'])->name('index');
        Route::group(['prefix' => 'board', 'as' => 'board.'], function () {
            Route::get('/', [BoardSalesController::class, 'board'])->name('board');
            Route::get('/order-company', [BoardSalesController::class, 'orderCompany'])->name('orderCompany');
            Route::post('/order-company/detail/{order_key}/change-lost', [BoardSalesController::class, 'changeLost'])->name('changeLost');
            Route::get('/order-company/detail/{order_key}', [BoardSalesController::class, 'detailBoard'])->name('detailBoard');
            Route::post('/order-company/detail/{order_key}', [BoardSalesController::class, 'changeOrder'])->name('changeOrder');
            Route::get('/order-company/detail/{order_key}/activity-task', [BoardSalesController::class, 'activityTask'])->name('activityTask');
            Route::post('/order-company/detail/{order_key}/activity-task', [BoardSalesController::class, 'storeActivityTask'])->name('storeActivityTask');
            Route::delete('/order-company/detail/{order_key}/activity-task/{id_task_order}', [BoardSalesController::class, 'deleteActivityTask'])->name('deleteActivityTask');
            Route::post('/order-company/detail/{order_key}/activity-task/{id_task_order}/mark-done', [BoardSalesController::class, 'markDoneActivityTask'])->name('markDoneActivityTask');
        });
        Route::group(['prefix' => 'task', 'as' => 'task.'], function () {
            Route::get('/', [SalesController::class, 'task'])->name('index');
            Route::post('/dataTables', [SalesController::class, 'dataTable'])->name('dataTable');
            Route::get('/list/{id}', [SalesController::class, 'taskList'])->name('list');
            Route::get('/show/{id}', [SalesController::class, 'taskShow'])->name('show');
            Route::post('/list/{id}/dataTables', [SalesController::class, 'dataTableListTask'])->name('dataTableListTask');
        });
    });

    Route::group(['prefix' => 'quotation', 'as' => 'quotation.'], function () {
        Route::get('/', [QuotationController::class, 'index'])->name('index');
        Route::get('/create', [QuotationController::class, 'create'])->name('create');
        Route::get('/edit/{id}', [QuotationController::class, 'edit'])->name('edit');
        Route::post('/store', [QuotationController::class, 'store'])->name('store');
        Route::post('/dataTables', [QuotationController::class, 'dataTable'])->name('dataTable');
        Route::get('/customers', [QuotationController::class, 'customers'])->name('customers');
        Route::delete('/{id}', [QuotationController::class, 'destroy'])->name('destroy');
        Route::get('/{id}', [QuotationController::class, 'show'])->name('show');
        Route::post('/generate-invoice/{id}', [QuotationController::class, 'generateInvoice'])->name('generateInvoice');
        Route::get('/generate-quotation/{id}', [QuotationController::class, 'generateQuotation'])->name('generateQuotation');
    });

    Route::group(['prefix' => 'invoice', 'as' => 'invoice.'], function () {
        Route::get('/', [InvoiceController::class, 'index'])->name('index');
        Route::get('/create', [InvoiceController::class, 'create'])->name('create');
        Route::get('/edit/{id}', [InvoiceController::class, 'edit'])->name('edit');
        Route::post('/store', [InvoiceController::class, 'store'])->name('store');
        Route::post('/dataTables', [InvoiceController::class, 'dataTable'])->name('dataTable');
        Route::get('/customers', [InvoiceController::class, 'customers'])->name('customers');
        Route::delete('/{id}', [InvoiceController::class, 'destroy'])->name('destroy');
        Route::get('/{id}', [InvoiceController::class, 'show'])->name('show');
        Route::get('/generate-invoice/{id}', [InvoiceController::class, 'generateInvoice'])->name('generateInvoice');
    });

    Route::group(['prefix' => 'surat-jalan', 'as' => 'surat-jalan.'], function () {
        Route::get('/', [SuratJalanController::class, 'index'])->name('index');
        Route::get('/create', [SuratJalanController::class, 'create'])->name('create');
        Route::get('/edit/{id}', [SuratJalanController::class, 'edit'])->name('edit');
        Route::post('/store', [SuratJalanController::class, 'store'])->name('store');
        Route::post('/dataTables', [SuratJalanController::class, 'dataTable'])->name('dataTable');
        Route::get('/customers', [SuratJalanController::class, 'customers'])->name('customers');
        Route::get('/getOrderDetail', [SuratJalanController::class, 'getOrderDetail'])->name('getOrderDetail');
        Route::delete('/{id}', [SuratJalanController::class, 'destroy'])->name('destroy');
        Route::get('/{id}', [SuratJalanController::class, 'show'])->name('show');
        Route::get('/generate-surat-jalan/{id}', [SuratJalanController::class, 'generateSuratJalan'])->name('generateSuratJalan');
    });

    Route::group(['prefix' => 'engagement', 'as' => 'engagement.'], function () {
        Route::get('/', [EngagementController::class, 'engagement'])->name('index');
        Route::get('/getData', [EngagementController::class, 'getDataEngagement'])->name('getData');
        Route::get('/create', [EngagementController::class, 'create'])->name('create');
        Route::get('/get-pic', [EngagementController::class, 'getPIC'])->name('getPIC');
        Route::get('/get-company', [EngagementController::class, 'getCompany'])->name('getCompany');
        Route::get('/edit/{id}', [EngagementController::class, 'edit'])->name('edit');
        Route::post('/store', [EngagementController::class, 'store'])->name('store');
        Route::delete('/{id}', [EngagementController::class, 'destroy'])->name('destroy');
        Route::get('/{id}', [EngagementController::class, 'show'])->name('show');
    });

    Route::group(['prefix' => 'report', 'as' => 'report.'], function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::group(['prefix' => 'forecast', 'as' => 'forecast.'], function () {
            Route::get('/', [ReportController::class, 'forecast'])->name('index');
            Route::get('/getData', [ReportController::class, 'getDataForecast'])->name('getData');
            Route::get('/download-excel', [ReportController::class, 'downloadExcelForecast'])->name('downloadExcel');
        });
        Route::group(['prefix' => 'responses', 'as' => 'responses.'], function () {
            Route::get('/', [ReportController::class, 'responses'])->name('index');
        });
        Route::group(['prefix' => 'tracker', 'as' => 'tracker.'], function () {
            Route::get('/', [ReportController::class, 'tracker'])->name('index');
            Route::get('/getData', [ReportController::class, 'trackingOrder'])->name('getData');
        });
        Route::group(['prefix' => 'target', 'as' => 'target.'], function () {
            Route::get('/', [ReportController::class, 'target'])->name('index');
            Route::get('/getSales', [ReportController::class, 'getSales'])->name('getSales');
            Route::get('/create/{sales_id}', [ReportController::class, 'createTarget'])->name('createTarget');
            Route::get('/edit/{id}', [ReportController::class, 'editTarget'])->name('editTarget');
            Route::post('/store', [ReportController::class, 'storeTarget'])->name('storeTarget');
            Route::delete('/delete/{id}', [ReportController::class, 'deleteTarget'])->name('deleteTarget');
            Route::get('/target-sales/{id}', [ReportController::class, 'targetSales'])->name('targetSales');
            Route::get('/create-edit/{sales_id}', [ReportController::class, 'createEditTarget'])->name('createEditTarget');
            Route::post('/update-sales/{id}', [ReportController::class, 'updateSales'])->name('updateSales');
            Route::get('/achievement', [ReportController::class, 'achievement'])->name('achievement');
            Route::get('/get-data-achievement', [ReportController::class, 'getDataAchievement'])->name('getDataAchievement');
            Route::get('/get-data-proyeksi', [ReportController::class, 'getDataProyeksi'])->name('getDataProyeksi');
            Route::get('/get-data-lost', [ReportController::class, 'getDataLost'])->name('getDataLost');
        });

        Route::group(['prefix' => 'grading', 'as' => 'grading.'], function () {
            Route::get('/', [ReportController::class, 'grading'])->name('index');
            Route::get('/get-data-grading', [ReportController::class, 'getDataGrading'])->name('getData');
        });
    });
});

Route::get('/survey/do_survey/{id}', [SurveyController::class, 'doSurvey'])->name('survey.do_survey');
Route::post('/survey/submit', [SurveyController::class, 'submitSurvey'])->name('survey.submit');
