<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RevisiTbTargets extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_targets', function (Blueprint $table) {
            $table->dropColumn('tgl_target');
            $table->dropColumn('bulan_target');
            $table->dropColumn('target');

            $table->integer('year_target')->nullable();
            $table->bigInteger('target_01')->after('year_target')->nullable();
            $table->bigInteger('target_02')->after('target_01')->nullable();
            $table->bigInteger('target_03')->after('target_02')->nullable();
            $table->bigInteger('target_04')->after('target_03')->nullable();
            $table->bigInteger('target_05')->after('target_04')->nullable();
            $table->bigInteger('target_06')->after('target_05')->nullable();
            $table->bigInteger('target_07')->after('target_06')->nullable();
            $table->bigInteger('target_08')->after('target_07')->nullable();
            $table->bigInteger('target_09')->after('target_08')->nullable();
            $table->bigInteger('target_10')->after('target_09')->nullable();
            $table->bigInteger('target_11')->after('target_10')->nullable();
            $table->bigInteger('target_12')->after('target_11')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_targets', function (Blueprint $table) {
            $table->dropColumn('year_target');
            $table->dropColumn('target_01');
            $table->dropColumn('target_02');
            $table->dropColumn('target_03');
            $table->dropColumn('target_04');
            $table->dropColumn('target_05');
            $table->dropColumn('target_06');
            $table->dropColumn('target_07');
            $table->dropColumn('target_08');
            $table->dropColumn('target_09');
            $table->dropColumn('target_10');
            $table->dropColumn('target_11');
            $table->dropColumn('target_12');
        });
    }
}
