<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNewField2ToTbSpks extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->bigInteger('harga1')->after('catatan_khusus')->nullable();
            $table->bigInteger('harga2')->after('harga1')->nullable();
            $table->bigInteger('harga3')->after('harga2')->nullable();
            $table->bigInteger('harga4')->after('harga3')->nullable();
            $table->boolean('pisaupond')->after('catatan_khusus')->nullable();
            $table->boolean('klisepoly')->after('pisaupond')->nullable();
            $table->boolean('kliseemboss')->after('klisepoly')->nullable();
            $table->boolean('klisespotuv')->after('kliseemboss')->nullable();
            $table->boolean('ctv')->after('klisespotuv')->nullable();
            $table->bigInteger('harga_pracetak')->after('ctv')->nullable();
            $table->bigInteger('harga_cetak')->after('harga_pracetak')->nullable();
            $table->bigInteger('harga_laminasi')->after('harga_cetak')->nullable();
            $table->bigInteger('harga_poly')->after('harga_laminasi')->nullable();
            $table->bigInteger('harga_emboss')->after('harga_poly')->nullable();
            $table->bigInteger('harga_spotuv')->after('harga_emboss')->nullable();
            $table->bigInteger('harga_lapis')->after('harga_spotuv')->nullable();
            $table->bigInteger('harga_jendelamika')->after('harga_lapis')->nullable();
            $table->bigInteger('harga_pond')->after('harga_jendelamika')->nullable();
            $table->bigInteger('harga_finishing')->after('harga_pond')->nullable();
            $table->date('tgl_selesai_all')->after('tgl_kirim')->nullable();

            $table->dropColumn(['harga', 'bahan_baku']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->dropColumn(['harga1', 'harga2', 'harga3', 'harga4', 'pisaupond', 'klisepoly', 'kliseemboss', 'klisespotuv', 'ctv', 'harga_pracetak', 'harga_cetak', 'harga_laminasi', 'harga_poly', 'harga_emboss', 'harga_spotuv', 'harga_lapis', 'harga_jendelamika', 'harga_pond', 'harga_finishing', 'tgl_selesai_all']);
            $table->bigInteger('harga');
            $table->string('bahan_baku');
        });
    }
}
