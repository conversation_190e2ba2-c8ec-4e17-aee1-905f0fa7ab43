<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterTableOrdersFlaggingForecast extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_orders', function (Blueprint $table) {
            $table->boolean('is_forecast')->nullable();
            $table->string('forecast_month')->nullable();
            $table->string('forecast_year')->nullable();
            $table->tinyInteger('forecast_week')->nullable();
            $table->integer('expected_revenue')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_orders', function (Blueprint $table) {
            $table->dropColumn('is_forecast');
            $table->dropColumn('forecast_month');
            $table->dropColumn('forecast_year');
            $table->dropColumn('forecast_week');
            $table->dropColumn('expected_revenue');
        });
    }
}
