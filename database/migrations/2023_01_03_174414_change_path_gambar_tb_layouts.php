<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangePathGambarTbLayouts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_layouts', function (Blueprint $table) {
            $table->string('path_gambar_produk')->nullable()->change();
            $table->string('path_gambar_layout')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_layouts', function (Blueprint $table) {
            $table->double('path_gambar_produk')->nullable()->change();
            $table->double('path_gambar_layout')->nullable()->change();
        });
    }
}
