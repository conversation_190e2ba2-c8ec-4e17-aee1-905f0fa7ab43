<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDimensiToTbLayouts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_layouts', function (Blueprint $table) {
            $table->dropColumn('dimensi');
            $table->string('dimensi_panjang')->nullable()->after('jenis_box');
            $table->string('dimensi_lebar')->nullable()->after('jenis_box');
            $table->string('dimensi_tinggi')->nullable()->after('jenis_box');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_layouts', function (Blueprint $table) {
            $table->dropColumn('dimensi_panjang');
            $table->dropColumn('dimensi_lebar');
            $table->dropColumn('dimensi_tinggi');
        });
    }
}
