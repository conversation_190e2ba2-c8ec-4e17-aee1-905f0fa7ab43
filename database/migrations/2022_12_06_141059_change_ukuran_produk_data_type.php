<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeUkuranProdukDataType extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_produksis', function (Blueprint $table) {
            $table->dropColumn('luas_permukaan');
            $table->dropColumn('dimensi_produk');
            $table->float('dp_panjang')->nullable()->change();
            $table->float('dp_lebar')->nullable()->change();
            $table->float('dp_tinggi')->nullable()->change();
            $table->float('lp_panjang')->nullable()->change();
            $table->float('lp_lebar')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_produksis', function (Blueprint $table) {
            $table->integer('luas_permukaan')->nullable();
            $table->integer('dimensi_produk')->nullable();
            $table->integer('dp_panjang')->nullable();
            $table->integer('dp_lebar')->nullable();
            $table->integer('dp_tinggi')->nullable();
            $table->integer('lp_panjang')->nullable();
            $table->integer('lp_lebar')->nullable();
        });
    }
}
