<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNewFieldToTbCustomers extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_customers', function (Blueprint $table) {
            //
            $table->string('email_bisnis')->after('no_hp')->nullable();
            $table->string('posisi_pic_cust')->after('email_bisnis')->nullable();
            $table->string('jenis_usaha')->after('posisi_pic_cust')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_customers', function (Blueprint $table) {
            //
            $table->dropColumn(['email_bisnis', 'posisi_pic_cust', 'jenis_usaha']);
        });
    }
}
