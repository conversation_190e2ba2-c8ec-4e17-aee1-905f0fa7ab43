<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPpnToTbProduksis extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_produksis', function (Blueprint $table) {
            $table->boolean('ppn')->after('total_harga')->default(false);
            $table->integer('ppn_percent')->after('ppn')->nullable();
            $table->bigInteger('total_ppn')->after('ppn_percent')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_produksis', function (Blueprint $table) {
            $table->dropColumn('ppn');
            // $table->dropColumn(['ppn', 'ppn_percent', 'total_ppn']);
        });
    }
}
