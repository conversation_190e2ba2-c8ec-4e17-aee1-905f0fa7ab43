<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNewFieldToTbSpks extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_spks', function (Blueprint $table) {

            $table->date('tgl_selesai_1')->after('tgl_produksi_1')->nullable();
            $table->date('tgl_selesai_2')->after('tgl_produksi_2')->nullable();
            $table->date('tgl_selesai_3')->after('tgl_produksi_3')->nullable();
            $table->date('tgl_selesai_4')->after('tgl_produksi_4')->nullable();
            $table->date('tgl_selesai_5')->after('tgl_produksi_5')->nullable();
            $table->date('tgl_selesai_6')->after('tgl_produksi_6')->nullable();
            $table->date('tgl_selesai_7')->after('tgl_produksi_7')->nullable();
            $table->date('tgl_selesai_8')->after('tgl_produksi_8')->nullable();
            $table->date('tgl_selesai_9')->after('tgl_produksi_9')->nullable();
            $table->date('tgl_selesai_10')->after('tgl_produksi_10')->nullable();
            $table->bigInteger('bahan_baku')->after('catatan_khusus')->nullable();
            $table->bigInteger('total_keseluruhan_harga')->after('tgl_kirim')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            //
        });
    }
}
