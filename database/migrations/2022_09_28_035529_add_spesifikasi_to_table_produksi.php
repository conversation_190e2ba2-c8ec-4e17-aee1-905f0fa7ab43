<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSpesifikasiToTableProduksi extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_produksis', function (Blueprint $table) {
            $table->integer('dimensi_produk')->after('spesifikasi')->nullable();
            $table->integer('luas_permukaan')->after('spesifikasi')->nullable();
            $table->string('jenis_kertas')->after('spesifikasi')->nullable();
            $table->string('gramasi')->after('spesifikasi')->nullable();
            $table->string('finishing')->after('spesifikasi')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_produksis', function (Blueprint $table) {
            $table->dropColumn('dimensi_produk');
            $table->dropColumn('luas_permukaan');
            $table->dropColumn('jenis_kertas');
            $table->dropColumn('gramasi');
            $table->dropColumn('finishing');
        });
    }
}
