<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeTglFlagProductionDatatype extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->dateTime('tgl_flag_status_1')->nullable()->change();
            $table->dateTime('tgl_flag_status_2')->nullable()->change();
            $table->dateTime('tgl_flag_status_3')->nullable()->change();
            $table->dateTime('tgl_flag_status_4')->nullable()->change();
            $table->dateTime('tgl_flag_status_5')->nullable()->change();
            $table->dateTime('tgl_flag_status_6')->nullable()->change();
            $table->dateTime('tgl_flag_status_7')->nullable()->change();
            $table->dateTime('tgl_flag_status_8')->nullable()->change();
            $table->dateTime('tgl_flag_status_9')->nullable()->change();
            $table->dateTime('tgl_flag_status_10')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->date('tgl_flag_status_1')->nullable()->change();
            $table->date('tgl_flag_status_2')->nullable()->change();
            $table->date('tgl_flag_status_3')->nullable()->change();
            $table->date('tgl_flag_status_4')->nullable()->change();
            $table->date('tgl_flag_status_5')->nullable()->change();
            $table->date('tgl_flag_status_6')->nullable()->change();
            $table->date('tgl_flag_status_7')->nullable()->change();
            $table->date('tgl_flag_status_8')->nullable()->change();
            $table->date('tgl_flag_status_9')->nullable()->change();
            $table->date('tgl_flag_status_10')->nullable()->change();
        });
    }
}
