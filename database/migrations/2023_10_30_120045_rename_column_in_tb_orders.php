<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RenameColumnInTbOrders extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_orders', function (Blueprint $table) {
            $table->renameColumn('pp_1', 'pp_harga');
            $table->renameColumn('pp_2', 'pp_waktu');
            $table->renameColumn('pp_3', 'pp_pengiriman');
            $table->renameColumn('pp_4', 'pp_kualitas');
            $table->renameColumn('pp_5', 'pp_pembayaran');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_orders', function (Blueprint $table) {
            //
        });
    }
}
