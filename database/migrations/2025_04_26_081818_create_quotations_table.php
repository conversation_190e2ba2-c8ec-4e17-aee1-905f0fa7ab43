<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateQuotationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('quotations', function (Blueprint $table) {
            $table->id();
            $table->uuid('quotation_key')->unique();
            $table->unsignedBigInteger('company_id')->nullable();
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->string('no_quotation')->nullable();
            $table->string('file_quotation')->nullable();
            $table->string('status')->default('draft')->comment('draft, submitted');

            $table->integer('total_quantity')->nullable();
            $table->decimal('total_price', 15, 2)->nullable();


            $table->integer('no_year')->nullable();
            $table->string('no_roman_month')->nullable();
            $table->integer('no_number')->nullable();
            
            $table->unsignedBigInteger('created_by_id')->nullable();
            $table->unsignedBigInteger('updated_by_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('quotations');
    }
}
