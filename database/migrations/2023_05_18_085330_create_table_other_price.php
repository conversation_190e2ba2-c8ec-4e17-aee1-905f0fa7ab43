<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableOtherPrice extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_other_price', function (Blueprint $table) {
            $table->id('id_other_price');
            $table->bigInteger('id_jenis_ongkos');
            $table->bigInteger('id_tools');
            $table->float('price_per_item');
            $table->bigInteger('price_minimum');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_other_price');
    }
}
