<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddFieldLampiranToTableInvoices extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->string('file_lampiran')->nullable()->after('status');
            $table->date('tanggal_pelunasan')->nullable()->after('tanggal_jatuh_tempo_tambahan');
            $table->date('tanggal_dp')->nullable()->after('tanggal_jatuh_tempo_tambahan');
            $table->string('quotation_key')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('invoices', function (Blueprint $table) {
            $table->dropColumn('file_lampiran');
            $table->dropColumn('tanggal_pelunasan');
            $table->dropColumn('tanggal_dp');
            $table->dropColumn('quotation_key');
        });
    }
}
