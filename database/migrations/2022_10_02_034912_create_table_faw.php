<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableFaw extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_faws', function (Blueprint $table) {
            $table->id('id_faw');
            $table->bigInteger('id_order');
            $table->string('order_key');
            $table->date('tgl_faw')->nullable();
            $table->text('keterangan_tambahan')->nullable();
            $table->integer('id_ttd')->nullable();
            $table->string('file_final')->nullable();
            $table->string('path_lampiran')->nullable();
            $table->string('waktu_produksi')->nullable();
            $table->date('tgl_deadline')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_faws');
    }
}
