<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateTableEngagementRecaps extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_engagement_recaps', function (Blueprint $table) {
            $table->id();
            $table->unsignedMediumInteger('company_id')->nullable();
            $table->smallInteger('type')->default(0)->nullable();
            $table->date('due_date')->nullable();
            $table->string('title')->nullable();
            $table->string('detail')->nullable();
            $table->unsignedInteger('assign_to')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->boolean('is_completed')->nullable();
            $table->unsignedInteger('created_by')->nullable();
            $table->datetime('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->datetime('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_engagement_recaps');
    }
}
