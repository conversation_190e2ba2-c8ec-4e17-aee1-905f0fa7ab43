<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableLayout extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_layouts', function (Blueprint $table) {
            $table->id('id_layout');
            $table->bigInteger('kode_layout');
            $table->string('nama_layout');
            $table->bigInteger('id_kategori_layout');
            $table->string('path_gambar_produk');
            $table->string('path_gambar_layout');
            $table->string('jenis_box');
            $table->string('dimensi');
            $table->string('tambahan_sisi_kertas');
            $table->integer('lebar_permukaan_cetak-panjang-1');
            $table->integer('lebar_permukaan_cetak-lebar-1');
            $table->integer('lebar_permukaan_cetak-tinggi-1');
            $table->integer('panjang_permukaan_cetak-panjang-1');
            $table->integer('panjang_permukaan_cetak-lebar-1');
            $table->integer('panjang_permukaan_cetak-tinggi-1');
            $table->integer('lebar_permukaan_cetak-panjang-2');
            $table->integer('lebar_permukaan_cetak-lebar-2');
            $table->integer('lebar_permukaan_cetak-tinggi-2');
            $table->integer('panjang_permukaan_cetak-panjang-2');
            $table->integer('panjang_permukaan_cetak-lebar-2');
            $table->integer('panjang_permukaan_cetak-tinggi-2');
            $table->integer('lebar_permukaan_cetak-panjang-3');
            $table->integer('lebar_permukaan_cetak-lebar-3');
            $table->integer('lebar_permukaan_cetak-tinggi-3');
            $table->integer('panjang_permukaan_cetak-panjang-3');
            $table->integer('panjang_permukaan_cetak-lebar-3');
            $table->integer('panjang_permukaan_cetak-tinggi-3');
            

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_layouts');
    }
}
