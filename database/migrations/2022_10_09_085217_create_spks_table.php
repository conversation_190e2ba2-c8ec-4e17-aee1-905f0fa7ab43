<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSpksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_spks', function (Blueprint $table) {
            $table->id('id_spk');
            $table->string('order_key');
            $table->date('tgl_deadline');
            $table->string('catatan_khusus');

            $table->string('progress_produksi_1')->nullable();
            $table->integer('jumlah_awal_pp_1')->nullable();
            $table->integer('jumlah_hasil_pp_1')->nullable();
            $table->integer('reject_pp_1')->nullable();
            $table->string('vendor_1')->nullable();
            $table->date('tgl_produksi_1')->nullable();
            $table->string('pic_validasi_1')->nullable();
            $table->string('flag_status_1')->nullable();

            $table->string('progress_produksi_2')->nullable();
            $table->integer('jumlah_awal_pp_2')->nullable();
            $table->integer('jumlah_hasil_pp_2')->nullable();
            $table->integer('reject_pp_2')->nullable();
            $table->string('vendor_2')->nullable();
            $table->date('tgl_produksi_2')->nullable();
            $table->string('pic_validasi_2')->nullable();
            $table->string('flag_status_2')->nullable();

            $table->string('progress_produksi_3')->nullable();
            $table->integer('jumlah_awal_pp_3')->nullable();
            $table->integer('jumlah_hasil_pp_3')->nullable();
            $table->integer('reject_pp_3')->nullable();
            $table->string('vendor_3')->nullable();
            $table->date('tgl_produksi_3')->nullable();
            $table->string('pic_validasi_3')->nullable();
            $table->string('flag_status_3')->nullable();

            $table->string('progress_produksi_4')->nullable();
            $table->integer('jumlah_awal_pp_4')->nullable();
            $table->integer('jumlah_hasil_pp_4')->nullable();
            $table->integer('reject_pp_4')->nullable();
            $table->string('vendor_4')->nullable();
            $table->date('tgl_produksi_4')->nullable();
            $table->string('pic_validasi_4')->nullable();
            $table->string('flag_status_4')->nullable();

            $table->string('progress_produksi_5')->nullable();
            $table->integer('jumlah_awal_pp_5')->nullable();
            $table->integer('jumlah_hasil_pp_5')->nullable();
            $table->integer('reject_pp_5')->nullable();
            $table->string('vendor_5')->nullable();
            $table->date('tgl_produksi_5')->nullable();
            $table->string('pic_validasi_5')->nullable();
            $table->string('flag_status_5')->nullable();

            $table->string('progress_produksi_6')->nullable();
            $table->integer('jumlah_awal_pp_6')->nullable();
            $table->integer('jumlah_hasil_pp_6')->nullable();
            $table->integer('reject_pp_6')->nullable();
            $table->string('vendor_6')->nullable();
            $table->date('tgl_produksi_6')->nullable();
            $table->string('pic_validasi_6')->nullable();
            $table->string('flag_status_6')->nullable();

            $table->string('progress_produksi_7')->nullable();
            $table->integer('jumlah_awal_pp_7')->nullable();
            $table->integer('jumlah_hasil_pp_7')->nullable();
            $table->integer('reject_pp_7')->nullable();
            $table->string('vendor_7')->nullable();
            $table->date('tgl_produksi_7')->nullable();
            $table->string('pic_validasi_7')->nullable();
            $table->string('flag_status_7')->nullable();

            $table->string('progress_produksi_8')->nullable();
            $table->integer('jumlah_awal_pp_8')->nullable();
            $table->integer('jumlah_hasil_pp_8')->nullable();
            $table->integer('reject_pp_8')->nullable();
            $table->string('vendor_8')->nullable();
            $table->date('tgl_produksi_8')->nullable();
            $table->string('pic_validasi_8')->nullable();
            $table->string('flag_status_8')->nullable();

            $table->string('progress_produksi_9')->nullable();
            $table->integer('jumlah_awal_pp_9')->nullable();
            $table->integer('jumlah_hasil_pp_9')->nullable();
            $table->integer('reject_pp_9')->nullable();
            $table->string('vendor_9')->nullable();
            $table->date('tgl_produksi_9')->nullable();
            $table->string('pic_validasi_9')->nullable();
            $table->string('flag_status_9')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_spks');
    }
}
