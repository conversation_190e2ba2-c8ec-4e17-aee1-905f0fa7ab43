<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePenanggulanganMasalahsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_fpms', function (Blueprint $table) {
            $table->id('id_fpms');
            $table->string('order_key')->nullable();
            $table->string('sko_key')->nullable();
            $table->date('tgl_masalah')->nullable();
            $table->string('kategori_masalah')->nullable();
            $table->text('detail_masalah')->nullable();
            $table->string('solusi')->nullable();
            $table->text('detail_solusi')->nullable();
            $table->string('cost')->nullable();
            $table->date('tgl_deadline_perbaikan')->nullable();
            $table->string('QC_1')->nullable();
            $table->string('QC_2')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_fpms');
    }
}
