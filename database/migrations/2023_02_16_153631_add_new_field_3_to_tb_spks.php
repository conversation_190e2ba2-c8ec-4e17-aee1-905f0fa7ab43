<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNewField3ToTbSpks extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->bigInteger('harga5')->after('harga4')->nullable();
            $table->bigInteger('harga6')->after('harga5')->nullable();
            $table->boolean('kertas')->after('ctv')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->dropColumn(['harga5', 'harga6', 'kertas']);
        });
    }
}
