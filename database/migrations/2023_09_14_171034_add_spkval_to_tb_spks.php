<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSpkvalToTbSpks extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->boolean('filmpisau')->after('kertas')->nullable();
            $table->boolean('bahanfinishing')->after('filmpisau')->nullable();
            $table->bigInteger('harga7')->after('harga6')->nullable();
            $table->bigInteger('harga8')->after('harga7')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->boolean('filmpisau')->after('kertas')->nullable();
            $table->boolean('bahanfinishing')->after('filmpisau')->nullable();
            $table->bigInteger('harga7')->after('harga6')->nullable();
            $table->bigInteger('harga8')->after('harga7')->nullable();
        });
    }
}
