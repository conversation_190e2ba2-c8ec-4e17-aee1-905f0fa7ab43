<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateActivityTaskOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('activity_task_orders', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->bigInteger('order_id')->nullable();
            $table->uuid('order_key')->nullable();
            $table->longText('title')->nullable();
            $table->longText('detail')->nullable();
            $table->string('due_date')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->boolean('is_completed')->nullable();
            $table->bigInteger('created_by_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('activity_task_orders');
    }
}
