<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTbProfiles extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_profiles', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('id_customer');
            $table->string('durasi_berdiri')->nullable();
            $table->string('skala_bisnis')->nullable();
            $table->string('tingkat_penjualan')->nullable();
            $table->string('segmen_pasar')->nullable();
            $table->string('channel_marketing')->nullable();
            $table->string('nama_campaign')->nullable();
            $table->date('anniv_usaha')->nullable();
            $table->integer('imlek')->nullable();
            $table->integer('idul_fitri')->nullable();
            $table->integer('natal')->nullable();
            $table->bigInteger('keb_packaging')->nullable();
            $table->integer('potensi_freq')->nullable();
            $table->integer('profiling')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_profiles');
    }
}
