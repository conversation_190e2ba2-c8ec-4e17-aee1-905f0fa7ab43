<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ModifyTbSumberTargetsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_sumber_targets', function (Blueprint $table) {
            // Add new columns
            $table->bigInteger('id_sumber')->after('id_customer')->nullable();
            $table->bigInteger('target_sumber')->after('id_sumber')->nullable();

            // Remove the 'month' column if you want to drop it
            // $table->dropColumn('month');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_sumber_targets', function (Blueprint $table) {
            // Reverse the changes made in the 'up' method
            $table->dropColumn(['id_sumber', 'target_sumber']);
        });
    }
}
