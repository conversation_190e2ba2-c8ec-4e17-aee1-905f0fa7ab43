<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnSpecialRumusAndMesin extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_layouts', function (Blueprint $table) {
            $table->smallInteger('tipe_rumus')->nullable();
        });

        Schema::table('tb_mesin', function (Blueprint $table) {
            $table->integer('insheet')->nullable();
            $table->integer('koefisien')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_layouts', function (Blueprint $table) {
            $table->dropColumn('tipe_rumus');
        });

        Schema::table('tb_mesin', function (Blueprint $table) {
            $table->dropColumn('insheet');
            $table->dropColumn('koefisien');
        });
    }
}
