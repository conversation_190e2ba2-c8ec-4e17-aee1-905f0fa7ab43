<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateInvoicesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->uuid('invoice_key')->unique();
            $table->unsignedBigInteger('company_id')->nullable();
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->string('no_invoice')->nullable();
            $table->string('file_invoice')->nullable();
            $table->enum('type', ["DP", "pelunasan1", "pelunasan2", "pelunasan3"])->default('DP');
            $table->date('tanggal_invoice')->nullable();
            $table->date('tanggal_jatuh_tempo')->nullable();
            $table->date('tanggal_jatuh_tempo_tambahan')->nullable();

            $table->string('status')->default('draft')->comment('draft, submitted');
            $table->string('status_paid')->default('draft')->comment('paid, unpaid');
            $table->boolean('is_ppn')->default(false)->comment('is this invoice include PPN?');

            $table->integer('total_quantity')->nullable();
            $table->bigInteger('diskon')->nullable();
            $table->bigInteger('biaya_pengiriman')->nullable();
            $table->bigInteger('subtotal')->nullable();
            $table->bigInteger('total_ppn')->nullable();
            $table->bigInteger('persentase_ppn')->nullable();

            $table->string('invoice_type')->nullable();
            
            $table->bigInteger('total')->nullable();
            $table->bigInteger('dp_terbayar')->nullable();
            $table->bigInteger('sisa_pelunasan')->nullable();
            $table->bigInteger('total_pembayaran')->nullable();


            $table->integer('no_year')->nullable();
            $table->string('no_roman_month')->nullable();
            $table->integer('no_number')->nullable();

            $table->string('no_po')->nullable();
            $table->text('note')->nullable();
            $table->unsignedBigInteger('created_by_id')->nullable();
            $table->unsignedBigInteger('updated_by_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoices');
    }
}
