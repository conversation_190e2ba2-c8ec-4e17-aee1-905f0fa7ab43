<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableCustomer extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_customers', function (Blueprint $table) {
            $table->id('id_customer');
            $table->string('kode_kustomer');
            $table->string('nama');
            $table->string('no_hp');
            $table->string('tipe_instansi')->nullable();
            $table->string('nama_instansi')->nullable();
            $table->text('alamat_instansi')->nullable();
            $table->string('npwp')->nullable();
            $table->string('grade')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_customers');
    }
}
