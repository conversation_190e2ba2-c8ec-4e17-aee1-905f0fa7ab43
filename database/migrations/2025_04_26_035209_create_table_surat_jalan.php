<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class CreateTableSuratJalan extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_surat_jalan', function (Blueprint $table) {
            $table->id();
            $table->uuid('surat_jalan_key')->unique();
            $table->string('no_surat_jalan')->nullable();
            $table->unsignedInteger('company_id')->nullable();
            $table->unsignedInteger('customer_id')->nullable();
            $table->string('no_po')->nullable();
            $table->date('delivery_date')->nullable();
            $table->date('print_date')->nullable();
            $table->text('path_lampiran')->nullable();
            $table->text('sign_lampiran')->nullable();
            $table->integer('total_quantity')->nullable();
            $table->integer('no_year')->nullable();
            $table->string('no_roman_month')->nullable();
            $table->string('status')->nullable();
            $table->integer('no_number')->nullable();

            $table->unsignedBigInteger('created_by_id')->nullable();
            $table->unsignedBigInteger('updated_by_id')->nullable();
            
            $table->datetime('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->datetime('updated_at')->default(DB::raw('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'));
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_surat_jalan');
        // Schema::dropIfExists('tb_surat_jalan_detail');
    }
}
