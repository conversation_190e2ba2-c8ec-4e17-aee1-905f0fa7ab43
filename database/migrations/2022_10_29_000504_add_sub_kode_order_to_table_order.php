<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSubKodeOrderToTableOrder extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_orders', function (Blueprint $table) {
            $table->string('sko_key')->after('order_key')->nullable();
            $table->string('sko')->after('sko_key')->nullable();
        });

        Schema::table('tb_produksis', function (Blueprint $table) {
            $table->string('sko_key')->after('order_key')->nullable();
        });

        Schema::table('tb_faws', function (Blueprint $table) {
            $table->string('sko_key')->after('order_key')->nullable();
        });

        Schema::table('tb_spks', function (Blueprint $table) {
            $table->string('sko_key')->after('order_key')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_orders', function (Blueprint $table) {
            $table->dropColumn('sko_key');
            $table->dropColumn('sko');
        });

        Schema::table('tb_produksis', function (Blueprint $table) {
            $table->dropColumn('sko_key');
        });

        Schema::table('tb_faws', function (Blueprint $table) {
            $table->dropColumn('sko_key');
        });

        Schema::table('tb_spks', function (Blueprint $table) {
            $table->dropColumn('sko_key');
        });
    }
}
