<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSuratJalanDetails extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_surat_jalan_detail', function (Blueprint $table) {
            $table->id();
            $table->uuid('surat_jalan_key')->index();
            $table->unsignedBigInteger('order_id')->nullable();
            $table->string('kode_order')->nullable();
            $table->string('product_name')->nullable();
            $table->string('jumlah_koli')->nullable();
            $table->integer('quantity')->nullable();
            $table->string('description')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_surat_jalan_detail');
    }
}
