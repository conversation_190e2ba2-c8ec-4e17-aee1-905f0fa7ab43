<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableRefferenceProvinceAndRelation extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_province', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('tb_district', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('province_id')->nullable();
            $table->string('name')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('tb_sub_district', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('district_id')->nullable();
            $table->string('name')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('tb_village', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('sub_district_id')->nullable();
            $table->string('name')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_province');
        Schema::dropIfExists('tb_district');
        Schema::dropIfExists('tb_sub_district');
        Schema::dropIfExists('tb_village');
    }
}
