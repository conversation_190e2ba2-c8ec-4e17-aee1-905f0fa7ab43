<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ChangeTbLayouts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_layouts', function (Blueprint $table) {
            $table->float('path_gambar_produk')->nullable()->change();
            $table->float('path_gambar_layout')->nullable()->change();
            $table->float('jenis_box')->nullable()->change();
            $table->float('dimensi')->nullable()->change();
            $table->float('tambahan_sisi_kertas')->nullable()->change();
            $table->dropColumn('lebar_permukaan_cetak-panjang-1');
            $table->dropColumn('lebar_permukaan_cetak-lebar-1');
            $table->dropColumn('lebar_permukaan_cetak-tinggi-1');
            $table->dropColumn('panjang_permukaan_cetak-panjang-1');
            $table->dropColumn('panjang_permukaan_cetak-lebar-1');
            $table->dropColumn('panjang_permukaan_cetak-tinggi-1');
            $table->dropColumn('lebar_permukaan_cetak-panjang-2');
            $table->dropColumn('lebar_permukaan_cetak-lebar-2');
            $table->dropColumn('lebar_permukaan_cetak-tinggi-2');
            $table->dropColumn('panjang_permukaan_cetak-panjang-2');
            $table->dropColumn('panjang_permukaan_cetak-lebar-2');
            $table->dropColumn('panjang_permukaan_cetak-tinggi-2');
            $table->dropColumn('lebar_permukaan_cetak-panjang-3');
            $table->dropColumn('lebar_permukaan_cetak-lebar-3');
            $table->dropColumn('lebar_permukaan_cetak-tinggi-3');
            $table->dropColumn('panjang_permukaan_cetak-panjang-3');
            $table->dropColumn('panjang_permukaan_cetak-lebar-3');
            $table->dropColumn('panjang_permukaan_cetak-tinggi-3');
            $table->integer('rumus_lebar_cetak_1_angka_1')->nullable();
            $table->string('rumus_lebar_cetak_1_parameter_1')->nullable();
            $table->integer('rumus_lebar_cetak_1_angka_2')->nullable();
            $table->string('rumus_lebar_cetak_1_parameter_2')->nullable();
            $table->integer('rumus_lebar_cetak_1_angka_3')->nullable();
            $table->string('rumus_lebar_cetak_1_parameter_3')->nullable();
            $table->integer('rumus_panjang_cetak_1_angka_1')->nullable();
            $table->string('rumus_panjang_cetak_1_parameter_1')->nullable();
            $table->integer('rumus_panjang_cetak_1_angka_2')->nullable();
            $table->string('rumus_panjang_cetak_1_parameter_2')->nullable();
            $table->integer('rumus_panjang_cetak_1_angka_3')->nullable();
            $table->string('rumus_panjang_cetak_1_parameter_3')->nullable();
            $table->integer('rumus_lebar_cetak_2_angka_1')->nullable();
            $table->string('rumus_lebar_cetak_2_parameter_1')->nullable();
            $table->integer('rumus_lebar_cetak_2_angka_2')->nullable();
            $table->string('rumus_lebar_cetak_2_parameter_2')->nullable();
            $table->integer('rumus_lebar_cetak_2_angka_3')->nullable();
            $table->string('rumus_lebar_cetak_2_parameter_3')->nullable();
            $table->integer('rumus_panjang_cetak_2_angka_1')->nullable();
            $table->string('rumus_panjang_cetak_2_parameter_1')->nullable();
            $table->integer('rumus_panjang_cetak_2_angka_2')->nullable();
            $table->string('rumus_panjang_cetak_2_parameter_2')->nullable();
            $table->integer('rumus_panjang_cetak_2_angka_3')->nullable();
            $table->string('rumus_panjang_cetak_2_parameter_3')->nullable();
            $table->integer('rumus_lebar_cetak_3_angka_1')->nullable();
            $table->string('rumus_lebar_cetak_3_parameter_1')->nullable();
            $table->integer('rumus_lebar_cetak_3_angka_2')->nullable();
            $table->string('rumus_lebar_cetak_3_parameter_2')->nullable();
            $table->integer('rumus_lebar_cetak_3_angka_3')->nullable();
            $table->string('rumus_lebar_cetak_3_parameter_3')->nullable();
            $table->integer('rumus_panjang_cetak_3_angka_1')->nullable();
            $table->string('rumus_panjang_cetak_3_parameter_1')->nullable();
            $table->integer('rumus_panjang_cetak_3_angka_2')->nullable();
            $table->string('rumus_panjang_cetak_3_parameter_2')->nullable();
            $table->integer('rumus_panjang_cetak_3_angka_3')->nullable();
            $table->string('rumus_panjang_cetak_3_parameter_3')->nullable();
            
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_layouts', function (Blueprint $table) {
            $table->dropColumn('rumus_lebar_cetak_1_angka_1');
            $table->dropColumn('rumus_lebar_cetak_1_parameter_1');
            $table->dropColumn('rumus_lebar_cetak_1_angka_2');
            $table->dropColumn('rumus_lebar_cetak_1_parameter_2');
            $table->dropColumn('rumus_lebar_cetak_1_angka_3');
            $table->dropColumn('rumus_lebar_cetak_1_parameter_3');
            $table->dropColumn('rumus_panjang_cetak_1_angka_1');
            $table->dropColumn('rumus_panjang_cetak_1_parameter_1');
            $table->dropColumn('rumus_panjang_cetak_1_angka_2');
            $table->dropColumn('rumus_panjang_cetak_1_parameter_2');
            $table->dropColumn('rumus_panjang_cetak_1_angka_3');
            $table->dropColumn('rumus_panjang_cetak_1_parameter_3');
            $table->dropColumn('rumus_lebar_cetak_2_angka_1');
            $table->dropColumn('rumus_lebar_cetak_2_parameter_1');
            $table->dropColumn('rumus_lebar_cetak_2_angka_2');
            $table->dropColumn('rumus_lebar_cetak_2_parameter_2');
            $table->dropColumn('rumus_lebar_cetak_2_angka_3');
            $table->dropColumn('rumus_lebar_cetak_2_parameter_3');
            $table->dropColumn('rumus_panjang_cetak_2_angka_1');
            $table->dropColumn('rumus_panjang_cetak_2_parameter_1');
            $table->dropColumn('rumus_panjang_cetak_2_angka_2');
            $table->dropColumn('rumus_panjang_cetak_2_parameter_2');
            $table->dropColumn('rumus_panjang_cetak_2_angka_3');
            $table->dropColumn('rumus_panjang_cetak_2_parameter_3');
            $table->dropColumn('rumus_lebar_cetak_3_angka_1');
            $table->dropColumn('rumus_lebar_cetak_3_parameter_1');
            $table->dropColumn('rumus_lebar_cetak_3_angka_2');
            $table->dropColumn('rumus_lebar_cetak_3_parameter_2');
            $table->dropColumn('rumus_lebar_cetak_3_angka_3');
            $table->dropColumn('rumus_lebar_cetak_3_parameter_3');
            $table->dropColumn('rumus_panjang_cetak_3_angka_1');
            $table->dropColumn('rumus_panjang_cetak_3_parameter_1');
            $table->dropColumn('rumus_panjang_cetak_3_angka_2');
            $table->dropColumn('rumus_panjang_cetak_3_parameter_2');
            $table->dropColumn('rumus_panjang_cetak_3_angka_3');
            $table->dropColumn('rumus_panjang_cetak_3_parameter_3');
        });
    }
}
