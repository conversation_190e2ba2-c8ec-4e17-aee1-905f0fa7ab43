<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableDesign extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_designs', function (Blueprint $table) {
            $table->id('id_design');
            $table->string('kode_design');
            $table->bigInteger('id_layout');
            $table->integer('ukuran_panjang');
            $table->integer('ukuran_lebar');
            $table->integer('ukuran_tinggi');
            $table->string('orientasi');
            $table->string('combi');
            $table->string('ukuran_plano');
            $table->integer('ukuran_kerta_potong_panjang');
            $table->integer('ukuran_kertas_potong_lebar');
            $table->integer('hasil_jumlah_kertas');
            $table->integer('hasil_jumlah_wasting');
            $table->string('material_type');
            $table->string('material_standard');
            $table->string('jenis_printing');
            $table->string('jenis_mesin');
            $table->string('jenis_coating');
            $table->string('jenis_surface_process');
            $table->string('jenis_others');
            $table->integer('quantity');
            $table->text('design_notes');
            $table->bigInteger('unit_price');
            $table->bigInteger('total_price');


            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_designs');
    }
}
