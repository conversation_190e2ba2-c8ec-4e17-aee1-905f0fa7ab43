<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexToTbOrders extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_orders', function (Blueprint $table) {
            $table->index(['sko_key']);
            $table->index(['waktu_kontak']);
            $table->index(['order_key']);
            $table->index(['id_pic']);
            $table->index(['id_customer']);
            $table->index(['status_deal']);
            $table->index(['flag_dummy']);
            $table->index(['updated_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_orders', function (Blueprint $table) {
            $table->dropIndex(['sko_key']);
            $table->dropIndex(['waktu_kontak']);
            $table->dropIndex(['order_key']);
            $table->dropIndex(['id_pic']);
            $table->dropIndex(['id_customer']);
            $table->dropIndex(['status_deal']);
            $table->dropIndex(['flag_dummy']);
            $table->dropIndex(['updated_at']);
        });
    }
}
