<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddVendoreksToTbSpks extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->string('flag_status_dummy')->after('check_dummy_7')->nullable();
            $table->date('tgl_flag_status_dummy')->after('flag_status_dummy')->nullable();
            $table->string('progress_produksi_ve')->after('tgl_flag_status_dummy')->nullable();
            $table->integer('jumlah_awal_pp_ve')->after('progress_produksi_ve')->nullable();
            $table->integer('jumlah_hasil_pp_ve')->after('jumlah_awal_pp_ve')->nullable();
            $table->integer('reject_pp_ve')->after('jumlah_hasil_pp_ve')->nullable();
            $table->string('vendor_ve')->after('reject_pp_ve')->nullable();
            $table->date('tgl_produksi_ve')->after('vendor_ve')->nullable();
            $table->date('tgl_selesai_ve')->after('tgl_produksi_ve')->nullable();
            $table->string('pic_validasi_ve')->after('tgl_selesai_ve')->nullable();
            $table->string('check_ve_1')->after('pic_validasi_ve')->nullable();
            $table->string('check_ve_2')->after('check_ve_1')->nullable();
            $table->string('check_ve_3')->after('check_ve_2')->nullable();
            $table->string('check_ve_4')->after('check_ve_3')->nullable();
            $table->string('catatan_ve')->after('check_ve_4')->nullable();
            $table->string('flag_status_ve')->after('catatan_ve')->nullable();
            $table->date('tgl_flag_status_ve')->after('flag_status_ve')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->dropColumn('flag_status_dummy');
            $table->dropColumn('tgl_flag_status_dummy');
            $table->dropColumn('progress_produksi_ve');
            $table->dropColumn('jumlah_awal_pp_ve');
            $table->dropColumn('jumlah_hasil_pp_ve');
            $table->dropColumn('reject_pp_ve');
            $table->dropColumn('vendor_ve');
            $table->dropColumn('tgl_produksi_ve');
            $table->dropColumn('tgl_selesai_ve');
            $table->dropColumn('pic_validasi_ve');
            $table->dropColumn('check_ve_1');
            $table->dropColumn('check_ve_2');
            $table->dropColumn('check_ve_3');
            $table->dropColumn('check_ve_4');
            $table->dropColumn('catatan_ve');
            $table->dropColumn('flag_status_ve');
            $table->dropColumn('tgl_flag_status_ve');
        });
    }
}
