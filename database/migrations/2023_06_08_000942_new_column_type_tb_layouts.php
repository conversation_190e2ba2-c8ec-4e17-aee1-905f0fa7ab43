<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class NewColumnTypeTbLayouts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_layouts', function (Blueprint $table) {
            $table->float('rumus_lebar_cetak_1_angka_1')->nullable();
            $table->string('rumus_lebar_cetak_1_parameter_1')->nullable();
            $table->float('rumus_lebar_cetak_1_angka_2')->nullable();
            $table->string('rumus_lebar_cetak_1_parameter_2')->nullable();
            $table->float('rumus_lebar_cetak_1_angka_3')->nullable();
            $table->string('rumus_lebar_cetak_1_parameter_3')->nullable();
            $table->float('rumus_lebar_cetak_1_angka_4')->nullable();
            $table->string('rumus_lebar_cetak_1_parameter_4')->nullable();
            $table->float('rumus_panjang_cetak_1_angka_1')->nullable();
            $table->string('rumus_panjang_cetak_1_parameter_1')->nullable();
            $table->float('rumus_panjang_cetak_1_angka_2')->nullable();
            $table->string('rumus_panjang_cetak_1_parameter_2')->nullable();
            $table->float('rumus_panjang_cetak_1_angka_3')->nullable();
            $table->string('rumus_panjang_cetak_1_parameter_3')->nullable();
            $table->float('rumus_panjang_cetak_1_angka_4')->nullable();
            $table->string('rumus_panjang_cetak_1_parameter_4')->nullable();
            $table->float('rumus_lebar_cetak_2_angka_1')->nullable();
            $table->string('rumus_lebar_cetak_2_parameter_1')->nullable();
            $table->float('rumus_lebar_cetak_2_angka_2')->nullable();
            $table->string('rumus_lebar_cetak_2_parameter_2')->nullable();
            $table->float('rumus_lebar_cetak_2_angka_3')->nullable();
            $table->string('rumus_lebar_cetak_2_parameter_3')->nullable();
            $table->float('rumus_lebar_cetak_2_angka_4')->nullable();
            $table->string('rumus_lebar_cetak_2_parameter_4')->nullable();
            $table->float('rumus_panjang_cetak_2_angka_1')->nullable();
            $table->string('rumus_panjang_cetak_2_parameter_1')->nullable();
            $table->float('rumus_panjang_cetak_2_angka_2')->nullable();
            $table->string('rumus_panjang_cetak_2_parameter_2')->nullable();
            $table->float('rumus_panjang_cetak_2_angka_3')->nullable();
            $table->string('rumus_panjang_cetak_2_parameter_3')->nullable();
            $table->float('rumus_panjang_cetak_2_angka_4')->nullable();
            $table->string('rumus_panjang_cetak_2_parameter_4')->nullable();
            $table->float('rumus_lebar_cetak_3_angka_1')->nullable();
            $table->string('rumus_lebar_cetak_3_parameter_1')->nullable();
            $table->float('rumus_lebar_cetak_3_angka_2')->nullable();
            $table->string('rumus_lebar_cetak_3_parameter_2')->nullable();
            $table->float('rumus_lebar_cetak_3_angka_3')->nullable();
            $table->string('rumus_lebar_cetak_3_parameter_3')->nullable();
            $table->float('rumus_lebar_cetak_3_angka_4')->nullable();
            $table->string('rumus_lebar_cetak_3_parameter_4')->nullable();
            $table->float('rumus_panjang_cetak_3_angka_1')->nullable();
            $table->string('rumus_panjang_cetak_3_parameter_1')->nullable();
            $table->float('rumus_panjang_cetak_3_angka_2')->nullable();
            $table->string('rumus_panjang_cetak_3_parameter_2')->nullable();
            $table->float('rumus_panjang_cetak_3_angka_3')->nullable();
            $table->string('rumus_panjang_cetak_3_parameter_3')->nullable();
            $table->float('rumus_panjang_cetak_3_angka_4')->nullable();
            $table->string('rumus_panjang_cetak_3_parameter_4')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_layouts', function (Blueprint $table) {
            $table->dropColumn('rumus_lebar_cetak_1_angka_1');
            $table->dropColumn('rumus_lebar_cetak_1_parameter_1');
            $table->dropColumn('rumus_lebar_cetak_1_angka_2');
            $table->dropColumn('rumus_lebar_cetak_1_parameter_2');
            $table->dropColumn('rumus_lebar_cetak_1_angka_3');
            $table->dropColumn('rumus_lebar_cetak_1_parameter_3');
            $table->dropColumn('rumus_lebar_cetak_1_angka_4');
            $table->dropColumn('rumus_lebar_cetak_1_parameter_4');
            $table->dropColumn('rumus_panjang_cetak_1_angka_1');
            $table->dropColumn('rumus_panjang_cetak_1_parameter_1');
            $table->dropColumn('rumus_panjang_cetak_1_angka_2');
            $table->dropColumn('rumus_panjang_cetak_1_parameter_2');
            $table->dropColumn('rumus_panjang_cetak_1_angka_3');
            $table->dropColumn('rumus_panjang_cetak_1_parameter_3');
            $table->dropColumn('rumus_panjang_cetak_1_angka_4');
            $table->dropColumn('rumus_panjang_cetak_1_parameter_4');
            $table->dropColumn('rumus_lebar_cetak_2_angka_1');
            $table->dropColumn('rumus_lebar_cetak_2_parameter_1');
            $table->dropColumn('rumus_lebar_cetak_2_angka_2');
            $table->dropColumn('rumus_lebar_cetak_2_parameter_2');
            $table->dropColumn('rumus_lebar_cetak_2_angka_3');
            $table->dropColumn('rumus_lebar_cetak_2_parameter_3');
            $table->dropColumn('rumus_lebar_cetak_2_angka_4');
            $table->dropColumn('rumus_lebar_cetak_2_parameter_4');
            $table->dropColumn('rumus_panjang_cetak_2_angka_1');
            $table->dropColumn('rumus_panjang_cetak_2_parameter_1');
            $table->dropColumn('rumus_panjang_cetak_2_angka_2');
            $table->dropColumn('rumus_panjang_cetak_2_parameter_2');
            $table->dropColumn('rumus_panjang_cetak_2_angka_3');
            $table->dropColumn('rumus_panjang_cetak_2_parameter_3');
            $table->dropColumn('rumus_panjang_cetak_2_angka_4');
            $table->dropColumn('rumus_panjang_cetak_2_parameter_4');
            $table->dropColumn('rumus_lebar_cetak_3_angka_1');
            $table->dropColumn('rumus_lebar_cetak_3_parameter_1');
            $table->dropColumn('rumus_lebar_cetak_3_angka_2');
            $table->dropColumn('rumus_lebar_cetak_3_parameter_2');
            $table->dropColumn('rumus_lebar_cetak_3_angka_3');
            $table->dropColumn('rumus_lebar_cetak_3_parameter_3');
            $table->dropColumn('rumus_lebar_cetak_3_angka_4');
            $table->dropColumn('rumus_lebar_cetak_3_parameter_4');
            $table->dropColumn('rumus_panjang_cetak_3_angka_1');
            $table->dropColumn('rumus_panjang_cetak_3_parameter_1');
            $table->dropColumn('rumus_panjang_cetak_3_angka_2');
            $table->dropColumn('rumus_panjang_cetak_3_parameter_2');
            $table->dropColumn('rumus_panjang_cetak_3_angka_3');
            $table->dropColumn('rumus_panjang_cetak_3_parameter_3');
            $table->dropColumn('rumus_panjang_cetak_3_angka_4');
            $table->dropColumn('rumus_panjang_cetak_3_parameter_4');
        });
    }
}
