<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateInvoiceDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invoice_details', function (Blueprint $table) {
            $table->id();
            $table->uuid('invoice_key')->index();
            $table->unsignedBigInteger('order_id')->nullable();

            $table->string('kode_order')->nullable();
            $table->string('product_name')->nullable();
            $table->text('spesifikasi')->nullable();
            
            $table->integer('quantity')->nullable();
            $table->bigInteger('harga_satuan')->nullable();
            $table->bigInteger('jumlah_harga')->nullable();
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoice_details');
    }
}
