<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTbSumberTargets extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_sumber_targets', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('id_customer');
            $table->integer('month')->nullable();
            $table->bigInteger('online_target')->nullable();
            $table->bigInteger('canvassing_target')->nullable();
            $table->bigInteger('relasi_target')->nullable();
            $table->bigInteger('repeat_order_target')->nullable();
            $table->bigInteger('relasi_customer_target')->nullable();
            $table->bigInteger('online_lintas_target')->nullable();
            $table->bigInteger('offline_target')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_sumber_targets');
    }
}
