<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddLapisAndDummyChecklistToTbSpks extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            //lapis
            $table->string('check_7_1', 10)->after('catatan_7')->nullable();
            $table->string('check_7_2', 10)->after('check_7_1')->nullable();

            //finishing geser ke 10
            $table->string('progress_produksi_10')->after('check_9_5')->nullable();
            $table->integer('jumlah_awal_pp_10')->after('progress_produksi_10')->nullable();
            $table->integer('jumlah_hasil_pp_10')->after('jumlah_awal_pp_10')->nullable();
            $table->integer('reject_pp_10')->after('jumlah_hasil_pp_10')->nullable();
            $table->string('vendor_10')->after('reject_pp_10')->nullable();
            $table->date('tgl_produksi_10')->after('vendor_10')->nullable();
            $table->string('pic_validasi_10')->after('tgl_produksi_10')->nullable();
            $table->string('flag_status_10')->after('pic_validasi_10')->nullable();
            $table->date('tgl_flag_status_10')->after('flag_status_10')->nullable();
            $table->text('catatan_10')->after('tgl_flag_status_10')->nullable();
            $table->string('check_10_1', 10)->after('catatan_10')->nullable();
            $table->string('check_10_2', 10)->after('check_10_1')->nullable();
            $table->string('check_10_3', 10)->after('check_10_2')->nullable();
            $table->string('check_10_4', 10)->after('check_10_3')->nullable();
            $table->string('check_10_5', 10)->after('check_10_4')->nullable();

            //checklist dummy
            $table->string('progress_produksi_dummy')->after('check_10_5')->nullable();
            $table->string('check_dummy_1')->after('progress_produksi_dummy')->nullable();
            $table->string('check_dummy_2')->after('check_dummy_1')->nullable();
            $table->string('check_dummy_3')->after('check_dummy_2')->nullable();
            $table->string('check_dummy_4')->after('check_dummy_3')->nullable();
            $table->string('check_dummy_5')->after('check_dummy_4')->nullable();
            $table->string('check_dummy_6')->after('check_dummy_5')->nullable();
            $table->string('check_dummy_7')->after('check_dummy_6')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->dropColumn('check_7_1');
            $table->dropColumn('check_7_2');
            $table->dropColumn('progress_produksi_10');
            $table->dropColumn('jumlah_awal_pp_10');
            $table->dropColumn('jumlah_hasil_pp_10');
            $table->dropColumn('reject_pp_10');
            $table->dropColumn('vendor_10');
            $table->dropColumn('tgl_produksi_10');
            $table->dropColumn('pic_validasi_10');
            $table->dropColumn('flag_status_10');
            $table->dropColumn('tgl_flag_status_10');
            $table->dropColumn('catatan_10');
            $table->dropColumn('check_10_1');
            $table->dropColumn('check_10_2');
            $table->dropColumn('check_10_3');
            $table->dropColumn('check_10_4');
            $table->dropColumn('check_10_5');
            $table->dropColumn('progress_produksi_dummy');
            $table->dropColumn('check_dummy_1');
            $table->dropColumn('check_dummy_2');
            $table->dropColumn('check_dummy_3');
            $table->dropColumn('check_dummy_4');
            $table->dropColumn('check_dummy_5');
            $table->dropColumn('check_dummy_6');
            $table->dropColumn('check_dummy_7');
        });
    }
}
