<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPainPointToTbOrders extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_orders', function (Blueprint $table) {
            $table->boolean('pp_1')->after('tipe_kontak')->default(false);
            $table->boolean('pp_2')->after('pp_1')->default(false);
            $table->boolean('pp_3')->after('pp_2')->default(false);
            $table->boolean('pp_4')->after('pp_3')->default(false);
            $table->boolean('pp_5')->after('pp_4')->default(false);
            $table->date('tgl_pelunasan')->after('flag_lunas')->nullable();
            $table->boolean('survey_order')->after('follow_up_terakhir')->default(false);
            $table->boolean('lead_prospek')->after('survey_order')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_orders', function (Blueprint $table) {
            $table->dropColumn(['pp_1', 'pp_2', 'pp_3', 'pp_4', 'pp_5', 'tgl_pelunasan', 'survey_order', 'lead_prospek']);
        });
    }
}
