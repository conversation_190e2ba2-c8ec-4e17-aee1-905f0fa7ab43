<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnMinimumOmzet extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_gradings', function (Blueprint $table) {
            $table->bigInteger('minimum_omzet')->default(0)->after('grading')->comment('Minimum omzet untuk grading');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_gradings', function (Blueprint $table) {
            $table->dropColumn('minimum_omzet');
        });
    }
}
