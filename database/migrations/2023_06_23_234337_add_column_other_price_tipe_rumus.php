<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnOtherPriceTipeRumus extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_other_price', function (Blueprint $table) {
            $table->smallInteger('tipe_rumus')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_other_price', function (Blueprint $table) {
            $table->dropColumn('tipe_rumus');
        });
    }
}
