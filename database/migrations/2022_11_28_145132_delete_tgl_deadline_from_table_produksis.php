<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DeleteTglDeadlineFromTableProduksis extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_produksis', function (Blueprint $table) {
            $table->dropColumn('tgl_deadline');
        });
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->dropColumn('tgl_deadline');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_produksis', function (Blueprint $table) {
            $table->date('tgl_deadline')->nullable();
        });
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->date('tgl_deadline')->nullable();
        });
    }
}
