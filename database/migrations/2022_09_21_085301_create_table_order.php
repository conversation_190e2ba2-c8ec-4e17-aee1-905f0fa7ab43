<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableOrder extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_orders', function (Blueprint $table) {
            $table->id('id_order');
            $table->bigInteger('id_customer');
            // $table->string('kode_order')->nullable();
            $table->bigInteger('id_pic');
            $table->string('sumber');
            $table->date('tgl_order')->nullable();
            $table->dateTime('waktu_kontak');
            $table->date('tgl_jatuh_tempo')->nullable();
            $table->string('status_deal');
            $table->string('status_order');
            $table->string('tipe_kontak')->nullable();
            // $table->string('hitung_harga_khusus')->nullable();
            $table->string('catatan_order')->nullable();
            // $table->date('tgl_invoice');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_orders');
    }
}
