<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDpToTableOrder extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_orders', function (Blueprint $table) {
            $table->bigInteger('DP')->after('grading')->nullable();
            $table->bigInteger('pelunasan')->after('DP')->nullable();
            $table->string('flag_lunas')->after('pelunasan')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_orders', function (Blueprint $table) {
            $table->dropColumn('DP');
            $table->dropColumn('pelunasan');
            $table->dropColumn('flag_lunas');
        });
    }
}
