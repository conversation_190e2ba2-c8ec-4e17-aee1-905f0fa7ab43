<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableTarget extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_targets', function (Blueprint $table) {
            $table->id('id_target');
            $table->bigInteger('id_pic');
            $table->date('tgl_target');
            $table->string('bulan_target');
            $table->bigInteger('target');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_targets');
    }
}
