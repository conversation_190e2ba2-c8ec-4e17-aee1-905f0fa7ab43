<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTbSumberTargetsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_sumber_targets', function (Blueprint $table) {
            // Add new columns
            $table->bigInteger('year_target')->after('month')->nullable();
            $table->renameColumn('id_customer', 'id_pic');

            // Remove the 'month' column if you want to drop it
            // $table->dropColumn('month');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_sumber_targets', function (Blueprint $table) {
            //
        });
    }
}
