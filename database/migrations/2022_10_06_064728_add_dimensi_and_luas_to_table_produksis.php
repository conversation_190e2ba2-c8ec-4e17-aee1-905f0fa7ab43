<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDimensiAndLuasToTableProduksis extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_produksis', function (Blueprint $table) {
            $table->integer('dp_panjang')->nullable();
            $table->integer('dp_lebar')->nullable();
            $table->integer('dp_tinggi')->nullable();
            $table->integer('lp_panjang')->nullable();
            $table->integer('lp_lebar')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_produksis', function (Blueprint $table) {
            $table->dropColumn('dp_panjang');
            $table->dropColumn('dp_lebar');
            $table->dropColumn('dp_tinggi');
            $table->dropColumn('lp_panjang');
            $table->dropColumn('lp_lebar');
        });
    }
}
