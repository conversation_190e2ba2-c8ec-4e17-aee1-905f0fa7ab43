<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTablePaperPrice extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_paper_price', function (Blueprint $table) {
            $table->id('id_paper_price');
            $table->bigInteger('id_jenis_kertas')->nullable();
            $table->bigInteger('id_kertas_plano')->nullable();
            $table->bigInteger('id_gramasi')->nullable();
            $table->bigInteger('price')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_paper_price');
    }
}
