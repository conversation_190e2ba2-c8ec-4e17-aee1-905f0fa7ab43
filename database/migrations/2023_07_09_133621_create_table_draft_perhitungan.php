<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTable<PERSON>raftPer<PERSON>ungan extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_draft_hitungan', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->unsignedBigInteger('id_layout')->nullable();
            $table->unsignedBigInteger('id_bahan')->nullable();
            $table->unsignedBigInteger('id_jenis_kertas')->nullable();
            $table->unsignedBigInteger('id_kertas_plano')->nullable();
            $table->unsignedBigInteger('id_gramasi')->nullable();
            $table->double('dimensi_p')->nullable();
            $table->double('dimensi_l')->nullable();
            $table->double('dimensi_t')->nullable();
            $table->string('luas_permukaan')->nullable();
            $table->string('luas_kertas')->nullable();
            $table->integer('jumlah_potong')->nullable();
            $table->integer('waste')->nullable();
            $table->text('detail')->nullable();
            $table->text('kebutuhan_kertas')->nullable();
            $table->text('additional_cost')->nullable();
            $table->text('description')->nullable();
            $table->integer('quantity')->nullable();
            $table->float('harga_modal')->nullable();
            $table->integer('type')->nullable();
            $table->integer('id_produksi')->nullable();
            $table->string('order_key')->nullable();
            $table->string('sko_key')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_draft_hitungan');
    }
}
