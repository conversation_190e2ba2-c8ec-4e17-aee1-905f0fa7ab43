<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class FixProductionTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            //persiapan
            $table->date('tgl_flag_status_1')->after('flag_status_1')->nullable();
            $table->text('catatan_1')->after('tgl_flag_status_1')->nullable();
            $table->string('check_1_1', 10)->after('catatan_1')->nullable();
            $table->string('check_1_2', 10)->after('check_1_1')->nullable();
            $table->string('check_1_3', 10)->after('check_1_2')->nullable();
            $table->string('check_1_4', 10)->after('check_1_3')->nullable();

            //cetak
            $table->date('tgl_flag_status_2')->after('flag_status_2')->nullable();
            $table->text('catatan_2')->after('tgl_flag_status_2')->nullable();
            $table->string('check_2_1', 10)->after('catatan_2')->nullable();
            $table->string('check_2_2', 10)->after('check_2_1')->nullable();
            $table->string('check_2_3', 10)->after('check_2_2')->nullable();
            $table->string('check_2_4', 10)->after('check_2_3')->nullable();

            //laminasi
            $table->date('tgl_flag_status_3')->after('flag_status_3')->nullable();
            $table->text('catatan_3')->after('tgl_flag_status_3')->nullable();
            $table->string('check_3_1', 10)->after('catatan_3')->nullable();
            $table->string('check_3_2', 10)->after('check_3_1')->nullable();
            $table->string('check_3_3', 10)->after('check_3_2')->nullable();
            $table->string('check_3_4', 10)->after('check_3_3')->nullable();

            //poly
            $table->date('tgl_flag_status_4')->after('flag_status_4')->nullable();
            $table->text('catatan_4')->after('tgl_flag_status_4')->nullable();

            //emboss
            $table->date('tgl_flag_status_5')->after('flag_status_5')->nullable();
            $table->text('catatan_5')->after('tgl_flag_status_5')->nullable();

            //spot uv
            $table->date('tgl_flag_status_6')->after('flag_status_6')->nullable();
            $table->text('catatan_6')->after('tgl_flag_status_6')->nullable();
            $table->string('check_6_1', 10)->after('catatan_6')->nullable();
            $table->string('check_6_2', 10)->after('check_6_1')->nullable();
            $table->string('check_6_3', 10)->after('check_6_2')->nullable();
            $table->string('check_6_4', 10)->after('check_6_3')->nullable();


            //jendela mika
            $table->date('tgl_flag_status_7')->after('flag_status_7')->nullable();
            $table->text('catatan_7')->after('tgl_flag_status_7')->nullable();

            //pond
            $table->date('tgl_flag_status_8')->after('flag_status_8')->nullable();
            $table->text('catatan_8')->after('tgl_flag_status_8')->nullable();
            $table->string('check_8_1', 10)->after('catatan_8')->nullable();
            $table->string('check_8_2', 10)->after('check_8_1')->nullable();
            $table->string('check_8_3', 10)->after('check_8_2')->nullable();
            $table->string('check_8_4', 10)->after('check_8_3')->nullable();
            $table->string('check_8_5', 10)->after('check_8_4')->nullable();

            //finishing
            $table->date('tgl_flag_status_9')->after('flag_status_9')->nullable();
            $table->text('catatan_9')->after('tgl_flag_status_9')->nullable();
            $table->string('check_9_1', 10)->after('catatan_9')->nullable();
            $table->string('check_9_2', 10)->after('check_9_1')->nullable();
            $table->string('check_9_3', 10)->after('check_9_2')->nullable();
            $table->string('check_9_4', 10)->after('check_9_3')->nullable();
            $table->string('check_9_5', 10)->after('check_9_4')->nullable();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->dropColumn('tgl_flag_status_1');
            $table->dropColumn('tgl_flag_status_2');
            $table->dropColumn('tgl_flag_status_3');
            $table->dropColumn('tgl_flag_status_4');
            $table->dropColumn('tgl_flag_status_5');
            $table->dropColumn('tgl_flag_status_6');
            $table->dropColumn('tgl_flag_status_7');
            $table->dropColumn('tgl_flag_status_8');
            $table->dropColumn('tgl_flag_status_9');
            $table->dropColumn('catatan_1');
            $table->dropColumn('catatan_2');
            $table->dropColumn('catatan_3');
            $table->dropColumn('catatan_4');
            $table->dropColumn('catatan_5');
            $table->dropColumn('catatan_6');
            $table->dropColumn('catatan_7');
            $table->dropColumn('catatan_8');
            $table->dropColumn('catatan_9');
            $table->dropColumn('catatan_9');
            $table->dropColumn('check_1_1');
            $table->dropColumn('check_1_2');
            $table->dropColumn('check_1_3');
            $table->dropColumn('check_1_4');
            $table->dropColumn('check_1_4');
            $table->dropColumn('check_2_1');
            $table->dropColumn('check_2_2');
            $table->dropColumn('check_2_3');
            $table->dropColumn('check_2_4');
            $table->dropColumn('check_3_1');
            $table->dropColumn('check_3_2');
            $table->dropColumn('check_3_3');
            $table->dropColumn('check_3_4');
            $table->dropColumn('check_6_1');
            $table->dropColumn('check_6_2');
            $table->dropColumn('check_6_3');
            $table->dropColumn('check_6_4');
            $table->dropColumn('check_8_1');
            $table->dropColumn('check_8_2');
            $table->dropColumn('check_8_3');
            $table->dropColumn('check_8_4');
            $table->dropColumn('check_8_5');
            $table->dropColumn('check_9_1');
            $table->dropColumn('check_9_2');
            $table->dropColumn('check_9_3');
            $table->dropColumn('check_9_4');
            $table->dropColumn('check_9_5');
        });
    }
}
