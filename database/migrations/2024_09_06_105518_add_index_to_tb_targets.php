<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddIndexToTbTargets extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_targets', function (Blueprint $table) {
            $table->index(['year_target']);
            $table->index(['id_pic']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_targets', function (Blueprint $table) {
            $table->dropIndex(['year_target']);
            $table->dropIndex(['id_pic']);
        });
    }
}
