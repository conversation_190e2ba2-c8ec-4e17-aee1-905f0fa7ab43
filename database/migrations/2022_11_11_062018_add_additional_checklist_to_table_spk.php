<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAdditionalChecklistToTableSpk extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->dropColumn('jumlah_awal_pp_1');
            $table->dropColumn('jumlah_hasil_pp_1');
            $table->dropColumn('reject_pp_1');
            $table->integer('jumlah_plano')->after('progress_produksi_1')->nullable();
            $table->string('check_4_1')->after('catatan_4')->nullable();
            $table->string('check_4_2')->after('check_4_1')->nullable();
            $table->string('check_4_3')->after('check_4_2')->nullable();
            $table->string('check_4_4')->after('check_4_3')->nullable();
            $table->string('check_4_5')->after('check_4_4')->nullable();
            $table->string('check_5_1')->after('catatan_5')->nullable();
            $table->string('check_5_2')->after('check_5_1')->nullable();
            $table->string('check_5_3')->after('check_5_2')->nullable();
            $table->string('check_5_4')->after('check_5_3')->nullable();
            $table->string('check_5_5')->after('check_5_4')->nullable();
            $table->integer('jumlah_fix')->after('check_dummy_7')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('tb_spks', function (Blueprint $table) {
            $table->dropColumn('jumlah_plano');
            $table->dropColumn('check_4_1');
            $table->dropColumn('check_4_2');
            $table->dropColumn('check_4_3');
            $table->dropColumn('check_4_4');
            $table->dropColumn('check_4_5');
            $table->dropColumn('check_5_1');
            $table->dropColumn('check_5_2');
            $table->dropColumn('check_5_3');
            $table->dropColumn('check_5_4');
            $table->dropColumn('check_5_5');
            $table->dropColumn('jumlah_fix');
        });
    }
}
