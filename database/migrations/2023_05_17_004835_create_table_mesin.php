<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableMesin extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_mesin', function (Blueprint $table) {
            $table->id('id_mesin');
            $table->string('mesin')->nullable();
            $table->float('max_area_print')->nullable();
            $table->float('min_area_print')->nullable();
            $table->float('max_size_paper')->nullable();
            $table->float('min_size_paper')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_mesin');
    }
}
