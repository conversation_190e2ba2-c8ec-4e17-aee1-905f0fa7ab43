<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTable<PERSON>roduksi extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tb_produksis', function (Blueprint $table) {
            $table->id('id_produksi');
            $table->bigInteger('id_order')->nullable();
            $table->date('tgl_produksi');

            $table->string('kategori_produksi')->nullable();
            $table->string('jenis_bahan')->nullable();
            $table->string('spesifikasi')->nullable();
            $table->string('isi_kertas')->nullable();
            $table->integer('jumlah_produk')->nullable();
            $table->bigInteger('harga_produk')->nullable();
            $table->bigInteger('total_harga')->nullable();
            $table->bigInteger('modal_sales')->nullable();

            $table->string('progress_produksi')->nullable();
            $table->string('status_produksi')->nullable();
            $table->string('reject')->nullable();
            $table->string('vendor')->nullable();

            $table->date('tgl_deadline')->nullable();
            $table->bigInteger('DP')->nullable();
            $table->text('notes_lost_dp')->nullable();
            $table->string('status_lunas')->nullable();
            $table->string('hitung_harga_khusus')->nullable();
            $table->text('notes')->nullable();
            $table->string('ukuran_kode')->nullable();
            $table->text('kendala_produksi')->nullable();
            $table->string('link_file_final')->nullable();
            $table->string('lampiran_path')->nullable();
            $table->string('lampiran_filename')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tb_produksis');
    }
}
