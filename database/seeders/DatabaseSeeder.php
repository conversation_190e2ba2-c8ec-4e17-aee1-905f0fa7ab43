<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        // \App\Models\User::factory(10)->create();

        User::create([
            'name' => 'sulton',
            'roles' => 'SUPERADMIN',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin12345')
        ]);
    }
}
