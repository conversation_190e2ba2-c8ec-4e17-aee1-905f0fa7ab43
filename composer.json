{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "barryvdh/laravel-dompdf": "^2.0", "doctrine/dbal": "^3.5", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "haruncpi/laravel-id-generator": "^1.1", "laravel/framework": "^8.75", "laravel/jetstream": "^2.9", "laravel/sanctum": "^2.11", "laravel/tinker": "^2.5", "livewire/livewire": "^2.5", "phpoffice/phpword": "^0.18.3", "ramsey/uuid": "^4.2", "yajra/laravel-datatables-buttons": "^4.13", "yajra/laravel-datatables-oracle": "~9.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.7", "facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nascent-africa/jetstrap": "^2.5", "nunomaduro/collision": "^5.10", "phpunit/phpunit": "^9.5.10"}, "autoload": {"psr-4": {"App\\": "app/", "Lib\\": "app/lib/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Http/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}