<div id="kt_aside" class="aside" data-kt-drawer="true" data-kt-drawer-name="aside"
    data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="auto"
    data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_aside_toggle">
    <!--begin::Logo-->
    <div class="aside-logo flex-column-auto pt-10 pt-lg-20" id="kt_aside_logo">
        <a href="{{ url('/') }}">
            <img alt="Logo" src="{{ url('images/Logo-Risepack.svg') }}" class="h-60px" />
        </a>
    </div>
    <!--end::Logo-->
    <!--begin::Nav-->
    <div class="aside-menu flex-column-fluid pt-0 pb-7 py-lg-10" id="kt_aside_menu">
        <!--begin::Aside menu-->
        <div id="kt_aside_menu_wrapper" class="w-100 hover-scroll-overlay-y scroll-ps d-flex" data-kt-scroll="true"
            data-kt-scroll-height="auto" data-kt-scroll-dependencies="#kt_aside_logo, #kt_aside_footer"
            data-kt-scroll-wrappers="#kt_aside, #kt_aside_menu" data-kt-scroll-offset="0">
            <div id="kt_aside_menu"
                class="menu menu-column menu-title-gray-600 menu-state-primary menu-state-icon-primary menu-state-bullet-primary menu-icon-gray-400 menu-arrow-gray-400 fw-bold fs-6 my-auto"
                data-kt-menu="true">
                <div data-kt-menu-trigger="click" data-kt-menu-placement="right-start" class="menu-item here show py-3">
                    <span class="menu-link" title="Dashboards" data-bs-toggle="tooltip" data-bs-trigger="hover"
                        data-bs-dismiss="click" data-bs-placement="right">
                        <span class="menu-icon {{ request()->segment(1) == '' ? 'my-primary' : 'my-white' }} side-menu"
                            data-url="{{ route('dashboard') }}">
                            <!--begin::Svg Icon | path: assets/media/icons/duotune/abstract/abs029.svg-->
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none">
                                <path
                                    d="M6.5 11C8.98528 11 11 8.98528 11 6.5C11 4.01472 8.98528 2 6.5 2C4.01472 2 2 4.01472 2 6.5C2 8.98528 4.01472 11 6.5 11Z"
                                    fill="{{ request()->segment(1) == '' ? 'white' : 'black' }}" />
                                <path opacity="0.3"
                                    d="M13 6.5C13 4 15 2 17.5 2C20 2 22 4 22 6.5C22 9 20 11 17.5 11C15 11 13 9 13 6.5ZM6.5 22C9 22 11 20 11 17.5C11 15 9 13 6.5 13C4 13 2 15 2 17.5C2 20 4 22 6.5 22ZM17.5 22C20 22 22 20 22 17.5C22 15 20 13 17.5 13C15 13 13 15 13 17.5C13 20 15 22 17.5 22Z"
                                    fill="{{ request()->segment(1) == '' ? 'white' : 'black' }}" />
                            </svg>
                            <!--end::Svg Icon-->
                        </span>
                    </span>
                </div>

                @if (Auth::user()->roles == 'SALES' || Auth::user()->roles == 'SALES SPV' || Auth::user()->roles == 'SUPERADMIN')
                    <div data-kt-menu-trigger="click" data-kt-menu-placement="right-start" class="menu-item py-3">
                        <span class="menu-link" title="CRM" data-bs-toggle="tooltip" data-bs-trigger="hover"
                            data-bs-dismiss="click" data-bs-placement="right">
                            <span
                                class="menu-icon {{ request()->segment(1) == 'crm' ? 'my-primary' : 'my-white' }} side-menu"
                                data-url="{{ route('crm') }}">
                                <!--begin::Svg Icon | path: assets/media/icons/duotune/communication/com014.svg-->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none">
                                    <path
                                        d="M16.0173 9H15.3945C14.2833 9 13.263 9.61425 12.7431 10.5963L12.154 11.7091C12.0645 11.8781 12.1072 12.0868 12.2559 12.2071L12.6402 12.5183C13.2631 13.0225 13.7556 13.6691 14.0764 14.4035L14.2321 14.7601C14.2957 14.9058 14.4396 15 14.5987 15H18.6747C19.7297 15 20.4057 13.8774 19.912 12.945L18.6686 10.5963C18.1487 9.61425 17.1285 9 16.0173 9Z"
                                        fill="{{ request()->segment(1) == 'crm' ? 'white' : 'black' }}" />
                                    <rect opacity="0.3" x="14" y="4" width="4" height="4" rx="2"
                                        fill="black" />
                                    <path
                                        d="M4.65486 14.8559C5.40389 13.1224 7.11161 12 9 12C10.8884 12 12.5961 13.1224 13.3451 14.8559L14.793 18.2067C15.3636 19.5271 14.3955 21 12.9571 21H5.04292C3.60453 21 2.63644 19.5271 3.20698 18.2067L4.65486 14.8559Z"
                                        fill="{{ request()->segment(1) == 'crm' ? 'white' : 'black' }}" />
                                    <rect opacity="0.3" x="6" y="5" width="6" height="6" rx="3"
                                        fill="black" />
                                </svg>
                                <!--end::Svg Icon-->
                            </span>
                        </span>
                    </div>
                @endif

                @if (Auth::user()->roles == 'PRODUKSI' || Auth::user()->roles == 'PRODUKSI SPV' || Auth::user()->roles == 'SUPERADMIN')
                    <div data-kt-menu-trigger="click" data-kt-menu-placement="right-start" class="menu-item py-3">
                        <span class="menu-link" title="Produksi" data-bs-toggle="tooltip" data-bs-trigger="hover"
                            data-bs-dismiss="click" data-bs-placement="right">
                            <span
                                class="menu-icon {{ request()->segment(1) == 'production' ? 'my-primary' : 'my-white' }} side-menu"
                                data-url="{{ route('production') }}">
                                <!--begin::Svg Icon | path: assets/media/icons/duotune/communication/com014.svg-->
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none">
                                    <path opacity="0.3"
                                        d="M2.10001 10C3.00001 5.6 6.69998 2.3 11.2 2L8.79999 4.39999L11.1 7C9.60001 7.3 8.30001 8.19999 7.60001 9.59999L4.5 12.4L2.10001 10ZM19.3 11.5L16.4 14C15.7 15.5 14.4 16.6 12.7 16.9L15 19.5L12.6 21.9C17.1 21.6 20.8 18.2 21.7 13.9L19.3 11.5Z"
                                        fill="{{ request()->segment(1) == 'production' ? 'white' : 'black' }}" />
                                    <rect opacity="0.3" x="14" y="4" width="4" height="4" rx="2"
                                        fill="black" />
                                    <path
                                        d="M13.8 2.09998C18.2 2.99998 21.5 6.69998 21.8 11.2L19.4 8.79997L16.8 11C16.5 9.39998 15.5 8.09998 14 7.39998L11.4 4.39998L13.8 2.09998ZM12.3 19.4L9.69998 16.4C8.29998 15.7 7.3 14.4 7 12.8L4.39999 15.1L2 12.7C2.3 17.2 5.7 20.9 10 21.8L12.3 19.4Z"
                                        fill="{{ request()->segment(1) == 'production' ? 'white' : 'black' }}" />
                                    <rect opacity="0.3" x="6" y="5" width="6" height="6" rx="3"
                                        fill="black" />
                                </svg>
                                <!--end::Svg Icon-->
                            </span>
                        </span>
                    </div>
                @endif

                @if (Auth::user()->roles == 'SALES' ||
                        Auth::user()->roles == 'SALES SPV' ||
                        Auth::user()->roles == 'SUPERADMIN' ||
                        Auth::user()->roles == 'PRODUKSI SPV' ||
                        Auth::user()->roles == 'PRODUKSI')
                    <div data-kt-menu-trigger="click" data-kt-menu-placement="right-start" class="menu-item py-4">
                        <span class="menu-link" title="design" data-bs-toggle="tooltip" data-bs-trigger="hover"
                            data-bs-dismiss="click" data-bs-placement="right">
                            <span
                                class="menu-icon {{ request()->segment(1) == 'design' ? 'my-primary' : 'my-white' }} side-menu"
                                data-url="{{ route('design') }}">
                                <!--begin::Svg Icon | path: assets/media/icons/duotune/communication/com014.svg-->
                                <svg fill="{{ request()->segment(1) == 'design' ? 'white' : 'black' }}"
                                    width="32px" height="32px" viewBox="0 0 36 36" version="1.1"
                                    preserveAspectRatio="xMidYMid meet" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" stroke="#000000">
                                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                    <g id="SVGRepo_iconCarrier">
                                        <title>design-solid</title>
                                        <path class="clr-i-solid clr-i-solid-path-1"
                                            d="M34.87,32.21,30,27.37V8.75L27.7,4.52a2,2,0,0,0-3.54,0L22,8.76V19.41L3.71,1.21A1,1,0,0,0,2,1.92V10H4.17v1.6H2V18H4.17v1.6H2v6.65H4.17v1.6H2v5.07a1,1,0,0,0,1,1H34.16a1,1,0,0,0,.71-1.71ZM10,26V16.94L19.07,26Zm18,2.11H24V25.68h4Zm0-4H24V9.25l1.94-3.77L28,9.26Z">
                                        </path>
                                        <rect x="0" y="0" width="36" height="36" fill-opacity="0"></rect>
                                    </g>
                                </svg>
                                <!--end::Svg Icon-->
                            </span>
                        </span>
                    </div>
                @endif

                <div data-kt-menu-trigger="click" data-kt-menu-placement="right-start" class="menu-item py-4">
                    <span class="menu-link" title="Sales" data-bs-toggle="tooltip" data-bs-trigger="hover"
                        data-bs-dismiss="click" data-bs-placement="right">
                        <span
                            class="menu-icon {{ request()->segment(1) == 'sales' ? 'my-primary' : 'my-white' }} side-menu"
                            data-url="{{ route('sales.index') }}">
                            <!--begin::Svg Icon | path: assets/media/icons/duotune/communication/com014.svg-->
                            <svg version="1.1" width="32px" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                                    viewBox="0 0 512 512" style="enable-background:new 0 0 512 512;" xml:space="preserve">
                                <path style="fill:{{ request()->segment(1) == 'sales' ? 'white' : 'black' }};" d="M423.796,512H88.203c-19.759,0-35.835-15.785-35.835-35.189V92.187
                                    c0-19.656,15.991-35.647,35.647-35.647h335.783c19.761,0,35.835,15.785,35.835,35.189v190.049c0,7.949-6.445,14.393-14.393,14.393
                                    s-14.393-6.444-14.393-14.393V91.729c0-3.531-3.162-6.402-7.048-6.402H88.014c-3.783,0-6.86,3.077-6.86,6.86v384.624
                                    c0,3.531,3.162,6.402,7.048,6.402h335.595c3.886,0,7.048-2.873,7.048-6.402V372.17c0-7.949,6.445-14.393,14.393-14.393
                                    s14.393,6.444,14.393,14.393v104.641C459.633,496.213,443.557,512,423.796,512z"/>
                                <path style="fill:{{ request()->segment(1) == 'sales' ? 'black' : '#CEE8FA' }};" d="M311.339,71.967c0.049-0.953,0.076-1.913,0.076-2.879c0-30.207-24.487-54.695-54.695-54.695
                                    s-54.695,24.487-54.695,54.695c0,0.966,0.027,1.926,0.076,2.879h-65.568v53.253h238.932V71.967H311.339z"/>
                                <path style="fill:{{ request()->segment(1) == 'sales' ? 'white' : 'black' }};" d="M375.464,139.613h-238.93c-7.949,0-14.393-6.444-14.393-14.393V71.967
                                    c0-7.949,6.444-14.393,14.393-14.393h52.057C194.089,24.939,222.546,0,256.719,0s62.631,24.939,68.128,57.574h50.617
                                    c7.948,0,14.393,6.444,14.393,14.393v53.253C389.858,133.169,383.414,139.613,375.464,139.613z M150.928,110.826h210.143V86.36
                                    h-49.733c-3.945,0-7.719-1.619-10.437-4.481c-2.717-2.861-4.141-6.713-3.937-10.654c0.036-0.708,0.056-1.421,0.056-2.137
                                    c0-22.222-18.08-40.301-40.301-40.301s-40.301,18.08-40.301,40.301c0,0.717,0.02,1.429,0.058,2.137
                                    c0.203,3.941-1.221,7.793-3.938,10.654c-2.717,2.861-6.49,4.481-10.437,4.481h-51.173L150.928,110.826L150.928,110.826z"/>
                                <rect x="136.536" y="181.875" style="fill:{{ request()->segment(1) == 'sales' ? 'black' : '#CEE8FA' }};" width="122.341" height="47.498"/>
                                <g>
                                    <path style="fill:{{ request()->segment(1) == 'sales' ? 'white' : 'black' }};" d="M258.875,243.761H136.534c-7.949,0-14.393-6.444-14.393-14.393v-47.498
                                        c0-7.949,6.444-14.393,14.393-14.393h122.342c7.948,0,14.393,6.444,14.393,14.393v47.498
                                        C273.268,237.317,266.825,243.761,258.875,243.761z M150.928,214.974h93.556v-18.711h-93.556V214.974z"/>
                                    <path style="fill:{{ request()->segment(1) == 'sales' ? 'white' : 'black' }};" d="M375.464,243.761h-57.575c-7.948,0-14.393-6.444-14.393-14.393v-47.498
                                        c0-7.949,6.445-14.393,14.393-14.393h57.575c7.948,0,14.393,6.444,14.393,14.393s-6.445,14.393-14.393,14.393h-43.182v18.711
                                        h43.182c7.948,0,14.393,6.444,14.393,14.393S383.414,243.761,375.464,243.761z"/>
                                </g>
                                <rect x="136.536" y="387.7" style="fill:{{ request()->segment(1) == 'sales' ? 'black' : '#CEE8FA' }};" width="122.341" height="47.498"/>
                                <g>
                                    <path style="fill:{{ request()->segment(1) == 'sales' ? 'white' : 'black' }};" d="M258.875,449.586H136.534c-7.949,0-14.393-6.444-14.393-14.393v-47.498
                                        c0-7.949,6.444-14.393,14.393-14.393h122.342c7.948,0,14.393,6.444,14.393,14.393v47.498
                                        C273.268,443.142,266.825,449.586,258.875,449.586z M150.928,420.799h93.556v-18.711h-93.556V420.799z"/>
                                    <path style="fill:{{ request()->segment(1) == 'sales' ? 'white' : 'black' }};" d="M375.464,449.586h-57.575c-7.948,0-14.393-6.444-14.393-14.393v-47.498
                                        c0-7.949,6.445-14.393,14.393-14.393h57.575c7.948,0,14.393,6.444,14.393,14.393s-6.445,14.393-14.393,14.393h-43.182v18.711
                                        h43.182c7.948,0,14.393,6.444,14.393,14.393C389.858,443.142,383.414,449.586,375.464,449.586z"/>
                                </g>
                                <rect x="253.122" y="284.787" style="fill:{{ request()->segment(1) == 'sales' ? 'black' : '#CEE8FA' }};" width="122.341" height="47.498"/>
                                <g>
                                    <path style="fill:{{ request()->segment(1) == 'sales' ? 'white' : 'black' }};" d="M375.464,346.673H253.123c-7.949,0-14.393-6.444-14.393-14.393v-47.498
                                        c0-7.949,6.444-14.393,14.393-14.393h122.341c7.948,0,14.393,6.444,14.393,14.393v47.498
                                        C389.858,340.229,383.414,346.673,375.464,346.673z M267.517,317.887h93.554v-18.711h-93.554V317.887z"/>
                                    <path style="fill:{{ request()->segment(1) == 'sales' ? 'white' : 'black' }};" d="M194.109,346.673h-57.575c-7.949,0-14.393-6.444-14.393-14.393v-47.498
                                        c0-7.949,6.444-14.393,14.393-14.393h57.575c7.949,0,14.393,6.444,14.393,14.393s-6.444,14.393-14.393,14.393h-43.182v18.711
                                        h43.182c7.949,0,14.393,6.444,14.393,14.393C208.503,340.229,202.059,346.673,194.109,346.673z"/>
                                </g>
                            </svg>
                            <!--end::Svg Icon-->
                        </span>
                    </span>
                </div>

                <div data-kt-menu-trigger="click" data-kt-menu-placement="right-start" class="menu-item py-4">
                    <span class="menu-link" title="Reports" data-bs-toggle="tooltip" data-bs-trigger="hover"
                        data-bs-dismiss="click" data-bs-placement="right">
                        <span
                            class="menu-icon {{ request()->segment(1) == 'report' ? 'my-primary' : 'my-white' }} side-menu"
                            data-url="{{ route('report.index') }}">
                            <!--begin::Svg Icon-->
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M3 3H21V21H3V3Z" fill="{{ request()->segment(1) == 'report' ? 'white' : 'black' }}" />
                                <path d="M7 7H17V9H7V7Z" fill="{{ request()->segment(1) == 'report' ? 'black' : '#CEE8FA' }}" />
                                <path d="M7 11H17V13H7V11Z" fill="{{ request()->segment(1) == 'report' ? 'black' : '#CEE8FA' }}" />
                                <path d="M7 15H13V17H7V15Z" fill="{{ request()->segment(1) == 'report' ? 'black' : '#CEE8FA' }}" />
                            </svg>
                            <!--end::Svg Icon-->
                        </span>
                    </span>
                </div>
            </div>
        </div>
        <!--end::Aside menu-->
    </div>
    <!--end::Nav-->
    @if (Auth::user()->roles == 'SUPERADMIN')
        <!--begin::Footer-->
        <div class="aside-footer flex-column-auto pb-5 pb-lg-10" id="kt_aside_footer">
            <!--begin::Menu-->
            <div class="d-flex flex-center w-100 scroll-px" data-bs-toggle="tooltip" data-bs-placement="right"
                data-bs-dismiss="click" title="Master Data">
                <button type="button"
                    class="btn btn-custom {{ request()->segment(1) == 'user' || request()->segment(1) == 'ttd' ? 'my-primary' : 'my-white' }}"
                    data-kt-menu-trigger="click" data-kt-menu-overflow="true" data-kt-menu-placement="top-start">
                    <i class="fa-solid fa-gear"
                        style="color: {{ request()->segment(1) == 'user' || request()->segment(1) == 'ttd' ? '#fff' : '#000' }}"></i>
                </button>

                <!--begin::Menu 2-->
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-bold w-400px"
                    data-kt-menu="true">
                    <!--begin::Menu item-->
                    <div class="menu-item px-3">
                        <div class="menu-content fs-6 text-dark fw-bolder px-3 py-4">Master Data</div>
                    </div>
                    <!--end::Menu item-->
                    <!--begin::Menu separator-->
                    <div class="separator mb-3 opacity-75"></div>
                    <!--end::Menu separator-->
                    <!--begin::Menu item-->
                    <div class="menu-item px-3 mb-3">
                        <a href="{{ route('user.index') }}" class="menu-link px-3">User Management</a>
                    </div>
                    <div class="menu-item px-3 mb-3" data-kt-menu-trigger="hover"
                        data-kt-menu-placement="right-start">
                        <a href="#" class="menu-link px-3">
                            <span class="menu-title">Customer Dataset</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <div class="menu-sub menu-sub-dropdown w-175px py-4">
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('instansi.index') }}" class="menu-link px-3">Tipe Instansi</a>
                            </div>
                        </div>
                    </div>
                    <div class="menu-item px-3 mb-3" data-kt-menu-trigger="hover"
                        data-kt-menu-placement="right-start">
                        <a href="#" class="menu-link px-3">
                            <span class="menu-title">Order Dataset</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <div class="menu-sub menu-sub-dropdown w-175px py-4">
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('sumber.index') }}" class="menu-link px-3">Sumber Order</a>
                            </div>
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('grading.index') }}" class="menu-link px-3">Grading</a>
                            </div>
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('follow_up.index') }}" class="menu-link px-3">Follow Up Status</a>
                            </div>
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('deal.index') }}" class="menu-link px-3">Deal Status</a>
                            </div>
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('lost.index') }}" class="menu-link px-3">Lost Status</a>
                            </div>
                        </div>
                    </div>
                    <div class="menu-item px-3 mb-3" data-kt-menu-trigger="hover"
                        data-kt-menu-placement="right-start">
                        <a href="#" class="menu-link px-3">
                            <span class="menu-title">Product Dataset</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <div class="menu-sub menu-sub-dropdown w-175px py-4">
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('bahan.index') }}" class="menu-link px-3">Jenis Bahan</a>
                            </div>
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('jenis_kertas.index') }}" class="menu-link px-3">Jenis Kertas</a>
                            </div>
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('gramasi.index') }}" class="menu-link px-3">Gramasi</a>
                            </div>
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('tipe_produk.index') }}" class="menu-link px-3">Tipe Produk</a>
                            </div>
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('kertas_plano.index') }}" class="menu-link px-3">Kertas Plano</a>
                            </div>
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('kategori_masalah.index') }}" class="menu-link px-3">Kategori
                                    Masalah</a>
                            </div>
                        </div>
                    </div>
                    <div class="menu-item px-3 mb-3" data-kt-menu-trigger="hover"
                        data-kt-menu-placement="right-start">
                        <a href="#" class="menu-link px-3">
                            <span class="menu-title">Production Dataset</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <div class="menu-sub menu-sub-dropdown w-175px py-4">
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('vendor_produksi.index') }}" class="menu-link px-3">Vendor</a>
                            </div>
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('mesin.index') }}" class="menu-link px-3">Mesin</a>
                            </div>
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('tools.index') }}" class="menu-link px-3">Tools</a>
                            </div>
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('jenis_ongkos.index') }}" class="menu-link px-3">Jenis Ongkos</a>
                            </div>
                        </div>
                    </div>
                    <div class="menu-item px-3 mb-3" data-kt-menu-trigger="hover"
                        data-kt-menu-placement="right-start">
                        <a href="#" class="menu-link px-3">
                            <span class="menu-title">Pricing Dataset</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <div class="menu-sub menu-sub-dropdown w-175px py-4">
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('paper_price.index') }}" class="menu-link px-3">Harga Kertas</a>
                            </div>
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('cutting_plat_price.index') }}" class="menu-link px-3">Ongkos
                                    Cetak+Plat</a>
                            </div>
                            <div class="menu-item px-3 mb-3">
                                <a href="{{ route('other_price.index') }}" class="menu-link px-3">Ongkos Lainnya</a>
                            </div>
                        </div>
                    </div>
                    <div class="menu-item px-3 mb-3">
                        <a href="{{ route('bank_account.index') }}" class="menu-link px-3">Bank Account</a>
                    </div>
                    {{-- <div class="menu-item px-3 mb-3">
                    <a href="{{ route('ttd.index') }}" class="menu-link px-3">Signed Management</a>
            </div> --}}
                    <!--end::Menu item-->
                </div>
                <!--end::Menu 2-->

            </div>
            <!--end::Menu-->
        </div>
    @endif
    <!--end::Footer-->
</div>
