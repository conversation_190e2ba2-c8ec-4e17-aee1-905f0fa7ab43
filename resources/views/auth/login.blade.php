{{-- <x-guest-layout>
    <x-jet-authentication-card>
        <x-slot name="logo">
            <x-jet-authentication-card-logo />
        </x-slot>

        <div class="card-body">

            <x-jet-validation-errors class="mb-3 rounded-0" />

            @if (session('status'))
                <div class="alert alert-success mb-3 rounded-0" role="alert">
                    {{ session('status') }}
                </div>
            @endif

            <form method="POST" action="{{ route('login') }}">
                @csrf
                <div class="mb-3">
                    <x-jet-label value="{{ __('Email') }}" />

                    <x-jet-input class="{{ $errors->has('email') ? 'is-invalid' : '' }}" type="email"
                                 name="email" :value="old('email')" required />
                    <x-jet-input-error for="email"></x-jet-input-error>
                </div>

                <div class="mb-3">
                    <x-jet-label value="{{ __('Password') }}" />

                    <x-jet-input class="form-control{{ $errors->has('password') ? ' is-invalid' : '' }}" type="password"
                                 name="password" required autocomplete="current-password" />
                    <x-jet-input-error for="password"></x-jet-input-error>
                </div>

                <div class="mb-3">
                    <div class="custom-control custom-checkbox">
                        <x-jet-checkbox id="remember_me" name="remember" />
                        <label class="custom-control-label" for="remember_me">
                            {{ __('Remember Me') }}
                        </label>
                    </div>
                </div>

                <div class="mb-0">
                    <div class="d-flex justify-content-end align-items-baseline">
                        @if (Route::has('password.request'))
                            <a class="text-muted me-3" href="{{ route('password.request') }}">
                                {{ __('Forgot your password?') }}
                            </a>
                        @endif

                        <x-jet-button>
                            {{ __('Log in') }}
                        </x-jet-button>
                    </div>
                </div>
            </form>
        </div>
    </x-jet-authentication-card>
</x-guest-layout> --}}

<!DOCTYPE html>
<html lang="en">
	<!--begin::Head-->
	<head>
		<title>Risepack</title>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<link rel="shortcut icon" href="{{ url('favicon.ico') }}" />
		<!--begin::Fonts-->
		<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700" />
		<!--end::Fonts-->
		<!--begin::Global Stylesheets Bundle(used by all pages)-->
		<link rel="stylesheet" href="{{ asset('css/app.css') }}">
		<link href="{{ url('css/plugin.bundle.css') }}" rel="stylesheet" type="text/css" />
		<link href="{{ url('css/style.bundle.css') }}" rel="stylesheet" type="text/css" />
		<link href="{{ url('css/custom.css') }}" rel="stylesheet" type="text/css" />
		<!--end::Global Stylesheets Bundle-->
	</head>
	<!--end::Head-->
	<!--begin::Body-->
	<body id="kt_body" class="bg-body">
		<!--begin::Main-->
		<!--begin::Root-->
		<div class="d-flex flex-column flex-root">
			<!--begin::Authentication - Signup Free Trial-->
			<div class="d-flex flex-column flex-xl-row flex-column-fluid">
				<!--begin::Aside-->
				<div class="d-flex flex-column flex-center flex-lg-row-fluid" style="background-color: #E4620C">
					<!--begin::Content-->
					<div class="d-flex align-items-center flex-column p-5 p-lg-15">
						<!--begin::Logo-->
						<a href="#" class="mb-8">
							<img alt="Logo" src="{{ url('images/risepack_logo.png.webp') }}" class="h-40px" />
						</a>
						<!--end::Logo-->
						<h1 class="text-white fs-2x mb-3">Welcome, To Risepack Development</h1>
						<!--begin::Description-->
						<div class="fw-bold fs-4 text-white mb-10">Plan your blog post by choosing a topic creating
						<br />an outline and checking facts</div>
						<!--begin::Description-->
						<!--begin::Illustration-->
						<img src="{{ url('images/12.png') }}" class="h-250px h-lg-350px" />
						<!--end::Illustration-->
					</div>
					<!--end::Content-->
				</div>
				<!--begin::Aside-->
				<!--begin::Content-->
				<div class="flex-row-fluid d-flex flex-center justfiy-content-xl-first p-10">
					<!--begin::Wrapper-->
					<div class="d-flex flex-center p-15 shadow-sm bg-body rounded w-100 w-md-550px mx-auto ms-xl-20">
						<!--begin::Form-->
                        <form method="POST" class="form w-100" action="{{ route('login') }}">
                            @csrf
							<!--begin::Heading-->
							<div class="text-center mb-10">
								<!--begin::Title-->
								<h1 class="text-dark mb-3">Sign In To Craft</h1>
								<!--end::Title-->
							</div>
							<!--begin::Heading-->
                            @if (session('status'))
                                    <div class="alert alert-success mb-3 rounded-0" role="alert">
                                        {{ session('status') }}
                                    </div>
                            @endif
							<!--begin::Input group-->
							<div class="fv-row mb-10">
								<label class="form-label fw-bolder text-dark fs-6">Email</label>
                                <x-jet-input class="{{ $errors->has('email') ? 'is-invalid' : '' }} form-control form-control-solid" type="email"
                                    name="email" :value="old('email')" required />
                                <x-jet-input-error for="email"></x-jet-input-error>
							</div>
							<!--end::Input group-->
							<!--begin::Row-->
							<div class="fv-row mb-10">
								<label class="form-label fw-bolder text-dark fs-6">Password</label>
                                <x-jet-input class="form-control form-control-solid{{ $errors->has('password') ? ' is-invalid' : '' }}" type="password"
                                    name="password" required autocomplete="current-password" />
							</div>
							<!--end::Row-->
							<!--begin::Row-->
							<div class="text-center pb-lg-0 pb-8 d-grid">
								<button type="submit" class="btn btn-lg btn-primary fw-bolder my-primary">
									<span class="indicator-label">Sign In</span>
								</button>
							</div>
							<!--end::Row-->
						</form>
						<!--end::Form-->
					</div>
					<!--end::Wrapper-->
				</div>
				<!--end::Right Content-->
			</div>
			<!--end::Authentication - Signup Free Trial-->
		</div>
		<!--end::Root-->
		<!--end::Main-->
		<!--begin::Javascript-->
		<script>var hostUrl = "assets/";</script>
		<script src="{{ mix('js/main.js') }}" defer></script>
		<!--begin::Global Javascript Bundle(used by all pages)-->
		<script src="{{ url('js/plugin.bundle.js') }}"></script>
		<script src="{{ url('js/scripts.bundle.js') }}"></script>
		<!--end::Global Javascript Bundle-->
		<!--begin::Page Custom Javascript(used by this page)-->
		<script src="{{ url('js/login.js') }}"></script>
		<!--end::Page Custom Javascript-->
		<!--end::Javascript-->
	</body>
	<!--end::Body-->
</html>