<div class="d-none" id="loader">
    <div class="row mb-5 g-5">
        @for ($i = 1; $i <= 4; $i++)    
            <div class="col-md-6">
                <div class="card shadow-sm rounded-md mb-5">
                    <div class="card-header" style="min-height: 0%">
                        <div class="shine my-2" style="width:100px;height:20px;"></div>
                    </div>
                    <div class="card-body">
                        <div class="d-flex">
                            <div class="shine" style="width:250px;height:179px;"></div>
                        </div>
                    </div>
                    <div class="card-footer p-3">
                        <div class="shine" style="width:70px;height:20px;"></div>
                    </div>
                </div>
            </div>
        @endfor
    </div>
</div>

<div class="d-none" id="list-loader">
    @for ($i = 1; $i <= 12; $i++)    
        <div class="col-md-3">
            <div class="card shadow-sm rounded-md mb-5">
                <div class="card-header" style="min-height: 0%">
                    <div class="shine my-2" style="width:100px;height:20px;"></div>
                </div>
                <div class="card-body">
                    <div class="d-flex">
                        <div class="shine" style="width:250px;height:179px;"></div>
                    </div>
                </div>
                <div class="card-footer p-3">
                    <div class="shine" style="width:70px;height:20px;"></div>
                </div>
            </div>
        </div>
    @endfor
</div>
<style>
    .shine {
        background: #f6f7f8;
        background-image: linear-gradient(to right, #f6f7f8 0%, #edeef1 20%, #f6f7f8 40%, #f6f7f8 100%);
        background-repeat: no-repeat;
        background-size: 800px 104px; 
        display: inline-block;
        position: relative; 
        
        -webkit-animation-duration: 1s;
        -webkit-animation-fill-mode: forwards; 
        -webkit-animation-iteration-count: infinite;
        -webkit-animation-name: placeholderShimmer;
        -webkit-animation-timing-function: linear;
    }

    @-webkit-keyframes placeholderShimmer {
        0% {
            background-position: -468px 0;
        }
        
        100% {
            background-position: 468px 0; 
        }
    }
</style>