@if ( count($layouts) > 0 )
    @php
        $index = 0;
    @endphp
    @foreach ($layouts as $layout)
        <div class="col-md-3">
            <div class="card shadow-sm rounded-md mb-5 btn-layout" role="button" id="card-{{$index}}" onclick="select_template('{{$index}}')" data-id="{{$layout->id_layout}}" data-kode="{{$layout->kode_layout}}" data-kategori="{{$layout->nama_kategori}}">
                <div class="card-header" style="min-height: 0%">
                    <small class="card-title">{{ $layout->nama_layout }}</small>
                </div>
                <div class="card-body">
                    <div class="d-flex">
                        @if ($layout->path_gambar_produk)
                            <img src="{{ url('designs/'.$layout->path_gambar_produk) }}" width="150" height="200" alt="">
                        @else    
                            <img src="{{ url('images/img-box.png') }}" alt="">
                        @endif

                        @if ($layout->path_gambar_layout)
                            <img src="{{ url('designs/'.$layout->path_gambar_layout) }}" width="150" height="200" alt="">
                        @else    
                            <img src="{{ url('images/img-pola.png') }}" alt="">
                        @endif
                    </div>
                </div>
                <div class="card-footer p-3">
                    <span class="badge special-card text-my-primary rounded-0">{{ $layout->kode_layout }}</span> <span class="badge special-card text-my-primary rounded-0">#{{ $layout->nama_kategori }}</span>
                </div>
            </div>
        </div>
        @php
            ++$index;
        @endphp
    @endforeach
    <div class="row">
        {!! $layouts->links() !!}
    </div>
@else
    <div class="row">
        <small class="my-3">No Data Layout</small>
    </div>
@endif
<script>
    $('.pagination li a').on('click', function(e){
        e.preventDefault();

        var target = '#content-kategori';
        var url = $(this).attr('href');

        $(target).html($('#list-loader').html());
        var id_kategori = $('#id_kategori').val();
        $.ajax({
            url: url,
            type: 'GET',
            data:{
                id_kategori:id_kategori,
                search: $('#layout-search-box').val()
            },
            success: function(data) {
                $(target).html(data);
            },
            error: function(xhr) {
                // Handle any errors
            }
        });
    });    

    // Add active class to the current button (highlight it)
    var header = document.getElementById("content-kategori");
    var btns = header.getElementsByClassName("btn-layout");
    for (var i = 0; i < btns.length; i++) {
        btns[i].addEventListener("click", function() {
            var current = document.getElementsByClassName("actived");
            current[0].className = current[0].className.replace(" actived", "");
            this.className += " actived";
        });
    }

    function select_template(index){
        // $('.btn-layout').each(function(i, obj) {
        //     $(this).removeClass('actived');
        // });

        // $('#card-'+index).addClass('actived');

        // var kode_layout = $('#card-'+index).data('kode');
        // $('#kode_layout').val(kode_layout);

        // var kategori_layout = $('#card-'+index).data('kategori');
        // $('#kategori_layout').val(kategori_layout);
        var id_layout = $('#card-'+index).data('id');
        window.location.href = '{{url("design/edit_layout/")}}/'+id_layout;
    }
</script>    
