<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ url()->previous() }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">
                        {{isset($layout) ? 'Edit Desain Layout Baru' : 'Tambah Desain Layout Baru'}}
                    </h3>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        {{-- @include('pages.design.data_jscript_layout') --}}
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-body">
                <form method="POST" id="form-layout" action="{{ route('design.store_layout') }}" enctype="multipart/form-data">
                    @csrf
                    @method('POST')
                    @if (isset($layout))
                        <input type="hidden" name="id_layout" value="{{$layout->id_layout}}"/>
                        <input type="hidden" name="kode_layout" value="{{$layout->kode_layout}}"/>
                    @endif
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-10">
                                <label for="nama_layout" class="form-label required">Nama Layout</label>
                                <input type="text" name="nama_layout" required id="nama_layout" class="form-control" value="{{isset($layout)? $layout->nama_layout : ''}}"/>
                            </div>
                            <div class="mb-10">
                                <label for="nama_layout" class="form-label required">Kategori Produk</label>
                                <select name="id_kategori_layout" id="id_kategori_layout" required class="form-select">
                                    <option value="" selected disabled>-- Pilih --</option>
                                    @foreach ($kategori as $item)
                                        <option value="{{ $item->id_kategori }}" {{isset($layout) && $layout->id_kategori_layout == $item->id_kategori ? 'selected' : ''}}>{{ $item->nama_kategori }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="mb-10">
                                <label for="jenis_box" class="form-label">Jenis Box</label>
                                <div class="row" id="jenis_box">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="jenis_box" value="1" id="jenis_box_1" {{isset($layout) && $layout->jenis_box == 1? 'checked' :''}}>
                                            <label class="form-check-label" for="jenis_box_1">
                                              Regular
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="jenis_box" value="2" id="jenis_box_2" {{isset($layout) && $layout->jenis_box == 2? 'checked' :''}}>
                                            <label class="form-check-label" for="jenis_box_2">
                                              2 Box
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="jenis_box" value="3" id="jenis_box_3" {{isset($layout) && $layout->jenis_box == 3? 'checked' :''}}>
                                            <label class="form-check-label" for="jenis_box_3">
                                              3 Box
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="my-3">
                                    <p>
                                        <button class="btn btn-sm btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
                                            Panduan Jenis Box
                                        </button>
                                    </p>
                                    <div class="collapse" id="collapseExample">
                                        <table class="table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Jenis</th>
                                                    <th>Keterangan</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td nowrap>Regular</td>
                                                    <td>Default jenis box diluar regulasi jenis <b>2 Box</b> dan <b>3 Box</b></td>
                                                </tr>
                                                <tr>
                                                    <td nowrap>2 Box</td>
                                                    <td>Untuk Menghitung Luas Permukaan Selimut (Atas) di Hardbox Slide</td>
                                                </tr>
                                                <tr>
                                                    <td nowrap>3 Box</td>
                                                    <td>Untuk Menghitung Luas Permukaan Selimut di Hardbox 3 Pieces</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="jenis_box" class="form-label">Tipe Rumus</label>
                                <div class="row" id="jenis_box">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="tipe_rumus" value="1" id="tipe_rumus_1" {{isset($layout) && $layout->tipe_rumus == 1? 'checked' :''}}>
                                            <label class="form-check-label" for="tipe_rumus_1">
                                              Tipe 1
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="tipe_rumus" value="2" id="tipe_rumus_2" {{isset($layout) && $layout->tipe_rumus == 2? 'checked' :''}}>
                                            <label class="form-check-label" for="tipe_rumus_2">
                                              Tipe 2
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="tipe_rumus" value="3" id="tipe_rumus_3" {{isset($layout) && $layout->tipe_rumus == 3? 'checked' :''}}>
                                            <label class="form-check-label" for="tipe_rumus_3">
                                             Tipe 3
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="my-3">
                                    <p>
                                        <button class="btn btn-sm btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample1" aria-expanded="false" aria-controls="collapseExample1">
                                            Panduan Tipe Rumus
                                        </button>
                                    </p>
                                    <div class="collapse" id="collapseExample1">
                                        <table class="table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Tipe</th>
                                                    <th>Keterangan Rumus</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td nowrap>Tipe 1</td>
                                                    <td>Default jenis box softbox dan corrugated umum</td>
                                                </tr>
                                                <tr>
                                                    <td nowrap>Tipe 2</td>
                                                    <td>Untuk Menghitung box corrugated B1</td>
                                                </tr>
                                                <tr>
                                                    <td nowrap>Tipe 3</td>
                                                    <td>Untuk Menghitung Luas Permukaan Selimut (Atas) di Hardbox Magnet</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-10">
                                <label for="path_gambar_produk" class="form-label">Gambar Produk</label>
                                <input type="file" name="path_gambar_produk" class="form-control"/>
                                @if (isset($layout) && $layout->path_gambar_produk)
                                    <img src="{{ url('designs/'.$layout->path_gambar_produk) }}" width="150" height="200" alt="" class="img-thumbnail">
                                @endif
                            </div>
                            <div class="mb-10">
                                <label for="path_gambar_layout" class="form-label">Gambar Layout/Luas Permukaan</label>
                                <input type="file" name="path_gambar_layout" class="form-control"/>
                                @if (isset($layout) && $layout->path_gambar_layout)
                                    <img src="{{ url('designs/'.$layout->path_gambar_layout) }}" width="150" height="200" alt="" class="img-thumbnail">
                                @endif
                            </div>
                            <div class="mb-10">
                                @php
                                    if (isset($layout) && $layout->tambahan_sisi_kertas != null) {
                                        $sisi_kertas_str = $layout->tambahan_sisi_kertas;
                                        $tambahan_sisi_kertas = explode(',', $sisi_kertas_str);
                                    }
                                @endphp     
                                <label for="tambahan_sisi_kertas" class="form-label">Tambahan Sisi Kertas</label>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Lem"
                                                name="tambahan_sisi_kertas[]" id="tambahan_sisi_kertas_1" {{isset($tambahan_sisi_kertas) && in_array('Lem',$tambahan_sisi_kertas) ? 'checked' :''}}/>
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Lem
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Klip"
                                                name="tambahan_sisi_kertas[]" id="tambahan_sisi_kertas_2" {{isset($tambahan_sisi_kertas) && in_array('Klip',$tambahan_sisi_kertas) ? 'checked' :''}}/>
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Klip
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Sayap"
                                                name="tambahan_sisi_kertas[]" id="tambahan_sisi_kertas_3" {{isset($tambahan_sisi_kertas) && in_array('Sayap',$tambahan_sisi_kertas) ? 'checked' :''}}/>
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Sayap
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="T2"
                                                name="tambahan_sisi_kertas[]" id="tambahan_sisi_kertas_3" {{isset($tambahan_sisi_kertas) && in_array('T2',$tambahan_sisi_kertas) ? 'checked' :''}}/>
                                            <label class="form-check-label" for="flexCheckDefault">
                                                T2
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <table class="mt-5 w-100">
                                    <thead>
                                        <tr>
                                            <th>Catatan :</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td nowrap>Selain jenis box berbahan <b>Softbox</b> Rumus tidak perlu di lengkapi</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-10 d-none">
                                <label for="dimensi" class="form-label">Dimensi</label>
                                <div class="row" id="dimensi_panjang">
                                    <div class="col-md-4">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="radio" name="dimensi_panjang" value="panjang" id="dimensi_panjang_1">
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Panjang
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="radio" name="dimensi_panjang" value="panjang 2" id="dimensi_panjang_2">
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Panjang 2
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="radio" name="dimensi_panjang" value="panjang 3" id="dimensi_panjang_3">
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Panjang 3
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="radio" name="dimensi_lebar" value="lebar" id="dimensi_lebar_1">
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Lebar
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="radio" name="dimensi_lebar" value="lebar 2" id="dimensi_lebar_2">
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Lebar 2
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="radio" name="dimensi_lebar" value="lebar 3" id="dimensi_lebar_3">
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Lebar 3
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="radio" name="dimensi_tinggi" value="lebar" id="dimensi_tinggi_1">
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Tinggi
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="radio" name="dimensi_tinggi" value="lebar 2" id="dimensi_tinggi_2">
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Tinggi 2
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="radio" name="dimensi_tinggi" value="lebar 3" id="dimensi_tinggi_3">
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Tinggi 3
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="" class="form-label">Rumus</label>
                                <div id="rumus_1" class="mb-15">
                                    <div class="row mt-3">
                                        <label for="" class="form-label fw-light">Lebar Permukaan Cetak</label>
                                        <div class="row d-flex">
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_lebar_cetak_1_angka_1" value="{{ isset($layout) ? $layout->rumus_lebar_cetak_1_angka_1 : ''}}" id="rumus_lebar_cetak_1_angka_1" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_lebar_cetak_1_parameter_1" id="rumus_lebar_cetak_1_parameter_1" class="form-select">
                                                            <option value="" {{ isset($layout) ?? 'selected'}}>-- Pilih --</option>
                                                            <option value="panjang" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_1 == 'panjang' ? 'selected' : ''}}>Panjang</option>
                                                            <option value="lebar" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_1 == 'lebar' ? 'selected' : ''}}>Lebar</option>
                                                            <option value="tinggi" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_1 == 'tinggi' ? 'selected' : ''}}>Tinggi</option>
                                                            <option value="lem" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_1 == 'lem' ? 'selected' : ''}}>Lem</option>
                                                            <option value="klip" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_1 == 'klip' ? 'selected' : ''}}>Klip</option>
                                                            <option value="sayap" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_1 == 'sayap' ? 'selected' : ''}}>Sayap</option>
                                                            <option value="t2" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_1 == 't2' ? 'selected' : ''}}>T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_lebar_cetak_1_angka_2" value="{{ isset($layout) ? $layout->rumus_lebar_cetak_1_angka_2 : ''}}" id="rumus_lebar_cetak_1_angka_2" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_lebar_cetak_1_parameter_2" id="rumus_lebar_cetak_1_parameter_2" class="form-select">
                                                            <option value="" {{ isset($layout) ?? 'selected'}}>-- Pilih --</option>
                                                            <option value="panjang" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_2 == 'panjang' ? 'selected' : ''}}>Panjang</option>
                                                            <option value="lebar" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_2 == 'lebar' ? 'selected' : ''}}>Lebar</option>
                                                            <option value="tinggi" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_2 == 'tinggi' ? 'selected' : ''}}>Tinggi</option>
                                                            <option value="lem" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_2 == 'lem' ? 'selected' : ''}}>Lem</option>
                                                            <option value="klip" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_2 == 'klip' ? 'selected' : ''}}>Klip</option>
                                                            <option value="sayap" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_2 == 'sayap' ? 'selected' : ''}}>Sayap</option>
                                                            <option value="t2" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_2 == 't2' ? 'selected' : ''}}>T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label  mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_lebar_cetak_1_angka_3" value="{{ isset($layout) ? $layout->rumus_lebar_cetak_1_angka_3 : ''}}" id="rumus_lebar_cetak_1_angka_3" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_lebar_cetak_1_parameter_3" id="rumus_lebar_cetak_1_parameter_3" class="form-select">
                                                            <option value="" {{ isset($layout) ?? 'selected'}}>-- Pilih --</option>
                                                            <option value="panjang" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_3 == 'panjang' ? 'selected' : ''}}>Panjang</option>
                                                            <option value="lebar" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_3 == 'lebar' ? 'selected' : ''}}>Lebar</option>
                                                            <option value="tinggi" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_3 == 'tinggi' ? 'selected' : ''}}>Tinggi</option>
                                                            <option value="lem" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_3 == 'lem' ? 'selected' : ''}}>Lem</option>
                                                            <option value="klip" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_3 == 'klip' ? 'selected' : ''}}>Klip</option>
                                                            <option value="sayap" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_3 == 'sayap' ? 'selected' : ''}}>Sayap</option>
                                                            <option value="t2" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_3 == 't2' ? 'selected' : ''}}>T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label  mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_lebar_cetak_1_angka_4" value="{{ isset($layout) ? $layout->rumus_lebar_cetak_1_angka_4 : ''}}" id="rumus_lebar_cetak_1_angka_4" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_lebar_cetak_1_parameter_4" id="rumus_lebar_cetak_1_parameter_4" class="form-select">
                                                            <option value="" {{ isset($layout) ?? 'selected'}}>-- Pilih --</option>
                                                            <option value="panjang" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_4 == 'panjang' ? 'selected' : ''}}>Panjang</option>
                                                            <option value="lebar" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_4 == 'lebar' ? 'selected' : ''}}>Lebar</option>
                                                            <option value="tinggi" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_4 == 'tinggi' ? 'selected' : ''}}>Tinggi</option>
                                                            <option value="lem" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_4 == 'lem' ? 'selected' : ''}}>Lem</option>
                                                            <option value="klip" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_4 == 'klip' ? 'selected' : ''}}>Klip</option>
                                                            <option value="sayap" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_4 == 'sayap' ? 'selected' : ''}}>Sayap</option>
                                                            <option value="t2" {{ isset($layout) && $layout->rumus_lebar_cetak_1_parameter_4 == 't2' ? 'selected' : ''}}>T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <label for="" class="form-label fw-light">Panjang Permukaan Cetak</label>
                                        <div class="row d-flex">
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_panjang_cetak_1_angka_1" value="{{ isset($layout) ? $layout->rumus_panjang_cetak_1_angka_1 : ''}}" id="rumus_panjang_cetak_1_angka_1" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_panjang_cetak_1_parameter_1" id="rumus_panjang_cetak_1_parameter_1" class="form-select">
                                                            <option value="" {{ isset($layout) ?? 'selected'}}>-- Pilih --</option>
                                                            <option value="panjang" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_1 == 'panjang' ? 'selected' : ''}}>Panjang</option>
                                                            <option value="lebar" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_1 == 'lebar' ? 'selected' : ''}}>Lebar</option>
                                                            <option value="tinggi" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_1 == 'tinggi' ? 'selected' : ''}}>Tinggi</option>
                                                            <option value="lem" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_1 == 'lem' ? 'selected' : ''}}>Lem</option>
                                                            <option value="klip" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_1 == 'klip' ? 'selected' : ''}}>Klip</option>
                                                            <option value="sayap" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_1 == 'sayap' ? 'selected' : ''}}>Sayap</option>
                                                            <option value="t2" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_1 == 't2' ? 'selected' : ''}}>T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_panjang_cetak_1_angka_2" value="{{ isset($layout) ? $layout->rumus_panjang_cetak_1_angka_2 : ''}}" id="rumus_panjang_cetak_1_angka_2" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_panjang_cetak_1_parameter_2" id="rumus_panjang_cetak_1_parameter_2" class="form-select">
                                                            <option value="" {{ isset($layout) ?? 'selected'}}>-- Pilih --</option>
                                                            <option value="panjang" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_2 == 'panjang' ? 'selected' : ''}}>Panjang</option>
                                                            <option value="lebar" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_2 == 'lebar' ? 'selected' : ''}}>Lebar</option>
                                                            <option value="tinggi" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_2 == 'tinggi' ? 'selected' : ''}}>Tinggi</option>
                                                            <option value="lem" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_2 == 'lem' ? 'selected' : ''}}>Lem</option>
                                                            <option value="klip" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_2 == 'klip' ? 'selected' : ''}}>Klip</option>
                                                            <option value="sayap" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_2 == 'sayap' ? 'selected' : ''}}>Sayap</option>
                                                            <option value="t2" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_2 == 't2' ? 'selected' : ''}}>T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label  mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_panjang_cetak_1_angka_3" value="{{ isset($layout) ? $layout->rumus_panjang_cetak_1_angka_3 : ''}}" id="rumus_panjang_cetak_1_angka_3" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_panjang_cetak_1_parameter_3" id="rumus_panjang_cetak_1_parameter_3" class="form-select">
                                                            <option value="" {{ isset($layout) ?? 'selected'}}>-- Pilih --</option>
                                                            <option value="panjang" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_3 == 'panjang' ? 'selected' : ''}}>Panjang</option>
                                                            <option value="lebar" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_3 == 'lebar' ? 'selected' : ''}}>Lebar</option>
                                                            <option value="tinggi" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_3 == 'tinggi' ? 'selected' : ''}}>Tinggi</option>
                                                            <option value="lem" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_3 == 'lem' ? 'selected' : ''}}>Lem</option>
                                                            <option value="klip" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_3 == 'klip' ? 'selected' : ''}}>Klip</option>
                                                            <option value="sayap" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_3 == 'sayap' ? 'selected' : ''}}>Sayap</option>
                                                            <option value="t2" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_3 == 't2' ? 'selected' : ''}}>T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label  mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_panjang_cetak_1_angka_4" value="{{ isset($layout) ? $layout->rumus_panjang_cetak_1_angka_4 : ''}}" id="rumus_panjang_cetak_1_angka_4" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_panjang_cetak_1_parameter_4" id="rumus_panjang_cetak_1_parameter_4" class="form-select">
                                                            <option value="" {{ isset($layout) ?? 'selected'}}>-- Pilih --</option>
                                                            <option value="panjang" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_4 == 'panjang' ? 'selected' : ''}}>Panjang</option>
                                                            <option value="lebar" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_4 == 'lebar' ? 'selected' : ''}}>Lebar</option>
                                                            <option value="tinggi" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_4 == 'tinggi' ? 'selected' : ''}}>Tinggi</option>
                                                            <option value="lem" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_4 == 'lem' ? 'selected' : ''}}>Lem</option>
                                                            <option value="klip" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_4 == 'klip' ? 'selected' : ''}}>Klip</option>
                                                            <option value="sayap" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_4 == 'sayap' ? 'selected' : ''}}>Sayap</option>
                                                            <option value="t2" {{ isset($layout) && $layout->rumus_panjang_cetak_1_parameter_4 == 't2' ? 'selected' : ''}}>T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="rumus_2" class="mb-15 d-none">
                                    <div class="row mt-3">
                                        <label for="" class="form-label fw-light">Lebar Permukaan Cetak</label>
                                        <div class="row d-flex">
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_lebar_cetak_2_angka_1" value="{{ old('rumus_lebar_cetak_2_angka_1') }}" id="rumus_lebar_cetak_2_angka_1" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_lebar_cetak_2_parameter_1" id="rumus_lebar_cetak_2_parameter_1" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_lebar_cetak_2_angka_2" value="{{ old('rumus_lebar_cetak_2_angka_2') }}" id="rumus_lebar_cetak_2_angka_2" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_lebar_cetak_2_parameter_2" id="rumus_lebar_cetak_2_parameter_2" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label  mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_lebar_cetak_2_angka_3" value="{{ old('rumus_lebar_cetak_2_angka_3') }}" id="rumus_lebar_cetak_2_angka_3" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_lebar_cetak_2_parameter_3" id="rumus_lebar_cetak_2_parameter_3" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_lebar_cetak_2_angka_4" value="{{ old('rumus_lebar_cetak_2_angka_4') }}" id="rumus_lebar_cetak_2_angka_4" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_lebar_cetak_2_parameter_4" id="rumus_lebar_cetak_2_parameter_4" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <label for="" class="form-label fw-light">Panjang Permukaan Cetak</label>
                                        <div class="row d-flex">
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_panjang_cetak_2_angka_1" value="{{ old('rumus_panjang_cetak_2_angka_1') }}" id="rumus_panjang_cetak_2_angka_1" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_panjang_cetak_2_parameter_1" id="rumus_panjang_cetak_2_parameter_1" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_panjang_cetak_2_angka_2" value="{{ old('rumus_panjang_cetak_2_angka_2') }}" id="nama_layout" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_panjang_cetak_2_parameter_2" id="rumus_panjang_cetak_2_parameter_2" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label  mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_panjang_cetak_2_angka_3" value="{{ old('rumus_panjang_cetak_2_angka_3') }}" id="rumus_panjang_cetak_2_angka_3" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_panjang_cetak_2_parameter_3" id="rumus_panjang_cetak_2_parameter_3" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_panjang_cetak_2_angka_4" value="{{ old('rumus_panjang_cetak_2_angka_4') }}" id="rumus_panjang_cetak_2_angka_4" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_panjang_cetak_2_parameter_4" id="rumus_panjang_cetak_2_parameter_4" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="rumus_3" class="d-none">
                                    <div class="row mt-3">
                                        <label for="" class="form-label fw-light">Lebar Permukaan Cetak</label>
                                        <div class="row d-flex">
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_lebar_cetak_3_angka_1" value="{{ old('rumus_lebar_cetak_3_angka_1') }}" id="rumus_lebar_cetak_3_angka_1" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_lebar_cetak_3_parameter_1" id="rumus_lebar_cetak_3_parameter_1" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_lebar_cetak_3_angka_2" value="{{ old('rumus_lebar_cetak_3_angka_2') }}" id="nama_layout" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_lebar_cetak_3_parameter_2" id="rumus_lebar_cetak_3_parameter_2" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label  mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_lebar_cetak_3_angka_3" value="{{ old('rumus_lebar_cetak_3_angka_3') }}" id="rumus_lebar_cetak_3_angka_3" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_lebar_cetak_3_parameter_3" id="rumus_lebar_cetak_3_parameter_3" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_lebar_cetak_3_angka_4" value="{{ old('rumus_lebar_cetak_3_angka_4') }}" id="nama_layout" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_lebar_cetak_3_parameter_4" id="rumus_lebar_cetak_3_parameter_4" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-3">
                                        <label for="" class="form-label fw-light">Panjang Permukaan Cetak</label>
                                        <div class="row d-flex">
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_panjang_cetak_3_angka_1" value="{{ old('rumus_panjang_cetak_3_angka_1') }}" id="rumus_panjang_cetak_3_angka_1" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_panjang_cetak_3_parameter_1" id="rumus_panjang_cetak_3_parameter_1" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_panjang_cetak_3_angka_2" value="{{ old('rumus_panjang_cetak_3_angka_2') }}" id="rumus_panjang_cetak_3_angka_2" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_panjang_cetak_3_parameter_2" id="rumus_panjang_cetak_3_parameter_2" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label  mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_panjang_cetak_3_angka_3" value="{{ old('rumus_panjang_cetak_3_angka_3') }}" id="rumus_panjang_cetak_3_angka_3" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_panjang_cetak_3_parameter_3" id="rumus_panjang_cetak_3_parameter_3" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div style="width: 20px;">
                                                <label for="" class="form-label mt-3">+</label>
                                            </div>
                                            <div class="col">
                                                <div class="row g-2">
                                                    <div class="col-md-5">
                                                        <input type="number" step="0.01" name="rumus_panjang_cetak_3_angka_4" value="{{ old('rumus_panjang_cetak_3_angka_4') }}" id="nama_layout" class="form-control"/>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <select name="rumus_panjang_cetak_3_parameter_4" id="rumus_panjang_cetak_3_parameter_4" class="form-select">
                                                            <option value="" selected disabled>-- Pilih --</option>
                                                            <option value="panjang">Panjang</option>
                                                            <option value="lebar">Lebar</option>
                                                            <option value="tinggi">Tinggi</option>
                                                            <option value="lem">Lem</option>
                                                            <option value="klip">Klip</option>
                                                            <option value="sayap">Sayap</option>
                                                            <option value="t2">T2</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input type="submit" class="btn btn-my-primary text-white" value="Simpan">
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>