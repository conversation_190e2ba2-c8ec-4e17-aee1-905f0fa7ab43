<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ route('design') }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">Design page</h3>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        <script>
            //CSRF
            $(document).ready(function () {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });

                $('#tab_1').trigger('click');
            });

            //modal insert
            $('#submit-form-kategori-layout').click(function (e) {
                e.preventDefault();
                $(this).html('Sending..');
                $.ajax({
                    data: $('#kategori-layout-form').serialize(),
                    url: "{{ route('design.kategori_layout.store') }}",
                    type: "POST",
                    dataType: 'json',
                    success: function (data) {
                        $('#modal-add-kategori-layout').modal('hide');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "kategori-layout baru berhasil dibuat",
                            icon: 'success',
                        })
                        location.reload();
                    },
                    error: function (data) {
                        $('#submit-form-kategori-layout').html('Save Changes');
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });

            $('#myTab .nav-link').on('click', function(e){
                
                e.preventDefault();

                $('#myTab .nav-link .active').removeClass('active');
                $(this).addClass('active');

                let target = '#content-kategori';
                let id = $(this).data('id');
                
                $('#id_kategori').val(id);

                let id_kategori = $('#id_kategori').val();
                $(target).html($('#list-loader').html());
                $.ajax({
                    type: "GET",
                    url: "{{route('design.kategori_layout.list_layout')}}",
                    data: {
                        id_kategori:id_kategori
                    },
                    success: function(data)
                    {
                        $(target).html(data);
                    }
                });
            });

            $('#layout-search-box').on('change keyup keydown', function(){
                let target = '#content-kategori';
                let id_kategori = $('#id_kategori').val();
                let search = $(this).val();
                console.log(search);
                $.ajax({
                    type: "GET",
                    url: "{{route('design.kategori_layout.list_layout')}}",
                    data: {
                        id_kategori:id_kategori,
                        search:search
                    },
                    success: function(data)
                    {
                        $(target).html(data);
                    }
                });
            })
        </script>
        <style>
            .btn-layout:hover {
                border:1px solid #F77C2A;
                box-shadow: none;
            }
        </style>
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bolder mt-n12" id="myTab">
            @if (count($kategori_layout) > 0)
                @php $no = 1; @endphp
                @foreach ($kategori_layout as $item)
                    <li class="nav-item">
                        <a class="nav-link text-white py-5 me-6" id="tab_{{$no}}" data-id="{{$item->id_kategori}}" data-bs-toggle="tab" data-bs-target="#tab_{{$item->id}}">{{$item->nama_kategori}} ({{$item->getCountLayout()}})</a>
                    </li>
                    @php $no++; @endphp
                @endforeach
            @endif
            <input type="hidden" name="id_kategori" id="id_kategori"/>
        </ul>
        <div class="tab-content mt-20" id="myTabContent">
            <div class="row justify-content-end mb-2">
                <div class="col-5 d-flex">
                    <div class="row">
                        <div class="col-md-7">
                            <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                                <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none">
                                        <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                            transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                        <path
                                            d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                            fill="black"></path>
                                    </svg>
                                </span>
                                <input type="text" id="layout-search-box"
                                    class="form-control form-control-solid w-450px ps-15 shadow" placeholder="Search ...">
                            </div>
                        </div>
                        <div class="col-md-5">
                            <button class="btn btn-my-primary text-white w-100" type="button" data-bs-toggle="modal"
                            data-bs-target="#modal-add-kategori-layout">Add new kategori</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" id="content-kategori">
            </div>
        </div>
    </div>
    @include('pages.design.skelton')
    <div class="modal" tabindex="-1" id="modal-add-kategori-layout">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                
                    <div class="modal-header">
                        <h5 class="modal-title">Tambah Kategori Layout</h5>
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-kategori-layout-close"
                            data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                        </div>
                    </div>

                    <div class="modal-body mt-n5">
                        <div class="row">
                            <div class="col-lg-12">
                                <form id="kategori-layout-form" name="kategori-layout-form" class="form-horizontal">
                                    <div class="mb-3">
                                        <label for="nama_kategori" class="required form-label">Kategori Layout</label>
                                        <input type="text" id="nama_kategori" name="nama_kategori" :value="old('nama_kategori')" required autofocus
                                            class="form-control" placeholder="Nama Kategori">
                                    </div>
                                    <div class="mb-3">
                                        <label for="keterangan" class="required form-label">Keterangan</label>
                                        <textarea id="keterangan" name="keterangan" required autofocus
                                            class="form-control" placeholder="Keterangan">{{old('keterangan')}}</textarea>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="add-kategori-layout-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                        <button type="submit" id="submit-form-kategori-layout" class="btn btn-primary my-primary">Save</button>
                    </div>
            </div>
        </div>
    </div>
</x-app-layout>