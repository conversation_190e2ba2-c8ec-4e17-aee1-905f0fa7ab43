<script>
    const format = (item) => {
        if (!item.id) {
            return item.text;
        }
        var img =""
        var span = $("<span>", {
            text: " " + item.text
        });
        span.prepend(img)
        return span
    }
    $(document).ready(function() {

        $('#id_bahans').select2({
            allowClear: true,
            debug: true,
            dropdownParent: $('#v-pills-2 .card'),
            ajax: {
                url: "{{ route('crm.get_bahan') }}",
                processResults: function(data) {
                    var resultsData = []

                    $.each(data, function(index, item) {
                        resultsData.push({
                            id: item.id_bahans,
                            text: item.bahan
                        })
                    })

                    return {
                        results: resultsData
                    };
                },
                // cache: true
            },
            templateResult: function(item) {
                return format(item)
            }
        })

        $('#id_jenis_kertas').select2({
            allowClear: true,
            debug: true,
            dropdownParent: $('#v-pills-2 .card'),
            ajax: {
                url: "{{ route('crm.get_jenis_kertas') }}",
                processResults: function(data) {
                    var resultsData = []

                    $.each(data, function(index, item) {
                        resultsData.push({
                            id: item.id_jenis_kertas,
                            text: item.jenis_kertas
                        })
                    })

                    return {
                        results: resultsData
                    };
                },
                // cache: true
            },
            templateResult: function(item) {
                return format(item)
            }
        })

        $('#id_jenis_kertas').on('change', function(){
            var id_jenis_kertas = $(this).val();
            $('#id_kertas_plano').select2({
                allowClear: true,
                debug: true,
                dropdownParent: $('#v-pills-2 .card'),
                ajax: {
                    url: "{{ route('crm.get_kertas_plano') }}",
                    data :{
                        id_jenis_kertas:id_jenis_kertas
                    },
                    processResults: function(data) {
                        var resultsData = []

                        $.each(data, function(index, item) {
                            resultsData.push({
                                id: item.id_kertas_plano,
                                text: item.kertas_plano
                            })
                        })

                        return {
                            results: resultsData
                        };
                    },
                    // cache: true
                },
                templateResult: function(item) {
                    return format(item)
                }
            })
        })

        $('#id_kertas_plano').on('change', function(){
            var id_jenis_kertas = $('#id_jenis_kertas').val();
            var id_kertas_plano = $(this).val();

            $('#id_gramasi').select2({
                allowClear: true,
                debug: true,
                dropdownParent: $('#v-pills-2 .card'),
                ajax: {
                    url: "{{ route('crm.get_gramasi') }}",
                    data :{
                        id_jenis_kertas:id_jenis_kertas,
                        id_kertas_plano:id_kertas_plano
                    },
                    processResults: function(data) {
                        var resultsData = []

                        $.each(data, function(index, item) {
                            resultsData.push({
                                id: item.id_gramasi,
                                text: item.gramasi
                            })
                        })

                        return {
                            results: resultsData
                        };
                    },
                    // cache: true
                },
                templateResult: function(item) {
                    return format(item)
                }
            })
        })

        $('#id_gramasi').on('change', function(){
            let id_gramasi = $(this).val();
            $('#id_mesin').select2({
                allowClear: true,
                debug: true,
                dropdownParent: $('#v-pills-4 .card'),
                ajax: {
                    url: "{{ route('design.list_mesin') }}",
                    data:{
                        id_gramasi:id_gramasi
                    },
                    processResults: function(data) {
                        var resultsData = []

                        $.each(data, function(index, item) {
                            resultsData.push({
                                id: item.id_mesin,
                                text: item.mesin
                            })
                        })

                        return {
                            results: resultsData
                        };
                    },
                    // cache: true
                },
                templateResult: function(item) {
                    return format(item)
                }
            })
        })

        $('#id_jenis_sekat').select2({
            allowClear: true,
            debug: true,
            dropdownParent: $('#v-pills-4 .card'),
            ajax: {
                url: "{{ route('crm.get_jenis_kertas') }}",
                processResults: function(data) {
                    var resultsData = []

                    $.each(data, function(index, item) {
                        resultsData.push({
                            id: item.id_jenis_kertas,
                            text: item.jenis_kertas
                        })
                    })

                    return {
                        results: resultsData
                    };
                },
                // cache: true
            },
            templateResult: function(item) {
                return format(item)
            }
        })

        getData();
    })

    function getData(){
        var target = '#list-layout';

        $(target).html($('#loader').html());

        $.ajax({
            url: '{{url("design/get_layout")}}',
            type: 'GET',
            data:{
                search: $('#layout-search-box').val()
            },
            success: function(data) {
                $(target).html(data);
            },
            error: function(xhr) {
                Swal.fire({
                    title: 'Failed!',
                    text: "Terjadi Kesalahan",
                    icon: 'error',
                })
            }
        });
    }

    $('#layout-search-box').on('change', function(){
        getData()
    })

    $('#preview-paper-cut').on('click', function(){
        $('#modal-calculator-paper-cut').modal('show');
    })

    $('#myForm').submit(function(e){
        e.preventDefault();
        var form = $('#myForm');
        var id_kertas_plano = $('#id_kertas_plano').val();
        var id_layout = $('#id_layout').val();
        var id_bahans = $('#id_bahans').val();
        var tipe = '';
        $(".tipe-material").each(function(index) {
            if ($(this).is(':checked')) {
                tipe = $(this).val();
            }
        });
        
        $.ajax({
            url: '{{url("design/counting_cutting_paper")}}',
            data: form.serialize()+"&id_kertas_plano="+id_kertas_plano+"&id_layout="+id_layout+"&id_bahans="+id_bahans+"&tipe="+tipe,
            success: function(response) {
                if (!response.p_cetak) {
                    Swal.fire({
                        title: 'Failed!',
                        text: "Terjadi Kesalahan",
                        icon: 'error',
                    })
                } else {
                    $('#panjang_cetak').val(response.p_cetak);
                    $('#lebar_cetak').val(response.l_cetak);
                    $('#panjang_kemasan').val(response.p_kemasan);
                    $('#lebar_kemasan').val(response.l_kemasan);
                    $('#jumlah_kertas_potong').val(response.jumlah_potong);
                    $('#wasting').val(response.percentage_waste);
                    $('#btn-hitung').text('Hitung Jumlah Kertas Potong');
                }
            },
            error: function(xhr) {
                Swal.fire({
                    title: 'Failed!',
                    text: "Terjadi Kesalahan",
                    icon: 'error',
                })
            }
        });
    });

    $('#s_panjang').on('change', function(){
        $('#dimensi_panjang').val($(this).val());
    })

    $('#s_lebar').on('change', function(){
        $('#dimensi_lebar').val($(this).val());
    })

    $('#s_tinggi').on('change', function(){
        $('#dimensi_tinggi').val($(this).val());
    })

    $('#form-layout').submit(function(e){
        e.preventDefault();
        var form = $('#form-layout');
        var id_bahans = $('#id_bahans').val();
        var id_jenis_kertas = $('#id_jenis_kertas').val();
        var id_kertas_plano = $('#id_kertas_plano').val();
        var id_gramasi = $('#id_gramasi').val();
        var isi_kertas = $('#isi_kertas').val();
        var tipe = '';
        $(".tipe-material").each(function(index) {
            if ($(this).is(':checked')) {
                tipe = $(this).val();
            }
        });
        $.ajax({
            type: 'POST',
            url: '{{url("design/calculating_price")}}',
            data: form.serialize()+"&id_kertas_plano="+id_kertas_plano+"&id_jenis_kertas="+id_jenis_kertas+"&id_gramasi="+id_gramasi+"&isi_kertas="+isi_kertas+"&id_bahans="+id_bahans+"&tipe="+tipe,
            success: function(response) {
                if (!response.quantity) {
                    Swal.fire({
                        title: 'Failed!',
                        text: "Terjadi Kesalahan",
                        icon: 'error',
                    })
                } else {
                    $('#final-qty').html(response.quantity);
                    $('#harga-satuan').html('Rp. '+response.harga_modal.toLocaleString('En-en'));
                    let subtotal = parseInt(response.harga_modal)*parseInt(response.quantity);
                    $('#subtotal').html('Rp. '+subtotal.toLocaleString('En-en'));
                    $('#total').html($('#subtotal').html());
                    $('#kebutuhan_kertas').val(JSON.stringify(response.kebutuhan_kertas));
                    $('#prices').val(JSON.stringify(response.prices));
                    $('#additional_cost').val(JSON.stringify(response.additional_cost));
                    $('#print_cost').val(response.cetak);
                    $('#harga_modal').val(response.harga_modal);
                }
            },
            error: function(xhr) {
                Swal.fire({
                    title: 'Failed!',
                    text: "Terjadi Kesalahan",
                    icon: 'error',
                })
            }
        });
    });

    function onchangeSize(){
        var tipe = '';
        $(".tipe-material").each(function(index) {
            if ($(this).is(':checked')) {
                tipe = $(this).val();
            }
        });
        $.ajax({
            url: '{{url("design/counting_cutting_paper")}}',
            data: {
                panjang_cetak : $('#panjang_cetak').val(),
                lebar_cetak : $('#lebar_cetak').val(),
                panjang_kemasan : $('#panjang_kemasan').val(),
                lebar_kemasan : $('#lebar_kemasan').val(),
                id_kertas_plano : $('#id_kertas_plano').val(),
                id_bahans : $('#id_bahans').val(),
                tipe : tipe,
                is_force:true
            },
            success: function(response) {
                if (!response.p_cetak) {
                    Swal.fire({
                        title: 'Failed!',
                        text: "Terjadi Kesalahan",
                        icon: 'error',
                    })
                } else {
                    $('#panjang_cetak').val(response.p_cetak);
                    $('#lebar_cetak').val(response.l_cetak);
                    $('#panjang_kemasan').val(response.p_kemasan);
                    $('#lebar_kemasan').val(response.l_kemasan);
                    $('#jumlah_kertas_potong').val(response.jumlah_potong);
                    $('#wasting').val(response.percentage_waste);
                }
            },
            error: function(xhr) {
                Swal.fire({
                    title: 'Failed!',
                    text: "Terjadi Kesalahan",
                    icon: 'error',
                })
            }
        });
    }

    $('input[name="is_hot_stamp"]').on('change', function(){
        var selectedValue = $(this).val();
        if (parseInt(selectedValue) > 0) {
            $('#pl-hot-stamp').removeClass('d-none');
        } else {
            $('#pl-hot-stamp').addClass('d-none');
        }
    })

    $('input[name="is_emboss"]').on('change', function(){
        var selectedValue = $(this).val();
        if (parseInt(selectedValue) > 0) {
            $('#pl-emboss').removeClass('d-none');
        } else {
            $('#pl-emboss').addClass('d-none');
        }
    })

    $('input[name="is_sekat"]').on('change', function(){
        var selectedValue = $(this).val();
        if (parseInt(selectedValue) > 0) {
            $('#pl-sekat').removeClass('d-none');
        } else {
            $('#pl-sekat').addClass('d-none');
        }
    })

    $('input[name="is_magnet_pita"]').on('change', function(){
        var selectedValue = $(this).val();
        if (parseInt(selectedValue) > 0) {
            $('#pl-magnet-pita').removeClass('d-none');
        } else {
            $('#pl-magnet-pita').addClass('d-none');
        }
    })

    function save_draft()
    {
        var id_layout = $('#id_layout').val();
        var id_bahans = $('#id_bahans').val();
        var id_jenis_kertas = $('#id_jenis_kertas').val();
        var id_kertas_plano = $('#id_kertas_plano').val();
        var id_gramasi = $('#id_gramasi').val();
        var isi_kertas = $('#isi_kertas').val();
        var harga_modal = $('#harga_modal').val();
        var kebutuhan_kertas = $('#kebutuhan_kertas').val();
        var additional_cost = $('#additional_cost').val();
        var print_cost = $('#print_cost').val();
        var prices = $('#prices').val();
        var dimensi_p = $('#s_panjang').val();
        var dimensi_l = $('#s_lebar').val();
        var dimensi_t = $('#s_tinggi').val();
        var quantity = $('#quantity').val();
        var panjang_cetak = $('#panjang_cetak').val();
        var lebar_cetak = $('#lebar_cetak').val();
        var panjang_kemasan = $('#panjang_kemasan').val();
        var lebar_kemasan = $('#lebar_kemasan').val();
        var jumlah_kertas_potong = $('#jumlah_kertas_potong').val();
        var wasting = $('#wasting').val();
        var description = $('#description').val();
        var tipe = '';
        $(".tipe-material").each(function(index) {
            if ($(this).is(':checked')) {
                tipe = $(this).val();
            }
        });
        
        var formData = new FormData();
        formData.append("id_layout", id_layout);
        formData.append("id_bahans", id_bahans);
        formData.append("id_jenis_kertas", id_jenis_kertas);
        formData.append("id_kertas_plano", id_kertas_plano);
        formData.append("id_gramasi", id_gramasi);
        formData.append("isi_kertas", isi_kertas);
        formData.append("kebutuhan_kertas", kebutuhan_kertas);
        formData.append("additional_cost", additional_cost);
        formData.append("print_cost", print_cost);
        formData.append("prices", prices);
        formData.append("dimensi_p", dimensi_p);
        formData.append("dimensi_l", dimensi_l);
        formData.append("dimensi_t", dimensi_t);
        formData.append("quantity", quantity);
        formData.append("panjang_cetak", panjang_cetak);
        formData.append("lebar_cetak", lebar_cetak);
        formData.append("panjang_kemasan", panjang_kemasan);
        formData.append("lebar_kemasan", lebar_kemasan);
        formData.append("description", description);
        formData.append("harga_modal", harga_modal);
        formData.append("jumlah_kertas_potong", jumlah_kertas_potong);
        formData.append("wasting", wasting);
        formData.append("tipe", tipe);

        $.ajax({
            type: 'POST',
            url: '{{url("design/save_draft")}}',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: formData,
            processData: false,
            contentType: false,
            type: "POST",
            dataType: "json",
            success: function(response) {
                if (response.status_code == 200) {
                    Swal.fire({
                        title: 'SUCCESS!',
                        text: "Data Berhasil Disimpan",
                        icon: 'success',
                    })
                    location.reload();
                } else {
                    Swal.fire({
                        title: 'Failed!',
                        text: "Terjadi Kesalahan",
                        icon: 'error',
                    })
                }
            },
            error: function(xhr) {
                Swal.fire({
                    title: 'Failed!',
                    text: "Terjadi Kesalahan",
                    icon: 'error',
                })
            }
        });
    }

    $('#id_bahans').on('change', function(){
        var id_bahan = $(this).val();
        if (id_bahan == 2) {
            //show opsi-hardbox
            $(".opsi-hardbox").each(function(index) {
                $(this).removeClass('d-none');
            });

            //hide opsi-hardbox
            $(".opsi-softbox").each(function(index) {
                $(this).addClass('d-none');
            });

            //change text btn-hitung
            $('#btn-hitung').text('Hitung Box Bawah');

            $.ajax({
                type:'get',
                url: '{{url("design/getBoxAtas")}}',
                success: function(data) {
                    $('#box-atas').html(data);
                },
                error: function(xhr) {
                    Swal.fire({
                    title: 'Failed!',
                    text: "Terjadi Kesalahan",
                    icon: 'error',
                })
                }
            });
        } else if (id_bahan == 1){
            //show opsi-hardbox
            $(".opsi-softbox").each(function(index) {
                $(this).removeClass('d-none');
            });

            //hide opsi-hardbox
            $(".opsi-hardbox").each(function(index) {
                $(this).addClass('d-none');
            });

            $('.primary-corrugated').addClass('d-none');

            //change text btn-hitung
            $('#btn-hitung').text('Hitung Jumlah Kertas Potong');

            $('#box-atas').html('');
        } else {
            //hide opsi-hardbox
            $(".opsi-hardbox").each(function(index) {
                $(this).addClass('d-none');
            });
            //hide opsi-hardbox
            $(".opsi-softbox").each(function(index) {
                $(this).addClass('d-none');
            });

            $(".opsi-corrugated").each(function(index) {
                $(this).removeClass('d-none');
            });
            $('#btn-hitung').text('Hitung Jumlah Kertas Potong');
            $('#box-atas').html('');
        }
    })

    $(".tipe-material").on('change',function(index) {
        if ($(this).is(':checked')) {
            tipe = $(this).val();
            if (tipe == 3 || tipe == 2) {
                $(".qtp").each(function(index) {
                    $(this).addClass('d-none');
                });
            } else {
                $(".qtp").each(function(index) {
                    $(this).removeClass('d-none');
                });
            }
        }
    });
    
</script>