@if ( count($layouts) > 0 )
    <div class="row mb-5 g-5">
        @php
            $index = 0;
        @endphp
        @foreach ($layouts as $layout)
            <div class="col-md-6">
                <div class="card shadow-sm rounded-md mb-5 btn-layout" role="button" id="card-{{$index}}" onclick="select_template('{{$index}}')" data-id="{{$layout->id_layout}}" data-kode="{{$layout->kode_layout}}" data-kategori="{{$layout->nama_kategori}}" data-name="{{$layout->nama_layout}}">
                    <div class="card-header" style="min-height: 0%">
                        <small class="card-title">{{ $layout->nama_layout }}</small>
                    </div>
                    <div class="card-body">
                        <div class="d-flex">
                            @if ($layout->path_gambar_produk)
                                <img src="{{ url('designs/'.$layout->path_gambar_produk) }}" width="150" height="200" alt="">
                            @else    
                                <img src="{{ url('images/img-box.png') }}" alt="">
                            @endif

                            @if ($layout->path_gambar_layout)
                                <img src="{{ url('designs/'.$layout->path_gambar_layout) }}" width="150" height="200" alt="">
                            @else    
                                <img src="{{ url('images/img-pola.png') }}" alt="">
                            @endif
                        </div>
                    </div>
                    <div class="card-footer p-3">
                        <span class="badge special-card text-my-primary rounded-0">{{ $layout->kode_layout }}</span> <span class="badge special-card text-my-primary rounded-0">#{{ $layout->nama_kategori }}</span>
                    </div>
                </div>
            </div>
            @php
                ++$index;
            @endphp
        @endforeach
    </div>
    <div class="row">
        {!! $layouts->links() !!}
    </div>
@else
    <div class="row">
        <small class="my-3">No Data Layout</small>
    </div>
@endif
<script>
    $('.pagination li a').on('click', function(e){
        e.preventDefault();

        var target = '#list-layout';
        var url = $(this).attr('href');

        $(target).html($('#loader').html());

        $.ajax({
            url: url,
            type: 'GET',
            data:{
                search: $('#layout-search-box').val()
            },
            success: function(data) {
                $(target).html(data);
            },
            error: function(xhr) {
                // Handle any errors
            }
        });
    });    

    // Add active class to the current button (highlight it)
    var header = document.getElementById("list-layout");
    var btns = header.getElementsByClassName("btn-layout");
    for (var i = 0; i < btns.length; i++) {
        btns[i].addEventListener("click", function() {
            var current = document.getElementsByClassName("actived");
            current[0].className = current[0].className.replace(" actived", "");
            this.className += " actived";
            $('#kode_layout').val($(this).data('kode'));
            $('#nama_layout').val($(this).data('name'));
            $('#name-box').html($(this).data('name'));
            $('#kategori_layout').val($(this).data('kategori'));
            $('#id_layout').val($(this).data('id'));
            call_layout($(this).data('id'));
        });
    }

    function select_template(index){
        $('.btn-layout').each(function(i, obj) {
            $(this).removeClass('actived');
        });

        $('#card-'+index).addClass('actived');

        var kode_layout = $('#card-'+index).data('kode');
        $('#kode_layout').val(kode_layout);

        var kategori_layout = $('#card-'+index).data('kategori');
        $('#kategori_layout').val(kategori_layout);
    }

    function call_layout(id){
        var target = '#add_form';

        $.ajax({
            url: "{{route('design.call_layout')}}",
            type: 'GET',
            data:{
                id: id
            },
            success: function(data) {
                $(target).html('');

                const string = data.tambahan_sisi_kertas;
                const array = string.split(",");
                
                var add = '<div class="col-md-3"></div><div class="col-md-9"><div class="row">';
                
                array.forEach(element => {
                    if (element == 'Lem') {
                        add += '<div class="col-md-4">'+
                            '<label for="lem" class="form-label">Lem</label>'+
                            '<input type="text" class="form-control" name="s_lem" id="s_lem">'+
                        '</div>';
                    }
                    if (element == 'Klip') {
                        add += '<div class="col-md-4">'+
                            '<label for="klip" class="form-label">Klip</label>'+
                            '<input type="text" class="form-control" name="s_klip" id="s_klip">'+
                        '</div>';
                    }
                    if (element == 'Sayap') {
                        add += '<div class="col-md-4">'+
                            '<label for="sayap" class="form-label">Sayap</label>'+
                            '<input type="text" class="form-control" name="s_sayap" id="s_sayap">'+
                        '</div>';
                    }
                    if (element == 'T2') {
                        add += '<div class="col-md-4">'+
                            '<label for="t2" class="form-label">T2</label>'+
                            '<input type="text" class="form-control" name="s_t2" id="s_t2">'+
                        '</div>';
                    }
                });

                add += '</></div>';
                $(target).html(add);
            },
            error: function(xhr) {
                // Handle any errors
            }
        });
    }
</script>    
