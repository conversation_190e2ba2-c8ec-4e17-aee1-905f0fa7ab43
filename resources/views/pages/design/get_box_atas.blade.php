<div class="border border-warning rounded">
    <div class="row m-5">
        <div class="col-md-12">
            <div class="row">
                <button type="button" class="btn btn-my-primary text-white" id="btn-hitung-atas">Hitung Box Atas</button>
            </div>
        </div>
    </div>
    <div class="row m-5">
        <div class="col-md-3">
            <label class="form-label">Ukuran Permukan Box Atas</label>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-6">
                    <label for="panjang" class="form-label">Panjang</label>
                    <input type="text" class="form-control text-center" id="panjang_cetak_atas" name="panjang_cetak_atas" onchange="onchangeSizeBoxAtas()">
                </div>
                <div class="col-md-6">
                    <label for="lebar" class="form-label">Lebar</label>
                    <input type="text" class="form-control text-center" id="lebar_cetak_atas" name="lebar_cetak_atas" onchange="onchangeSizeBoxAtas()">
                </div>
            </div>
        </div>
    </div>
    <div class="row m-5">
        <div class="col-md-3">
            <label class="form-label">Ukuran Kertas Potong/Board Atas</label>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-6">
                    <label for="panjang" class="form-label">Panjang</label>
                    <input type="text" class="form-control text-center" id="panjang_kemasan_atas" name="panjang_kemasan_atas" onchange="onchangeSizeBoxAtas()">
                </div>
                <div class="col-md-6">
                    <label for="lebar" class="form-label">Lebar</label>
                    <input type="text" class="form-control text-center" id="lebar_kemasan_atas" name="lebar_kemasan_atas" onchange="onchangeSizeBoxAtas()">
                </div>
            </div>
        </div>
    </div>
    <div class="row m-5">
        <div class="col-md-3">
            <label class="form-label">Hasil Kertas Akhir Box Atas</label>
        </div>
        <div class="col-md-9">
            <div class="row">
                <div class="col-md-6">
                    <label for="jumlah_kertas" class="form-label">Jumlah kertas yang didapat</label>
                    <input type="text" class="form-control text-center" name="jumlah_kertas_potong_atas" id="jumlah_kertas_potong_atas" readonly>
                </div>
                <div class="col-md-6">
                    <label for="wasting" class="form-label">Wasting (%)</label>
                    <input type="text" class="form-control text-center" name="wasting_atas" id="wasting_atas" readonly>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $('#btn-hitung-atas').on('click', function(){
        var s_panjang = $('#s_panjang').val();
        var s_lebar = $('#s_lebar').val();
        var s_tinggi = $('#s_tinggi').val();
        var id_kertas_plano = $('#id_kertas_plano').val();
        var id_layout = $('#id_layout').val();

        $.ajax({
            type:'get',
            url: '{{url("design/getHitungBoxAtas")}}',
            data:{
                p : s_panjang,
                l : s_lebar,
                t : s_tinggi,
                id_kertas_plano : id_kertas_plano,
                id_layout : id_layout
            },
            success: function(response) {
                console.log();
                $('#panjang_cetak_atas').val(response.p_cetak);
                $('#lebar_cetak_atas').val(response.l_cetak);
                $('#panjang_kemasan_atas').val(response.p_kemasan);
                $('#lebar_kemasan_atas').val(response.l_kemasan);
                $('#jumlah_kertas_potong_atas').val(response.jumlah_potong);
                $('#wasting_atas').val(response.percentage_waste);
            },
            error: function(xhr) {
                // Handle any errors
            }
        });
    })

    function onchangeSizeBoxAtas(){
        $.ajax({
            url: '{{url("design/getHitungBoxAtas")}}',
            data: {
                panjang_cetak : $('#panjang_cetak_atas').val(),
                lebar_cetak : $('#lebar_cetak_atas').val(),
                panjang_kemasan : $('#panjang_kemasan_atas').val(),
                lebar_kemasan : $('#lebar_kemasan_atas').val(),
                id_kertas_plano : $('#id_kertas_plano').val(),
                id_bahans : $('#id_bahans').val(),
                is_force:true
            },
            success: function(response) {
                $('#panjang_cetak_atas').val(response.p_cetak);
                $('#lebar_cetak_atas').val(response.l_cetak);
                $('#panjang_kemasan_atas').val(response.p_kemasan);
                $('#lebar_kemasan_atas').val(response.l_kemasan);
                $('#jumlah_kertas_potong_atas').val(response.jumlah_potong);
                $('#wasting_atas').val(response.percentage_waste);
            },
            error: function(xhr) {
                // Handle any errors
            }
        });
    }
</script>