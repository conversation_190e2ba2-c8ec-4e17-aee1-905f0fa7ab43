<script type="text/javascript">
    // SweetAlert Alert Example
    @if (session()->has('message'))
        function showAlert(title, message, type) {
            window.swal.fire({
                    title: "Response Message",
                    text: title,
                    icon: 'success',
                    type: type,
                    confirmButtonText: "OK",
                    html: message,
                    // cancelButtonText: "Cancel",
                    showCancelButton: false,
                }).then(console.log)
                .catch(console.error);
        }
        showAlert('{{ strtolower(session('message')['type']) }}', '{{ session('message')['text'] }}');
    @endif
</script>
<script>
    $(document).ready(function() {
        //datatable page production
        const dataTableLanguage = {
            loadingRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div class="spinner-grow text-warning" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>`,
            emptyTable: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
            zeroRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
        };

        dt_prod = $('#datatable_production').DataTable({
            // processing: true,
            // serverSide: true,
            language: dataTableLanguage,
            ajax: {
                url: "{{ route('production.table_production') }}",
                data: function(d) {
                    d.tgl_deadline_from_date = $('input[name=tgl_deadline_from_date]').val();
                    d.tgl_deadline_to_date = $('input[name=tgl_deadline_to_date]').val();
                }
            },
            deferRender: true,
            columnDefs: [ { type: 'date', targets: [6] } ],
            order: [[ 6, 'desc' ]],
            columns: [{
                    data: 'nama',
                    name: 'nama',
                    width: '11%'
                },
                {
                    data: 'sko',
                    name: 'sko',
                    orderable: false
                },
                {
                    data: 'tgl_faw',
                    name: 'tgl_faw'
                },
                {
                    data: 'tgl_deadline',
                    name: 'tgl_deadline'
                },
                {
                    data: 'progress_produksi',
                    name: 'progress_produksi'
                },
                {
                    data: 'catatan_khusus',
                    name: 'catatan_khusus'
                },
                {
                    data: 'last_update',
                    name: 'tp_spks.updated_at'
                },
                {
                    data: 'action',
                    name: 'action',
                    orderable: false,
                    searchable: false,
                    width: '5%'
                }
            ]
        });

        $(document).on('click', '#filter-production-tgl-deadline', function() {
            $('#kt-toolbar-filter-produksi').removeClass('show');
        });


        //sorting
        // $('.sort').on('click', function() {
        //     var column = $(this).data('sort');
        //     var direction = 'asc';

        //     if (table.order()[0][0] === column && table.order()[0][1] === 'asc') {
        //         direction = 'desc';
        //     }

        //     table.order([column, direction]).draw();
        // });

        //filter page cust
        const filterSearch = document.querySelector('[data-kt-docs-table-filter="search-production"]')
        filterSearch.addEventListener('keyup', function(e) {
            dt_prod.search(e.target.value).draw()
        })

        const filterProgress = document.querySelector('.filter-produksi-progress')
        filterProgress.addEventListener('change', function(e) {
            dt_prod.columns(4).search(e.target.value).draw()
        })

        const filterCatatan = document.querySelector('.filter-produksi-catatan')
        filterCatatan.addEventListener('change', function(e) {
            dt_prod.columns(5).search(e.target.value).draw()
        })

        $('#filter-production-tgl-deadline').click(function() {
            dt_prod.draw();
        });


        //datatable page production bermasalah
        dt_prod_2 = $('#datatable_produksi_bermasalah').DataTable({
            // processing: true,
            // serverSide: true,
            language: dataTableLanguage,
            ajax: {
                url: "{{ route('production.table_production_bermasalah') }}"
            },
            deferRender: true,
            columnDefs: [ { type: 'date', targets: [3] } ],
            order: [[ 3, 'desc' ]],
            columns: [
                {
                    data: 'sko',
                    name: 'sko',
                    orderable: false
                },
                {
                    data: 'nama',
                    name: 'nama',
                    width: '10%'
                },
                {
                    data: 'tgl_order',
                    name: 'tgl_order',
                    className: 'dt-body-center',
                    width: '10%'
                },
                {
                    data: 'tgl_masalah',
                    name: 'tgl_masalah',
                    width: '11%'
                },
                {
                    data: 'kategori_masalah',
                    name: 'kategori_masalah'
                },
                {
                    data: 'solusi',
                    name: 'solusi'
                },
                {
                    data: 'total_harga',
                    name: 'total_harga',
                    width: '10%'
                },
                {
                    data: 'modal_sales',
                    name: 'modal_sales',
                    width: '10%'
                },
                {
                    data: 'status_order',
                    name: 'status_order'
                },
                {
                    data: 'last_edited',
                    name: 'tb_orders.updated_at',
                    width: '12%'
                }
                
            ],

        });


        const filterSearch_2 = document.querySelector('[data-kt-docs-table-filter="search-produksi_bermasalah"]')
        filterSearch_2.addEventListener('keyup', function(e) {
            dt_prod_2.search(e.target.value).draw()
        })

        $('#filter_by_date').on('click', function() {
            var startDate = $('#filter-dummy-from').val();
            var endDate = $('#filter-dummy-to').val();

            // Convert string dates to Date objects
            var startDateValue = new Date(startDate);
            var endDateValue = new Date(endDate);

            // Clear existing DataTable search function and reapply it
            $.fn.dataTable.ext.search = [];

            $.fn.dataTable.ext.search.push(function(settings, searchData, index, rowData, counter) {
                var dateColumnValue = new Date(searchData[3]);

                if (!startDate && !endDate) {
                    return true; // No date range specified, show all rows
                } else if (!startDate && dateColumnValue <= endDateValue) {
                    return true; // Only end date specified, show rows with dates before or equal to end date
                } else if (!endDate && dateColumnValue >= startDateValue) {
                    return true; // Only start date specified, show rows with dates after or equal to start date
                } else if (dateColumnValue >= startDateValue && dateColumnValue <= endDateValue) {
                    return true; // Date is within the specified range
                }

                return false; // Date is outside the specified range, exclude row
            });

            dt_prod.draw()
        });

        $('#reset_date_filter').on('click', function() {
            // Clear date inputs
            $('#filter-dummy-from').val('');
            $('#filter-dummy-to').val('');

            // Remove custom search function
            $.fn.dataTable.ext.search.pop();

            // Redraw the DataTable
            dt_prod.draw();
        });

        $('#filter_by_date_trouble').on('click', function() {
            console.log("?????");
            var startDate = $('#filter-dummy-from-trouble').val();
            var endDate = $('#filter-dummy-to-trouble').val();

            // Convert string dates to Date objects
            var startDateValue = new Date(startDate);
            var endDateValue = new Date(endDate);

            // Clear existing DataTable search function and reapply it
            $.fn.dataTable.ext.search = [];

            $.fn.dataTable.ext.search.push(function(settings, searchData, index, rowData, counter) {
                var dateColumnValue = new Date(searchData[3]);

                if (!startDate && !endDate) {
                    return true; // No date range specified, show all rows
                } else if (!startDate && dateColumnValue <= endDateValue) {
                    return true; // Only end date specified, show rows with dates before or equal to end date
                } else if (!endDate && dateColumnValue >= startDateValue) {
                    return true; // Only start date specified, show rows with dates after or equal to start date
                } else if (dateColumnValue >= startDateValue && dateColumnValue <= endDateValue) {
                    return true; // Date is within the specified range
                }

                return false; // Date is outside the specified range, exclude row
            });

            dt_prod_2.draw()
        });

        $('#reset_date_filter_trouble').on('click', function() {
            // Clear date inputs
            $('#filter-dummy-from-trouble').val('');
            $('#filter-dummy-to-trouble').val('');

            // Remove custom search function
            $.fn.dataTable.ext.search.pop();

            // Redraw the DataTable
            dt_prod_2.draw();
        });

    });

    function deleteConfirmation(id) {
        // console.log('ID:', id);
        swal.fire({
            title: "Hapus ?",
            text: "Harap pastikan",
            icon: "warning",
            showCancelButton: !0,
            confirmButtonText: "Ya, Hapus!",
            cancelButtonText: "Tidak, batal!",
            reverseButtons: !0
        }).then(function(e) {
            // console.log('Swal:', e);
            if (e.value === true) {
                $.ajax({
                    url: "/production/delete_spk/" + id,
                    type: 'DELETE',
                    data: {
                        "id": id,
                    },
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(data) {
                        if (data.success) {
                            $('#datatable_production').DataTable().ajax.reload();
                            Swal.fire({
                                title: 'SUCCESS!',
                                text: "grading berhasil dihapus",
                                icon: 'success',
                            })
                        } else {
                            Swal.fire({
                                title: 'FAILED!',
                                text: "grading gagal dihapus",
                                icon: 'error',
                            })
                        }
                    },
                    error: function(xhr, status, error) {
                        Swal.fire({
                            title: 'FAILED!',
                            text: "gagal dihapus",
                            icon: 'error',
                        })
                    }
                });
            } else {
                e.dismiss;
            }
        }, function(dismiss) {
            return false;
        })
    };
</script>
