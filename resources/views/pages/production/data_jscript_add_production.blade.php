<link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">
<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script src="{{ url('js/simple.money.format.js') }}"></script>

<script>
    $(document).ready(function() {
        $("#sko").autocomplete({
            source: function(request, response) {
                var listData = [];
                $.ajax({
                    url: "{{ route('production.kode_order_get') }}",
                    dataType: "json",
                    method: "GET",
                    headers: {
                        'X-api-token': 'f9854c56b995ac7e09f207c07a656750ba80b63e'
                    },
                    data: {
                        sko: request.term
                    },
                    success: function(data) {
                        response($.map(data, function(item) {
                            if (item.notes == null) {
                                notes = '';
                            } else {
                                notes = item.notes;
                            }
                            return {
                                label: item.sko + ' - ' + item.nama,
                                value: item.order_key,
                                sko: item.sko,
                                sko_key: item.sko_key,
                                nama_customer: item.nama,
                                tgl_deadline: item.tgl_deadline,
                                spesifikasi: item.lp_panjang + ' X ' + item
                                    .lp_lebar + ', ' +
                                    item.jenis_kertas + ', ' +
                                    item.gramasi + ', ' +
                                    item.laminasi + ', ' +
                                    item.sisi_laminasi + ', ' +
                                    item.finishing + ', ' +
                                    item.jumlah_produk + ' pcs, ' +
                                    notes,
                                waktu_produksi: item.waktu_produksi,
                                tgl_faw: item.tgl_faw,
                                file_final: item.file_final,
                                path_lampiran: item.path_lampiran,
                                keterangan_tambahan: item
                                    .keterangan_tambahan
                            };
                        }));
                    },
                    error: function(response) {
                        console.log(response);
                    }
                });
            },
            select: function(event, ui) {
                $("#sko").val(ui.item.sko);
                $("#order_key").val(ui.item.value);
                $("#sko_key").val(ui.item.sko_key);
                $("#nama_customer").val(ui.item.nama_customer);
                $('#spesifikasi').val(ui.item.spesifikasi);
                $('#tgl_deadline').val(ui.item.tgl_deadline);
                $('#waktu_produksi').html(ui.item.waktu_produksi);
                $('#tgl_faw').html(ui.item.tgl_faw);
                $('#keterangan_tambahan').html(ui.item.keterangan_tambahan);
                $('<a href="' + ui.item.file_final + '">' + ui.item.file_final + '</a>').appendTo($(
                    '#file_final'));
                $('<img width="300" src="{!! url("storage/'+ui.item.sko_key+'/faw/lampiran/'+ui.item.path_lampiran+'") !!}">').appendTo($('#path_lampiran'))
                return false;
            },
            classes: {
                "ui-autocomplete": "highlight"
            }
        })
        $('#sko').on('click', function() {
            $("#sko").autocomplete("search", "%");
            $('#file_final').html("");
            $('#path_lampiran').html("");
        })





        $('#dateFinalOne').hide();
        $('#dateFinalTwo').hide();
        $('#dateFinalThree').hide();
        $('#dateFinalFour').hide();
        $('#dateFinalFive').hide();
        $('#dateFinalSix').hide();
        $('#dateFinalSeven').hide();
        $('#dateFinalEight').hide();
        $('#dateFinalNine').hide();
        $('#dateFinalTen').hide();

        if ($('#flag_status_1').is(':checked')) {
            $('#dateFinalOne').show();
        } else {
            $('#dateFinalOne').hide();
        }
        if ($('#flag_status_2').is(':checked')) {
            $('#dateFinalTwo').show();
        } else {
            $('#dateFinalTwo').hide();
        }
        if ($('#flag_status_3').is(':checked')) {
            $('#dateFinalThree').show();
        } else {
            $('#dateFinalThree').hide();
        }
        if ($('#flag_status_4').is(':checked')) {
            $('#dateFinalFour').show();
        } else {
            $('#dateFinalFour').hide();
        }
        if ($('#flag_status_5').is(':checked')) {
            $('#dateFinalFive').show();
        } else {
            $('#dateFinalFive').hide();
        }
        if ($('#flag_status_6').is(':checked')) {
            $('#dateFinalSix').show();
        } else {
            $('#dateFinalSix').hide();
        }
        if ($('#flag_status_7').is(':checked')) {
            $('#dateFinalSeven').show();
        } else {
            $('#dateFinalSeven').hide();
        }
        if ($('#flag_status_8').is(':checked')) {
            $('#dateFinalEight').show();
        } else {
            $('#dateFinalEight').hide();
        }
        if ($('#flag_status_9').is(':checked')) {
            $('#dateFinalNine').show();
        } else {
            $('#dateFinalNine').hide();
        }
        if ($('#flag_status_10').is(':checked')) {
            $('#dateFinalTen').show();
        } else {
            $('#dateFinalTen').hide();
        }



        $("#flag_status_1").click(function() {
            if ($(this).is(":checked")) {
                $("#dateFinalOne").show();
            } else {
                $("#dateFinalOne").hide();
            }
        });
        $("#flag_status_2").click(function() {
            if ($(this).is(":checked")) {
                $("#dateFinalTwo").show();
            } else {
                $("#dateFinalTwo").hide();
            }
        });
        $("#flag_status_3").click(function() {
            if ($(this).is(":checked")) {
                $("#dateFinalThree").show();
            } else {
                $("#dateFinalThree").hide();
            }
        });
        $("#flag_status_4").click(function() {
            if ($(this).is(":checked")) {
                $("#dateFinalFour").show();
            } else {
                $("#dateFinalFour").hide();
            }
        });
        $("#flag_status_5").click(function() {
            if ($(this).is(":checked")) {
                $("#dateFinalFive").show();
            } else {
                $("#dateFinalFive").hide();
            }
        });
        $("#flag_status_6").click(function() {
            if ($(this).is(":checked")) {
                $("#dateFinalSix").show();
            } else {
                $("#dateFinalSix").hide();
            }
        });
        $("#flag_status_7").click(function() {
            if ($(this).is(":checked")) {
                $("#dateFinalSeven").show();
            } else {
                $("#dateFinalSeven").hide();
            }
        });
        $("#flag_status_8").click(function() {
            if ($(this).is(":checked")) {
                $("#dateFinalEight").show();
            } else {
                $("#dateFinalEight").hide();
            }
        });
        $("#flag_status_9").click(function() {
            if ($(this).is(":checked")) {
                $("#dateFinalNine").show();
            } else {
                $("#dateFinalNine").hide();
            }
        });
        $("#flag_status_10").click(function() {
            if ($(this).is(":checked")) {
                $("#dateFinalTen").show();
            } else {
                $("#dateFinalTen").hide();
            }
        });

        $('#accordOne').hide();
        $('#accordTwo').hide();
        $('#accordThree').hide();
        $('#accordFour').hide();
        $('#accordFive').hide();
        $('#accordSix').hide();
        $('#accordSeven').hide();
        $('#accordEight').hide();
        $('#accordNine').hide();
        $('#accordTen').hide();
        $('#accordDummy').hide();

        $('#progress_produksi_1').on('click', function() {
            if ($('#progress_produksi_1').is(':checked')) {
                $('#accordOne').show();
            } else {
                $('#accordOne').hide();
                $("#harga_pracetak").val("");
            }
        });

        $('#progress_produksi_2').on('click', function() {
            if ($('#progress_produksi_2').is(':checked')) {
                $('#accordTwo').show();
            } else {
                $('#accordTwo').hide();
                $("#harga_cetak").val("");
            }
        });

        $('#progress_produksi_3').on('click', function() {
            if ($('#progress_produksi_3').is(':checked')) {
                $('#accordThree').show();
            } else {
                $('#accordThree').hide();
                $("#harga_laminasi").val("");
            }
        });

        $('#progress_produksi_4').on('click', function() {
            if ($('#progress_produksi_4').is(':checked')) {
                $('#accordFour').show();
            } else {
                $('#accordFour').hide();
                $("#harga_poly").val("");
            }
        });

        $('#progress_produksi_5').on('click', function() {
            if ($('#progress_produksi_5').is(':checked')) {
                $('#accordFive').show();
            } else {
                $('#accordFive').hide();
                $("#harga_emboss").val("");
            }
        });

        $('#progress_produksi_6').on('click', function() {
            if ($('#progress_produksi_6').is(':checked')) {
                $('#accordSix').show();
            } else {
                $('#accordSix').hide();
                $("#harga_spotuv").val("");
            }
        });

        $('#progress_produksi_7').on('click', function() {
            if ($('#progress_produksi_7').is(':checked')) {
                $('#accordSeven').show();
            } else {
                $('#accordSeven').hide();
                $("#harga_lapis").val("");

            }
        });

        $('#progress_produksi_8').on('click', function() {
            if ($('#progress_produksi_8').is(':checked')) {
                $('#accordEight').show();
            } else {
                $('#accordEight').hide();
                $("#harga_jendelamika").val("");
            }
        });

        $('#progress_produksi_9').on('click', function() {
            if ($('#progress_produksi_9').is(':checked')) {
                $('#accordNine').show();
            } else {
                $('#accordNine').hide()
                $("#harga_pond").val("");
            }
        });

        $('#progress_produksi_10').on('click', function() {
            if ($('#progress_produksi_10').is(':checked')) {
                $('#accordTen').show();
            } else {
                $('#accordTen').hide();
                $("#harga_finishing").val("");
            }
        });

        $('#progress_produksi_dummy').on('click', function() {
            if ($('#progress_produksi_dummy').is(':checked')) {
                $('#accordDummy').show();
            } else {
                $('#accordDummy').hide();
            }
        });



        //pp_waktu
        $('#jumlah_hasil_pp_2').on('keyup change', function() {
            var jumlah_awal_pp_2 = $('#jumlah_awal_pp_2').val();
            var jumlah_hasil_pp_2 = $('#jumlah_hasil_pp_2').val();

            count_reject_pp_2 = jumlah_awal_pp_2 - jumlah_hasil_pp_2;
            $('#reject_pp_2').val(count_reject_pp_2);
            $('#jumlah_awal_pp_3').val(jumlah_hasil_pp_2);
            $('#jumlah_awal_pp_4').val(jumlah_hasil_pp_2);
            $('#jumlah_awal_pp_5').val(jumlah_hasil_pp_2);
            $('#jumlah_awal_pp_6').val(jumlah_hasil_pp_2);
            $('#jumlah_awal_pp_7').val(jumlah_hasil_pp_2);
            $('#jumlah_awal_pp_8').val(jumlah_hasil_pp_2);
            $('#jumlah_awal_pp_9').val(jumlah_hasil_pp_2);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_2);
        });
        $('#reject_pp_2').on('keyup change', function() {
            var jumlah_awal_pp_2 = $('#jumlah_awal_pp_2').val();
            var reject_pp_2 = $('#reject_pp_2').val();
            var jumlah_hasil_pp_2 = $('#jumlah_hasil_pp_2').val();

            count_hasil_pp_2 = jumlah_awal_pp_2 - reject_pp_2;
            $('#jumlah_hasil_pp_2').val(count_hasil_pp_2);
            $('#jumlah_awal_pp_3').val(jumlah_hasil_pp_2);
            $('#jumlah_awal_pp_4').val(jumlah_hasil_pp_2);
            $('#jumlah_awal_pp_5').val(jumlah_hasil_pp_2);
            $('#jumlah_awal_pp_6').val(jumlah_hasil_pp_2);
            $('#jumlah_awal_pp_7').val(jumlah_hasil_pp_2);
            $('#jumlah_awal_pp_8').val(jumlah_hasil_pp_2);
            $('#jumlah_awal_pp_9').val(jumlah_hasil_pp_2);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_2);
        });
        //pp_pengiriman
        $('#jumlah_hasil_pp_3').on('keyup change', function() {
            var jumlah_awal_pp_3 = $('#jumlah_awal_pp_3').val();
            var jumlah_hasil_pp_3 = $('#jumlah_hasil_pp_3').val();

            count_reject_pp_3 = jumlah_awal_pp_3 - jumlah_hasil_pp_3;
            $('#reject_pp_3').val(count_reject_pp_3);
            $('#jumlah_awal_pp_4').val(jumlah_hasil_pp_3);
            $('#jumlah_awal_pp_5').val(jumlah_hasil_pp_3);
            $('#jumlah_awal_pp_6').val(jumlah_hasil_pp_3);
            $('#jumlah_awal_pp_7').val(jumlah_hasil_pp_3);
            $('#jumlah_awal_pp_8').val(jumlah_hasil_pp_3);
            $('#jumlah_awal_pp_9').val(jumlah_hasil_pp_3);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_3);
        });
        $('#reject_pp_3').on('keyup change', function() {
            var jumlah_awal_pp_3 = $('#jumlah_awal_pp_3').val();
            var reject_pp_3 = $('#reject_pp_3').val();
            var jumlah_hasil_pp_3 = $('#jumlah_hasil_pp_3').val();

            count_hasil_pp_3 = jumlah_awal_pp_3 - reject_pp_3;
            $('#jumlah_hasil_pp_3').val(count_hasil_pp_3);
            $('#jumlah_awal_pp_4').val(jumlah_hasil_pp_3);
            $('#jumlah_awal_pp_5').val(jumlah_hasil_pp_3);
            $('#jumlah_awal_pp_6').val(jumlah_hasil_pp_3);
            $('#jumlah_awal_pp_7').val(jumlah_hasil_pp_3);
            $('#jumlah_awal_pp_8').val(jumlah_hasil_pp_3);
            $('#jumlah_awal_pp_9').val(jumlah_hasil_pp_3);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_3);
        });
        //pp_kualitas
        $('#jumlah_hasil_pp_4').on('keyup change', function() {
            var jumlah_awal_pp_4 = $('#jumlah_awal_pp_4').val();
            var jumlah_hasil_pp_4 = $('#jumlah_hasil_pp_4').val();

            count_reject_pp_4 = jumlah_awal_pp_4 - jumlah_hasil_pp_4;
            $('#reject_pp_4').val(count_reject_pp_4);
            $('#jumlah_awal_pp_5').val(jumlah_hasil_pp_4);
            $('#jumlah_awal_pp_6').val(jumlah_hasil_pp_4);
            $('#jumlah_awal_pp_7').val(jumlah_hasil_pp_4);
            $('#jumlah_awal_pp_8').val(jumlah_hasil_pp_4);
            $('#jumlah_awal_pp_9').val(jumlah_hasil_pp_4);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_4);
        });
        $('#reject_pp_4').on('keyup change', function() {
            var jumlah_awal_pp_4 = $('#jumlah_awal_pp_4').val();
            var reject_pp_4 = $('#reject_pp_4').val();
            var jumlah_hasil_pp_4 = $('#jumlah_hasil_pp_4').val();

            count_hasil_pp_4 = jumlah_awal_pp_4 - reject_pp_4;
            $('#jumlah_hasil_pp_4').val(count_hasil_pp_4);
            $('#jumlah_awal_pp_5').val(jumlah_hasil_pp_4);
            $('#jumlah_awal_pp_6').val(jumlah_hasil_pp_4);
            $('#jumlah_awal_pp_7').val(jumlah_hasil_pp_4);
            $('#jumlah_awal_pp_8').val(jumlah_hasil_pp_4);
            $('#jumlah_awal_pp_9').val(jumlah_hasil_pp_4);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_4);
        });
        //pp_pembayaran
        $('#jumlah_hasil_pp_5').on('keyup change', function() {
            var jumlah_awal_pp_5 = $('#jumlah_awal_pp_5').val();
            var jumlah_hasil_pp_5 = $('#jumlah_hasil_pp_5').val();

            count_reject_pp_5 = jumlah_awal_pp_5 - jumlah_hasil_pp_5;
            $('#reject_pp_5').val(count_reject_pp_5);
            $('#jumlah_awal_pp_6').val(jumlah_hasil_pp_5);
            $('#jumlah_awal_pp_7').val(jumlah_hasil_pp_5);
            $('#jumlah_awal_pp_8').val(jumlah_hasil_pp_5);
            $('#jumlah_awal_pp_9').val(jumlah_hasil_pp_5);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_5);
        });
        $('#reject_pp_5').on('keyup change', function() {
            var jumlah_awal_pp_5 = $('#jumlah_awal_pp_5').val();
            var reject_pp_5 = $('#reject_pp_5').val();
            var jumlah_hasil_pp_5 = $('#jumlah_hasil_pp_5').val();

            count_hasil_pp_5 = jumlah_awal_pp_5 - reject_pp_5;
            $('#jumlah_hasil_pp_5').val(count_hasil_pp_5);
            $('#jumlah_awal_pp_6').val(jumlah_hasil_pp_5);
            $('#jumlah_awal_pp_7').val(jumlah_hasil_pp_5);
            $('#jumlah_awal_pp_8').val(jumlah_hasil_pp_5);
            $('#jumlah_awal_pp_9').val(jumlah_hasil_pp_5);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_5);
        });
        //pp_6
        $('#jumlah_hasil_pp_6').on('keyup change', function() {
            var jumlah_awal_pp_6 = $('#jumlah_awal_pp_6').val();
            var jumlah_hasil_pp_6 = $('#jumlah_hasil_pp_6').val();

            count_reject_pp_6 = jumlah_awal_pp_6 - jumlah_hasil_pp_6;
            $('#reject_pp_6').val(count_reject_pp_6);
            $('#jumlah_awal_pp_7').val(jumlah_hasil_pp_6);
            $('#jumlah_awal_pp_8').val(jumlah_hasil_pp_6);
            $('#jumlah_awal_pp_9').val(jumlah_hasil_pp_6);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_6);
        });
        $('#reject_pp_6').on('keyup change', function() {
            var jumlah_awal_pp_6 = $('#jumlah_awal_pp_6').val();
            var reject_pp_6 = $('#reject_pp_6').val();
            var jumlah_hasil_pp_6 = $('#jumlah_hasil_pp_6').val();

            count_hasil_pp_6 = jumlah_awal_pp_6 - reject_pp_6;
            $('#jumlah_hasil_pp_6').val(count_hasil_pp_6);
            $('#jumlah_awal_pp_7').val(jumlah_hasil_pp_6);
            $('#jumlah_awal_pp_8').val(jumlah_hasil_pp_6);
            $('#jumlah_awal_pp_9').val(jumlah_hasil_pp_6);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_6);
        });
        //pp_7
        $('#jumlah_hasil_pp_7').on('keyup change', function() {
            var jumlah_awal_pp_7 = $('#jumlah_awal_pp_7').val();
            var jumlah_hasil_pp_7 = $('#jumlah_hasil_pp_7').val();

            count_reject_pp_7 = jumlah_awal_pp_7 - jumlah_hasil_pp_7;
            $('#reject_pp_7').val(count_reject_pp_7);
            $('#jumlah_awal_pp_8').val(jumlah_hasil_pp_7);
            $('#jumlah_awal_pp_9').val(jumlah_hasil_pp_7);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_7);
        });
        $('#reject_pp_7').on('keyup change', function() {
            var jumlah_awal_pp_7 = $('#jumlah_awal_pp_7').val();
            var reject_pp_7 = $('#reject_pp_7').val();
            var jumlah_hasil_pp_7 = $('#jumlah_hasil_pp_7').val();

            count_hasil_pp_7 = jumlah_awal_pp_7 - reject_pp_7;
            $('#jumlah_hasil_pp_7').val(count_hasil_pp_7);
            $('#jumlah_awal_pp_8').val(jumlah_hasil_pp_7);
            $('#jumlah_awal_pp_9').val(jumlah_hasil_pp_7);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_7);
        });
        //pp_8
        $('#jumlah_hasil_pp_8').on('keyup change', function() {
            var jumlah_awal_pp_8 = $('#jumlah_awal_pp_8').val();
            var jumlah_hasil_pp_8 = $('#jumlah_hasil_pp_8').val();

            count_reject_pp_8 = jumlah_awal_pp_8 - jumlah_hasil_pp_8;
            $('#reject_pp_8').val(count_reject_pp_8);
            $('#jumlah_awal_pp_9').val(jumlah_hasil_pp_8);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_8);
        });
        $('#reject_pp_8').on('keyup change', function() {
            var jumlah_awal_pp_8 = $('#jumlah_awal_pp_8').val();
            var reject_pp_8 = $('#reject_pp_8').val();
            var jumlah_hasil_pp_8 = $('#jumlah_hasil_pp_8').val();

            count_hasil_pp_8 = jumlah_awal_pp_8 - reject_pp_8;
            $('#jumlah_hasil_pp_8').val(count_hasil_pp_8);
            $('#jumlah_awal_pp_9').val(jumlah_hasil_pp_8);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_8);
        });
        //pp_9
        $('#jumlah_hasil_pp_9').on('keyup change', function() {
            var jumlah_awal_pp_9 = $('#jumlah_awal_pp_9').val();
            var jumlah_hasil_pp_9 = $('#jumlah_hasil_pp_9').val();

            count_reject_pp_9 = jumlah_awal_pp_9 - jumlah_hasil_pp_9;
            $('#reject_pp_9').val(count_reject_pp_9);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_9);
        });
        $('#reject_pp_9').on('keyup change', function() {
            var jumlah_awal_pp_9 = $('#jumlah_awal_pp_9').val();
            var reject_pp_9 = $('#reject_pp_9').val();
            var jumlah_hasil_pp_9 = $('#jumlah_hasil_pp_9').val();

            count_hasil_pp_9 = jumlah_awal_pp_9 - reject_pp_9;
            $('#jumlah_hasil_pp_9').val(count_hasil_pp_9);
            $('#jumlah_awal_pp_10').val(jumlah_hasil_pp_9);
        });
        //pp_10
        $('#jumlah_hasil_pp_10').on('keyup change', function() {
            var jumlah_awal_pp_10 = $('#jumlah_awal_pp_10').val();
            var jumlah_hasil_pp_10 = $('#jumlah_hasil_pp_10').val();

            count_reject_pp_10 = jumlah_awal_pp_10 - jumlah_hasil_pp_10;
            $('#reject_pp_10').val(count_reject_pp_10);
            $('#jumlah_fix').val(jumlah_hasil_pp_10);
        });
        $('#reject_pp_10').on('keyup change', function() {
            var jumlah_awal_pp_10 = $('#jumlah_awal_pp_10').val();
            var reject_pp_10 = $('#reject_pp_10').val();
            var jumlah_hasil_pp_10 = $('#jumlah_hasil_pp_10').val();
            count_hasil_pp_10 = jumlah_awal_pp_10 - reject_pp_10;
            $('#jumlah_hasil_pp_10').val(count_hasil_pp_10);
            $('#jumlah_fix').val(jumlah_hasil_pp_10);
        });

        $('#vendor_1_lainnya').hide();
        $('#vendor_1').on('change', function() {
            if ($(this).val() == 'Lainnya') {
                $('#vendor_1_lainnya').show();
            } else {
                $('#vendor_1_lainnya').hide();
            }
        });

        $('#vendor_2_lainnya').hide();
        $('#vendor_2').on('change', function() {
            if ($(this).val() == 'Lainnya') {
                $('#vendor_2_lainnya').show();
            } else {
                $('#vendor_2_lainnya').hide();
            }
        });

        $('#vendor_3_lainnya').hide();
        $('#vendor_3').on('change', function() {
            if ($(this).val() == 'Lainnya') {
                $('#vendor_3_lainnya').show();
            } else {
                $('#vendor_3_lainnya').hide();
            }
        });

        $('#vendor_4_lainnya').hide();
        $('#vendor_4').on('change', function() {
            if ($(this).val() == 'Lainnya') {
                $('#vendor_4_lainnya').show();
            } else {
                $('#vendor_4_lainnya').hide();
            }
        });

        $('#vendor_5_lainnya').hide();
        $('#vendor_5').on('change', function() {
            if ($(this).val() == 'Lainnya') {
                $('#vendor_5_lainnya').show();
            } else {
                $('#vendor_5_lainnya').hide();
            }
        });

        $('#vendor_6_lainnya').hide();
        $('#vendor_6').on('change', function() {
            if ($(this).val() == 'Lainnya') {
                $('#vendor_6_lainnya').show();
            } else {
                $('#vendor_6_lainnya').hide();
            }
        });

        $('#vendor_7_lainnya').hide();
        $('#vendor_7').on('change', function() {
            if ($(this).val() == 'Lainnya') {
                $('#vendor_7_lainnya').show();
            } else {
                $('#vendor_7_lainnya').hide();
            }
        });

        $('#vendor_8_lainnya').hide();
        $('#vendor_8').on('change', function() {
            if ($(this).val() == 'Lainnya') {
                $('#vendor_8_lainnya').show();
            } else {
                $('#vendor_8_lainnya').hide();
            }
        });

        $('#vendor_9_lainnya').hide();
        $('#vendor_9').on('change', function() {
            if ($(this).val() == 'Lainnya') {
                $('#vendor_9_lainnya').show();
            } else {
                $('#vendor_9_lainnya').hide();
            }
        });

        $('#vendor_10_lainnya').hide();
        $('#vendor_10').on('change', function() {
            if ($(this).val() == 'Lainnya') {
                $('#vendor_10_lainnya').show();
            } else {
                $('#vendor_10_lainnya').hide();
            }
        });
    });

    $('#harga1,#harga2,#harga3,#harga4,#harga5,#harga_pracetak,#harga_cetak,#harga_laminasi,#harga_poly,#harga_emboss,#harga_spotuv,#harga_lapis,#harga_jendelamika,#harga_pond,#harga_finishing,#harga_dummy,#total_keseluruhan_harga')
        .simpleMoneyFormat();
    //autocalctotal
    $('#harga1,#harga2,#harga3,#harga4,#harga5').on("keyup change", function() {
        $('harga_pracetak').simpleMoneyFormat();
        harga1 = parseInt($("#harga1").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;
        harga2 = parseInt($("#harga2").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;
        harga3 = parseInt($("#harga3").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;
        harga4 = parseInt($("#harga4").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;
        harga5 = parseInt($("#harga5").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;

        total = harga1 + harga2 + harga3 + harga4 + harga5;

        $('#harga_pracetak').val(total).simpleMoneyFormat();
    })

    $('#harga_pracetak,#harga_cetak,#harga_laminasi,#harga_poly,#harga_emboss,#harga_spotuv,#harga_lapis,#harga_jendelamika,#harga_pond,#harga_dummy,#harga_finishing')
        .on("keyup change", function() {
            $('#total_keseluruhan_harga').simpleMoneyFormat();
            harga_pracetak = parseInt($("#harga_pracetak").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;
            harga_cetak = parseInt($("#harga_cetak").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;
            harga_laminasi = parseInt($("#harga_laminasi").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;
            harga_poly = parseInt($("#harga_poly").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;
            harga_emboss = parseInt($("#harga_emboss").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;
            harga_spotuv = parseInt($("#harga_spotuv").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;
            harga_lapis = parseInt($("#harga_lapis").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;
            harga_jendelamika = parseInt($("#harga_jendelamika").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;
            harga_pond = parseInt($("#harga_pond").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;
            harga_finishing = parseInt($("#harga_finishing").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;
            harga_dummy = parseInt($("#harga_dummy").val().replace(/[A-Za-z$. ,-]/g, "")) || 0;

            total_all = harga_pracetak + harga_cetak + harga_laminasi + harga_poly + harga_emboss + harga_spotuv +
                harga_lapis + harga_jendelamika + harga_pond + harga_finishing + harga_dummy;

            $('#total_keseluruhan_harga').val(total_all).simpleMoneyFormat();
        })

    $(document).ready(function() {
        if ($('#jumlah_fix').val() > 0) {
            $('#catatan_khusus').prop('disabled', false);
        } else {
            $('#catatan_khusus').prop('disabled', true);
        }

        $('#jumlah_hasil_pp_10').on('keyup change', function() {
            if ($('#jumlah_fix').val() > 0) {
                $('#catatan_khusus').prop('disabled', false);
            } else {
                $('#catatan_khusus').prop('disabled', true);
            }
        });
    });




    $(document).ready(function() {


        if ($('#bahan_baku1').is(':checked')) {
            $('#harga1').show();

        } else {
            $('#harga1').hide();
            $("#harga1").val("");

        }
        if ($('#bahan_baku2').is(':checked')) {
            $('#harga2').show();
        } else {
            $('#harga2').hide();
            $("#harga2").val("");
        }
        if ($('#bahan_baku3').is(':checked')) {
            $('#harga3').show();
        } else {
            $('#harga3').hide();
            $("#harga3").val("");
        }
        if ($('#bahan_baku5').is(':checked')) {
            $('#harga4').show();
        } else {
            $('#harga4').hide();
            $("#harga4").val("");
        }
        if ($('#bahan_baku6').is(':checked')) {
            $('#harga5').show();
        } else {
            $('#harga5').hide();
            $("#harga5").val("");
        }


        $("#bahan_baku1").click(function() {
            if ($(this).is(':checked')) {
                $('#harga1').show();
            } else {
                $('#harga1').hide();
                $("#harga1").val("");
            }
        });

        $("#bahan_baku2").click(function() {
            if ($(this).is(':checked')) {
                $('#harga2').show();
            } else {
                $('#harga2').hide();
                $("#harga2").val("");
            }
        });
        $("#bahan_baku3").click(function() {
            if ($(this).is(':checked')) {
                $('#harga3').show();
            } else {
                $('#harga3').hide();
                $("#harga3").val("");
            }
        });
        $("#bahan_baku5").click(function() {
            if ($(this).is(':checked')) {
                $('#harga4').show();
            } else {
                $('#harga4').hide();
                $("#harga4").val("");
            }
        });
        $("#bahan_baku6").click(function() {
            if ($(this).is(':checked')) {
                $('#harga5').show();
            } else {
                $('#harga5').hide();
                $("#harga5").val("");
            }
        });





        const bahan_baku1 = document.getElementById("bahan_baku1");
        const bahan_baku2 = document.getElementById("bahan_baku2");
        const bahan_baku3 = document.getElementById("bahan_baku3");
        const bahan_baku5 = document.getElementById("bahan_baku5");
        const bahan_baku6 = document.getElementById("bahan_baku6");

        const harga1 = document.getElementById("harga1");
        const harga2 = document.getElementById("harga2");
        const harga3 = document.getElementById("harga3");
        const harga4 = document.getElementById("harga4");
        const harga5 = document.getElementById("harga5");



        bahan_baku1.addEventListener("change", function() {
            if (bahan_baku1.checked) {
                harga1.style.display = "block";
            } else {
                harga1.style.display = "none";
            }
        });
        bahan_baku2.addEventListener("change", function() {
            if (bahan_baku2.checked) {
                harga2.style.display = "block";
            } else {
                harga2.style.display = "none";
            }
        });
        bahan_baku3.addEventListener("change", function() {
            if (bahan_baku3.checked) {
                harga3.style.display = "block";
            } else {
                harga3.style.display = "none";
            }
        });
        bahan_baku5.addEventListener("change", function() {
            if (bahan_baku5.checked) {
                harga4.style.display = "block";
            } else {
                harga4.style.display = "none";
            }
        });
        bahan_baku6.addEventListener("change", function() {
            if (bahan_baku6.checked) {
                harga5.style.display = "block";
            } else {
                harga5.style.display = "none";
            }
        });
    });
</script>
