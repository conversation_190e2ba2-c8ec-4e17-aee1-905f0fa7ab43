<div class="row g-6 g-xl-9 mt-10 mt-md-15">
    <!--begin::Col-->
    <div class="col">
        <!--begin::Summary-->
        <a href="{{ route('production.ongoing') }}" class="">
            <div class="card bg-success hover-elevate-up">
                <span class="menu-icon m-6 mb-0" >
                    <i class="fa-solid fa-people-carry-box fs-4x" style="color: #fff"></i>
                </span>

                <div class="card-body d-flex flex-column ">
                    <!--begin::Amount-->
                    <span class="fs-2hx fw-bolder text-white me-2 lh-1 ls-n2">{{ $count_ongoing }}</span>
                    <!--end::Amount-->
                    <!--begin::Subtitle-->
                    <span class="text-white pt-1 fw-semibold fs-6"><PERSON>du<PERSON><PERSON></span>
                    <!--end::Subtitle-->
                </div>
            </div>
        </a>

        <!--end::Summary-->
    </div>
    <!--end::Col-->

    <!--begin::Col-->
    <div class="col">
        <!--begin::Summary-->
        <a href="{{ route('production.done_page') }}">
            <div class="card bg-primary hover-elevate-up">
                <span class="menu-icon m-6 mb-0">
                    <i class="fa-solid fa-circle-check fs-4x" style="color: #fff"></i>
                </span>

                <div class="card-body d-flex flex-column">
                    <!--begin::Amount-->
                    <span class="fs-2hx fw-bolder text-white me-2 lh-1 ls-n2">{{ $count_done }}</span>
                    <!--end::Amount-->
                    <!--begin::Subtitle-->
                    <span class="text-white pt-1 fw-semibold fs-6">Produksi Selesai</span>
                    <!--end::Subtitle-->
                </div>
            </div>
        </a>
        <!--end::Summary-->
    </div>
    <!--end::Col-->

    <!--begin::Col-->
    <div class="col">
        <!--begin::Summary-->
        <a href="{{ route('production.dummy') }}">
            <div class="card bg-warning hover-elevate-up">
                <span class="menu-icon m-6 mb-0">
                    <i class="fa-solid fa-cubes-stacked fs-4x" style="color: #fff"></i>
                </span>

                <div class="card-body d-flex flex-column">
                    <!--begin::Amount-->
                    <span class="fs-2hx fw-bolder text-white me-2 lh-1 ls-n2">{{ $count_dummy }}</span>
                    <!--end::Amount-->
                    <!--begin::Subtitle-->
                    <span class="text-white pt-1 fw-semibold fs-6">Dummy Berjalan</span>
                    <!--end::Subtitle-->
                </div>
            </div>
        </a>
        <!--end::Summary-->
    </div>
    <!--end::Col-->

    <!--begin::Col-->
    <div class="col">
        <!--begin::Summary-->
        <a href="{{ route('production.dummy_done') }}">
            <div class="card hover-elevate-up" style="background-color: #9681EB; ">
                <span class="menu-icon m-6 mb-0">
                    <i class="fa-solid fa-circle-check fs-4x" style="color: #fff"></i>
                </span>

                <div class="card-body d-flex flex-column">
                    <!--begin::Amount-->
                    <span class="fs-2hx fw-bolder text-white me-2 lh-1 ls-n2">{{ $count_dummy_done }}</span>
                    <!--end::Amount-->
                    <!--begin::Subtitle-->
                    <span class="text-white pt-1 fw-semibold fs-6">Dummy Selesai</span>
                    <!--end::Subtitle-->
                </div>
            </div>
        </a>
        <!--end::Summary-->
    </div>
    <!--end::Col-->

    @if(Auth::user()->roles == 'PRODUKSI SPV' || Auth::user()->roles == 'SUPERADMIN')
    <!--begin::Col-->
    <div class="col">
        <!--begin::Summary-->
        <a href="{{ route('production.not_acc_faw') }}">
            <div class="card bg-danger hover-elevate-up">
                <span class="menu-icon m-6 mb-0">
                    <i class="fa-solid fa-hourglass-half fs-4x" style="color: #fff"></i>
                </span>

                <div class="card-body d-flex flex-column">
                    <!--begin::Amount-->
                    <span class="fs-2hx fw-bolder text-white me-2 lh-1 ls-n2">{{ $count_not_acc_faw }}</span>
                    <!--end::Amount-->
                    <!--begin::Subtitle-->
                    <span class="text-white pt-1 fw-semibold fs-6">Belum Acc FAW</span>
                    <!--end::Subtitle-->
                </div>
            </div>
        </a>
        <!--end::Summary-->
    </div>
    <!--end::Col-->

    <!--begin::Col-->
    <div class="col">
        <!--begin::Summary-->
        <a href="{{ route('production.acc_faw_not_spk') }}">
            <div class="card bg-dark hover-elevate-up">
                <span class="menu-icon m-6 mb-0">
                    <i class="fa-solid fa-thumbtack fs-4x" style="color: #fff"></i>
                </span>

                <div class="card-body d-flex flex-column">
                    <!--begin::Amount-->
                    <span class="fs-2hx fw-bolder text-white me-2 lh-1 ls-n2">{{ $count_acc_faw_not_spk }}</span>
                    <!--end::Amount-->
                    <!--begin::Subtitle-->
                    <span class="text-white pt-1 fw-semibold fs-6">Belum SPK</span>
                    <!--end::Subtitle-->
                </div>
            </div>
        </a>
        <!--end::Summary-->
    </div>
    <!--end::Col-->
    @endif
</div>

<div class="card card-flush mt-10">
    <div class="card-body pt-0">
        <div class="py-5">
            <div class="d-flex flex-stack flex-wrap mb-5">
                <!--begin::Search-->
                <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                    <span class="svg-icon svg-icon-1 position-absolute ms-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                            <path
                                d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                fill="black"></path>
                        </svg>
                    </span>
                    <input type="text" data-kt-docs-table-filter="search-production"
                        class="form-control form-control-solid w-450px w-md-350px ps-15" placeholder="Search ...">
                </div>
                <!--end::Search-->
                <!--begin::Toolbar-->
                <div class="d-flex flex-row-fluid justify-content-end gap-3">
                    <div class="w-100 mw-200px">
                        <select class="form-select form-select-solid filter-produksi-progress">
                            <option hidden>Progress Produksi</option>
                            <option value="">All</option>
                            <option value="Belum Berjalan">Belum Berjalan</option>
                            <option value="Pra Cetak">Pra Cetak</option>
                            <option value="Cetak">Cetak</option>
                            <option value="Laminasi">Laminasi</option>
                            <option value="Poly">Poly</option>
                            <option value="Emboss">Emboss</option>
                            <option value="Spot UV">Spot UV</option>
                            <option value="Lapis">Lapis</option>
                            <option value="Jendela Mika">Jendela Mika</option>
                            <option value="Pond">Pond</option>
                            <option value="Finishing">Finishing</option>
                            <option value="Produksi Selesai">Produksi Selesai</option>
                            <option disabled>-------------------</option>
                            <option value="Dummy">Dummy</option>
                            <option value="Vendor Eks">Vendor Eksternal</option>

                        </select>
                    </div>
                    <div class="w-100 mw-200px">
                        <select class="form-select form-select-solid filter-produksi-catatan">
                            <option hidden>Status Produksi</option>
                            <option value="">All</option>
                            <option value="Belum SPK">Belum SPK</option>
                            <option value="Berjalan">Berjalan</option>
                            <option value="Tuntas">Tuntas</option>
                            <option value="Reject">Reject</option>
                        </select>
                    </div>
                    <button type="button" class="btn btn-light-primary" data-kt-menu-trigger="click"
                        data-kt-menu-placement="bottom-end">
                        <span class="svg-icon svg-icon-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none">
                                <path
                                    d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                                    fill="black"></path>
                            </svg>
                        </span>
                        Filter
                    </button>
                    <div class="menu menu-sub menu-sub-dropdown w-400px w-md-625px" data-kt-menu="true"
                        id="kt-toolbar-filter-produksi">
                        <div class="px-7 py-5">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="mb-10">
                                        <label class="form-label fs-5 fw-bold mb-3">Tanggal Deadline</label>
                                        <div class="row align-items-start">
                                            <div class="col-md-6">
                                                <input type="date" id="filter-dummy-from" name="tgl_deadline_from_date" class="form-control form-control-solid fw-bold">
                                                <small>From</small>
                                            </div>
                                            <div class="col-md-6">
                                                <input type="date" id="filter-dummy-to" name="tgl_deadline_to_date" class="form-control form-control-solid fw-bold">
                                                <small>To</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between mt-2">
                                        <button type="button" name="filter-production-tgl-deadline" id="reset_date_filter" data-kt-menu-dismiss="true" class="btn btn-danger btn-sm">Reset</button>
                                        <button type="button" name="filter-production-tgl-deadline" id="filter_by_date" data-kt-menu-dismiss="true" class="btn btn-primary btn-sm">Cari</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--begin::Add order-->
                    <!--begin::Add order-->
                    <!-- <a class="btn btn-success"href="{{ route('production.form_add_production') }}" title="Add produksi">
                        <span class="svg-icon svg-icon-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none">
                                <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1"
                                    transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                                <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="black"></rect>
                            </svg>
                        </span>
                    Tambah Produksi</a> -->
                    <!--end::Add customer-->
                </div>
                <!--end::Toolbar-->
            </div>
            <table id="datatable_production"
                class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer compact nowrap">
                <thead>
                    <tr class="fw-bold">
                        <th>Nama Customer <a href="#" class="sort" data-sort="nama"><i class="fas fa-sort"></i></a></th>
                        <th>SKO</th>
                        <th>Tgl FAW <a href="#" class="sort" data-sort="tgl_faw"><i class="fas fa-sort"></i></a></th>
                        <th>Deadline <a href="#" class="sort" data-sort="tgl_deadline"><i class="fas fa-sort"></i></a></th>
                        <th>Progress Produksi <a href="#" class="sort" data-sort="progress_produksi"><i class="fas fa-sort"></i></a></th>
                        <th>Status Produksi <a href="#" class="sort" data-sort="catatan_khusus"><i class="fas fa-sort"></i></a></th>
                        <th>Last Update <a href="#" class="sort" data-sort="created_at"><i class="fas fa-sort"></i></a></th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody> </tbody>
            </table>
        </div>
    </div>
</div>
