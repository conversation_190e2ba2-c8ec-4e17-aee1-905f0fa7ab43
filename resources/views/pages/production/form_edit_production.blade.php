<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ route('production') }}" class="pe-3"><i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i></a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3"><h3 class="display-6 text-white">Edit Produksi</h3></a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        @include('pages.production.data_jscript_edit_production')
    </x-slot>

    <div class="container-xxl mt-20" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-header">
                <h3 class="card-title text-dark"></h3>
            </div>
            <div class="card-body pt-0">
                <form id="form-direct-order" action="">
                    @csrf
                    @method('POST')
                    <input type="hidden" name="id_customer" value="">

                    <ul class="nav nav-tabs nav-line-tabs fs-6 mb-5">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#kt_tab_pane_1">Data FAW</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_pane_2">Form SPK</a>
                        </li>
                    </ul>

                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="kt_tab_pane_1" role="tabpanel">
                            <div class="row mt-10" hidden>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="sko" class="required form-label">Sub Kode Order</label>
                                        <input type="text" name="sko" id="sko" readonly
                                            value="{{ old('sko') ?? $data->sko }}"
                                            class="form-control form-control-solid" />
                                        <input type="hidden" name="order_key" value="{{ $data->order_key }}"
                                            id="order_key" class="form-control">
                                        <input type="hidden" name="sko_key" value="{{ $data->sko_key }}" id="sko_key"
                                            class="form-control">
                                    </div>
                                    <div class="mb-10">
                                        <label for="nama_customer" class="form-label">Nama Customer</label>
                                        <input type="text" name="nama_customer"
                                            value="{{ old('nama') ?? $data->nama }}" readonly id="nama_customer"
                                            class="form-control form-control-solid" />
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="spesifikasi" class="required form-label">spesifikasi</label>
                                        <textarea name="spesifikasi" id="spesifikasi" rows="6" class="form-control">{{ $data->lp_panjang . ' X ' . $data->lp_lebar . ', ' . $data->jenis_kertas . ', ' . $data->gramasi . ', ' . $data->laminasi . ', ' . $data->sisi_laminasi . ', ' . $data->finishing . ', ' . $data->jumlah_produk . ' pcs, ' . $data->notes }}</textarea>
                                    </div>
                                    <div class="mb-10">
                                        <label for="tgl_deadline" class="required form-label">Tanggal Deadline</label>
                                        <input type="date" value="{{ old('tgl_deadline') ?? $data->tgl_deadline }}"
                                            name="tgl_deadline" id="tgl_deadline"
                                            class="form-control" />
                                    </div>

                                </div>
                            </div>
                            <hr>
                            <div class="mt-10">
                                <table class="table-bordered table">
                                    <tbody>
                                        <tr>
                                            <td>Sub Kode Order</td>
                                            <td>:</td>
                                            <td id="sko">{{ old('sko') ?? $data->sko }}</td>
                                        </tr>
                                        <tr>
                                            <td>Nama Customer</td>
                                            <td>:</td>
                                            <td id="nama_customer">{{ old('nama') ?? $data->nama }}</td>
                                        </tr>
                                        <tr>
                                            <td>Spesifikasi</td>
                                            <td>:</td>
                                            <td id="spesifikasi">
                                                {{ $data->lp_panjang . ' X ' . $data->lp_lebar . ', ' . $data->jenis_kertas . ', ' . $data->gramasi . ', ' . $data->laminasi . ', ' . $data->sisi_laminasi . ', ' . $data->finishing . ', ' . $data->jumlah_produk . ' pcs, ' . $data->notes }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Tanggal Deadline</td>
                                            <td>:</td>
                                            <td id="tgl_deadline">
                                                {{ $data->tgl_deadline ? date('d F Y', strtotime($data->tgl_deadline)) : '' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Waktu Produksi</td>
                                            <td>:</td>
                                            <td id="waktu_produksi">{{ $data->waktu_produksi }}</td>
                                        </tr>
                                        <tr>
                                            <td>Tanggal FAW</td>
                                            <td>:</td>
                                            <td id="tgl_faw">
                                                {{ $data->tgl_faw ? date('d F Y', strtotime($data->tgl_faw)) : '' }}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Link File Final</td>
                                            <td>:</td>
                                            <td id="file_final">
                                                <a class="badge badge-light-primary" href="{{ $data->file_final }}">Open Link</a>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Lampiran</td>
                                            <td>:</td>
                                            <td id="path_lampiran">
                                                <img id="zoomable-image" width="300"
                                                    src="{{ url('storage/' . $data->sko_key . '/faw/lampiran/' . $data->path_lampiran) }}"
                                                    alt="">
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Keterangan</td>
                                            <td>:</td>
                                            <td id="keterangan_tambahan">{{ $data->keterangan_tambahan }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div class="d-flex justify-content-end">
                                    <a href="{{ url('crm/generate_faw/' . $data->sko_key) }}"  class="btn btn-my-primary text-white">Download FAW</a>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="kt_tab_pane_2" role="tabpanel">
                            <div class="row justify-content-center mt-10">

                                <div class="mb-10">
                                    <div class="row">
                                        @if (substr($data->sko, -1) != 'D')
                                            <div class="col-md-4">
                                                <label for="progress_produksi" class="required form-label">Progress Produksi</label>
                                            </div>
                                            <div class="col-md-8">
                                                <div class="row">
                                                    <div class="col">
                                                        <div class="form-check form-check-custom mb-2">
                                                            <input class="form-check-input" type="checkbox"
                                                                {{ $data->progress_produksi_1 ? 'checked' : '' }}
                                                                value="1" name="progress_produksi_1"
                                                                id="progress_produksi_1" />
                                                            <label class="form-check-label" for="progress_produksi_1">
                                                                Pra Cetak
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-check-custom mb-2">
                                                            <input class="form-check-input" type="checkbox"
                                                                {{ $data->progress_produksi_2 ? 'checked' : '' }}
                                                                value="1" name="progress_produksi_2"
                                                                id="progress_produksi_2" />
                                                            <label class="form-check-label" for="progress_produksi_2">
                                                                Cetak
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-check-custom mb-2">
                                                            <input class="form-check-input" type="checkbox"
                                                                {{ $data->progress_produksi_3 ? 'checked' : '' }}
                                                                value="1" name="progress_produksi_3"
                                                                id="progress_produksi_3" />
                                                            <label class="form-check-label" for="progress_produksi_3">
                                                                Laminasi
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col">
                                                        <div class="form-check form-check-custom mb-2">
                                                            <input class="form-check-input" type="checkbox"
                                                                {{ $data->progress_produksi_4 ? 'checked' : '' }}
                                                                value="1" name="progress_produksi_4"
                                                                id="progress_produksi_4" />
                                                            <label class="form-check-label" for="progress_produksi_4">
                                                                Poly
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-check-custom mb-2">
                                                            <input class="form-check-input" type="checkbox"
                                                                {{ $data->progress_produksi_5 ? 'checked' : '' }}
                                                                value="1" name="progress_produksi_5"
                                                                id="progress_produksi_5" />
                                                            <label class="form-check-label" for="progress_produksi_5">
                                                                Emboss
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-check-custom mb-2">
                                                            <input class="form-check-input" type="checkbox"
                                                                {{ $data->progress_produksi_6 ? 'checked' : '' }}
                                                                value="1" name="progress_produksi_6"
                                                                id="progress_produksi_6" />
                                                            <label class="form-check-label" for="progress_produksi_6">
                                                                Spot UV
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col">
                                                        <div class="form-check form-check-custom mb-2">
                                                            <input class="form-check-input" type="checkbox"
                                                                {{ $data->progress_produksi_7 ? 'checked' : '' }}
                                                                value="1" name="progress_produksi_7"
                                                                id="progress_produksi_7" />
                                                            <label class="form-check-label" for="progress_produksi_7">
                                                                Lapis
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-check-custom mb-2">
                                                            <input class="form-check-input" type="checkbox"
                                                                {{ $data->progress_produksi_8 ? 'checked' : '' }}
                                                                value="1" name="progress_produksi_8"
                                                                id="progress_produksi_8" />
                                                            <label class="form-check-label" for="progress_produksi_8">
                                                                Jendela Mika
                                                            </label>
                                                        </div>
                                                        <div class="form-check form-check-custom mb-2">
                                                            <input class="form-check-input" type="checkbox"
                                                                {{ $data->progress_produksi_9 ? 'checked' : '' }}
                                                                value="1" name="progress_produksi_9"
                                                                id="progress_produksi_9" />
                                                            <label class="form-check-label" for="progress_produksi_9">
                                                                Pond
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col d-flex flex-column justify-content-between">
                                                        <div class="form-check form-check-custom mb-2">
                                                            <input class="form-check-input" type="checkbox"
                                                                {{ $data->progress_produksi_10 ? 'checked' : '' }}
                                                                value="1" name="progress_produksi_10"
                                                                id="progress_produksi_10" />
                                                            <label class="form-check-label" for="progress_produksi_10">
                                                                Finishing
                                                            </label>
                                                        </div>
                                                        <div class="form-check mb-2">
                                                            <input class="form-check-input" name="progress_produksi_ve" type="checkbox" {{ $data->progress_produksi_ve ? 'checked' : '' }} value="1" id="checkVendorEks">
                                                            <label class="form-check-label" for="checkVendorEks">
                                                                <strong>Vendor Eksternal</strong>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                            </div>
                                        @else
                                            <div hidden>
                                                <div class="form-check form-check-custom ml-3 mb-2 mt-10">
                                                    <input class="form-check-input" type="checkbox"
                                                        checked
                                                        value="1" name="progress_produksi_dummy"
                                                        id="progress_produksi_dummy" />
                                                    <label class="form-check-label" for="progress_produksi_dummy">
                                                        <strong>Dummy</strong>
                                                    </label>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="row justify-content-center mt-10">
                                <div class="col-md-12 mb-10">
                                    <div class="accordion accordion-flush" id="progress_prod">
                                        <div class="accordion-item border" id="accordDummy">
                                            <h2 class="accordion-header" id="headingDummy">
                                                <button class="accordion-button" type="button"
                                                    data-bs-toggle="collapse" data-bs-target="#collapseDummy"
                                                    aria-expanded="true" aria-controls="collapseDummy">
                                                    <h3><strong>Dummy</strong></h3>
                                                </button>
                                            </h2>
                                            <div id="collapseDummy" class="accordion-collapse show collapse"
                                                aria-labelledby="headingDummy" data-bs-parent="#progress_prod">
                                                <div class="accordion-body">

                                                    <div class="alert alert-danger rounded-3 mb-2 mb-5">
                                                        <div class="bold fs-6 text-center"><strong>Deadline
                                                                {{ date('d F Y', strtotime($data->tgl_deadline)) }}</strong>
                                                        </div>
                                                    </div>
                                                    <label for="checklist" class="form-label">Checklist</label>
                                                    <div class="row">

                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_dummy_1 ? 'checked' : '' }}
                                                                    value="1" name="check_dummy_1"
                                                                    id="check_dummy_1" />
                                                                <label class="form-check-label" for="check_dummy_1">
                                                                    Cetak
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_dummy_2 ? 'checked' : '' }}
                                                                    value="1" name="check_dummy_2"
                                                                    id="check_dummy_2" />
                                                                <label class="form-check-label" for="check_dummy_2">
                                                                    Laminasi
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_dummy_3 ? 'checked' : '' }}
                                                                    value="1" name="check_dummy_3"
                                                                    id="check_dummy_3" />
                                                                <label class="form-check-label" for="check_dummy_3">
                                                                    Spot UV
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_dummy_4 ? 'checked' : '' }}
                                                                    value="1" name="check_dummy_4"
                                                                    id="check_dummy_4" />
                                                                <label class="form-check-label" for="check_dummy_4">
                                                                    Poly/Emboss
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_dummy_5 ? 'checked' : '' }}
                                                                    value="1" name="check_dummy_5"
                                                                    id="check_dummy_5" />
                                                                <label class="form-check-label" for="check_dummy_5">
                                                                    Potong Manual
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_dummy_6 ? 'checked' : '' }}
                                                                    value="1" name="check_dummy_6"
                                                                    id="check_dummy_6" />
                                                                <label class="form-check-label" for="check_dummy_6">
                                                                    Potong Mesin
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_dummy_7 ? 'checked' : '' }}
                                                                    value="1" name="check_dummy_7"
                                                                    id="check_dummy_7" />
                                                                <label class="form-check-label" for="check_dummy_7">
                                                                    Finishing Lem
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row mt-10">
                                                        <div class="mb-10 col-7">
                                                            <label for="harga_dummy" class="form-label">Harga</label>
                                                            <div class="input-group">
                                                                <span class="input-group-text mt-3">Rp. </span>
                                                                <input type="text" name="harga_dummy"
                                                                    id="harga_dummy" class="form-control mt-3"
                                                                    value="{{ old('harga_dummy') ?? $data->harga_dummy }}"
                                                                    placeholder="Harga Dummy" />
                                                            </div>
                                                        </div>
                                                        <div class="mb-10 col-5">
                                                            <label for="flag_status_dummy"
                                                                class="form-label">Selesai</label>
                                                            <div class="form-check form-switch form-check-custom mt-3">
                                                                <input class="form-check-input"
                                                                    name="flag_status_dummy"
                                                                    {{ $data->flag_status_dummy ? 'checked' : '' }}
                                                                    type="checkbox" value="1"
                                                                    id="flag_status_dummy" />
                                                                <label class="form-check-label"
                                                                    for="flexSwitchDefault">Belum/Sudah</label>
                                                            </div>
            
                                                            <div class="mb-10 mt-10" id="dateFinalDummy">
                                                                <label for="tgl_selesai_dummy"
                                                                    class="required form-label">Tanggal
                                                                    Selesai</label>
                                                                {{-- <input type="date" name="tgl_selesai_dummy"
                                                                    value="{{ old('tgl_selesai_dummy') ?? $data->tgl_selesai_dummy }}"
                                                                    id="tgl_selesai_dummy" class="form-control"> --}}
                                                                <div class="alert alert-success w-50">
                                                                    @if ($data->tgl_flag_status_dummy)
                                                                        {{ $data->tgl_flag_status_dummy }}
                                                                    @else
                                                                        {{ date('Y-m-d H:i:s') }}
                                                                    @endif
            
                                                                </div>
            
                                                            </div>
            
                                                        </div>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>

                                        <div class="accordion-item border" id="accordOne">
                                            <h2 class="accordion-header" id="headingOne">
                                                <button class="accordion-button" type="button"
                                                    data-bs-toggle="collapse" data-bs-target="#collapseOne"
                                                    aria-expanded="true" aria-controls="collapseOne">
                                                    <h3><strong>Pra Cetak</strong></h3>
                                                </button>
                                            </h2>
                                            <div id="collapseOne" class="accordion-collapse show collapse"
                                                aria-labelledby="headingOne" data-bs-parent="#progress_prod">
                                                <div class="accordion-body">

                                                    <div class="alert alert-danger rounded-3 mb-2">
                                                        <div class="bold fs-6 text-center"><strong>Deadline
                                                                {{ date('d F Y', strtotime($data->tgl_deadline)) }}</strong>
                                                        </div>
                                                    </div>
                                                    <div class="card mb-10">
                                                        <div class="card-body bg-primary rounded-3 bg-opacity-20">
                                                            <h5 class="card-title">Spesifikasi</h5>
                                                            <p class="card-text">
                                                                {{ $data->lp_panjang . ' X ' . $data->lp_lebar . ', ' . $data->jenis_kertas . ', ' . $data->gramasi . ', ' . $data->laminasi . ', ' . $data->sisi_laminasi . ', ' . $data->finishing . ', ' . $data->jumlah_produk . ' pcs, ' . $data->notes }}
                                                            </p>
                                                        </div>
                                                    </div>

                                                    <div class="mb-10">
                                                        <label for="jumlah_plano" class="form-label">Jumlah
                                                            Plano</label>
                                                        <input type="text" name="jumlah_plano"
                                                            value="{{ old('jumlah_plano') ?? $data->jumlah_plano }}"
                                                            id="jumlah_plano" class="form-control" />
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="vendor_1"
                                                            class="required form-label">Vendor</label>
                                                        <select name="vendor_1" id="vendor_1" class="form-select">
                                                            <option value="{{ $data->vendor_1 }}">
                                                                {{ $data->vendor_1 }}</option>
                                                            <option value="" disabled>-- Pilih Vendor</option>
                                                            @foreach ($vendor as $vlist)
                                                                <option value="{{ $vlist->vendor }}">
                                                                    {{ $vlist->vendor }}</option>
                                                            @endforeach
                                                            <option value="Lainnya">Lainnya</option>
                                                        </select>
                                                        <input type="text" name="vendor_1_lainnya"
                                                            id="vendor_1_lainnya" class="form-control mt-3"
                                                            placeholder="vendor lainnya" />
                                                    </div>

                                                    <div class="mb-10">
                                                        <label for="harga_pracetak" class="form-label">Harga</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text mt-3">Rp. </span>
                                                            <input type="text" name="harga_pracetak"
                                                                id="harga_pracetak" class="form-control mt-3"
                                                                value="{{ old('harga_pracetak') ?? $data->harga_pracetak }}"
                                                                placeholder="Harga Pracetak" />
                                                        </div>
                                                    </div>

                                                    <div class="mb-10">
                                                        {{-- Tanggal Produksi diganti tanggal mulai --}}
                                                        <label for="tgl_produksi_1"
                                                            class="required form-label">Tanggal
                                                            Mulai</label>
                                                        <input type="date" name="tgl_produksi_1"
                                                            value="{{ old('tgl_produksi_1') ?? $data->tgl_produksi_1 }}"
                                                            id="tgl_produksi_1" class="form-control" />
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="mb-10">
                                                                <label for="pic_validasi_1"
                                                                    class="required form-label">PIC Validasi</label>
                                                                <input type="text" name="pic_validasi_1"
                                                                    value="{{ old('pic_validasi_1') ?? $data->pic_validasi_1 }}"
                                                                    id="pic_validasi_1" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="flag_status_1"
                                                                    class="form-label">Selesai</label>
                                                                <div class="form-check form-switch form-check-custom">
                                                                    <input class="form-check-input"
                                                                        name="flag_status_1"
                                                                        {{ $data->flag_status_1 ? 'checked' : '' }}
                                                                        type="checkbox" value="1"
                                                                        id="flag_status_1" />
                                                                    <label class="form-check-label"
                                                                        for="flexSwitchDefault">Belum/Sudah</label>
                                                                </div>

                                                                <div class="mb-10 mt-10" id="dateFinalOne">
                                                                    <label for="tgl_selesai_1"
                                                                        class="required form-label">Tanggal
                                                                        Selesai</label>
                                                                    {{-- <input type="date" name="tgl_selesai_1"
                                                                        value="{{ old('tgl_selesai_1') ?? $data->tgl_selesai_1 }}"
                                                                        id="tgl_selesai_1" class="form-control"> --}}
                                                                    <div class="alert alert-success">
                                                                        @if ($data->tgl_flag_status_1)
                                                                            {{ $data->tgl_flag_status_1 }}
                                                                        @else
                                                                            {{ date('Y-m-d H:i:s') }}
                                                                        @endif


                                                                    </div>

                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="catatan_1" class="form-label">Catatan</label>
                                                        <textarea type="date" name="catatan_1" rows="3" id="catatan_1" class="form-control">{{ $data->catatan_1 }}</textarea>
                                                    </div>
                                                    <hr>
                                                    <label for="checklist" class="form-label">Checklist</label>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_1_1 ? 'checked' : '' }}
                                                                    value="1" name="check_1_1" id="check_1_1" />
                                                                <label class="form-check-label" for="check_1_1">
                                                                    Kesesuaian Kertas
                                                                </label>

                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_1_2 ? 'checked' : '' }}
                                                                    value="1" name="check_1_2" id="check_1_2" />
                                                                <label class="form-check-label" for="check_1_2">
                                                                    Kesesuaian CTP
                                                                </label>

                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_1_3 ? 'checked' : '' }}
                                                                    value="1" name="check_1_3" id="check_1_3" />
                                                                <label class="form-check-label" for="check_1_3">
                                                                    Jadwal Naik Cetak
                                                                </label>

                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_1_4 ? 'checked' : '' }}
                                                                    value="1" name="check_1_4" id="check_1_4" />
                                                                <label class="form-check-label" for="check_1_4">
                                                                    Acuan Cetak
                                                                </label>


                                                            </div>
                                                        </div>
                                                    </div>
                                                    <hr>
                                                    <label for="checklist" class="form-label">Bahan Baku</label>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->pisaupond ? 'checked' : '' }}
                                                                    value="1" name="pisaupond"
                                                                    id="bahan_baku1" />
                                                                <label class="form-check-label" for="bahan_baku1">
                                                                    Pisau Pond
                                                                </label>
                                                                <div class="m-1">
                                                                    <input type="text" name="harga1"
                                                                        value="{{ old('harga1') ?? $data->harga1 }}"
                                                                        id="harga1" class="form-control" />
                                                                </div>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->klisepoly ? 'checked' : '' }}
                                                                    value="1" name="klisepoly"
                                                                    id="bahan_baku2" />
                                                                <label class="form-check-label" for="bahan_baku2">
                                                                    Klise Poly
                                                                </label>
                                                                <div class="m-1">
                                                                    <input type="text" name="harga2"
                                                                        value="{{ old('harga2') ?? $data->harga2 }}"
                                                                        id="harga2" class="form-control" />
                                                                </div>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->kliseemboss ? 'checked' : '' }}
                                                                    value="1" name="kliseemboss"
                                                                    id="bahan_baku3" />
                                                                <label class="form-check-label" for="bahan_baku3">
                                                                    Klise Emboss
                                                                </label>
                                                                <div class="m-1">
                                                                    <input type="text" name="harga3"
                                                                        value="{{ old('harga3') ?? $data->harga3 }}"
                                                                        id="harga3" class="form-control"/>
                                                                </div>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->filmpisau ? 'checked' : '' }}
                                                                    value="1" name="filmpisau"
                                                                    id="bahan_baku7" />
                                                                <label class="form-check-label" for="bahan_baku7">
                                                                    HVS/Film Pisau
                                                                </label>
                                                                <div class="m-1">
                                                                    <input type="text" name="harga7"
                                                                        value="{{ old('harga7') ?? $data->harga7 }}"
                                                                        id="harga7" class="form-control"/>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->klisespotuv ? 'checked' : '' }}
                                                                    value="1" name="klisespotuv"
                                                                    id="bahan_baku4" />
                                                                <label class="form-check-label" for="bahan_baku4">
                                                                    Film Spot UV
                                                                </label>
                                                                <div class="m-1">
                                                                    <input type="text" name="harga6"
                                                                        value="{{ old('harga6') ?? $data->harga6 }}"
                                                                        id="harga6" class="form-control" />
                                                                </div>
                                                            </div>

                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->ctv ? 'checked' : '' }} value="1"
                                                                    name="ctv" id="bahan_baku5" />
                                                                <label class="form-check-label" for="bahan_baku5">
                                                                    CTP
                                                                </label>
                                                                <div class="m-1">
                                                                    <input type="text" name="harga4"
                                                                        value="{{ old('harga4') ?? $data->harga4 }}"
                                                                        id="harga4" class="form-control" />
                                                                </div>

                                                            </div>

                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->kertas ? 'checked' : '' }}
                                                                    value="1" name="kertas" id="bahan_baku6" />
                                                                <label class="form-check-label" for="bahan_baku6">
                                                                    Kertas
                                                                </label>
                                                                <div class="m-1">
                                                                    <input type="text" name="harga5"
                                                                        value="{{ old('harga5') ?? $data->harga5 }}"
                                                                        id="harga5" class="form-control" />
                                                                </div>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                {{ $data->bahanfinishing ? 'checked' : '' }}
                                                                    value="1" name="bahanfinishing"
                                                                    id="bahan_baku8" value="0"/>
                                                                <label class="form-check-label" for="bahan_baku8">
                                                                    Bahan Finishing
                                                                </label>
                                                                <div class="m-1">
                                                                    <input type="text" name="harga8"
                                                                        value="{{ old('harga8') ?? $data->harga8 }}"
                                                                        id="harga8" class="form-control" value="0"/>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="accordion-item border" id="accordTwo">
                                            <h2 class="accordion-header" id="headingTwo">
                                                <button class="accordion-button collapsed" type="button"
                                                    data-bs-toggle="collapse" data-bs-target="#collapseTwo"
                                                    aria-expanded="false" aria-controls="collapseTwo">
                                                    <h3><strong>Cetak</strong></h3>
                                                </button>
                                            </h2>
                                            <div id="collapseTwo" class="accordion-collapse collapse"
                                                aria-labelledby="headingTwo" data-bs-parent="#progress_prod">
                                                <div class="accordion-body">
                                                    <div class="alert alert-danger rounded-3 mb-2 mb-5">
                                                        <div class="bold fs-6 text-center"><strong>Deadline
                                                                {{ date('d F Y', strtotime($data->tgl_deadline)) }}</strong>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_awal_pp_2"
                                                                    class="required form-label">Jumlah Awal</label>
                                                                <input type="text" name="jumlah_awal_pp_2"
                                                                    value="{{ old('jumlah_awal_pp_2') ?? $data->jumlah_awal_pp_2 }}"
                                                                    id="jumlah_awal_pp_2" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">2
                                                                <label for="jumlah_hasil_pp_2"
                                                                    class="required form-label">Jumlah Akhir</label>
                                                                <input type="text" name="jumlah_hasil_pp_2"
                                                                    value="{{ old('jumlah_hasil_pp_2') ?? $data->jumlah_hasil_pp_2 }}"
                                                                    id="jumlah_hasil_pp_2" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="reject_pp_2"
                                                                    class="required form-label">Reject</label>
                                                                <input type="text" name="reject_pp_2"
                                                                    value="{{ old('reject_pp_2') ?? $data->reject_pp_2 }}"
                                                                    id="reject_pp_2" class="form-control" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="vendor_2"
                                                            class="required form-label">Vendor</label>
                                                        <select name="vendor_2" id="vendor_2" class="form-select">
                                                            <option value="{{ $data->vendor_2 }}">
                                                                {{ $data->vendor_2 }}</option>
                                                            <option value="" disabled>-- Pilih Vendor</option>
                                                            @foreach ($vendor as $vlist)
                                                                <option value="{{ $vlist->vendor }}">
                                                                    {{ $vlist->vendor }}</option>
                                                            @endforeach
                                                            <option value="Lainnya">Lainnya</option>
                                                        </select>
                                                        <input type="text" name="vendor_2_lainnya"
                                                            id="vendor_2_lainnya" class="form-control mt-3"
                                                            placeholder="vendor lainnya" />
                                                    </div>

                                                    <div class="mb-10">
                                                        <label for="harga_cetak" class="form-label">Harga</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text mt-3">Rp. </span>
                                                            <input type="text" name="harga_cetak" id="harga_cetak"
                                                                class="form-control mt-3"
                                                                value="{{ old('harga_cetak') ?? $data->harga_cetak }}"
                                                                placeholder="Harga Cetak" />
                                                        </div>
                                                    </div>

                                                    <div class="mb-10">
                                                        <label for="tgl_produksi_2"
                                                            class="required form-label">Tanggal
                                                            Mulai</label>
                                                        <input type="date" name="tgl_produksi_2"
                                                            value="{{ old('tgl_produksi_2') ?? $data->tgl_produksi_2 }}"
                                                            id="tgl_produksi_2" class="form-control" />
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="mb-10">
                                                                <label for="pic_validasi_2"
                                                                    class="required form-label">PIC Validasi</label>
                                                                <input type="text" name="pic_validasi_2"
                                                                    value="{{ old('pic_validasi_2') ?? $data->pic_validasi_2 }}"
                                                                    id="pic_validasi_2" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="flag_status_2"
                                                                    class="form-label">Selesai</label>
                                                                <div class="form-check form-switch form-check-custom">
                                                                    <input class="form-check-input"
                                                                        name="flag_status_2"
                                                                        {{ $data->flag_status_2 ? 'checked' : '' }}
                                                                        type="checkbox" value="1"
                                                                        id="flag_status_2" />
                                                                    <label class="form-check-label"
                                                                        for="flexSwitchDefault">Belum/Sudah</label>
                                                                </div>

                                                                <div class="mb-10 mt-10" id="dateFinalTwo">
                                                                    <label for="tgl_selesai_2"
                                                                        class="required form-label">Tanggal
                                                                        Selesai</label>
                                                                    {{-- <input type="date" name="tgl_selesai_2"
                                                                        value="{{ old('tgl_selesai_2') ?? $data->tgl_selesai_2 }}"
                                                                        id="tgl_selesai_2" class="form-control"> --}}
                                                                    <div class="alert alert-success">
                                                                        @if ($data->tgl_flag_status_2)
                                                                            {{ $data->tgl_flag_status_2 }}
                                                                        @else
                                                                            {{ date('Y-m-d H:i:s') }}
                                                                        @endif

                                                                    </div>

                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="catatan_2" class="form-label">Catatan</label>
                                                        <textarea type="date" name="catatan_2" rows="3" id="catatan_2" class="form-control">{{ $data->catatan_2 }}</textarea>
                                                    </div>
                                                    <hr>
                                                    <label for="checklist" class="form-label">Checklist</label>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_2_1 ? 'checked' : '' }}
                                                                    value="1" name="check_2_1" id="check_2_1" />
                                                                <label class="form-check-label" for="check_2_1">
                                                                    Acc Cetak
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_2_2 ? 'checked' : '' }}
                                                                    value="1" name="check_2_2" id="check_2_2" />
                                                                <label class="form-check-label" for="check_2_2">
                                                                    Jaga warna
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_2_3 ? 'checked' : '' }}
                                                                    value="1" name="check_2_3" id="check_2_3" />
                                                                <label class="form-check-label" for="check_2_3">
                                                                    Hasil didiamkan satu hari
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_2_4 ? 'checked' : '' }}
                                                                    value="1" name="check_2_4" id="check_2_4" />
                                                                <label class="form-check-label" for="check_2_4">
                                                                    Cek acak hasil cetak
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item border" id="accordThree">
                                            <h2 class="accordion-header" id="headingThree">
                                                <button class="accordion-button collapsed" type="button"
                                                    data-bs-toggle="collapse" data-bs-target="#collapseThree"
                                                    aria-expanded="false" aria-controls="collapseThree">
                                                    <h3><strong>Laminasi</strong></h3>
                                                </button>
                                            </h2>
                                            <div id="collapseThree" class="accordion-collapse collapse"
                                                aria-labelledby="headingThree" data-bs-parent="#progress_prod">
                                                <div class="accordion-body">
                                                    <div class="alert alert-danger rounded-3 mb-2 mb-5">
                                                        <div class="bold fs-6 text-center"><strong>Deadline
                                                                {{ date('d F Y', strtotime($data->tgl_deadline)) }}</strong>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_awal_pp_3"
                                                                    class="required form-label">Jumlah Awal</label>
                                                                <input type="text" name="jumlah_awal_pp_3"
                                                                    value="{{ old('jumlah_awal_pp_3') ?? $data->jumlah_awal_pp_3 }}"
                                                                    id="jumlah_awal_pp_3" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_hasil_pp_3"
                                                                    class="required form-label">Jumlah Akhir</label>
                                                                <input type="text" name="jumlah_hasil_pp_3"
                                                                    value="{{ old('jumlah_hasil_pp_3') ?? $data->jumlah_hasil_pp_3 }}"
                                                                    id="jumlah_hasil_pp_3" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="reject_pp_3"
                                                                    class="required form-label">Reject</label>
                                                                <input type="text" name="reject_pp_3"
                                                                    value="{{ old('reject_pp_3') ?? $data->reject_pp_3 }}"
                                                                    id="reject_pp_3" class="form-control" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="vendor_3"
                                                            class="required form-label">Vendor</label>
                                                        <select name="vendor_3" id="vendor_3" class="form-select">
                                                            <option value="{{ $data->vendor_3 }}">
                                                                {{ $data->vendor_3 }}</option>
                                                            <option value="" disabled>-- Pilih Vendor</option>
                                                            @foreach ($vendor as $vlist)
                                                                <option value="{{ $vlist->vendor }}">
                                                                    {{ $vlist->vendor }}</option>
                                                            @endforeach
                                                            <option value="Lainnya">Lainnya</option>
                                                        </select>
                                                        <input type="text" name="vendor_3_lainnya"
                                                            id="vendor_3_lainnya" class="form-control mt-3"
                                                            placeholder="vendor lainnya" />
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="harga_laminasi" class="form-label">Harga</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text mt-3">Rp. </span>
                                                            <input type="text" name="harga_laminasi"
                                                                id="harga_laminasi" class="form-control mt-3"
                                                                value="{{ old('harga_laminasi') ?? $data->harga_laminasi }}"
                                                                placeholder="Harga Laminasi" />
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="tgl_produksi_3"
                                                            class="required form-label">Tanggal
                                                            Mulai</label>
                                                        <input type="date" name="tgl_produksi_3"
                                                            value="{{ old('tgl_produksi_3') ?? $data->tgl_produksi_3 }}"
                                                            id="tgl_produksi_3" class="form-control" />
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="mb-10">
                                                                <label for="pic_validasi_3"
                                                                    class="required form-label">PIC Validasi</label>
                                                                <input type="text" name="pic_validasi_3"
                                                                    value="{{ old('pic_validasi_3') ?? $data->pic_validasi_3 }}"
                                                                    id="pic_validasi_3" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="flag_status_3"
                                                                    class="form-label">Selesai</label>
                                                                <div class="form-check form-switch form-check-custom">
                                                                    <input class="form-check-input"
                                                                        name="flag_status_3"
                                                                        {{ $data->flag_status_3 ? 'checked' : '' }}
                                                                        type="checkbox" value="1"
                                                                        id="flag_status_3" />
                                                                    <label class="form-check-label"
                                                                        for="flexSwitchDefault">Belum/Sudah</label>
                                                                </div>

                                                                <div class="mb-10 mt-10" id="dateFinalThree">
                                                                    <label for="tgl_selesai_3"
                                                                        class="required form-label">Tanggal
                                                                        Selesai</label>
                                                                    {{-- <input type="date" name="tgl_selesai_3"
                                                                        value="{{ old('tgl_selesai_3') ?? $data->tgl_selesai_3 }}"
                                                                        id="tgl_selesai_3" class="form-control"> --}}
                                                                    <div class="alert alert-success">
                                                                        @if ($data->tgl_flag_status_3)
                                                                            {{ $data->tgl_flag_status_3 }}
                                                                        @else
                                                                            {{ date('Y-m-d H:i:s') }}
                                                                        @endif
                                                                    </div>

                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="catatan_3" class="form-label">Catatan</label>
                                                        <textarea type="date" name="catatan_3" rows="3" id="catatan_3" class="form-control">{{ $data->catatan_3 }}</textarea>
                                                    </div>
                                                    <hr>
                                                    <label for="checklist" class="form-label">Checklist</label>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_3_1 ? 'checked' : '' }}
                                                                    value="1" name="check_3_1" id="check_3_1" />
                                                                <label class="form-check-label" for="check_3_1">
                                                                    Jadwal naik laminasi
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_3_2 ? 'checked' : '' }}
                                                                    value="1" name="check_3_2" id="check_3_2" />
                                                                <label class="form-check-label" for="check_3_2">
                                                                    SPK laminasi sesuai
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_3_3 ? 'checked' : '' }}
                                                                    value="1" name="check_3_3" id="check_3_3" />
                                                                <label class="form-check-label" for="check_3_3">
                                                                    Cek acak hasil
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item border" id="accordFour">
                                            <h2 class="accordion-header" id="headingFour">
                                                <button class="accordion-button collapsed" type="button"
                                                    data-bs-toggle="collapse" data-bs-target="#collapseFour"
                                                    aria-expanded="false" aria-controls="collapseFour">
                                                    <h3><strong>Poly</strong></h3>
                                                </button>
                                            </h2>
                                            <div id="collapseFour" class="accordion-collapse collapse"
                                                aria-labelledby="headingFour" data-bs-parent="#progress_prod">
                                                <div class="accordion-body">
                                                    <div class="alert alert-danger rounded-3 mb-2 mb-5">
                                                        <div class="bold fs-6 text-center"><strong>Deadline
                                                                {{ date('d F Y', strtotime($data->tgl_deadline)) }}</strong>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_awal_pp_4"
                                                                    class="required form-label">Jumlah Awal</label>
                                                                <input type="text" name="jumlah_awal_pp_4"
                                                                    value="{{ old('jumlah_awal_pp_4') ?? $data->jumlah_awal_pp_4 }}"
                                                                    id="jumlah_awal_pp_4" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_hasil_pp_4"
                                                                    class="required form-label">Jumlah Akhir</label>
                                                                <input type="text" name="jumlah_hasil_pp_4"
                                                                    value="{{ old('jumlah_hasil_pp_4') ?? $data->jumlah_hasil_pp_4 }}"
                                                                    id="jumlah_hasil_pp_4" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="reject_pp_4"
                                                                    class="required form-label">Reject</label>
                                                                <input type="text" name="reject_pp_4"
                                                                    value="{{ old('reject_pp_4') ?? $data->jumlah_hasil_pp_4 }}"
                                                                    id="reject_pp_4" class="form-control" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="vendor_4"
                                                            class="required form-label">Vendor</label>
                                                        <select name="vendor_4" id="vendor_4" class="form-select">
                                                            <option value="{{ $data->vendor_4 }}">
                                                                {{ $data->vendor_4 }}</option>
                                                            <option value="" disabled>-- Pilih Vendor</option>
                                                            @foreach ($vendor as $vlist)
                                                                <option value="{{ $vlist->vendor }}">
                                                                    {{ $vlist->vendor }}</option>
                                                            @endforeach
                                                            <option value="Lainnya">Lainnya</option>
                                                        </select>
                                                        <input type="text" name="vendor_4_lainnya"
                                                            id="vendor_4_lainnya" class="form-control mt-3"
                                                            placeholder="vendor lainnya" />
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="harga_poly" class="form-label">Harga</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text mt-3">Rp. </span>
                                                            <input type="text" name="harga_poly" id="harga_poly"
                                                                class="form-control mt-3"
                                                                value="{{ old('harga_poly') ?? $data->harga_poly }}"
                                                                placeholder="Harga Poly" />
                                                        </div>
                                                    </div>

                                                    <div class="mb-10">
                                                        <label for="tgl_produksi_4"
                                                            class="required form-label">Tanggal
                                                            Mulai</label>
                                                        <input type="date" name="tgl_produksi_4"
                                                            value="{{ old('tgl_produksi_4') ?? $data->tgl_produksi_4 }}"
                                                            id="tgl_produksi_4" class="form-control" />
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="mb-10">
                                                                <label for="pic_validasi_4"
                                                                    class="required form-label">PIC Validasi</label>
                                                                <input type="text" name="pic_validasi_4"
                                                                    value="{{ old('pic_validasi_4') ?? $data->pic_validasi_4 }}"
                                                                    id="pic_validasi_4" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="flag_status_4"
                                                                    class="form-label">Selesai</label>
                                                                <div class="form-check form-switch form-check-custom">
                                                                    <input class="form-check-input"
                                                                        name="flag_status_4"
                                                                        {{ $data->flag_status_4 ? 'checked' : '' }}
                                                                        type="checkbox" value="1"
                                                                        id="flag_status_4" />
                                                                    <label class="form-check-label"
                                                                        for="flexSwitchDefault">Belum/Sudah</label>
                                                                </div>

                                                                <div class="mb-10 mt-10" id="dateFinalFour">
                                                                    <label for="tgl_selesai_4"
                                                                        class="required form-label">Tanggal
                                                                        Selesai</label>
                                                                    {{-- <input type="date" name="tgl_selesai_4"
                                                                        value="{{ old('tgl_selesai_4') ?? $data->tgl_selesai_4 }}"
                                                                        id="tgl_selesai_4" class="form-control"> --}}
                                                                    <div class="alert alert-success">
                                                                        @if ($data->tgl_flag_status_4)
                                                                            {{ $data->tgl_flag_status_4 }}
                                                                        @else
                                                                            {{ date('Y-m-d H:i:s') }}
                                                                        @endif
                                                                    </div>

                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="catatan_4" class="form-label">Catatan</label>
                                                        <textarea type="date" name="catatan_4" rows="3" id="catatan_4" class="form-control">{{ $data->catatan_4 }}</textarea>
                                                    </div>
                                                    <hr>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_4_1 ? 'checked' : '' }}
                                                                    value="1" name="check_4_1" id="check_4_1" />
                                                                <label class="form-check-label" for="check_4_1">
                                                                    Jadwal naik Poly
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_4_2 ? 'checked' : '' }}
                                                                    value="1" name="check_4_2" id="check_4_2" />
                                                                <label class="form-check-label" for="check_4_2">
                                                                    Klise Poly
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_4_3 ? 'checked' : '' }}
                                                                    value="1" name="check_4_3" id="check_4_3" />
                                                                <label class="form-check-label" for="check_4_3">
                                                                    Acuan poly
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_4_4 ? 'checked' : '' }}
                                                                    value="1" name="check_4_4" id="check_4_4" />
                                                                <label class="form-check-label" for="check_4_4">
                                                                    ACC Poly
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_4_5 ? 'checked' : '' }}
                                                                    value="1" name="check_4_5" id="check_4_5" />
                                                                <label class="form-check-label" for="check_4_5">
                                                                    Check acak hasil
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item border" id="accordFive">
                                            <h2 class="accordion-header" id="headingFive">
                                                <button class="accordion-button collapsed" type="button"
                                                    data-bs-toggle="collapse" data-bs-target="#collapseFive"
                                                    aria-expanded="false" aria-controls="collapseFive">
                                                    <h3><strong>Emboss</strong></h3>
                                                </button>
                                            </h2>
                                            <div id="collapseFive" class="accordion-collapse collapse"
                                                aria-labelledby="headingFive" data-bs-parent="#progress_prod">
                                                <div class="accordion-body">
                                                    <div class="alert alert-danger rounded-3 mb-2 mb-5">
                                                        <div class="bold fs-6 text-center"><strong>Deadline
                                                                {{ date('d F Y', strtotime($data->tgl_deadline)) }}</strong>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_awal_pp_5"
                                                                    class="required form-label">Jumlah Awal</label>
                                                                <input type="text" name="jumlah_awal_pp_5"
                                                                    value="{{ old('jumlah_awal_pp_5') ?? $data->jumlah_awal_pp_5 }}"
                                                                    id="jumlah_awal_pp_5" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_hasil_pp_5"
                                                                    class="required form-label">Jumlah Akhir</label>
                                                                <input type="text" name="jumlah_hasil_pp_5"
                                                                    value="{{ old('jumlah_hasil_pp_5') ?? $data->jumlah_awal_pp_5 }}"
                                                                    id="jumlah_hasil_pp_5" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="reject_pp_5"
                                                                    class="required form-label">Reject</label>
                                                                <input type="text" name="reject_pp_5"
                                                                    value="{{ old('reject_pp_5') ?? $data->reject_pp_5 }}"
                                                                    id="reject_pp_5" class="form-control" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="vendor_5"
                                                            class="required form-label">Vendor</label>
                                                        <select name="vendor_5" id="vendor_5"
                                                            class="form-select">
                                                            <option value="{{ $data->vendor_5 }}">
                                                                {{ $data->vendor_5 }}</option>
                                                            <option value="" disabled>-- Pilih Vendor</option>
                                                            @foreach ($vendor as $vlist)
                                                                <option value="{{ $vlist->vendor }}">
                                                                    {{ $vlist->vendor }}</option>
                                                            @endforeach
                                                            <option value="Lainnya">Lainnya</option>
                                                        </select>
                                                        <input type="text" name="vendor_5_lainnya"
                                                            id="vendor_5_lainnya" class="form-control mt-3"
                                                            placeholder="vendor lainnya" />
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="harga_emboss" class="form-label">Harga</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text mt-3">Rp. </span>
                                                            <input type="text" name="harga_emboss"
                                                                id="harga_emboss" class="form-control mt-3"
                                                                value="{{ old('harga_emboss') ?? $data->harga_cetak }}"
                                                                placeholder="Harga Emboss" />
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="tgl_produksi_5"
                                                            class="required form-label">Tanggal
                                                            Mulai</label>
                                                        <input type="date" name="tgl_produksi_5"
                                                            value="{{ old('tgl_produksi_5') ?? $data->tgl_produksi_5 }}"
                                                            id="tgl_produksi_5" class="form-control" />
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="mb-10">
                                                                <label for="pic_validasi_5"
                                                                    class="required form-label">PIC
                                                                    Validasi</label>
                                                                <input type="text" name="pic_validasi_5"
                                                                    value="{{ old('pic_validasi_5') ?? $data->pic_validasi_5 }}"
                                                                    id="pic_validasi_5" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="flag_status_5"
                                                                    class="form-label">Selesai</label>
                                                                <div class="form-check form-switch form-check-custom">
                                                                    <input class="form-check-input"
                                                                        name="flag_status_5"
                                                                        {{ $data->flag_status_5 ? 'checked' : '' }}
                                                                        type="checkbox" value="1"
                                                                        id="flag_status_5" />
                                                                    <label class="form-check-label"
                                                                        for="flexSwitchDefault">Belum/Sudah</label>
                                                                </div>

                                                                <div class="mb-10 mt-10" id="dateFinalFive">
                                                                    <label for="tgl_selesai_5"
                                                                        class="required form-label">Tanggal
                                                                        Selesai</label>
                                                                    {{-- <input type="date" name="tgl_selesai_5"
                                                                            value="{{ old('tgl_selesai_5') ?? $data->tgl_selesai_5 }}"
                                                                            id="tgl_selesai_5" class="form-control"> --}}
                                                                    <div class="alert alert-success">
                                                                        @if ($data->tgl_flag_status_5)
                                                                            {{ $data->tgl_flag_status_5 }}
                                                                        @else
                                                                            {{ date('Y-m-d H:i:s') }}
                                                                        @endif
                                                                    </div>

                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="catatan_5" class="form-label">Catatan</label>
                                                        <textarea type="date" name="catatan_5" rows="3" id="catatan_5" class="form-control">{{ $data->catatan_5 }}</textarea>
                                                    </div>
                                                    <hr>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_5_1 ? 'checked' : '' }}
                                                                    value="1" name="check_5_1"
                                                                    id="check_5_1" />
                                                                <label class="form-check-label" for="check_5_1">
                                                                    Jadwal naik Emboss
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_5_2 ? 'checked' : '' }}
                                                                    value="1" name="check_5_2"
                                                                    id="check_5_2" />
                                                                <label class="form-check-label" for="check_5_2">
                                                                    Klise Emboss
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_5_3 ? 'checked' : '' }}
                                                                    value="1" name="check_5_3"
                                                                    id="check_5_3" />
                                                                <label class="form-check-label" for="check_5_3">
                                                                    Acuan Emboss
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_5_4 ? 'checked' : '' }}
                                                                    value="1" name="check_5_4"
                                                                    id="check_5_4" />
                                                                <label class="form-check-label" for="check_5_4">
                                                                    ACC Emboss
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_5_5 ? 'checked' : '' }}
                                                                    value="1" name="check_5_4"
                                                                    id="check_5_4" />
                                                                <label class="form-check-label" for="check_5_4">
                                                                    Check acak hasil
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item border" id="accordSix">
                                            <h2 class="accordion-header" id="headingSix">
                                                <button class="accordion-button collapsed" type="button"
                                                    data-bs-toggle="collapse" data-bs-target="#collapseSix"
                                                    aria-expanded="false" aria-controls="collapseSix">
                                                    <h3><strong>Spot UV</strong></h3>
                                                </button>
                                            </h2>
                                            <div id="collapseSix" class="accordion-collapse collapse"
                                                aria-labelledby="headingSix" data-bs-parent="#progress_prod">
                                                <div class="accordion-body">
                                                    <div class="alert alert-danger rounded-3 mb-2 mb-5">
                                                        <div class="bold fs-6 text-center"><strong>Deadline
                                                                {{ date('d F Y', strtotime($data->tgl_deadline)) }}</strong>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_awal_pp_6"
                                                                    class="required form-label">Jumlah
                                                                    Awal</label>
                                                                <input type="text" name="jumlah_awal_pp_6"
                                                                    value="{{ old('jumlah_awal_pp_6') ?? $data->jumlah_awal_pp_6 }}"
                                                                    id="jumlah_awal_pp_6" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_hasil_pp_6"
                                                                    class="required form-label">Jumlah
                                                                    Akhir</label>
                                                                <input type="text" name="jumlah_hasil_pp_6"
                                                                    value="{{ old('jumlah_hasil_pp_6') ?? $data->jumlah_hasil_pp_6 }}"
                                                                    id="jumlah_hasil_pp_6" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="reject_pp_6"
                                                                    class="required form-label">Reject</label>
                                                                <input type="text" name="reject_pp_6"
                                                                    value="{{ old('reject_pp_6') ?? $data->reject_pp_6 }}"
                                                                    id="reject_pp_6" class="form-control" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="vendor_6"
                                                            class="required form-label">Vendor</label>
                                                        <select name="vendor_6" id="vendor_6"
                                                            class="form-select">
                                                            <option value="{{ $data->vendor_6 }}">
                                                                {{ $data->vendor_6 }}</option>
                                                            <option value="" disabled>-- Pilih Vendor
                                                            </option>
                                                            @foreach ($vendor as $vlist)
                                                                <option value="{{ $vlist->vendor }}">
                                                                    {{ $vlist->vendor }}</option>
                                                            @endforeach
                                                            <option value="Lainnya">Lainnya</option>
                                                        </select>
                                                        <input type="text" name="vendor_6_lainnya"
                                                            id="vendor_6_lainnya" class="form-control mt-3"
                                                            placeholder="vendor lainnya" />
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="harga_spotuv"
                                                            class="required form-label">Harga</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text mt-3">Rp. </span>
                                                            <input type="text" name="harga_spotuv"
                                                                id="harga_spotuv" class="form-control mt-3"
                                                                value="{{ old('harga_spotuv') ?? $data->harga_spotuv }}"
                                                                placeholder="Harga Spot UV" />
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="tgl_produksi_6"
                                                            class="required form-label">Tanggal
                                                            Mulai</label>
                                                        <input type="date" name="tgl_produksi_6"
                                                            value="{{ old('tgl_produksi_6') ?? $data->tgl_produksi_6 }}"
                                                            id="tgl_produksi_6" class="form-control" />
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="mb-10">
                                                                <label for="pic_validasi_6"
                                                                    class="required form-label">PIC
                                                                    Validasi</label>
                                                                <input type="text" name="pic_validasi_6"
                                                                    value="{{ old('pic_validasi_6') ?? $data->pic_validasi_6 }}"
                                                                    id="pic_validasi_6" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="flag_status_6"
                                                                    class="form-label">Selesai</label>
                                                                <div class="form-check form-switch form-check-custom">
                                                                    <input class="form-check-input"
                                                                        name="flag_status_6"
                                                                        {{ $data->flag_status_6 ? 'checked' : '' }}
                                                                        type="checkbox" value="1"
                                                                        id="flag_status_6" />
                                                                    <label class="form-check-label"
                                                                        for="flexSwitchDefault">Belum/Sudah</label>
                                                                </div>

                                                                <div class="mb-10 mt-10" id="dateFinalSix">
                                                                    <label for="tgl_selesai_6"
                                                                        class="required form-label">Tanggal
                                                                        Selesai</label>
                                                                    {{-- <input type="date" name="tgl_selesai_6"
                                                                            value="{{ old('tgl_selesai_6') ?? $data->tgl_selesai_6 }}"
                                                                            id="tgl_selesai_6" class="form-control"> --}}
                                                                    <div class="alert alert-success">
                                                                        @if ($data->tgl_flag_status_6)
                                                                            {{ $data->tgl_flag_status_6 }}
                                                                        @else
                                                                            {{ date('Y-m-d H:i:s') }}
                                                                        @endif
                                                                    </div>

                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="catatan_6" class="form-label">Catatan</label>
                                                        <textarea type="date" name="catatan_6" rows="3" id="catatan_6" class="form-control">{{ $data->catatan_6 }}</textarea>
                                                    </div>
                                                    <hr>
                                                    <label for="checklist" class="form-label">Checklist</label>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_6_1 ? 'checked' : '' }}
                                                                    value="1" name="check_6_1"
                                                                    id="check_6_1" />
                                                                <label class="form-check-label" for="check_6_1">
                                                                    Jadwal naik spot UV
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_6_2 ? 'checked' : '' }}
                                                                    value="1" name="check_6_2"
                                                                    id="check_6_2" />
                                                                <label class="form-check-label" for="check_6_2">
                                                                    Film Spot UV
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_6_3 ? 'checked' : '' }}
                                                                    value="1" name="check_6_3"
                                                                    id="check_6_3" />
                                                                <label class="form-check-label" for="check_6_3">
                                                                    Cek acak hasil
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item border" id="accordSeven">
                                            <h2 class="accordion-header" id="headingSeven">
                                                <button class="accordion-button collapsed" type="button"
                                                    data-bs-toggle="collapse" data-bs-target="#collapseSeven"
                                                    aria-expanded="false" aria-controls="collapseSeven">
                                                    <h3><strong>Lapis</strong></h3>
                                                </button>
                                            </h2>
                                            <div id="collapseSeven" class="accordion-collapse collapse"
                                                aria-labelledby="headingSeven" data-bs-parent="#progress_prod">
                                                <div class="accordion-body">
                                                    <div class="alert alert-danger rounded-3 mb-2 mb-5">
                                                        <div class="bold fs-6 text-center"><strong>Deadline
                                                                {{ date('d F Y', strtotime($data->tgl_deadline)) }}</strong>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_awal_pp_7"
                                                                    class="required form-label">Jumlah
                                                                    Awal</label>
                                                                <input type="text" name="jumlah_awal_pp_7"
                                                                    value="{{ old('jumlah_awal_pp_7') ?? $data->jumlah_awal_pp_7 }}"
                                                                    id="jumlah_awal_pp_7" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_hasil_pp_7"
                                                                    class="required form-label">Jumlah
                                                                    Akhir</label>
                                                                <input type="text" name="jumlah_hasil_pp_7"
                                                                    value="{{ old('jumlah_hasil_pp_7') ?? $data->jumlah_hasil_pp_7 }}"
                                                                    id="jumlah_hasil_pp_7" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="reject_pp_7"
                                                                    class="required form-label">Reject</label>
                                                                <input type="text" name="reject_pp_7"
                                                                    value="{{ old('reject_pp_7') ?? $data->reject_pp_7 }}"
                                                                    id="reject_pp_7" class="form-control" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="vendor_7"
                                                            class="required form-label">Vendor</label>
                                                        <select name="vendor_7" id="vendor_7"
                                                            class="form-select">
                                                            <option value="{{ $data->vendor_7 }}">
                                                                {{ $data->vendor_7 }}</option>
                                                            <option value="" disabled>-- Pilih Vendor
                                                            </option>
                                                            @foreach ($vendor as $vlist)
                                                                <option value="{{ $vlist->vendor }}">
                                                                    {{ $vlist->vendor }}</option>
                                                            @endforeach
                                                            <option value="Lainnya">Lainnya</option>
                                                        </select>
                                                        <input type="text" name="vendor_7_lainnya"
                                                            id="vendor_7_lainnya" class="form-control mt-3"
                                                            placeholder="vendor lainnya" />
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="harga_lapis"
                                                            class="required form-label">Harga</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text mt-3">Rp. </span>
                                                            <input type="text" name="harga_lapis"
                                                                id="harga_lapis" class="form-control mt-3"
                                                                value="{{ old('harga_lapis') ?? $data->harga_lapis }}"
                                                                placeholder="Harga Lapis" />
                                                        </div>
                                                    </div>

                                                    <div class="mb-10">
                                                        <label for="tgl_produksi_7"
                                                            class="required form-label">Tanggal
                                                            Mulai</label>
                                                        <input type="date" name="tgl_produksi_7"
                                                            value="{{ old('tgl_produksi_7') ?? $data->tgl_produksi_7 }}"
                                                            id="tgl_produksi_7" class="form-control" />
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="mb-10">
                                                                <label for="pic_validasi_7"
                                                                    class="required form-label">PIC
                                                                    Validasi</label>
                                                                <input type="text" name="pic_validasi_7"
                                                                    value="{{ old('pic_validasi_7') ?? $data->pic_validasi_7 }}"
                                                                    id="pic_validasi_7" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="flag_status_7"
                                                                    class="form-label">Selesai</label>
                                                                <div class="form-check form-switch form-check-custom">
                                                                    <input class="form-check-input"
                                                                        name="flag_status_7"
                                                                        {{ $data->flag_status_7 ? 'checked' : '' }}
                                                                        type="checkbox" value="1"
                                                                        id="flag_status_7" />
                                                                    <label class="form-check-label"
                                                                        for="flexSwitchDefault">Belum/Sudah</label>
                                                                </div>

                                                                <div class="mb-10 mt-10" id="dateFinalSeven">
                                                                    <label for="tgl_selesai_7"
                                                                        class="required form-label">Tanggal
                                                                        Selesai</label>
                                                                    {{-- <input type="date" name="tgl_selesai_7"
                                                                            value="{{ old('tgl_selesai_7') ?? $data->tgl_selesai_7 }}"
                                                                            id="tgl_selesai_7" class="form-control"> --}}
                                                                    <div class="alert alert-success">
                                                                        @if ($data->tgl_flag_status_7)
                                                                            {{ $data->tgl_flag_status_7 }}
                                                                        @else
                                                                            {{ date('Y-m-d H:i:s') }}
                                                                        @endif
                                                                    </div>

                                                                </div>

                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="catatan_7" class="form-label">Catatan</label>
                                                        <textarea type="date" name="catatan_7" rows="3" id="catatan_7" class="form-control">{{ $data->catatan_7 }}</textarea>
                                                    </div>
                                                    <hr>
                                                    <label for="checklist" class="form-label">Checklist</label>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_7_1 ? 'checked' : '' }}
                                                                    value="1" name="check_7_1"
                                                                    id="check_7_1" />
                                                                <label class="form-check-label" for="check_7_1">
                                                                    Jadwal naik Lapis
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_7_2 ? 'checked' : '' }}
                                                                    value="1" name="check_7_2"
                                                                    id="check_7_2" />
                                                                <label class="form-check-label" for="check_7_2">
                                                                    Cek acak hasil
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item border" id="accordEight">
                                            <h2 class="accordion-header" id="headingEight">
                                                <button class="accordion-button collapsed" type="button"
                                                    data-bs-toggle="collapse" data-bs-target="#collapseEight"
                                                    aria-expanded="false" aria-controls="collapseEight">
                                                    <h3><strong>Jendela Mika</strong></h3>
                                                </button>
                                            </h2>
                                            <div id="collapseEight" class="accordion-collapse collapse"
                                                aria-labelledby="headingEight" data-bs-parent="#progress_prod">
                                                <div class="accordion-body">
                                                    <div class="alert alert-danger rounded-3 mb-2 mb-5">
                                                        <div class="bold fs-6 text-center"><strong>Deadline
                                                                {{ date('d F Y', strtotime($data->tgl_deadline)) }}</strong>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_awal_pp_8"
                                                                    class="required form-label">Jumlah
                                                                    Awal</label>
                                                                <input type="text" name="jumlah_awal_pp_8"
                                                                    value="{{ old('jumlah_awal_pp_8') ?? $data->jumlah_awal_pp_8 }}"
                                                                    id="jumlah_awal_pp_8" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_hasil_pp_8"
                                                                    class="required form-label">Jumlah
                                                                    Akhir</label>
                                                                <input type="text" name="jumlah_hasil_pp_8"
                                                                    value="{{ old('jumlah_hasil_pp_8') ?? $data->jumlah_hasil_pp_8 }}"
                                                                    id="jumlah_hasil_pp_8" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="reject_pp_8"
                                                                    class="required form-label">Reject</label>
                                                                <input type="text" name="reject_pp_8"
                                                                    value="{{ old('reject_pp_8') ?? $data->reject_pp_8 }}"
                                                                    id="reject_pp_8" class="form-control" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="vendor_8"
                                                            class="required form-label">Vendor</label>
                                                        <select name="vendor_8" id="vendor_8"
                                                            class="form-select">
                                                            <option value="{{ $data->vendor_8 }}">
                                                                {{ $data->vendor_8 }}</option>
                                                            <option value="" disabled>-- Pilih Vendor
                                                            </option>
                                                            @foreach ($vendor as $vlist)
                                                                <option value="{{ $vlist->vendor }}">
                                                                    {{ $vlist->vendor }}</option>
                                                            @endforeach
                                                            <option value="Lainnya">Lainnya</option>
                                                        </select>
                                                        <input type="text" name="vendor_8_lainnya"
                                                            id="vendor_8_lainnya" class="form-control mt-3"
                                                            placeholder="vendor lainnya" />
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="harga_jendelamika"
                                                            class="required form-label">Harga</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text mt-3">Rp. </span>
                                                            <input type="text" name="harga_jendelamika"
                                                                id="harga_jendelamika" class="form-control mt-3"
                                                                value="{{ old('harga_jendelamika') ?? $data->harga_jendelamika }}"
                                                                placeholder="Harga Jendela Mika" />
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="tgl_produksi_8"
                                                            class="required form-label">Tanggal
                                                            Mulai</label>
                                                        <input type="date" name="tgl_produksi_8"
                                                            value="{{ old('tgl_produksi_8') ?? $data->tgl_produksi_8 }}"
                                                            id="tgl_produksi_8" class="form-control" />
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="mb-10">
                                                                <label for="pic_validasi_8"
                                                                    class="required form-label">PIC
                                                                    Validasi</label>
                                                                <input type="text" name="pic_validasi_8"
                                                                    value="{{ old('pic_validasi_8') ?? $data->pic_validasi_8 }}"
                                                                    id="pic_validasi_8" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="flag_status_8"
                                                                    class="form-label">Selesai</label>
                                                                <div class="form-check form-switch form-check-custom">
                                                                    <input class="form-check-input"
                                                                        name="flag_status_8"
                                                                        {{ $data->flag_status_8 ? 'checked' : '' }}
                                                                        type="checkbox" value="1"
                                                                        id="flag_status_8" />
                                                                    <label class="form-check-label"
                                                                        for="flexSwitchDefault">Belum/Sudah</label>
                                                                </div>
                                                                <div class="mb-10 mt-10" id="dateFinalEight">
                                                                    <label for="tgl_selesai_8"
                                                                        class="required form-label">Tanggal
                                                                        Selesai</label>
                                                                    {{-- <input type="date" name="tgl_selesai_8"
                                                                            value="{{ old('tgl_selesai_8') ?? $data->tgl_selesai_8 }}"
                                                                            id="tgl_selesai_8" class="form-control"> --}}
                                                                    <div class="alert alert-success">
                                                                        @if ($data->tgl_flag_status_8)
                                                                            {{ $data->tgl_flag_status_8 }}
                                                                        @else
                                                                            {{ date('Y-m-d H:i:s') }}
                                                                        @endif
                                                                    </div>

                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="catatan_8" class="form-label">Catatan</label>
                                                        <textarea type="date" name="catatan_8" rows="3" id="catatan_8" class="form-control">{{ $data->catatan_8 }}</textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="accordion-item border" id="accordNine">
                                            <h2 class="accordion-header" id="headingNine">
                                                <button class="accordion-button collapsed" type="button"
                                                    data-bs-toggle="collapse" data-bs-target="#collapseNine"
                                                    aria-expanded="false" aria-controls="collapseNine">
                                                    <h3><strong>Pond</strong></h3>

                                                </button>
                                            </h2>
                                            <div id="collapseNine" class="accordion-collapse collapse"
                                                aria-labelledby="headingNine" data-bs-parent="#progress_prod">
                                                <div class="accordion-body">
                                                    <div class="alert alert-danger rounded-3 mb-2 mb-5">
                                                        <div class="bold fs-6 text-center"><strong>Deadline
                                                                {{ date('d F Y', strtotime($data->tgl_deadline)) }}</strong>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_awal_pp_9"
                                                                    class="required form-label">Jumlah
                                                                    Awal</label>
                                                                <input type="text" name="jumlah_awal_pp_9"
                                                                    value="{{ old('jumlah_awal_pp_9') ?? $data->jumlah_awal_pp_9 }}"
                                                                    id="jumlah_awal_pp_9" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_hasil_pp_9"
                                                                    class="required form-label">Jumlah
                                                                    Akhir</label>
                                                                <input type="text" name="jumlah_hasil_pp_9"
                                                                    value="{{ old('jumlah_hasil_pp_9') ?? $data->jumlah_hasil_pp_9 }}"
                                                                    id="jumlah_hasil_pp_9" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="reject_pp_9"
                                                                    class="required form-label">Reject</label>
                                                                <input type="text" name="reject_pp_9"
                                                                    value="{{ old('reject_pp_9') ?? $data->reject_pp_9 }}"
                                                                    id="reject_pp_9" class="form-control" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="vendor_9"
                                                            class="required form-label">Vendor</label>
                                                        <select name="vendor_9" id="vendor_9"
                                                            class="form-select">
                                                            <option value="{{ $data->vendor_9 }}">
                                                                {{ $data->vendor_9 }}</option>
                                                            <option value="" disabled>-- Pilih Vendor
                                                            </option>
                                                            @foreach ($vendor as $vlist)
                                                                <option value="{{ $vlist->vendor }}">
                                                                    {{ $vlist->vendor }}</option>
                                                            @endforeach
                                                            <option value="Lainnya">Lainnya</option>
                                                        </select>
                                                        <input type="text" name="vendor_9_lainnya"
                                                            id="vendor_9_lainnya" class="form-control mt-3"
                                                            placeholder="vendor lainnya" />
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="harga_pond"
                                                            class="required form-label">Harga</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text mt-3">Rp. </span>
                                                            <input type="text" name="harga_pond"
                                                                id="harga_pond" class="form-control mt-3"
                                                                value="{{ old('harga_pond') ?? $data->harga_pond }}"
                                                                placeholder="Harga Pond" />
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="tgl_produksi_9"
                                                            class="required form-label">Tanggal
                                                            Mulai</label>
                                                        <input type="date" name="tgl_produksi_9"
                                                            value="{{ old('tgl_produksi_9') ?? $data->tgl_produksi_9 }}"
                                                            id="tgl_produksi_9" class="form-control" />
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="mb-10">
                                                                <label for="pic_validasi_9"
                                                                    class="required form-label">PIC
                                                                    Validasi</label>
                                                                <input type="text" name="pic_validasi_9"
                                                                    value="{{ old('pic_validasi_9') ?? $data->pic_validasi_9 }}"
                                                                    id="pic_validasi_9" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="flag_status_9"
                                                                    class="form-label">Selesai</label>
                                                                <div class="form-check form-switch form-check-custom">
                                                                    <input class="form-check-input"
                                                                        name="flag_status_9"
                                                                        {{ $data->flag_status_9 ? 'checked' : '' }}
                                                                        type="checkbox" value="1"
                                                                        id="flag_status_9" />
                                                                    <label class="form-check-label"
                                                                        for="flexSwitchDefault">Belum/Sudah</label>
                                                                </div>
                                                                <div class="mb-10 mt-10" id="dateFinalNine">
                                                                    <label for="tgl_selesai_9"
                                                                        class="required form-label">Tanggal
                                                                        Selesai</label>
                                                                    {{-- <input type="date" name="tgl_selesai_9"
                                                                            value="{{ old('tgl_selesai_9') ?? $data->tgl_selesai_9 }}"
                                                                            id="tgl_selesai_9" class="form-control"> --}}
                                                                    <div class="alert alert-success">
                                                                        @if ($data->tgl_flag_status_9)
                                                                            {{ $data->tgl_flag_status_9 }}
                                                                        @else
                                                                            {{ date('Y-m-d H:i:s') }}
                                                                        @endif
                                                                    </div>

                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="catatan_9" class="form-label">Catatan</label>
                                                        <textarea type="date" name="catatan_9" rows="3" id="catatan_9" class="form-control">{{ $data->catatan_9 }}</textarea>
                                                    </div>
                                                    <hr>
                                                    <label for="checklist" class="form-label">Checklist</label>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_9_1 ? 'checked' : '' }}
                                                                    value="1" name="check_9_1"
                                                                    id="check_9_1" />
                                                                <label class="form-check-label" for="check_9_1">
                                                                    Jadwal naik pond
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_9_2 ? 'checked' : '' }}
                                                                    value="1" name="check_9_2"
                                                                    id="check_9_2" />
                                                                <label class="form-check-label" for="check_9_2">
                                                                    Pisau pond
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_9_3 ? 'checked' : '' }}
                                                                    value="1" name="check_9_3"
                                                                    id="check_9_3" />
                                                                <label class="form-check-label" for="check_9_3">
                                                                    Acuan pond
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_9_4 ? 'checked' : '' }}
                                                                    value="1" name="check_9_4"
                                                                    id="check_9_4" />
                                                                <label class="form-check-label" for="check_9_4">
                                                                    ACC pond
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_9_5 ? 'checked' : '' }}
                                                                    value="1" name="check_9_5"
                                                                    id="check_9_5" />
                                                                <label class="form-check-label" for="check_9_5">
                                                                    Cek acak hasil
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item border" id="accordTen">
                                            <h2 class="accordion-header" id="headingTen">
                                                <button class="accordion-button collapsed" type="button"
                                                    data-bs-toggle="collapse" data-bs-target="#collapseTen"
                                                    aria-expanded="false" aria-controls="collapseTen">
                                                    <h3><strong>Finishing</strong></h3>
                                                </button>
                                            </h2>
                                            <div id="collapseTen" class="accordion-collapse collapse"
                                                aria-labelledby="headingTen" data-bs-parent="#progress_prod">
                                                <div class="accordion-body">
                                                    <div class="alert alert-danger rounded-3 mb-2 mb-5">
                                                        <div class="bold fs-6 text-center"><strong>Deadline
                                                                {{ date('d F Y', strtotime($data->tgl_deadline)) }}</strong>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_awal_pp_10"
                                                                    class="required form-label">Jumlah
                                                                    Awal</label>
                                                                <input type="text" name="jumlah_awal_pp_10"
                                                                    value="{{ old('jumlah_awal_pp_10') ?? $data->jumlah_awal_pp_10 }}"
                                                                    id="jumlah_awal_pp_10" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="jumlah_hasil_pp_10"
                                                                    class="required form-label">Jumlah
                                                                    Akhir</label>
                                                                <input type="text" name="jumlah_hasil_pp_10"
                                                                    value="{{ old('jumlah_hasil_pp_10') ?? $data->jumlah_hasil_pp_10 }}"
                                                                    id="jumlah_hasil_pp_10" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="reject_pp_10"
                                                                    class="required form-label">Reject</label>
                                                                <input type="text" name="reject_pp_10"
                                                                    value="{{ old('reject_pp_10') ?? $data->reject_pp_10 }}"
                                                                    id="reject_pp_10" class="form-control" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="vendor_10"
                                                            class="required form-label">Vendor</label>
                                                        <select name="vendor_10" id="vendor_10"
                                                            class="form-select">
                                                            <option value="{{ $data->vendor_10 }}">
                                                                {{ $data->vendor_10 }}</option>
                                                            <option value="" disabled>-- Pilih Vendor
                                                            </option>
                                                            @foreach ($vendor as $vlist)
                                                                <option value="{{ $vlist->vendor }}">
                                                                    {{ $vlist->vendor }}</option>
                                                            @endforeach
                                                            <option value="Lainnya">Lainnya</option>
                                                        </select>
                                                        <input type="text" name="vendor_10_lainnya"
                                                            id="vendor_10_lainnya" class="form-control mt-3"
                                                            placeholder="vendor lainnya" />
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="harga_finishing"
                                                            class="required form-label">Harga</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text mt-3">Rp. </span>
                                                            <input type="text" name="harga_finishing"
                                                                id="harga_finishing" class="form-control mt-3"
                                                                value="{{ old('harga_finishing') ?? $data->harga_finishing }}"
                                                                placeholder="Harga Finishing" />
                                                        </div>
                                                    </div>

                                                    <div class="mb-10">
                                                        <label for="tgl_produksi_10"
                                                            class="required form-label">Tanggal
                                                            Mulai</label>
                                                        <input type="date" name="tgl_produksi_10"
                                                            value="{{ old('tgl_produksi_10') ?? $data->tgl_produksi_10 }}"
                                                            id="tgl_produksi_10" class="form-control" />
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="mb-10">
                                                                <label for="pic_validasi_10"
                                                                    class="required form-label">PIC
                                                                    Validasi</label>
                                                                <input type="text" name="pic_validasi_10"
                                                                    value="{{ old('pic_validasi_10') ?? $data->pic_validasi_10 }}"
                                                                    id="pic_validasi_10" class="form-control" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="mb-10">
                                                                <label for="flag_status_10"
                                                                    class="form-label">Selesai</label>
                                                                <div class="form-check form-switch form-check-custom">
                                                                    <input class="form-check-input"
                                                                        name="flag_status_10"
                                                                        {{ $data->flag_status_10 ? 'checked' : '' }}
                                                                        type="checkbox" value="1"
                                                                        id="flag_status_10" />
                                                                    <label class="form-check-label"
                                                                        for="flexSwitchDefault">Belum/Sudah</label>
                                                                </div>
                                                                <div class="mb-10 mt-10" id="dateFinalTen">
                                                                    <label for="tgl_selesai_10"
                                                                        class="required form-label">Tanggal
                                                                        Selesai</label>
                                                                    {{-- <input type="date" name="tgl_selesai_10"
                                                                            value="{{ old('tgl_selesai_10') ?? $data->tgl_selesai_10 }}"
                                                                            id="tgl_selesai_10"
                                                                            class="form-control"> --}}
                                                                    <div class="alert alert-success">
                                                                        @if ($data->tgl_flag_status_10)
                                                                            {{ $data->tgl_flag_status_10 }}
                                                                        @else
                                                                            {{ date('Y-m-d H:i:s') }}
                                                                        @endif
                                                                    </div>

                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="mb-10">
                                                        <label for="catatan_10" class="form-label">Catatan</label>
                                                        <textarea type="date" name="catatan_10" rows="3" id="catatan_10" class="form-control">{{ $data->catatan_10 }}</textarea>
                                                    </div>
                                                    <hr>
                                                    <label for="checklist" class="form-label">Checklist</label>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_10_1 ? 'checked' : '' }}
                                                                    value="1" name="check_10_1"
                                                                    id="check_10_1" />
                                                                <label class="form-check-label" for="check_10_1">
                                                                    Jadwal naik finishing
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_10_2 ? 'checked' : '' }}
                                                                    value="1" name="check_10_2"
                                                                    id="check_10_2" />
                                                                <label class="form-check-label" for="check_10_2">
                                                                    Cek acak/ sortir hasil
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_10_3 ? 'checked' : '' }}
                                                                    value="1" name="check_10_3"
                                                                    id="check_10_3" />
                                                                <label class="form-check-label" for="check_10_3">
                                                                    Packing
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_10_4 ? 'checked' : '' }}
                                                                    value="1" name="check_10_4"
                                                                    id="check_10_4" />
                                                                <label class="form-check-label" for="check_10_4">
                                                                    Label QC
                                                                </label>
                                                            </div>
                                                            <div class="form-check form-check-custom mb-2">
                                                                <input class="form-check-input" type="checkbox"
                                                                    {{ $data->check_10_5 ? 'checked' : '' }}
                                                                    value="1" name="check_10_5"
                                                                    id="check_10_5" />
                                                                <label class="form-check-label" for="check_10_5">
                                                                    Surat Jalan
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="border mb-10" id="accordVendorEks">
                                <div style="background-color: #e4620c; height: 67px; padding: 19.5px;">
                                    <h3 class="text-light"><strong>Vendor Eksternal</strong></h3>
                                </div>
                                <div class="p-5">
                                    <div class="alert alert-danger rounded-3 mb-2 mb-5">
                                        <div class="bold fs-6 text-center"><strong>Deadline
                                                {{ date('d F Y', strtotime($data->tgl_deadline)) }}</strong>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-10">
                                                <label for="jumlah_awal_pp_ve"
                                                    class="required form-label">Jumlah Awal</label>
                                                <input type="text" name="jumlah_awal_pp_ve"
                                                    value="{{ old('jumlah_awal_pp_ve') ?? $data->jumlah_awal_pp_ve }}"
                                                    id="jumlah_awal_pp_ve" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-10">
                                                <label for="jumlah_hasil_pp_ve"
                                                    class="required form-label">Jumlah Akhir</label>
                                                <input type="text" name="jumlah_hasil_pp_ve"
                                                    value="{{ old('jumlah_hasil_pp_ve') ?? $data->jumlah_hasil_pp_ve }}"
                                                    id="jumlah_hasil_pp_ve" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-10">
                                                <label for="reject_pp_ve"
                                                    class="required form-label">Reject</label>
                                                <input type="text" name="reject_pp_ve"
                                                    value="{{ old('reject_pp_ve') ?? $data->reject_pp_ve }}"
                                                    id="reject_pp_ve" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-10">
                                        <label for="vendor_ve"
                                            class="required form-label">Nama Vendor
                                        </label>
                                        <input type="text" name="vendor_ve" id="vendor_ve" class="form-control mt-3" placeholder="vendor lainnya" value="{{ old('vendor_ve') ?? $data->vendor_ve }}" />
                                    </div>

                                    <div class="mb-10">
                                        <label for="harga_ve" class="form-label">Harga</label>
                                        <div class="input-group">
                                            <span class="input-group-text mt-3">Rp. </span>
                                            <input type="text" name="harga_ve" id="harga_ve"
                                                class="form-control mt-3"
                                                value="{{ old('harga_ve') ?? $data->harga_ve }}"
                                                placeholder="Harga Vendor Eksternal" />
                                        </div>
                                    </div>

                                    <div class="mb-10">
                                        <label for="tgl_produksi_ve"
                                            class="required form-label">Tanggal
                                            Mulai</label>
                                        <input type="date" name="tgl_produksi_ve"
                                            value="{{ old('tgl_produksi_ve') ?? $data->tgl_produksi_ve }}"
                                            id="tgl_produksi_ve" class="form-control" />
                                    </div>
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="mb-10">
                                                <label for="pic_validasi_ve"
                                                    class="required form-label">PIC Validasi</label>
                                                <input type="text" name="pic_validasi_ve"
                                                    value="{{ old('pic_validasi_ve') ?? $data->pic_validasi_ve }}"
                                                    id="pic_validasi_ve" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-10">
                                                <label for="flag_status_ve"
                                                    class="form-label">Selesai</label>
                                                <div class="form-check form-switch form-check-custom">
                                                    <input class="form-check-input"
                                                        name="flag_status_ve"
                                                        {{-- {{ $data->flag_status_ve ? 'checked' : '' }} --}}
                                                        type="checkbox" value="1"
                                                        id="flag_status_ve" />
                                                    <label class="form-check-label"
                                                        for="flexSwitchDefault">Belum/Sudah</label>
                                                </div>

                                                <div class="mb-10 mt-10" id="dateFinalVe">
                                                    <label for="tgl_selesai_ve"
                                                        class="required form-label">Tanggal
                                                        Selesai</label>
                                                    {{-- <input type="date" name="tgl_selesai_ve"
                                                        value="{{ old('tgl_selesai_ve') ?? $data->tgl_flag_status_ve }}"
                                                        id="tgl_selesai_ve" class="form-control"> --}}
                                                    <div class="alert alert-success">
                                                        @if ($data->tgl_flag_status_ve)
                                                            {{ $data->tgl_flag_status_ve }}
                                                        @else
                                                            {{ date('Y-m-d H:i:s') }}
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-10">
                                        <label for="catatan_2" class="form-label">Catatan</label>
                                        <textarea type="date" name="catatan_2" rows="3" id="catatan_2" class="form-control">{{ $data->catatan_2 }}</textarea>
                                    </div>
                                    <hr>
                                    <label for="checklist" class="form-label">Checklist</label>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check form-check-custom mb-2">
                                                <input class="form-check-input" type="checkbox"
                                                    {{ $data->check_2_1 ? 'checked' : '' }}
                                                    value="1" name="check_2_1" id="check_2_1" />
                                                <label class="form-check-label" for="check_2_1">
                                                    Acc Cetak
                                                </label>
                                            </div>
                                            <div class="form-check form-check-custom mb-2">
                                                <input class="form-check-input" type="checkbox"
                                                    {{ $data->check_2_2 ? 'checked' : '' }}
                                                    value="1" name="check_2_2" id="check_2_2" />
                                                <label class="form-check-label" for="check_2_2">
                                                    Jaga warna
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check form-check-custom mb-2">
                                                <input class="form-check-input" type="checkbox"
                                                    {{ $data->check_2_3 ? 'checked' : '' }}
                                                    value="1" name="check_2_3" id="check_2_3" />
                                                <label class="form-check-label" for="check_2_3">
                                                    Hasil didiamkan satu hari
                                                </label>
                                            </div>
                                            <div class="form-check form-check-custom mb-2">
                                                <input class="form-check-input" type="checkbox"
                                                    {{ $data->check_2_4 ? 'checked' : '' }}
                                                    value="1" name="check_2_4" id="check_2_4" />
                                                <label class="form-check-label" for="check_2_4">
                                                    Cek acak hasil cetak
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="catatan_khusus" class="required form-label">Status Produksi</label>
                                <select name="catatan_khusus" id="catatan_khusus" class="form-select">
                                    @php
                                        $options = ['Belum SPK', 'Berjalan', 'Tuntas', 'Reject'];
                                    @endphp

                                    @foreach ($options as $option)
                                        @if ($data->catatan_khusus == $option)
                                            <option value="{{ $data->catatan_khusus }}" selected>{{ $data->catatan_khusus }}</option>
                                        @else
                                            <option value="{{ $option }}">{{ $option }}</option>
                                        @endif
                                    @endforeach
                                </select>
                            </div>
                            <div id="prod_done_form">
                                <div class="fv-row mb-10">
                                    <label for="jumlah_fix" class="required form-label">Jumlah Akhir Produksi</label>
                                    <input type="number" name="jumlah_fix"
                                        value="{{ old('jumlah_fix') ?? $data->jumlah_fix }}" id="jumlah_fix"
                                        class="form-control" />
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="tgl_selesai_all" class="required form-label">Tanggal Selesai
                                        Keseluruhan</label>
                                    <input type="date" name="tgl_selesai_all" id="tgl_selesai_all"
                                        class="form-control mt-3"
                                        value="{{ old('tgl_selesai_all') ?? $data->tgl_selesai_all }}" />
                                </div>
                                <div class="mb-10">
                                    <label for="total_keseluruhan_harga" class="form-label">Harga
                                        Total</label>
                                    <div class="input-group">
                                        <span class="input-group-text mt-3">Rp. </span>
                                        <input type="text" name="total_keseluruhan_harga"
                                            id="total_keseluruhan_harga" class="form-control mt-3"
                                            style="background-color: #f5f8fa"
                                            value="{{ old('total_keseluruhan_harga') ?? $data->total_keseluruhan_harga }}"
                                            placeholder="Harga Total" readonly />
                                    </div>
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="tgl_kirim" class="form-label">Tanggal Kirim</label>
                                    <input type="date" value="{{ old('tgl_kirim') ?? $data->tgl_kirim }}"
                                        name="tgl_kirim" id="tgl_kirim" class="form-control" />
                                </div>
                            </div>
                            @if (Auth::user()->roles == 'PRODUKSI SPV' || Auth::user()->roles == 'SUPERADMIN')
                                <input type="submit" name="update_spk" form="form-direct-order"
                                    value="Export SPK" class="btn my-primary float-end mx-3 text-white">
                            @endif
                            <button type="submit" id="update_spk_btn"  class="btn my-primary float-end text-white">Submit</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
