<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ url()->previous() }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">Export SPK</h3>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        {{-- @include('pages.production.data_jscript_edit_production') --}}
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-body pt-0">
                <div class="row g-5 g-xl-8 mb-5 mt-5 d-flex">
                    <div class="col-md-6">
                        <h3>Surat Perintah Kerja</h3>
                    </div>
                    <div class="col-md-6">
                        <a href="{{ route('production.generate_spk', $enc_sko_key) }}" class="btn btn-primary text-white float-end"><i class="fa-regular fa-file-word"></i> Download</a>
                    </div>
                </div>
                <div class="row g-5 g-xl-8">
                    <div class="col-md-12">
                        @if(!empty($data_spk->progress_produksi_1))
                        <div class="table-responsive mb-3" id="spk-pracetak">
                            <table class="table table-bordered table-rounded table-striped border gy-7 gs-7">
                                <thead class="fw-bolder">
                                  <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Jumlah Plano</th>
                                    <th scope="col">Vendor</th>
                                    <th scope="col">Tgl produksi</th>
                                    <th scope="col">PIC</th>
                                    <th scope="col">Checklist 1</th>
                                    <th scope="col">Checklist 2</th>
                                    <th scope="col">Checklist 3</th>
                                    <th scope="col">Checklist 4</th>
                                    <th scope="col">Selesai</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <th rowspan="2" scope="row" style="vertical-align : middle;" class="fw-bolder text-align-center">Pra Cetak</th>
                                    <td>2</td>
                                    <td>{{ $data_spk->vendor_1 }}</td>
                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_1)) }}</td>
                                    <td>{{ $data_spk->pic_validasi_1 }}
                                    <td>{!! $data_spk->check_1_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_1_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_1_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_1_4 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->flag_status_1 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                  </tr>
                                  <tr>
                                    <th class="fw-bolder">Catatan :</th>
                                    <td colspan="8">{!! $data_spk->catatan_1 ? $data_spk->catatan_1 : '-' !!}</td>
                                  </tr>
                                </tbody>
                              </table>
                        </div>
                        @endif

                        @if(!empty($data_spk->progress_produksi_2))
                        <div class="table-responsive mb-3" id="spk-cetak">
                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                <thead class="fw-bolder">
                                  <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Jumlah Awal</th>
                                    <th scope="col">Jumlah Akhir</th>
                                    <th scope="col">Jumlah Reject</th>
                                    <th scope="col">Vendor</th>
                                    <th scope="col">Tgl produksi</th>
                                    <th scope="col">PIC</th>
                                    <th scope="col">Checklist 1</th>
                                    <th scope="col">Checklist 2</th>
                                    <th scope="col">Checklist 3</th>
                                    <th scope="col">Checklist 4</th>
                                    <th scope="col">Selesai</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <th rowspan="2" style="vertical-align : middle;" scope="row" class="fw-bolder">Cetak</th>
                                    <td>{{ $data_spk->jumlah_awal_pp_2 }}</td>
                                    <td>{{ $data_spk->jumlah_hasil_pp_2 }}</td>
                                    <td>{{ $data_spk->reject_pp_2 }}</td>
                                    <td>{{ $data_spk->vendor_2 }}</td>
                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_2)) }}</td>
                                    <td>{{ $data_spk->pic_validasi_2 }}
                                    <td>{!! $data_spk->check_2_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_2_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_2_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_2_4 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->flag_status_2 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                  </tr>
                                  <tr>
                                    <th class="fw-bolder">Catatan :</th>
                                    <td colspan="10">{!! $data_spk->catatan_2 ? $data_spk->catatan_2 : '-' !!}</td>
                                  </tr>
                                </tbody>
                              </table>
                        </div>
                        @endif

                        @if(!empty($data_spk->progress_produksi_3))
                        <div class="table-responsive mb-3" id="spk-laminasi">
                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                <thead class="fw-bolder">
                                  <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Jumlah Awal</th>
                                    <th scope="col">Jumlah Akhir</th>
                                    <th scope="col">Jumlah Reject</th>
                                    <th scope="col">Vendor</th>
                                    <th scope="col">Tgl produksi</th>
                                    <th scope="col">PIC</th>
                                    <th scope="col">Checklist 1</th>
                                    <th scope="col">Checklist 2</th>
                                    <th scope="col">Checklist 3</th>
                                    <th scope="col">Selesai</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <th scope="row" rowspan="2" style="vertical-align : middle;" class="fw-bolder">Laminasi</th>
                                    <td>{{ $data_spk->jumlah_awal_pp_3 }}</td>
                                    <td>{{ $data_spk->jumlah_hasil_pp_3 }}</td>
                                    <td>{{ $data_spk->reject_pp_3 }}</td>
                                    <td>{{ $data_spk->vendor_3 }}</td>
                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_3)) }}</td>
                                    <td>{{ $data_spk->pic_validasi_3 }}
                                    <td>{!! $data_spk->check_3_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_3_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_3_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->flag_status_3 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                  </tr>
                                  <tr>
                                    <th class="fw-bolder">Catatan :</th>
                                    <td colspan="9">{!! $data_spk->catatan_3 ? $data_spk->catatan_3 : '-' !!}</td>
                                  </tr>
                                </tbody>
                              </table>
                        </div>
                        @endif

                        @if(!empty($data_spk->progress_produksi_4))
                        <div class="table-responsive mb-3" id="spk-poly">
                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                <thead class="fw-bolder">
                                  <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Jumlah Awal</th>
                                    <th scope="col">Jumlah Akhir</th>
                                    <th scope="col">Jumlah Reject</th>
                                    <th scope="col">Vendor</th>
                                    <th scope="col">Tgl produksi</th>
                                    <th scope="col">PIC</th>
                                    <th scope="col">Checklist 1</th>
                                    <th scope="col">Checklist 2</th>
                                    <th scope="col">Checklist 3</th>
                                    <th scope="col">Checklist 4</th>
                                    <th scope="col">Checklist 5</th>
                                    <th scope="col">Selesai</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <th scope="row" rowspan="2" style="vertical-align : middle;" class="fw-bolder">Poly</th>
                                    <td>{{ $data_spk->jumlah_awal_pp_4 }}</td>
                                    <td>{{ $data_spk->jumlah_hasil_pp_4 }}</td>
                                    <td>{{ $data_spk->reject_pp_4 }}</td>
                                    <td>{{ $data_spk->vendor_4 }}</td>
                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_4)) }}</td>
                                    <td>{{ $data_spk->pic_validasi_4 }}
                                    <td>{!! $data_spk->check_4_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_4_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_4_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_4_4 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_4_5 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->flag_status_4 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                  </tr>
                                  <tr>
                                    <th class="fw-bolder">Catatan :</th>
                                    <td colspan="11">{!! $data_spk->catatan_4 ? $data_spk->catatan_4 : '-' !!}</td>
                                  </tr>
                                </tbody>
                              </table>
                        </div>
                        @endif

                        @if(!empty($data_spk->progress_produksi_5))
                        <div class="table-responsive mb-3" id="spk-emboss">
                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                <thead class="fw-bolder">
                                  <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Jumlah Awal</th>
                                    <th scope="col">Jumlah Akhir</th>
                                    <th scope="col">Jumlah Reject</th>
                                    <th scope="col">Vendor</th>
                                    <th scope="col">Tgl produksi</th>
                                    <th scope="col">PIC</th>
                                    <th scope="col">Checklist 1</th>
                                    <th scope="col">Checklist 2</th>
                                    <th scope="col">Checklist 3</th>
                                    <th scope="col">Checklist 4</th>
                                    <th scope="col">Checklist 5</th>
                                    <th scope="col">Selesai</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <th scope="row" rowspan="2" style="vertical-align : middle;" class="fw-bolder">Emboss</th>
                                    <td>{{ $data_spk->jumlah_awal_pp_5 }}</td>
                                    <td>{{ $data_spk->jumlah_hasil_pp_5 }}</td>
                                    <td>{{ $data_spk->reject_pp_5 }}</td>
                                    <td>{{ $data_spk->vendor_5 }}</td>
                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_5)) }}</td>
                                    <td>{{ $data_spk->pic_validasi_5 }}
                                    <td>{!! $data_spk->check_5_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_5_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_5_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_5_4 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_5_5 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->flag_status_5 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                  </tr>
                                  <tr>
                                    <th class="fw-bolder">Catatan :</th>
                                    <td colspan="11">{!! $data_spk->catatan_5 ? $data_spk->catatan_5 : '-' !!}</td>
                                  </tr>
                                </tbody>
                              </table>
                        </div>
                        @endif

                        @if(!empty($data_spk->progress_produksi_6))
                        <div class="table-responsive mb-3" id="spk-spotuv">
                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                <thead class="fw-bolder">
                                  <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Jumlah Awal</th>
                                    <th scope="col">Jumlah Akhir</th>
                                    <th scope="col">Jumlah Reject</th>
                                    <th scope="col">Vendor</th>
                                    <th scope="col">Tgl produksi</th>
                                    <th scope="col">PIC</th>
                                    <th scope="col">Checklist 1</th>
                                    <th scope="col">Checklist 2</th>
                                    <th scope="col">Checklist 3</th>
                                    <th scope="col">Selesai</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <th scope="row" rowspan="2" style="vertical-align : middle;" class="fw-bolder">Spot UV</th>
                                    <td>{{ $data_spk->jumlah_awal_pp_6 }}</td>
                                    <td>{{ $data_spk->jumlah_hasil_pp_6 }}</td>
                                    <td>{{ $data_spk->reject_pp_6 }}</td>
                                    <td>{{ $data_spk->vendor_6 }}</td>
                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_6)) }}</td>
                                    <td>{{ $data_spk->pic_validasi_6 }}
                                    <td>{!! $data_spk->check_6_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_6_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_6_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->flag_status_6 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                  </tr>
                                  <tr>
                                    <th class="fw-bolder">Catatan :</th>
                                    <td colspan="9">{!! $data_spk->catatan_6 ? $data_spk->catatan_6 : '-' !!}</td>
                                  </tr>
                                </tbody>
                              </table>
                        </div>
                        @endif
                        
                        @if(!empty($data_spk->progress_produksi_7))
                        <div class="table-responsive mb-3" id="spk-lapis">
                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                <thead class="fw-bolder">
                                  <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Jumlah Awal</th>
                                    <th scope="col">Jumlah Akhir</th>
                                    <th scope="col">Jumlah Reject</th>
                                    <th scope="col">Vendor</th>
                                    <th scope="col">Tgl produksi</th>
                                    <th scope="col">PIC</th>
                                    <th scope="col">Checklist 1</th>
                                    <th scope="col">Checklist 2</th>
                                    <th scope="col">Selesai</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <th scope="row" rowspan="2" style="vertical-align : middle;" class="fw-bolder">Lapis</th>
                                    <td>{{ $data_spk->jumlah_awal_pp_7 }}</td>
                                    <td>{{ $data_spk->jumlah_hasil_pp_7 }}</td>
                                    <td>{{ $data_spk->reject_pp_7 }}</td>
                                    <td>{{ $data_spk->vendor_7 }}</td>
                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_7)) }}</td>
                                    <td>{{ $data_spk->pic_validasi_7 }}
                                    <td>{!! $data_spk->check_7_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_7_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->flag_status_7 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                  </tr>
                                  <tr>
                                    <th class="fw-bolder">Catatan :</th>
                                    <td colspan="8">{!! $data_spk->catatan_7 ? $data_spk->catatan_7 : '-' !!}</td>
                                  </tr>
                                </tbody>
                              </table>
                        </div>
                        @endif

                        @if(!empty($data_spk->progress_produksi_8))
                        <div class="table-responsive mb-3" id="spk-jendelamika">
                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                <thead class="fw-bolder">
                                  <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Jumlah Awal</th>
                                    <th scope="col">Jumlah Akhir</th>
                                    <th scope="col">Jumlah Reject</th>
                                    <th scope="col">Vendor</th>
                                    <th scope="col">Tgl produksi</th>
                                    <th scope="col">PIC</th>
                                    <th scope="col">Selesai</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <th scope="row" rowspan="2" style="vertical-align : middle;" class="fw-bolder">Jendela Mika</th>
                                    <td>{{ $data_spk->jumlah_awal_pp_8 }}</td>
                                    <td>{{ $data_spk->jumlah_hasil_pp_8 }}</td>
                                    <td>{{ $data_spk->reject_pp_8 }}</td>
                                    <td>{{ $data_spk->vendor_8 }}</td>
                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_8)) }}</td>
                                    <td>{{ $data_spk->pic_validasi_8 }}
                                    <td>{!! $data_spk->flag_status_8 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                  </tr>
                                  <tr>
                                    <th class="fw-bolder">Catatan :</th>
                                    <td colspan="6">{!! $data_spk->catatan_8 ? $data_spk->catatan_8 : '-' !!}</td>
                                  </tr>
                                </tbody>
                              </table>
                        </div>
                        @endif

                        @if(!empty($data_spk->progress_produksi_9))
                        <div class="table-responsive mb-3" id="spk-pond">
                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                <thead class="fw-bolder">
                                  <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Jumlah Awal</th>
                                    <th scope="col">Jumlah Akhir</th>
                                    <th scope="col">Jumlah Reject</th>
                                    <th scope="col">Vendor</th>
                                    <th scope="col">Tgl produksi</th>
                                    <th scope="col">PIC</th>
                                    <th scope="col">Checklist 1</th>
                                    <th scope="col">Checklist 2</th>
                                    <th scope="col">Checklist 3</th>
                                    <th scope="col">Checklist 4</th>
                                    <th scope="col">Checklist 5</th>
                                    <th scope="col">Selesai</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <th scope="row" rowspan="2" style="vertical-align : middle;" class="fw-bolder">Pond</th>
                                    <td>{{ $data_spk->jumlah_awal_pp_9 }}</td>
                                    <td>{{ $data_spk->jumlah_hasil_pp_9 }}</td>
                                    <td>{{ $data_spk->reject_pp_9 }}</td>
                                    <td>{{ $data_spk->vendor_9 }}</td>
                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_9)) }}</td>
                                    <td>{{ $data_spk->pic_validasi_9 }}
                                    <td>{!! $data_spk->check_9_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_9_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_9_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_9_4 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_9_5 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->flag_status_9 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                  </tr>
                                  <tr>
                                    <th class="fw-bolder">Catatan :</th>
                                    <td colspan="11">{!! $data_spk->catatan_9 ? $data_spk->catatan_9: '-' !!}</td>
                                  </tr>
                                </tbody>
                              </table>
                        </div>
                        @endif

                        @if(!empty($data_spk->progress_produksi_10))
                        <div class="table-responsive mb-3" id="spk-finishing">
                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                <thead class="fw-bolder">
                                  <tr>
                                    <th scope="col">#</th>
                                    <th scope="col">Jumlah Awal</th>
                                    <th scope="col">Jumlah Akhir</th>
                                    <th scope="col">Jumlah Reject</th>
                                    <th scope="col">Vendor</th>
                                    <th scope="col">Tgl produksi</th>
                                    <th scope="col">PIC</th>
                                    <th scope="col">Checklist 1</th>
                                    <th scope="col">Checklist 2</th>
                                    <th scope="col">Checklist 3</th>
                                    <th scope="col">Checklist 4</th>
                                    <th scope="col">Checklist 5</th>
                                    <th scope="col">Selesai</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  <tr>
                                    <th scope="row" rowspan="2" style="vertical-align : middle;" class="fw-bolder">Finishing</th>
                                    <td>{{ $data_spk->jumlah_awal_pp_10 }}</td>
                                    <td>{{ $data_spk->jumlah_hasil_pp_10 }}</td>
                                    <td>{{ $data_spk->reject_pp_10 }}</td>
                                    <td>{{ $data_spk->vendor_10 }}</td>
                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_10)) }}</td>
                                    <td>{{ $data_spk->pic_validasi_10 }}
                                    <td>{!! $data_spk->check_10_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_10_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_10_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_10_4 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->check_10_5 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                    <td>{!! $data_spk->flag_status_10 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                  </tr>
                                  <tr>
                                    <th class="fw-bolder">Catatan :</th>
                                    <td colspan="11">{!! $data_spk->catatan_10 ? $data_spk->catatan_10: '-' !!}</td>
                                  </tr>
                                </tbody>
                              </table>
                        </div>
                        @endif

                    </div>
                </div>

            </div>
        </div>
    </div>
</x-app-layout>