<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ url()->previous() }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">Dummy <PERSON></h3>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        @include('pages.production.data_jscript_dummy')
    </x-slot>

    <div class="container-xxl mt-20" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <!--begin::Search-->
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                        transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                    <path
                                        d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                        fill="black"></path>
                                </svg>
                            </span>
                            <!--end::Svg Icon-->
                            <input type="text" data-kt-docs-table-filter="search-production"
                                class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                        </div>
                        <!--end::Search-->
                        <!--begin::Toolbar-->
                        <div class="d-flex gap-3">
                            <div>
                                <select name="name" class="form-select form-select-solid fw-bold filter-produksi-progress">
                                    <option hidden>Progress Produksi</option>
                                    <option value="">All</option>
                                    <option value="Belum Berjalan">Belum Berjalan</option>
                                    <option value="Pra Cetak">Pra Cetak</option>
                                    <option value="Cetak">Cetak</option>
                                    <option value="Laminasi">Laminasi</option>
                                    <option value="Poly">Poly</option>
                                    <option value="Emboss">Emboss</option>
                                    <option value="Spot UV">Spot UV</option>
                                    <option value="Jendela Mika">Jendela Mika</option>
                                    <option value="Pond">Pond</option>
                                    <option value="Finishing">Finishing</option>
                                    <option disabled>-------------------</option>
                                    <option value="Dummy">Dummy</option>
                                    <option value="Vendor Eks">Vendor Eksternal</option>
                                </select>
                            </div>
                            <!--end::Toolbar-->
                            <div>
                                <button type="button" class="btn btn-light-primary" data-kt-menu-trigger="click"
                                    data-kt-menu-placement="bottom-end">
                                    <span class="svg-icon svg-icon-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                            fill="none">
                                            <path
                                                d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                                                fill="black"></path>
                                        </svg>
                                    </span>
                                    Filter
                                </button>
                                <div class="menu menu-sub menu-sub-dropdown w-400px w-md-625px" data-kt-menu="true"
                                    id="kt-toolbar-filter-produksi">
                                    <div class="px-7 py-5">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="mb-10">
                                                    <label class="form-label fs-5 fw-bold mb-3">Tanggal Deadline</label>
                                                    <div class="row align-items-start">
                                                        <div class="col-md-6">
                                                            <input type="date" id="filter-dummy-from" name="tgl_deadline_from_date" class="form-control form-control-solid fw-bold">
                                                            <small>From</small>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <input type="date" id="filter-dummy-to" name="tgl_deadline_to_date" class="form-control form-control-solid fw-bold">
                                                            <small>To</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-between mt-2">
                                                    <button type="button" name="filter-production-tgl-deadline" id="reset_date_filter" data-kt-menu-dismiss="true" class="btn btn-danger btn-sm">Reset</button>
                                                    <button type="button" name="filter-production-tgl-deadline" id="filter_by_date" data-kt-menu-dismiss="true" class="btn btn-primary btn-sm">Cari</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <table id="datatable_production"
                        class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer compact nowrap">
                        <thead>
                            <tr class="fw-bold">
                                <th>Nama Customer</th>
                                <th>SKO</th>
                                <th>Tgl FAW</th>
                                <th>Deadline</th>
                                <th>Progress Produksi</th>
                                <th>Status Produksi</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody> </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
