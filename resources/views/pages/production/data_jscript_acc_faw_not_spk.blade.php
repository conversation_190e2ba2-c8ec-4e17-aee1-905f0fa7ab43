<script>
    $(document).ready(function () {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        const dataTableLanguage = {
            loadingRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div class="spinner-grow text-warning" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>`,
            emptyTable: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
            zeroRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
        };

        //datatable page cust
        dt_faw = $('#datatable_faw').DataTable({
            // processing: true,
            // serverSide: true,
            language: dataTableLanguage,
            ajax: {
                url: "{{ route('production.table_acc_faw_not_spk') }}"
            },
            deferRender: true,
            columnDefs: [ { type: 'date', targets: [4] } ],
            order: [[ 4, 'desc' ]],
            columns: [
                // {
                //     data: 'kode_order',
                //     name: 'tb_kode_orders.kode_order'
                // },
                {
                    data: 'sko',
                    name: 'tb_orders.sko'
                },
                {
                    data: 'nama',
                    name: 'nama'
                },
                {
                    data: 'status_faw',
                    name: 'tb_faws.status_faw',
                    visible: false
                },
                {
                    data: 'status_faw_disp',
                    name: 'tb_faws.status_faw',
                },
                {
                    data: 'created_at',
                    name: 'tb_faws.created_at',
                },
                {
                    data: 'updated_at',
                    name: 'tb_faws.updated_at',
                },
                {
                    data: 'action',
                    name: 'action',
                    orderable: false,
                    searchable: false,
                    width: '5%'
                }
            ],

        });

        //filter page cust
        const filterSearch = document.querySelector('[data-kt-docs-table-filter="search-faw"]')
        filterSearch.addEventListener('keyup', function (e) {
            dt_faw.search(e.target.value).draw()
        })

        // const filterFaw = document.querySelector('.filter-faw')
        // filterFaw.addEventListener('change', function (e) {
        //     regExSearch = '^\\s' + e.target.value +'\\s*$';
        //     dt_faw.column(3).search(e.target.value, true, false).draw()
        // })

        // Select filter options
        filterStatusFAW = document.querySelectorAll('[data-kt-docs-table-filter="status_faw"] [name="status_faw"]')
        const filterButton = document.querySelector('[data-kt-docs-table-filter="filter-faw"]')

        // Filter datatable on submit
        filterButton.addEventListener('click', function () {
        // Get filter values
        let status_faw = ''

        // Get payment value
        filterStatusFAW.forEach(r => {
            if (r.checked) {
                status_faw = r.value
            }

            // Reset payment value if "All" is selected
            if (status_faw === 'all') {
                status_faw = ''
            }
        })

        // Filter datatable --- official docs reference: https://datatables.net/reference/api/search()
        dt_faw.column(3).search(status_faw, true, false).draw()
        })
        
    }); 
    function approveConfirmation(id) {
            swal.fire({
                title: "Ubah Status FAW ?",
                text: "Harap pastikan",
                icon: "warning",
                showCancelButton: !0,
                confirmButtonText: "Ya!",
                cancelButtonText: "Tidak, batal!",
                reverseButtons: !0
            }).then(function (e) {
                if (e.value === true) {
                    $.ajax({
                        url: "{{ route('production.approve_faw','') }}" + '/' + id,
                        type: 'POST',
                        data: {
                            "id": id
                            // "_token": token,
                        },
                        success: function (data) {
                            if (data) {
                                console.log(data)
                                $('#datatable_faw').DataTable().ajax.reload()
                                swal.fire("Done!", data.success, "success");
                            } else {
                                swal("Error!", results.message, "error");
                            }
                        }
                    });

                } else {
                    e.dismiss;
                }
            }, function (dismiss) {
                return false;
            })
        }
</script>
