<script type="text/javascript">

    // SweetAlert Alert Example
    @if(session()->has('message'))
    function showAlert(title, message, type) {
        window.swal.fire({
            title: "Response Message",
            text: title,
            icon: 'success',
            type: type,
            confirmButtonText: "OK",
            html: message,
            // cancelButtonText: "Cancel",
            showCancelButton: false,
        }).then(console.log)
            .catch(console.error);
    }
    showAlert('{{ strtolower(session('message')['type']) }}', '{{ session('message')['text'] }}');
    @endif
</script>
<script>
    $(document).ready(function(){
        //datatable page cust
        const dataTableLanguage = {
            loadingRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div class="spinner-grow text-warning" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>`,
            emptyTable: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
            zeroRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
        };

        dt_prod = $('#datatable_production').DataTable({
            // processing: true,
            // serverSide: true,
            language: dataTableLanguage,
            ajax: {
                url: "{{ route('production.table_dummy_done') }}"
            },
            deferRender: true,
            columnDefs: [ { type: 'date', targets: [4] } ],
            order: [[ 4, 'desc' ]],
            columns: [
                {
                    data: 'nama',
                    name: 'nama'
                },
                {
                    data: 'sko',
                    name: 'sko'
                },
                {
                    data: 'progress_produksi',
                    name: 'progress_produksi'
                },
                {
                    data: 'catatan_khusus',
                    name: 'catatan_khusus'

                },
                {
                    data: 'tgl_selesai',
                    name: 'tgl_selesai'
                },
                {
                    data: 'tgl_pengiriman',
                    name: 'tgl_pengiriman'
                },
                {
                    data: 'action',
                    name: 'action',
                    orderable: false,
                    searchable: false,
                    width: '5%'
                }
            ],
            
        });

        //filter page cust
        const filterSearch = document.querySelector('[data-kt-docs-table-filter="search-production"]')
        filterSearch.addEventListener('keyup', function (e) {
            dt_prod.search(e.target.value).draw()
        })

        const filterProgress = document.querySelector('.filter-produksi-progress')
        filterProgress.addEventListener('change', function (e) {
            dt_prod.search(e.target.value).draw()
        })

        $('#filter_by_date').on('click', function() {
            var startDate = $('#filter-dummy-from').val();
            var endDate = $('#filter-dummy-to').val();

            // Convert string dates to Date objects
            var startDateValue = new Date(startDate);
            var endDateValue = new Date(endDate);

            // Clear existing DataTable search function and reapply it
            $.fn.dataTable.ext.search = [];

            $.fn.dataTable.ext.search.push(function(settings, searchData, index, rowData, counter) {
                var dateColumnValue = new Date(searchData[4]);

                if (!startDate && !endDate) {
                    return true; // No date range specified, show all rows
                } else if (!startDate && dateColumnValue <= endDateValue) {
                    return true; // Only end date specified, show rows with dates before or equal to end date
                } else if (!endDate && dateColumnValue >= startDateValue) {
                    return true; // Only start date specified, show rows with dates after or equal to start date
                } else if (dateColumnValue >= startDateValue && dateColumnValue <= endDateValue) {
                    return true; // Date is within the specified range
                }

                return false; // Date is outside the specified range, exclude row
            });

            dt_prod.draw()
        });

        $('#reset_date_filter').on('click', function() {
            // Clear date inputs
            $('#filter-dummy-from').val('');
            $('#filter-dummy-to').val('');

            // Remove custom search function
            $.fn.dataTable.ext.search.pop();

            // Redraw the DataTable
            dt_prod.draw();
        });
    });
</script>