<script src="https://cdn.jsdelivr.net/npm/medium-zoom@1.0.6/dist/medium-zoom.min.js"></script>
<script>
    $(document).ready(function () {
        mediumZoom('#zoomable-image');
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        const dataTableLanguage = {
            loadingRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div class="spinner-grow text-warning" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>`,
            emptyTable: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
            zeroRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
        };

        //datatable page cust
        dt_faw = $('#datatable_faw').DataTable({
            language: dataTableLanguage,
            ajax: {
                url: "{{ route('production.table_not_acc_faw') }}"
            },
            deferRender: true,
            columnDefs: [ { type: 'date', targets: [5] } ],
            order: [[ 5, 'desc' ]],
            columns: [{
                    data: 'kode_order',
                    name: 'tb_kode_orders.kode_order'
                },
                {
                    data: 'sko',
                    name: 'tb_orders.sko'
                },
                {
                    data: 'nama',
                    name: 'nama'
                },
                {
                    data: 'status_faw',
                    name: 'tb_faws.status_faw',
                    visible: false
                },
                {
                    data: 'status_faw_disp',
                    name: 'tb_faws.status_faw',
                },
                {
                    data: 'tgl_faw',
                    name: 'tgl_faw'
                },
                {
                    data: 'action',
                    name: 'action'
                }
            ],

        });

        //filter page cust
        const filterSearch = document.querySelector('[data-kt-docs-table-filter="search-faw"]')
        filterSearch.addEventListener('keyup', function (e) {
            dt_faw.search(e.target.value).draw()
        })

        // const filterFaw = document.querySelector('.filter-faw')
        // filterFaw.addEventListener('change', function (e) {
        //     regExSearch = '^\\s' + e.target.value +'\\s*$';
        //     dt_faw.column(3).search(e.target.value, true, false).draw()
        // })

        // Select filter options
        filterStatusFAW = document.querySelectorAll('[data-kt-docs-table-filter="status_faw"] [name="status_faw"]')
        const filterButton = document.querySelector('[data-kt-docs-table-filter="filter-faw"]')

        // Filter datatable on submit
        filterButton.addEventListener('click', function () {
        // Get filter values
        let status_faw = ''

        // Get payment value
        filterStatusFAW.forEach(r => {
            if (r.checked) {
                status_faw = r.value
            }

            // Reset payment value if "All" is selected
            if (status_faw === 'all') {
                status_faw = ''
            }
        })

        // Filter datatable --- official docs reference: https://datatables.net/reference/api/search()
        dt_faw.column(3).search(status_faw, true, false).draw()
        })

        //modal-harga_rinci
        $('body').on('click', '.view-faw', function (event) {
            event.preventDefault();
            var key = $(this).data('key');
            $.get("{{ url('production/show_faw/') }}/" + key, function (data) {
                $('#modal_faw').modal('show');
                var spesifikasi= data[0].lp_panjang+' X '+data[0].lp_lebar+', '+
                                             data[0].jenis_kertas+', '+
                                             data[0].gramasi+', '+
                                             data[0].laminasi+', '+
                                             data[0].sisi_laminasi+', '+
                                             data[0].finishing+', '+
                                             data[0].jumlah_produk+' pcs, '+
                                             data[0].notes;
                $('#waktu_produksi').html(data[0].waktu_produksi);
                const dateObj = new Date(data[0].tgl_faw);
                const formattedDate = new Intl.DateTimeFormat("id", { day: "numeric", month: "short", year: "numeric" }).format(dateObj);
                $('#tgl_faw').html(formattedDate);
                $('#keterangan_tambahan').html(data[0].keterangan_tambahan);
                $('<a href="'+data[0].file_final+'">Open Link</a>').appendTo($('#file_final'));
                $('<img id="zoomable-image" width="300" src="{!! url("storage/'+data[0].sko_key+'/faw/lampiran/'+data[0].path_lampiran+'"); !!}">').appendTo($('#path_lampiran'));
                $('<a href="{!! url("crm/generate_faw/'+data[0].sko_key+'"); !!}"  class="btn btn-my-primary text-white">Download FAW</a>').appendTo($('#download_faw'));
                $('#spesifikasi').html(spesifikasi);
            })
        });

        $('#modal_faw').on('hidden.bs.modal', function () {
            $('#file_final').html("");
            $('#path_lampiran').html("");
            $('#download_faw').html("");
            $('#spesifikasi').html("");
            $('#datatable_faw').DataTable().ajax.reload();
        })

        
    }); 
    function approveConfirmation(id) {
            swal.fire({
                title: "Ubah Status FAW ?",
                text: "Harap pastikan",
                icon: "warning",
                showCancelButton: !0,
                confirmButtonText: "Ya!",
                cancelButtonText: "Tidak, batal!",
                reverseButtons: !0
            }).then(function (e) {
                if (e.value === true) {
                    $.ajax({
                        url: "{{ route('production.approve_faw','') }}" + '/' + id,
                        type: 'POST',
                        data: {
                            "id": id
                            // "_token": token,
                        },
                        success: function (data) {
                            if (data) {
                                console.log(data)
                                $('#datatable_faw').DataTable().ajax.reload()
                                swal.fire("Done!", data.success, "success");
                            } else {
                                swal("Error!", results.message, "error");
                            }
                        }
                    });

                } else {
                    e.dismiss;
                }
            }, function (dismiss) {
                return false;
            })
        }
</script>
