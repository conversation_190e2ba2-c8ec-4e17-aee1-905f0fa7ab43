@extends('layouts.survey')

@section('content')
<div class="container mt-5">
    <div class="card p-5">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center w-100">
                <div>
                    <div class="d-flex justify-content-start align-items-center">
                        <img src="{{ url('images/Logo-Risepack.svg') }}" alt="Logo" class="logo mb-3" />
                        <div class="ms-3 mb-4">
                            <h3 class="card-title">Survey Form</h3>
                            <p class="card-text">Please fill out the survey form below.</p>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <h3>{{$survey->order->tb_customer->nama_instansi ?? ''}}</h3>
                    <p class="text-muted">Order : {{$survey->order->sko}}</p>
                </div>
            </div>
        </div>
        <div class="card-body">
            @php
                $i = 1;
            @endphp
            @if ($survey->submited_date)
                <div class="alert alert-success" role="alert">
                    <i class="fa fa-check text-success"></i> This survey has already been submitted at <strong>{{date('d M Y H:i', strtotime($survey->submited_date))}}</strong>.
                </div>
                @if (isset($is_show))
                    <h2>Nama : {{$survey->submited_by}}</h2>
                    <hr>
                    <div class="row">
                        @if (count($survey->responses) > 0)
                            @php
                                $total = count(collect($survey->responses)->whereNull('answer')) * 100;
                                $total_bobot = 0;
                            @endphp
                            <div class="col-md-6">
                                    @foreach ($survey->responses as $response)
                                        @php
                                            $total_bobot += $response->bobot;
                                        @endphp
                                        <label class="form-label">{{$i}}. {{ $response->question_text }}</label>
                                        <div class="mb-3">
                                            <label class="form-label">Jawaban:</label>
                                            {{ $response->option_text }}
                                        </div>
                                        @php
                                            $i++;
                                        @endphp
                                    @endforeach
                                
                            </div>
                            <div class="col-md-6">
                                @php
                                    $final_score = round($total_bobot / $total * 100);
                                @endphp
                                <div class="card border" style="width: 18rem;">
                                    <div class="card-body">
                                        <h5 class="card-title">Score</h5>
                                        <h1>{{$final_score}} / 100</h1>
                                    </div>
                                </div>
                            </div>
                        @endif  
                    </div>
                @endif
            @else
                <form action="{{ route('survey.submit') }}" method="POST">
                    @csrf
                    <div class="mb-3">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <input type="hidden" name="survey_link_id" value="{{ $survey->id }}">
                    </div>
                    @if (count($questions) > 0)
                        @foreach ($questions as $q)
                            <input type="hidden" name="survey_question_id[]" value="{{ $q->id }}">
                            @php
                                $question = $q->question;
                            @endphp
                            <input type="hidden" name="question_type[]" value="{{ $question->question_type }}">
                            <input type="hidden" name="question_id[]" value="{{ $question->id }}">
                            <div class="mb-3">
                                <label for="question_{{ $question->id }}" class="form-label">{{$i}}. {{ $question->question_text }}</label>
                                @if ($question->question_type == 'multiple_choice')
                                    @foreach ($question->options->sortBy('sequence') as $option)
                                        <div class="form-check pb-2">
                                            <input class="form-check-input" type="checkbox" id="question_{{ $question->id }}_{{ $option->id }}" name="answers[{{ $question->id }}][]" value="{{ $option->id }}">
                                            <label class="form-check-label" for="question_{{ $question->id }}_{{ $option->id }}">{{ $option->option_text }}</label>
                                        </div>
                                    @endforeach
                                @elseif ($question->question_type == 'single_choice')
                                    @foreach ($question->options->sortBy('sequence') as $option)
                                        <div class="form-check pb-2">
                                            <input class="form-check-input" type="radio" id="question_{{ $question->id }}_{{ $option }}" name="answers[{{ $question->id }}]" value="{{ $option->id }}">
                                            <label class="form-check-label" for="question_{{ $question->id }}_{{ $option }}">{{ $option->option_text }}</label>
                                        </div>
                                    @endforeach
                                @elseif ($question->question_type == 'essay')
                                    <input type="text" class="form-control" id="question_{{ $question->id }}" name="answers[{{ $question->id }}]" required>     
                                @endif
                            </div>
                            @php
                                $i++;
                            @endphp
                        @endforeach
                    @endif
                    <button type="submit" class="btn btn-primary">Submit</button>
                </form>
            @endif
        </div>
    </div>
</div>
@endsection