<script>
    //CSRF
    $(document).ready(function () {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $('#survey-id').select2({
            allowClear: true,
            debug: true,
            placeholder: 'Pilih Survey',
            dropdownParent: $('#generate-link-form'),
            ajax: {
                url: "{{ route('survey.get_list') }}",
                processResults: function(data) {
                    var resultsData = []

                    $.each(data, function(index, item) {
                        resultsData.push({
                            id: item.id,
                            text: item.name
                        })
                    })

                    return {
                        results: resultsData
                    };
                },
                // cache: true
            }
        })

        // Initialize filter buttons
        let resetButton = document.querySelector('[data-kt-docs-table-filter="reset"]');
        let filterButton = document.querySelector('[data-kt-docs-table-filter="filter-tanggalKirim"]');
        
        if (resetButton) {
            resetButton.addEventListener('click', function() {
                $('#filter_start_date').val('');
                $('#filter_end_date').val('');
                $('#filter_start_submit_date').val('');
                $('#filter_end_submit_date').val('');
                $('#filter_status_survey').val('');
                datatable.draw();
            });
        }

        // Add click event for Apply button
        $('[data-kt-docs-table-filter="filter-tanggalKirim"]').on('click', function() {
            datatable.draw();
            $('#kt-toolbar-filter').removeClass('show');
        });
    });

    var datatable;

    datatable = $('#datatable_assign_survey').DataTable({
        processing: true,
        serverSide: true,
        paging: true,
        pageLength: 10,
        ajax: {
            url: "{{ route('survey.getAssignSurvey') }}",
            data: function (d) {
                d.status_survey = $('#filter_status_survey').val() || '';
                d.from_date = $('#filter_start_date').val() || '';
                d.to_date = $('#filter_end_date').val() || '';
                d.from_submit_date = $('#filter_start_submit_date').val() || '';
                d.to_submit_date = $('#filter_end_submit_date').val() || '';
            }
        },
        deferRender: true,
        columnDefs: [ { type: 'date', targets: [2] } ],
        order: [[ 2, 'desc' ]],
        columns: [
            {
                data: 'sko',
                name: 'tb_orders.sko'
            },
            {
                data: 'nama',
                name: 'nama'
            },
            {
                data: 'tgl_order',
                name: 'tgl_order',
                searchable: false
            },
            {
                data: 'submited_date',
                name: 'tb_survey_link.submited_date',
                searchable: false
            },
            {
                data: 'action',
                name: 'action',
                orderable: false,
                searchable: false,
            }
        ],
    });

    $('#btn-filter').on('click', function (e) {
        $('#kt-toolbar-filter').toggleClass('show');
    });

    // Close filter menu when clicking outside
    $(document).on('click', function (e) {
        if (!$(e.target).closest('#btn-filter, #kt-toolbar-filter').length) {
            $('#kt-toolbar-filter').removeClass('show');
        }
    });

    $('body').on('click', '.generate-link', function (event) {
        event.preventDefault();
        var id = $(this).data('order-id');
        console.log(id);
        $('#modal-generate-link').modal('show');
        $('#id-order').val(id);
    });

    $('#submit-generate-link').on('click', function (e) {
        e.preventDefault();
        var formData = $('#generate-link-form').serialize();
        $.ajax({
            url: "{{ route('survey.assign_survey.generate') }}",
            type: "POST",
            data: formData,
            success: function (response) {
                console.log(response);
                if (response.generate_link) {
                    var $tempInput = document.createElement('input');
                    document.body.appendChild($tempInput);
                    $tempInput.value = response.generate_link;
                    $tempInput.select();
                    document.execCommand('copy');
                    document.body.removeChild($tempInput);

                    Swal.fire({
                        title: 'Link Generated!',
                        html: `
                            <p>The link has been generated.</p>
                            <p><strong>${response.generate_link}</strong></p>
                        `,
                        showCancelButton: true,
                        cancelButtonText: 'Close'
                    });

                    $('#modal-generate-link').modal('hide');
                    $('#generate-link-form')[0].reset();
                    $('#survey-id').val(null).trigger('change');
                    $('#datatable_assign_survey').DataTable().ajax.reload();
                } else {
                    console.error('generate_link not found in response');
                }
            },
            error: function (xhr, status, error) {
                Swal.fire({
                    title: 'Error!',
                    text: "Link survey gagal generate",
                    icon: 'error',
                })
            }
        });
    });

    function copyLink(link) {
        var $tempInput = document.createElement('input');
        document.body.appendChild($tempInput);
        $tempInput.value = link;
        $tempInput.select();
        document.execCommand('copy');
        document.body.removeChild($tempInput);
        Swal.fire({
            title: 'Success!',
            text: "Link survey berhasil di copy",
            icon: 'success',
        })
    }
</script>
<div class="card card-flush">
    <div class="card-body">
        <div class="py-1">
            <div class="d-flex flex-stack flex-wrap mb-5">
                <!--begin::Search-->
                <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                    <!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
                    <span class="svg-icon svg-icon-1 position-absolute ms-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                            <path
                                d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                fill="black"></path>
                        </svg>
                    </span>
                    <!--end::Svg Icon-->
                    <input type="text" data-kt-docs-table-filter="search-faw"
                        class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                </div>
                <!--end::Search-->
                <!--begin::Toolbar-->
                <div class="d-flex flex-row-fluid justify-content-end gap-3">
                    <button type="button" class="btn btn-light-primary me-3" data-kt-menu-trigger="click"
                        data-kt-menu-placement="bottom-end" id="btn-filter">
                        <span class="svg-icon svg-icon-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none">
                                <path
                                    d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                                    fill="black"></path>
                            </svg>
                        </span>
                        Filter
                    </button>
                    <div class="menu menu-sub menu-sub-dropdown position-absolute mt-15" data-kt-menu="true" id="kt-toolbar-filter">
                        <div class="px-7 py-5">
                            <label class="form-label fs-5 fw-bold mb-3">Status Survey</label>
                            <select class="form-select form-select-solid mb-2" name="filter_status_survey" id="filter_status_survey">
                                <option hidden>Status Survey</option>
                                <option value="">All</option>
                                <option value="ungenerated">Belum Generate Link Survey</option>
                                <option value="unsubmited">Belum Submit</option>
                                <option value="submited">Submit</option>
                            </select>
                            <label class="form-label fs-5 fw-bold mb-3">Tanggal Order:</label>
                            <div class="form-group">
                                <div class="d-flex align-items-center gap-4">
                                    <div class="input-group">
                                        <span class="input-group-text">Start Date</span>
                                        <input type="date" class="form-control form-control-solid"
                                            name="filter_start_date" id="filter_start_date" placeholder="Tanggal Mulai">
                                    </div>
                                    <div class="input-group">
                                        <span class="input-group-text">End Date</span>
                                        <input type="date" class="form-control form-control-solid"
                                            name="filter_end_date" id="filter_end_date" placeholder="Tanggal Akhir">
                                    </div>
                                </div>
                            </div>
                            <label class="form-label fs-5 fw-bold mb-3">Tanggal Submit:</label>
                            <div class="form-group">
                                <div class="d-flex align-items-center gap-4">
                                    <div class="input-group">
                                        <span class="input-group-text">Start Date</span>
                                        <input type="date" class="form-control form-control-solid"
                                            name="filter_start_submit_date" id="filter_start_submit_date" placeholder="Tanggal Mulai">
                                    </div>
                                    <div class="input-group">
                                        <span class="input-group-text">End Date</span>
                                        <input type="date" class="form-control form-control-solid"
                                            name="filter_end_submit_date" id="filter_end_submit_date" placeholder="Tanggal Akhir">
                                    </div>
                                </div>
                                <div class="d-flex justify-content-end mt-3">
                                    <button type="reset" class="btn btn-light btn-active-light-primary me-2"
                                        data-kt-menu-dismiss="true" data-kt-docs-table-filter="reset">Reset</button>
                                    <button type="submit" class="btn btn-primary" data-kt-menu-dismiss="true"
                                        data-kt-docs-table-filter="filter-tanggalKirim">Apply</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--end::Toolbar-->
            </div>
            <table id="datatable_assign_survey" class="table align-middle table-row-bordered compact text-nowrap dataTable no-footer">
                <thead>
                    <tr class="fw-bold">
                        <th>SKO</th>
                        <th>Nama Customer</th>
                        <th>Tgl Order</th>
                        <th>Tgl Submit</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody> </tbody>
            </table>
        </div>
    </div>
</div>
<div class="modal" tabindex="-1" id="modal-add-question">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            
                <div class="modal-header">
                    <h5 class="modal-title">Tambah Question Baru</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-question-close"
                        data-bs-dismiss="modal" aria-label="Close">
                        <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                    </div>
                </div>

                <div class="modal-body mt-n5">
                    <div class="row">
                        <div class="col-lg-12">
                            <form id="question-form" name="question-form" class="form-horizontal">
                                <div class="mb-3">
                                    <label for="question" class="required form-label">Question</label>
                                    <input type="text" id="question-text" name="question_text" required autofocus class="form-control" placeholder="Nama question">
                                </div>
                                <div class="mb-3">
                                    <label for="question" class="required form-label">Type</label>
                                    <select class="form-select" name="question_type">
                                        <option value="" disabled>Pilih Type</option>
                                        @foreach (questionType() as $key => $value)
                                            <option value="{{ $key }}" {{$key=="single_choice" ? "selected" : "disabled"}}>{{ $value }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div id="question-option">
                                        <label for="question" class="required form-label">Option</label>
                                        <div class="row mt-2 border-bottom">    
                                            <div class="col-lg-8">
                                                <label class="form-label">Option Text</label>
                                            </div>
                                            <div class="col-lg-2">
                                                <label class="form-label">Bobot</label>
                                            </div>
                                            <div class="col-lg-2">
                                                <label class="form-label">Sequence</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-8">
                                                <input type="text" id="option-text" name="option_text[]"
                                                    class="form-control" placeholder="Nama Option">
                                            </div>
                                            <div class="col-lg-2">
                                                <input type="number" id="bobot" name="bobot[]"
                                                    class="form-control" placeholder="Bobot">
                                            </div>
                                            <div class="col-lg-2">
                                                <input type="number" id="sequence" name="sequence[]"
                                                    class="form-control" placeholder="Sequence">
                                            </div>
                                            <div class="col-lg-12 mt-2">
                                                <button type="button" id="add-option"
                                                    class="btn btn-primary my-primary">Add Option</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" id="add-question-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                    <button type="submit" id="submit-form-question" class="btn btn-primary my-primary">Save</button>
                </div>
        </div>
    </div>
</div>
<div class="modal fade" tabindex="-1" id="modal-generate-link">
    <div class="modal-dialog modal-dialog-centered modal-md">
        <div class="modal-content">
            <form id="generate-link-form" name="generate-link-form" class="form-horizontal">
                <div class="modal-header">
                    <h5 class="modal-title">Generate Link Survey</h5>
                    <!--begin::Close-->
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-order-close"
                        data-bs-dismiss="modal" aria-label="Close">
                        <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                    </div>
                    <!--end::Close-->
                </div>

                <div class="modal-body mt-n5">
                    <div class="row">
                        <div class="col-lg-12">
                            <input type="hidden" name="id_order" id="id-order">
                            <div class="mb-3">
                                <label for="survey" class="required form-label">Survey</label>
                                <select class="form-select" name="survey_id" id="survey-id">
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" id="generate-link-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                    <button type="button" id="submit-generate-link" class="btn btn-primary my-primary">Save
                        changes</button>
                </div>
            </form>
        </div>
    </div>
</div>
