<x-app-layout>
    <x-slot name="header">
        <span><a href="{{ route('survey') }}" class="pe-3">
                <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
            </a><span class="text-white fs-1 mt-5">Survey Management</span></span>
        <small class="text-white fs-6 fw-normal mt-3">Edit Question</small>
    </x-slot>
    <x-slot name="script">
        <script>
            //CSRF
            $(document).ready(function () {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
            });

            $('#add-option').click(function (e) {
                e.preventDefault();
                var html = '<div class="row mt-2"><input type="hidden" name="option_id[]" value=""><div class="col-lg-8"><input type="text" id="option-text" name="option_text[]" class="form-control" placeholder="Nama Option"></div><div class="col-lg-2"><input type="number" id="bobot" name="bobot[]" class="form-control text-center" placeholder="Bobot"></div><div class="col-lg-2"><input type="number" id="sequence" name="sequence[]" class="form-control text-center" placeholder="Sequence"></div></div>';
                $('#question-option').append(html);
            });

            $('#question-form').on('submit', function (e) {
                e.preventDefault();
                var totalBobot = 0;
                $('input[name="bobot[]"]').each(function () {
                    var val = parseFloat($(this).val());
                    if (!isNaN(val)) {
                        totalBobot += val;
                    }
                });
                if (totalBobot > 100) {
                    Swal.fire({
                        icon: 'warning',
                        title: 'Total Bobot Melebihi 100',
                        text: 'Jumlah total bobot tidak boleh lebih dari 100.',
                    });
                    return false;
                }
                var formData = $(this).serialize();
                $.ajax({
                    url: "{{ route('survey.question.update', $question->id) }}",
                    type: "POST",
                    data: formData,
                    success: function (response) {
                        window.location.href = "{{ route('survey', ['is_question=true']) }}";
                    },
                    error: function (xhr, status, error) {
                        alert("An error occurred while processing the request.");
                    }
                });
            });
            $('#add-question-close').click(function (e) {
                e.preventDefault();
                window.location.href = "{{ route('survey', ['is_question=true']) }}";
            });
            $('.remove-option').click(function (e) {
                e.preventDefault();
                var optionId = $(this).data('option-id');
                $('#option-' + optionId).remove();
                $('#question-form').append('<input type="hidden" name="remove_option_id[]" value="' + optionId + '">');
            });

            $('#question_type').on('change', function(){
                var type = $(this).val();
                if (type === 'essay') {
                    $('#option-section').addClass('d-none');
                } else {
                    $('#option-section').removeClass('d-none');
                    $('#question-option').find('input').val('');
                    $('#question-option').find('div.row.mt-2').remove();
                }
            })
        </script>
    </x-slot>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush">
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-12">
                        <form id="question-form" name="question-form" class="form-horizontal">
                            <input type="hidden" name="question_id" value="{{$question->id}}">
                            <div class="pb-3 border-bottom d-flex flex-stack align-items-end">
                                <button type="button" id="add-question-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                                <button type="submit" id="submit-form-question" class="btn btn-primary">Save</button>
                            </div>
                            <div class="mb-3">
                                <label for="question" class="required form-label">Question</label>
                                <input type="text" id="question-text" name="question_text" value="{{$question->question_text}}" autofocus class="form-control" placeholder="Nama question">
                            </div>
                            <div class="mb-3">
                                <label for="question" class="required form-label">Type</label>
                                <select class="form-select" name="question_type">
                                    <option value="" disabled>Pilih Type</option>
                                    @foreach (questionType() as $key => $value)
                                        <option value="{{ $key }}" {{$key!=$question->question_text ?? "disabled"}}>{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="mb-3" id='option-section'>
                                <div id="question-option">
                                    <label for="question" class="required form-label">Option</label>
                                    <div class="row mt-2 border-bottom">    
                                        <div class="col-lg-8">
                                            <label class="form-label">Option Text</label>
                                        </div>
                                        <div class="col-lg-2 text-center">
                                            <label class="form-label">Bobot</label>
                                        </div>
                                        <div class="col-lg-2 text-center">
                                            <label class="form-label">Sequence</label>
                                        </div>
                                    </div>
                                    @if (count($options) > 0)
                                        @foreach ($options as $option)
                                            <input type="hidden" name="option_id[]" value="{{$option->id}}">
                                            <div class="row mt-2" id="option-{{$option->id}}">
                                                <div class="col-lg-8">
                                                    <input type="text" id="option-text" name="option_text[]"
                                                        class="form-control" value="{{$option->option_text}}" placeholder="Nama Option">
                                                </div>
                                                <div class="col-lg-2">
                                                    <input type="number" id="bobot" name="bobot[]"
                                                        class="form-control text-center" value="{{$option->bobot}}" placeholder="Bobot">
                                                </div>
                                                <div class="col-lg-2">
                                                    <div class="d-flex justify-content-center">
                                                        <input type="number" id="sequence" name="sequence[]"
                                                            class="form-control text-center" value="{{$option->sequence}}" placeholder="Sequence">
                                                        <button type="button" class="btn btn-danger remove-option" style="margin-top: 5px;" data-option-id="{{$option->id}}"><i class="fa fa-times"></i></button>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif
                                    <div class="col-lg-12 mt-2">
                                        <button type="button" id="add-option"
                                            class="btn btn-primary my-primary">Add Option</button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
