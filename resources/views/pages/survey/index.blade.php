
<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1 mt-5"><a href="{{ route('sales.index') }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
                </a> Survey Management</span>
        <small class="text-white fs-6 fw-normal mt-3">Overview Responses Survey</small>
    </x-slot>
    <x-slot name="script">
    </x-slot>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <div class="container-xxl" id="kt_content_container">
        <div class="d-grid my-3 mb-5">
            <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bolder mt-n12">
                {{-- <li class="nav-item">
                    <a class="nav-link text-white btn-color-grey-600 active me-3"
                    data-bs-toggle="tab" href="#kt_tab_pane_1" data-tab="responses">Responses</a>
                </li> --}}
                <li class="nav-item">
                    <a class="nav-link text-white btn-color-grey-600 active me-3" data-bs-toggle="tab"
                    href="#kt_tab_pane_2" data-tab="assign-survey">Assign Survey</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-white btn-color-grey-600 me-3" data-bs-toggle="tab"
                    href="#kt_tab_pane_3" data-tab="list-survey">Daftar Survey</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-white btn-color-grey-600 me-3" data-bs-toggle="tab"
                    href="#kt_tab_pane_4" data-tab="question">Question</a>
                </li>
            </ul>
        </div>
        <div class="tab-content" id="myTabContent">
            <div id="content"></div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            $('.nav-stretch .nav-link').on('click', function(event) {
                var clickedNavLink = $(this);
                var tabData = clickedNavLink.data('tab'); 
                console.log("Clicked tab's data-tab value:", tabData);  

                const target = $('#content');
                target.html('<div class="spinner-grow text-dark" role="status"><span class="sr-only">Loading...</span></div>');
                $.ajax({
                    data: {
                        tab: tabData
                    },
                    url: "{{ route('survey.detail') }}",
                    type: "GET",
                    success: function(data) {
                        target.html(data);
                    },
                    error: function(data) {
                    }
                });
            });

            $('.nav-stretch .nav-link:first').trigger('click');

            let is_question = "{{ request()->get('is_question') }}";
            if (is_question) {
                self.$('[data-tab="question"]').trigger('click');
                self.$('[data-tab="question"]').addClass('active');
                self.$('[data-tab="responses"]').removeClass('active');
            }

            let is_survey = "{{ request()->get('is_survey') }}";
            if (is_survey) {
                self.$('[data-tab="list-survey"]').trigger('click');
                self.$('[data-tab="list-survey"]').addClass('active');
                self.$('[data-tab="responses"]').removeClass('active');
            }
        });
        
        function loadContentForTab(tabName) {
            console.log("Loading content for:", tabName);            
        }
    </script>
</x-app-layout>