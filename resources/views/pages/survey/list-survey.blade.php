<script>
    //CSRF
    $(document).ready(function () {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    });

    // var datatable;

    datatable = $('#datatable_survey').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "{{ route('survey.get_table') }}",
        },
        paging: true, //Aktifkan pagination
        pageLength: 10, //Tentukan jumlah data yang akan ditampilkan dalam satu halaman
        order: [[ 1, 'desc' ]],
        columns: [{
                data: 'no',
                name: 'no',
                render: function (data, type, row, meta) {
                    if (type === 'display') {
                        // meta.row gives the current row index (starting from 0)
                        return meta.row + 1; // Add 1 to make it start from 1
                    }
                    return meta.row + 1; // For other types (like export), you might want the number as well
                }
            },
            {
                data: 'title',
                name: 'title'
            },
            {
                data: 'description',
                name: 'description'
            },
            {
                data: 'total_question',
                name: 'total_question',
            },
            {
                data: 'action',
                name: 'action',
                orderable: false,
                searchable: false,
                width: '25%'
            }
        ],
        // Add data-filter attribute
        createdRow: function (row, data, dataIndex) {
            $(row).find('td:eq(4)').attr('data-filter', data.roles)
        }
    });

    // const filterSearch = document.querySelector('[data-kt-docs-table-filter="search"]')
    // filterSearch.addEventListener('keyup', function (e) {
    //     datatable.search(e.target.value).draw()
    // })


    //modal editor
    $('#submit-form-survey').click(function (e) {
        e.preventDefault();
        $(this).html('Sending..');
        $.ajax({
            data: $('#survey-form').serialize(),
            url: "{{ route('survey.store') }}",
            type: "POST",
            dataType: 'json',
            success: function (data) {
                datatable.draw();
                $('#datatable_survey').DataTable().ajax.reload()
                setTimeout(function () {
                            self.$("#add-survey-close").trigger("click");
                        }, 1200);
                $('#modal-add-survey').modal('hide');
                $('#survey-form').trigger('reset');
                Swal.fire({
                    title: 'SUCCESS!',
                    text: "Survey baru berhasil dibuat",
                    icon: 'success',
                })
            },
            error: function (data) {
                $('#submit-form-survey').html('Save Changes');
                Swal.fire({
                    title: 'ERROR!',
                    text: "Harap Lengkapi form yang ada",
                    icon: 'error',
                })
            }
        });
    });



    function deleteConfirmation(id) {
        swal.fire({
            title: "Hapus ?",
            text: "Harap pastikan",
            icon: "warning",
            showCancelButton: !0,
            confirmButtonText: "Ya, Hapus!",
            cancelButtonText: "Tidak, batal!",
            reverseButtons: !0
        }).then(function (e) {
            if (e.value === true) {
                $.ajax({
                    url: "{{ route('survey.destroy','') }}"+'/'+id,
                    type: 'DELETE',
                    data: {
                        "id": id
                    },
                    success: function (data) {
                        if (data) {
                            $('#datatable_survey').DataTable().ajax.reload()
                            Swal.fire({
                                title: 'SUCCESS!',
                                text: "Survey berhasil dihapus",
                                icon: 'success',
                            })
                        } else {
                            Swal.fire({
                                title: 'FAILED!',
                                text: "Survey gagal dihapus",
                                icon: 'error',
                            })
                        }
                    }
                });

            } else {
                e.dismiss;
            }
        }, function (dismiss) {
            return false;
        })
    }

    $('#add-question').click(function (e) {
        e.preventDefault();
        var html = '<select class="form-select mb-2" name="question_id[]" id=question_id>'+
                        '<option value="" disabled>Pilih Question</option>'+
                        '@if (count($questions) > 0)'+
                            '@foreach ($questions as $question)'+
                                '<option value="{{ $question->id }}">{{ $question->question_text }} - {{ questionType($question->question_type) }}</option>'+
                            '@endforeach'+
                        '@endif'+
                    '</select>';
        $('#question-list').append(html);
    });
</script>
<div class="card card-flush">
    <div class="card-body">
        <div class="py-1">
            <div class="py-1">
                <div class="d-flex flex-stack flex-wrap mb-5">
                    <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                        <span class="svg-icon svg-icon-1 position-absolute ms-6">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none">
                                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                    transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                <path
                                    d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                    fill="black"></path>
                            </svg>
                        </span>
                        <input type="text" id="survey-search-box" data-kt-docs-table-filter="search"
                            class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                    </div>
                    <div class="d-flex justify-content-end" data-kt-docs-table-toolbar="base">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal"
                            data-bs-target="#modal-add-survey" title="Add Survey">
                            <span class="svg-icon svg-icon-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none">
                                    <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1"
                                        transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                                    <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="black"></rect>
                                </svg>
                            </span>
                            Add Survey
                        </button>
                    </div>
                    <div class="d-flex justify-content-end align-items-center d-none"
                        data-kt-docs-table-toolbar="selected">
                        <div class="fw-bolder me-5">
                            <span class="me-2" data-kt-docs-table-select="selected_count"></span>Selected</div>
                        <button type="button" class="btn btn-danger"
                            data-kt-docs-table-select="delete_selected">Selection Action</button>
                    </div>
                </div>

                <table id="datatable_survey" class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Survey</th>
                            <th>Description</th>
                            <th>Total Question</th>
                            <th class="text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody> </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div class="modal" tabindex="-1" id="modal-add-survey">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            
                <div class="modal-header">
                    <h5 class="modal-title">Tambah Survey Baru</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-survey-close"
                        data-bs-dismiss="modal" aria-label="Close">
                        <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                    </div>
                </div>

                <div class="modal-body mt-n5">
                    <div class="row">
                        <div class="col-lg-12">
                            <form id="survey-form" name="survey-form" class="form-horizontal">
                                <div class="mb-3">
                                    <label for="title" class="required form-label">Title</label>
                                    <input type="text" id="title" name="title" required autofocus class="form-control" placeholder="Nama Survey">
                                </div>
                                <div class="mb-3">
                                    <label for="description" class="required form-label">Description</label>
                                    <textarea id="description" name="description" required class="form-control" placeholder="Deskripsi Survey"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="question" class="required form-label">Question</label>
                                    <div id="question-list">
                                        <select class="form-select mb-2" name="question_id[]" id=question_id>
                                            <option value="" disabled>Pilih Question</option>
                                            @if (count($questions) > 0)
                                                @foreach ($questions as $question)
                                                    <option value="{{ $question->id }}">{{ $question->question_text }} - {{ questionType($question->question_type) }}</option>
                                                @endforeach
                                            @endif
                                        </select>
                                    </div>
                                    <div class="col-lg-12 mt-2">
                                        <button type="button" id="add-question"
                                            class="btn btn-primary my-primary">Add Question</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" id="add-survey-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                    <button type="submit" id="submit-form-survey" class="btn btn-primary my-primary">Save</button>
                </div>
        </div>
    </div>
</div>
