<script>
    //CSRF
    $(document).ready(function () {
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
    });

    // var datatable;

    datatable = $('#datatable_question').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "{{ route('survey.question.get_table') }}",
        },
        paging: true, //Aktifkan pagination
        pageLength: 10, //Tentukan jumlah data yang akan ditampilkan dalam satu halaman
        order: [[ 1, 'desc' ]],
        columns: [{
                data: 'no',
                name: 'no',
                render: function (data, type, row, meta) {
                    if (type === 'display') {
                        // meta.row gives the current row index (starting from 0)
                        return meta.row + 1; // Add 1 to make it start from 1
                    }
                    return meta.row + 1; // For other types (like export), you might want the number as well
                }
            },
            {
                data: 'question_text',
                name: 'question_text'
            },
            {
                data: 'question_type',
                name: 'question_type'
            },
            {
                data: 'options',
                name: 'options'
            },
            {
                data: 'action',
                name: 'action',
                orderable: false,
                searchable: false,
                width: '25%'
            }
        ],
        // Add data-filter attribute
        createdRow: function (row, data, dataIndex) {
            $(row).find('td:eq(4)').attr('data-filter', data.roles)
        }
    });

    // const filterSearch = document.querySelector('[data-kt-docs-table-filter="search"]')
    // filterSearch.addEventListener('keyup', function (e) {
    //     datatable.search(e.target.value).draw()
    // })


    //modal editor
    $('#submit-form-question').click(function (e) {
        e.preventDefault();
        var totalBobot = 0;
        $('input[name="bobot[]"]').each(function () {
            var val = parseFloat($(this).val());
            if (!isNaN(val)) {
                totalBobot += val;
            }
        });
        if (totalBobot > 100) {
            Swal.fire({
                icon: 'warning',
                title: 'Total Bobot Melebihi 100',
                text: 'Jumlah total bobot tidak boleh lebih dari 100.',
            });
            return false;
        }
        $(this).html('Sending..');
        $.ajax({
            data: $('#question-form').serialize(),
            url: "{{ route('survey.question.store') }}",
            type: "POST",
            dataType: 'json',
            success: function (data) {
                datatable.draw();
                $('#datatable_question').DataTable().ajax.reload()
                setTimeout(function () {
                            self.$("#add-question-close").trigger("click");
                        }, 1200);
                $('#modal-add-question').modal('hide');
                $('#question-form').trigger('reset');
                Swal.fire({
                    title: 'SUCCESS!',
                    text: "Question baru berhasil dibuat",
                    icon: 'success',
                })
            },
            error: function (data) {
                $('#submit-form-question').html('Save Changes');
                Swal.fire({
                    title: 'ERROR!',
                    text: "Harap Lengkapi form yang ada",
                    icon: 'error',
                })
            }
        });
    });



    function deleteConfirmation(id) {
        swal.fire({
            title: "Hapus ?",
            text: "Harap pastikan",
            icon: "warning",
            showCancelButton: !0,
            confirmButtonText: "Ya, Hapus!",
            cancelButtonText: "Tidak, batal!",
            reverseButtons: !0
        }).then(function (e) {
            if (e.value === true) {
                $.ajax({
                    url: "{{ route('survey.question.destroy','') }}"+'/'+id,
                    type: 'DELETE',
                    data: {
                        "id": id
                    },
                    success: function (data) {
                        if (data) {
                            $('#datatable_question').DataTable().ajax.reload()
                            Swal.fire({
                                title: 'SUCCESS!',
                                text: "Question berhasil dihapus",
                                icon: 'success',
                            })
                        } else {
                            Swal.fire({
                                title: 'FAILED!',
                                text: "Question gagal dihapus",
                                icon: 'error',
                            })
                        }
                    }
                });

            } else {
                e.dismiss;
            }
        }, function (dismiss) {
            return false;
        })
    }

    $('#add-option').click(function (e) {
        e.preventDefault();
        var html = '<div class="row mt-2"><div class="col-lg-8"><input type="text" id="option-text" name="option_text[]" class="form-control" placeholder="Nama Option"></div><div class="col-lg-2"><input type="number" id="bobot" name="bobot[]" class="form-control" placeholder="Bobot"></div><div class="col-lg-2"><input type="number" id="sequence" name="sequence[]" class="form-control" placeholder="Sequence"></div></div>';
        $('#question-option').append(html);
    });

    $('#question_type').on('change', function(){
        var type = $(this).val();
        if (type === 'essay') {
            $('#option-section').addClass('d-none');
        } else {
            $('#option-section').removeClass('d-none');
            $('#question-option').find('input').val('');
            $('#question-option').find('div.row.mt-2').remove();
        }
    })
</script>
<div class="card card-flush">
    <div class="card-body">
        <div class="py-1">
            <div class="py-1">
                <div class="d-flex flex-stack flex-wrap mb-5">
                    <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                        <span class="svg-icon svg-icon-1 position-absolute ms-6">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none">
                                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                    transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                <path
                                    d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                    fill="black"></path>
                            </svg>
                        </span>
                        <input type="text" id="question-search-box" data-kt-docs-table-filter="search"
                            class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                    </div>
                    <div class="d-flex justify-content-end" data-kt-docs-table-toolbar="base">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal"
                            data-bs-target="#modal-add-question" title="Add question">
                            <span class="svg-icon svg-icon-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none">
                                    <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1"
                                        transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                                    <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="black"></rect>
                                </svg>
                            </span>
                            Add Question
                        </button>
                    </div>
                    <div class="d-flex justify-content-end align-items-center d-none"
                        data-kt-docs-table-toolbar="selected">
                        <div class="fw-bolder me-5">
                            <span class="me-2" data-kt-docs-table-select="selected_count"></span>Selected</div>
                        <button type="button" class="btn btn-danger"
                            data-kt-docs-table-select="delete_selected">Selection Action</button>
                    </div>
                </div>

                <table id="datatable_question" class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Question</th>
                            <th>Type</th>
                            <th>Option</th>
                            <th class="text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody> </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div class="modal" tabindex="-1" id="modal-add-question">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            
                <div class="modal-header">
                    <h5 class="modal-title">Tambah Question Baru</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-question-close"
                        data-bs-dismiss="modal" aria-label="Close">
                        <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                    </div>
                </div>

                <div class="modal-body mt-n5">
                    <div class="row">
                        <div class="col-lg-12">
                            <form id="question-form" name="question-form" class="form-horizontal">
                                <div class="mb-3">
                                    <label for="question" class="required form-label">Question</label>
                                    <input type="text" id="question-text" name="question_text" required autofocus class="form-control" placeholder="Nama question">
                                </div>
                                <div class="mb-3">
                                    <label for="question" class="required form-label">Type</label>
                                    <select class="form-select" name="question_type" id="question_type">
                                        <option value="" disabled>Pilih Type</option>
                                        @foreach (questionType() as $key => $value)
                                            <option value="{{ $key }}">{{ $value }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="mb-3" id="option-section">
                                    <div id="question-option">
                                        <label for="question" class="required form-label">Option</label>
                                        <div class="row mt-2 border-bottom">    
                                            <div class="col-lg-8">
                                                <label class="form-label">Option Text</label>
                                            </div>
                                            <div class="col-lg-2">
                                                <label class="form-label">Bobot</label>
                                            </div>
                                            <div class="col-lg-2">
                                                <label class="form-label">Sequence</label>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-lg-8">
                                                <input type="text" id="option-text" name="option_text[]"
                                                    class="form-control" placeholder="Nama Option">
                                            </div>
                                            <div class="col-lg-2">
                                                <input type="number" id="bobot" name="bobot[]"
                                                    class="form-control" placeholder="Bobot">
                                            </div>
                                            <div class="col-lg-2">
                                                <input type="number" id="sequence" name="sequence[]"
                                                    class="form-control" placeholder="Sequence">
                                            </div>
                                            <div class="col-lg-12 mt-2">
                                                <button type="button" id="add-option"
                                                    class="btn btn-primary my-primary">Add Option</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" id="add-question-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                    <button type="submit" id="submit-form-question" class="btn btn-primary my-primary">Save</button>
                </div>
        </div>
    </div>
</div>
