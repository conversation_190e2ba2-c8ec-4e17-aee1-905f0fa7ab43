<x-app-layout>
    <x-slot name="header">
        <span><a href="{{ route('survey') }}" class="pe-3">
                <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
            </a><span class="text-white fs-1 mt-5">Survey Management</span></span>
        <small class="text-white fs-6 fw-normal mt-3">Edit Survey</small>
    </x-slot>
    <x-slot name="script">
        <script>
            //CSRF
            $(document).ready(function () {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
            });

            $('#add-question').click(function (e) {
                e.preventDefault();
                var html = '<select class="form-select mb-2 mt-2" name="question_id[]" id=question_id>'+
                        '<option value="" disabled>Pilih Question</option>'+
                        '@if (count($data_questions) > 0)'+
                            '@foreach ($data_questions as $question)'+
                                '<option value="{{ $question->id }}">{{ $question->question_text }} - {{ questionType($question->question_type) }}</option>'+
                            '@endforeach'+
                        '@endif'+
                    '</select>';
                $('#question-list').append(html);
            });

            $('#sruvey-form').on('submit', function (e) {
                e.preventDefault();
                var formData = $(this).serialize();
                $.ajax({
                    url: "{{ route('survey.update', $survey->id) }}",
                    type: "POST",
                    data: formData,
                    success: function (response) {
                        window.location.href = "{{ route('survey', ['is_survey=true']) }}";
                    },
                    error: function (xhr, status, error) {
                        alert("An error occurred while processing the request.");
                    }
                });
            });
            $('#add-survey-close').click(function (e) {
                e.preventDefault();
                window.location.href = "{{ route('survey', ['is_survey=true']) }}";
            });
            $('.remove-question').click(function (e) {
                e.preventDefault();
                var questionId = $(this).data('question-id');
                $('#question-id-' + questionId).remove();
                $('#sruvey-form').append('<input type="hidden" name="remove_question_id[]" value="' + questionId + '">');
            });
        </script>
    </x-slot>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush">
            <div class="card-body">
                <div class="row">
                    <div class="col-lg-12">
                        <form id="sruvey-form" name="sruvey-form" class="form-horizontal">
                            <input type="hidden" name="survey_id" value="{{$survey->id}}">
                            <div class="pb-3 border-bottom d-flex flex-stack align-items-end">
                                <button type="button" id="add-survey-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                                <button type="submit" id="submit-form-survey" class="btn btn-primary">Save</button>
                            </div>
                            <div class="mb-3">
                                <label for="title" class="required form-label">Title</label>
                                <input type="text" id="title" name="title" value="{{$survey->title}}" autofocus class="form-control" placeholder="Nama Survey">
                            </div>
                            <div class="mb-3">
                                <label for="description" class="required form-label">Description</label>
                                <textarea id="description" name="description" class="form-control" placeholder="Deskripsi Survey">{{$survey->description}}</textarea>
                            </div>
                            <div class="mb-3">
                                <label for="question" class="required form-label">Question</label>
                                <div id="question-list">
                                    @if ($questions)
                                        @foreach ($questions as $question)
                                            <input type="hidden" name="survey_question_id[]" value="{{$question->id}}">
                                            <div class="row mt-2" id="question-id-{{$question->id}}">
                                                <div class="col-lg-11">
                                                    <select class="form-select mb-2 mr-2" name="question_id[]" id=question_id>
                                                        <option value="" disabled>Pilih Question</option>
                                                        @if (count($data_questions) > 0)
                                                            @foreach ($data_questions as $q)
                                                                <option value="{{ $q->id }}" {{$q->id == $question->question_id ? "selected" : ""}}>{{ $q->question_text }} - {{ questionType($q->question_type) }}</option>
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                </div>
                                                <div class="col-lg-1">
                                                    <button type="button" class="btn w-100 btn-danger remove-question" data-question-id="{{$question->id}}"><i class="fa fa-times"></i></button>
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                                <div class="col-lg-12 mt-2">
                                    <button type="button" id="add-question"
                                        class="btn btn-primary my-primary">Add Question</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
