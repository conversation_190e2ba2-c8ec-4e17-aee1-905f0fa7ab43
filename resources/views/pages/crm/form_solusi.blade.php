<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ route('crm').'#tab_kendala'; }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">Form Solusi Penanggulangan Masalah</h3>
                    <small class="text-white fs-6 fw-normal pt-2">{{ $solusi }}</small>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        @include('pages.crm.data_jscript_form_solusi')
    </x-slot>
    <div class="container-xxl mt-20" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-header">
                <h2 class="card-title">#{{ $data->kode_order }} - {{ $data->nama }}</h2>
            </div>
            <div class="card-body pt-0">
                <form method="POST" id="form-faw" action="{{ route('crm.store_solusi') }}" enctype="multipart/form-data">
                    @csrf
                    @method('POST')
                    <input type="hidden" name="id_order" value="{{ $data->id_order }}">
                    <input type="hidden" name="order_key" value="{{ $data->order_key }}">
                    <input type="hidden" name="sko_key" value="{{ $data->sko_key }}">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="sko" class="form-label">Sub Kode Order</label>
                                        <input type="text" name="sko" value="{{ $data->sko }}" readonly id="sko" class="form-control form-control-solid"/>
                                    </div>
                                    <div class="mb-10">
                                        <label for="nama" class="form-label">Nama Customer</label>
                                        <input type="text" name="nama" id="nama" value="{{ $data->nama }}" readonly class="form-control form-control-solid"/>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="pic" class="form-label">PIC</label>
                                        <input type="text" name="pic" id="pic" value="{{ $data->name }}" readonly class="form-control form-control-solid"/>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="tgl_masalah" class="required form-label">Tanggal Masalah Dilaporkan</label>
                                        <input type="date" name="tgl_masalah" value="{{ $data->tgl_masalah }}" readonly id="tgl_masalah_fpm" class="form-control form-control-solid" />
                                    </div>
                                    <div class="mb-10">
                                        <label for="kategori_masalah" class="required form-label">Kategori Masalah</label>
                                        <input class="form-control" type="text" name="kategori_masalah" id="kategori_masalah_fpm" value="{{ $data->kategori_masalah }}" disabled>
                                    </div>
                                    <div class="mb-10">
                                        <label for="detail_masalah" class="form-label">Detail Masalah</label>
                                        <textarea name="detail_masalah" id="detail_masalah_fpm" disabled class="form-control"
                                            rows="3">{{ $data->detail_masalah }}</textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="solusi" class="required form-label">Solusi</label>
                                <input type="input" name="solusi" id="solusi" readonly value="{{ $solusi }}" class="form-control form-control-solid"/>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="tgl_deadline_perbaikan" class="required form-label">Tanggal Deadline Penyelesaian</label>
                                <input type="date" name="tgl_deadline_perbaikan" id="tgl_deadline_perbaikan" class="form-control"/>
                            </div>
                            <div class="mb-10">
                                <label for="tgl_deadline" class="form-label">Detail Solusi</label>
                                <textarea name="detail_solusi" id="detail_solusi" rows="3" class="form-control"></textarea>
                            </div>
                            <div class="mb-10">
                                <label for="cost" class="form-label">Cost yang ditimbulkan</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp. </span>
                                    <input type="text" name="cost" id="cost" class="form-control"/>
                                </div>
                            </div>
                            {{-- @if ($solusi == 'produksi-ulang')
                            <div class="border p-3 shadow-sm mb-10 rounded">
                                <h3 class="mb-10">Form FAW Produksi Ulang</h3>
                                <div class="fv-row mb-10">
                                    <label for="waktu_produksi" class="required form-label">Waktu Produksi</label>
                                    <input type="text" name="waktu_produksi" value="{{ old('waktu_produksi') ?? $data->waktu_produksi}}" id="waktu_produksi" class="form-control"/>
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="link_file_final" class="required form-label">File Final (Gdrive)</label>
                                    <div class="input-group">
                                        <input type="text" name="link_file_final" id="link_file_final" value="{{ old('file_final') ?? $data->file_final }}" class="form-control"/>
                                        <span class="input-group-text" id="basic-addon2"><i class="fa-solid fa-link"></i></span>
                                    </div>
                                </div>
                                <div class="mb-10">
                                    <label for="lampiran" class="form-label">lampiran</label>
                                    <input type="file" name="lampiran" class="form-control"/>
                                    @if (!empty($data->path_lampiran))
                                    <div class="notice d-flex bg-light-warning rounded border-warning border border-dashed p-6 mt-3">
                                        <!--begin::Wrapper-->
                                        <div class="d-flex flex-stack flex-grow-1">
                                            <!--begin::Content-->
                                            <div class="fw-semibold">
                                                <div class="fs-6 text-gray-700"><i class="fa-solid fa-paperclip"></i> {{ $data->path_lampiran }}</div>
                                            </div>
                                            <a href="javascript:void(0)" onclick="deleteConfirmation();"><i class="fa-solid fa-trash-can"></i></a>
                                            <!--end::Content-->
                                        </div>
                                        <!--end::Wrapper-->
                                    </div>
                                    @endif
                                </div>
                                <div class="mb-10">
                                    <label for="keterangan_tambahan" class="form-label">Keterangan Tambahan</label>
                                    <textarea type="text" name="keterangan_tambahan" id="notes" rows="6" class="form-control">{{ old('keterangan_tambahan') ?? $data->keterangan_tambahan }}</textarea>
                                </div>
                                
                            </div>
                            @endif --}}
                            @if ($solusi == 'produksi-ulang')
                            <div class="alert alert-warning d-flex align-items-center p-5 mb-10">
                                <!--begin::Svg Icon | path: icons/duotune/general/gen048.svg-->
                                <span class="svg-icon svg-icon-2hx svg-icon-warning me-4">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path opacity="0.3" d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor"></path>
                                        <path d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z" fill="currentColor"></path>
                                    </svg>
                                </span>
                                <!--end::Svg Icon-->
                                <div class="d-flex flex-column">
                                    <h4 class="mb-1 text-warning">Peringatan</h4>
                                    <span>Solusi produksi ulang akan membuat data order (record) baru.</span>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                    <input type="submit" form="form-faw" value="Submit" id="store-faw" class="btn my-primary text-white float-end">
                </form>
            </div>
        </div>
    </div>

    
</x-app-layout>