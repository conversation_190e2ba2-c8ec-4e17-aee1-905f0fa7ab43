<script>
    $(document).ready(function(){
        
        // jQuery.fn.dataTableExt.afnFiltering.push(
        //     function( oSettings, aData, iDataIndex ) {
        //         var iColumn = 4;
        //         var iMin = document.getelementByClassName('modal-sales-min').value * 1;
        //         var iMax = document.getelementByClassName('modal-sales-max').value * 1;
        
        //         var iVersion = aData[iColumn] == "-" ? 0 : aData[iColumn]*1;
        //         if ( iMin === "" && iMax === "" )
        //         {
        //             return true;
        //         }
        //         else if ( iMin === "" && iVersion < iMax )
        //         {
        //             return true;
        //         }
        //         else if ( iMin < iVersion && "" === iMax )
        //         {
        //             return true;
        //         }
        //         else if ( iMin < iVersion && iVersion < iMax )
        //         {
        //             return true;
        //         }
        //         return false;
        //     }
        // );
        const dataTableLanguage = {
            loadingRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div class="spinner-grow text-warning" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>`,
            emptyTable: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
            zeroRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
        };

        //filter page cust
        const filterSearch = document.querySelector('[data-kt-docs-table-filter="search-harga-rinci"]')
        filterSearch.addEventListener('keyup', function (e) {
            window.throttleDTSearch(window.dt_hr, e.target.value)
        })

        $('.filter-order-status-deal').on('change', function (e) {
            window.dt_hr.columns(3).search(e.target.value).draw();
        })

        $('.filter_modal_sales').on('change', function(e) {
            window.dt_hr.columns(2).search(e.target.value).draw()
        });

        $('#jenis_kertas_lainnya_hr').hide();
        $('#gramasi_lainnya_hr').hide();
        $('#finishing_lainnya_hr').hide();

        $('#jenis_kertas_hr').on('change', function(){
            if($('#jenis_kertas_hr').val() == 'Lainnya'){
                $('#jenis_kertas_lainnya_hr').show();
            }else{
                $('#jenis_kertas_lainnya_hr').hide();
            }
        });

        $('#gramasi_hr').on('change', function(){
            if($('#gramasi_hr').val() == 'Lainnya'){
                $('#gramasi_lainnya_hr').show();
            }else{
                $('#gramasi_lainnya_hr').hide();
            }
        });

        $('#finishing12_hr').on('change', function(){
            if($('#finishing12_hr').is(':checked')){
                $('#finishing_lainnya_hr').show();
            }else{
                $('#finishing_lainnya_hr').hide();
            }
        });

        //modal-harga_rinci
        $('body').on('click', '.harga_rinci', function (event) {
            event.preventDefault();
            var key = $(this).data('key');
            $.get("{{ url('crm/show_produk/') }}/" + key, function (data) {

                //autocalctotal
                $('#harga_produk_hr, #jumlah_produk_hr').on("keyup change", function(){
                    $('#total_harga_hr').simpleMoneyFormat();
                    jumlah_produk = $("#jumlah_produk_hr").val();
                    harga_produk = $("#harga_produk_hr").val().replace(/[A-Za-z$. ,-]/g, "");
                    $('#total_harga_hr').val(jumlah_produk*harga_produk).simpleMoneyFormat();
                });

                var jenis_kertas_arr = Array('Art Paper',
                                        'Art Carton',
                                        'Ivory',
                                        'Duplex',
                                        'Brown Kraft Liner',
                                        'Kraft PE',
                                        'Ivory Food Grade',
                                        'E Flute',
                                        'B Flute',
                                        'C Flute',
                                        'CB Flute',
                                        'Lainnya');
                var gramasi_arr = Array('K125/M125/M125 (SF COKLAT)',
                                        'K125/M125/K125 (COKLAT-COKLAT)',
                                        'K150/M125/K150 (COKLAT-COKLAT)',
                                        'WK140/M125/M125 (SF PUTIH)',
                                        'WK140/M1215/K125 (PUTIH-COKLAT)',
                                        'WK140/M125/K150 (PUTIH-COKLAT)',
                                        'WK140/M125/WK140 (PUTIH-PUTIH)',
                                        'Lainnya');
                
                $('#modal_harga_rinci').modal('show');
                $('#id_produksi_hr').val(data[0].id_produksi);
                $('#kategori_produksi_hr').find('option[value="' + data[0].kategori_produksi + '"]').prop('selected', true);
                $('#jenis_bahan_hr').find('option[value="' + data[0].jenis_bahan + '"]').prop('selected', true);
                $('#dp_panjang_hr').val(data[0].dp_panjang);
                $('#dp_lebar_hr').val(data[0].dp_lebar);
                $('#dp_tinggi_hr').val(data[0].dp_tinggi);
                $('#lp_panjang_hr').val(data[0].lp_panjang);
                $('#lp_lebar_hr').val(data[0].lp_lebar);
                if(jenis_kertas_arr.includes(data[0].jenis_kertas)){
                    $('#jenis_kertas_lainnya_hr').hide();
                    $('#jenis_kertas_hr').find('option[value="' + data[0].jenis_kertas + '"]').prop('selected', true);
                }else{
                    $('#jenis_kertas_lainnya_hr').show();
                    $('#jenis_kertas_lainnya_hr').val(data[0].jenis_kertas);
                    $('#jenis_kertas_hr').find('option[value="Lainnya"]').prop('selected', true);
                }
                if(gramasi_arr.includes(data[0].gramasi)){
                    $('#gramasi_lainnya_hr').hide();
                    $('#gramasi_hr').find('option[value="' + data[0].gramasi + '"]').prop('selected', true);
                }else{
                    $('#gramasi_lainnya_hr').show();
                    $('#gramasi_lainnya_hr').val(data[0].gramasi);
                    $('#gramasi_hr').find('option[value="Lainnya"]').prop('selected', true);
                }
                $('#laminasi_hr').find('option[value="' + data[0].laminasi + '"]').prop('selected', true);
                $('#sisi_laminasi_hr').find('option[value="' + data[0].sisi_laminasi + '"]').prop('selected', true);
                $('#tipe_produk_hr').find('option[value="' + data[0].tipe_produk + '"]').prop('selected', true);
                $('#isi_kertas_hr').val(data[0].isi_kertas);
                $('#get_plano_hr').val(data[0].get_plano);
                $('#jumlah_produk_hr').val(data[0].jumlah_produk);
                $('#harga_produk_hr').val(data[0].harga_produk).simpleMoneyFormat();
                $('#total_harga_hr').val(data[0].total_harga).simpleMoneyFormat();
                $('#modal_sales_hr').val(data[0].modal_sales).simpleMoneyFormat();
                $("#hitung_harga_khusus_hr[value='" + data[0].hitung_harga_khusus + "']").prop('checked', true);
                $('#notes_hr').val(data[0].notes);
                $('#harga-rinci #nama_produk_edit').val(data[0].nama_produk);
                if (data[0].ppn === 1) {
                    $("#harga-rinci #ppn_edit").prop('checked', true);
                    $('#harga-rinci #ppn_form_edit').show();
                    $('#harga-rinci #ppn_percent_edit').val(data[0].ppn_percent);
                    $('#harga-rinci #total_ppn_edit').val(data[0].total_ppn);
                }
                $.each(data.selesai, function(i, val){
                    $("input[value='" + val + "']").prop('checked', true);
                    if(data.selesai == 'Lainnya'){
                        $('#finishing_lainnya_hr').show();
                        $('#finishing_lainnya_hr').val(data.selesai);
                    }else{
                        $('#finishing_lainnya_hr').hide();
                    }
                });
            })
        });

        $('#harga-rinci #ppn_edit').on('change', function() {
            if ($('#harga-rinci #ppn_edit').is(':checked')) {
                $('#harga-rinci #ppn_form_edit').show();
            } else {
                $('#harga-rinci #ppn_form_edit').hide();
            }
        });

        $('#process-form-harga-rinci').click(function (e) {
            e.preventDefault();
            // if(validator_hr_order) {
                // validator_hr_order.validate().then(function (status) {
                    // if (status == 'Valid') {
                        $.ajax({
                            data: $('#harga-rinci').serialize(),
                            url: "{{ route('crm.edit_produk') }}",
                            type: "POST",
                            dataType: 'json',
                            success: function (data) {
                                $('#harga-rinci').trigger("reset");
                                setTimeout(function () {
                                    self.$("#close-form-harga-rinci").trigger("click");
                                }, 1200);
                                window.dt_hr.ajax.reload();
                                $( "#count_price_null" ).load(window.location.href + " #count_price_null" );
                                $( "#count_prioritas" ).load(window.location.href + " #count_prioritas" );
                                Swal.fire({
                                    title: 'SUCCESS!',
                                    text: "Order berhasil diedit/update",
                                    icon: 'success',
                                })
                            },
                            error: function (data) {
                                console.log('Error:', data);
                                $('#process-form-harga-rinci').html('Error');
                            }
                        });
                    // }if (status == 'Invalid'){
                    //     Swal.fire({
                    //         title: 'Failed!',
                    //         text: "Lengkapi kolom yang wajib diisi",
                    //         icon: 'error',
                    //     })
                    // }
                // });
            // }
        });
    });
</script>