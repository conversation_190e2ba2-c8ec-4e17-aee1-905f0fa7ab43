<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/css/tempus-dominus.css" rel="stylesheet" type="text/css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/js/tempus-dominus.js"></script>
<script src="{{ url('js/simple.money.format.js') }}"></script>
<link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">
<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script>
    $(document).ready(function(){
        $("#kode_order_edit").autocomplete({
                source: function (request, response) {
                    var listData = [];
                    $.ajax({
                        url: "{{ route('crm.main_kode_order_get') }}",
                        dataType: "json",
                        method: "GET",
                        headers: {
                            'X-api-token': 'f9854c56b995ac7e09f207c07a656750ba80b63e'
                        },
                        data: {
                            kode_order: request.term,
                            id_customer: $('#id_cust_edit').val()
                        },
                        success: function (data) {
                            response($.map(data, function (item) {
                                return {
                                    label: item.kode_order,
                                    value: item.order_key,
                                    nama_customer: item.nama,
                                };
                            }));
                        },
                        error: function (response) {
                            console.log(response);
                        }
                    });
                },
                select: function (event, ui) {
                    $("#kode_order_edit").val(ui.item.label);
                    $("#main_order_key_edit").val(ui.item.value);
                    return false;
                },
                appendTo: '#edit-order'
            });

        $('#kode_order_edit').on('click', function() {
            $("#kode_order_edit").autocomplete( "search", "%" );
        })

        $('#status_deal_edit').not('.form-select-solid').change(function(){
            var status_deal = $(this).val();

            if(status_deal == 'Follow Up'){
                $('#status_order1_edit').show();
                $('#status_order2_edit').hide();
                $('#status_order3_edit').hide();
                $('#tanggal_order_edit').hide();
                $('#section_dp_edit').hide();
                $('#follow_up_terakhir').hide();
            }else if(status_deal == 'Deal'){
                $('#status_order1_edit').hide();
                $('#status_order2_edit').show();
                $('#status_order3_edit').hide();
                $('#tanggal_order_edit').show();
                $('#section_dp_edit').show();
                $('#follow_up_terakhir').hide();
            }else if(status_deal == 'Lost'){
                $('#status_order1_edit').hide();
                $('#status_order2_edit').hide();
                $('#status_order3_edit').show();
                $('#tanggal_order_edit').hide();
                $('#section_dp_edit').hide();
                $('#follow_up_terakhir').show();
            }
        });

        var id_order = $('#id_order_edit').val();
        $.get("{{ url('crm/show_order/') }}/" + id_order, function (data) {
            $('#order_key_edit').val(data[0].order_key);
            $('#id_cust_edit').val(data[0].idcust);
            $('#id_customer_order_edit').val(data[0].nama);
            $('#kode_order_edit').val(data[0].kode_order);
            $('#main_kode_order_edit').val(data[0].kode_order);
            if(data[0].kode_order != null){
                $('#kode_order_edit').prop('readonly', true);
                $('#kode_order_edit').addClass('form-select-solid', true);
            }
            $('#sko_edit').val(data[0].sko);
            $('#produk_ke_edit').find('option[value="' + data[0].produk_ke + '"]').prop('selected', true);
            $('#sumber_edit').val(data[0].sumber);
            $('#waktu_kontak_edit').val(moment(data[0].waktu_kontak).format('DD/MM/YYYY HH:mm'));
            $('#tgl_order_edit').val(data[0].tgl_order);
            $('#tipe_kontak_edit').val(data[0].tipe_kontak);
            $('#grading_edit').find('option[value="' + data[0].grading + '"]').prop('selected', true);
            $('#id_pic_edit').val(data[0].id);
            $('#status_deal_edit').find('option[value="' + data[0].status_deal + '"]').prop('selected',
                true);
            $('#DP_edit').val(data[0].DP).simpleMoneyFormat();
            $("#flag_lunas_edit[value='" + data[0].flag_lunas + "']").prop('checked', true);
            if(data[0].status_deal == 'Follow Up'){
                $('#status_order1_edit').show();
                $('#status_order1_edit').find('option[value="' + data[0].status_order + '"]').prop('selected',
                true);
                $('#status_order2_edit').hide();
                $('#status_order3_edit').hide();
                $('#tanggal_order_edit').hide();
                $('#section_dp_edit').hide();
                $('#follow_up_terakhir').hide();
            }else if(data[0].status_deal == 'Deal'){
                $('#status_order1_edit').hide();
                $('#status_order2_edit').show();
                $('#status_order2_edit').find('option[value="' + data[0].status_order + '"]').prop('selected',
                true);
                $('#status_order3_edit').hide();
                $('#tanggal_order_edit').show();
                $('#section_dp_edit').show();
                $('#follow_up_terakhir').hide();
            }else if(data[0].status_deal == 'Lost'){
                $('#status_order1_edit').hide();
                $('#status_order2_edit').hide(); 
                $('#status_order3_edit').show(); 
                $('#status_order3_edit').find('option[value="' + data[0].status_order + '"]').prop('selected',
                true);
                $('#tanggal_order_edit').hide();
                $('#section_dp_edit').hide();
                $('#follow_up_terakhir').show();
                $('#follow_up_terakhir_edit').find('option[value="' + data[0].follow_up_terakhir + '"]').prop('selected',
                true);
            }
            $('#catatan_order_edit').val(data[0].catatan_order);
            $("#flag_dummy_d_edit[value='" + data[0].flag_dummy + "']").prop('checked', true);
            $("#flag_dummy_s_edit[value='" + data[0].flag_dummy + "']").prop('checked', true);
            $("#flag_dummy_r_edit[value='" + data[0].flag_dummy + "']").prop('checked', true);
            $("#flag_dummy_f_edit[value='" + data[0].flag_dummy + "']").prop('checked', true);
            $("#flag_dummy_j_edit[value='" + data[0].flag_dummy + "']").prop('checked', true);
            $("#flag_dummy_n_edit[value='" + data[0].flag_dummy + "']").prop('checked', true);
            
        })

        //waktu_kontak_add_order
        $(function() {
            const picker = new tempusDominus.TempusDominus(document.getElementById('waktu_kontak_edit'),
            {
                display: {
                sideBySide: true,
                calendarWeeks: false,
                viewMode: 'calendar',
                toolbarPlacement: 'top',
                components: {
                    calendar: true,
                    date: true,
                    month: true,
                    year: true,
                    decades: true,
                    clock: true,
                    hours: true,
                    minutes: true,
                    seconds: false,
                    useTwentyfourHour: true
                },
                theme: 'light'
                }
            });

            picker.dates.formatInput = date => moment(date).format('DD/MM/YYYY HH:mm')
            picker.dates.setValue();
        });

        $('#proccess-edit-order').click(function (e) {
            e.preventDefault();
                        $.ajax({
                            data: $('#form-edit-order').serialize(),
                            url: "{{ route('crm.edit_order') }}",
                            type: "POST",
                            dataType: 'json',
                            success: function (data) {
                                $('#form-edit-order').trigger("reset");
                                Swal.fire({
                                    title: 'SUCCESS!',
                                    text: "Order berhasil diedit/update",
                                    icon: 'success',
                                });
                                window.location = "{{ route('crm.detail_order', $param_encrypt) }}";
                            },
                            error: function (data) {
                                console.log('Error:', data);
                                $('#proccess-edit-order').html('Error');
                            }
                        });
        });

    });
</script>