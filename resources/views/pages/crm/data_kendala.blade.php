<div class="row mt-20">
    <div class="col-md-9">
        <div class="card card-flush">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <!--begin::Search-->
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                        transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                    <path
                                        d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                        fill="black"></path>
                                </svg>
                            </span>
                            <!--end::Svg Icon-->
                            <input type="text" data-kt-docs-table-filter="search-kendala"
                                class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                        </div>
                        <!--end::Search-->
                        <!--begin::Toolbar-->
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-light-primary me-3" data-kt-menu-trigger="click"
                                data-kt-menu-placement="bottom-end">
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none">
                                        <path
                                            d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                                            fill="black"></path>
                                    </svg>
                                </span>
                                Filter
                            </button>
                            <div class="menu menu-sub menu-sub-dropdown w-300px w-md-325px" data-kt-menu="true"
                                id="kt-toolbar-filter">
                                <div class="px-7 py-5">
                                    <div class="mb-10">
                                        <label class="form-label fs-5 fw-bold mb-3">Solusi:</label>
                                        <div class="d-flex flex-column flex-wrap fw-bold"
                                            data-kt-docs-table-filter="solusi">
                                            <label
                                                class="form-check form-check-sm form-check-custom form-check-solid mb-3 me-5">
                                                <input class="form-check-input" type="radio" name="solusi"
                                                    value="all" checked="checked">
                                                <span class="form-check-label text-gray-600">All</span>
                                            </label>
                                        </div>
                                        <div class="d-flex flex-column flex-wrap fw-bold"
                                            data-kt-docs-table-filter="solusi">
                                            <label
                                                class="form-check form-check-sm form-check-custom form-check-solid mb-3 me-5">
                                                <input class="form-check-input" type="radio" name="solusi"
                                                    value="produksi-ulang">
                                                <span class="form-check-label text-gray-600">Produksi Ulang</span>
                                            </label>
                                        </div>
                                        <div class="d-flex flex-column flex-wrap fw-bold"
                                            data-kt-docs-table-filter="solusi">
                                            <label
                                                class="form-check form-check-sm form-check-custom form-check-solid mb-3 me-5">
                                                <input class="form-check-input" type="radio" name="solusi"
                                                    value="negosiasi">
                                                <span class="form-check-label text-gray-600">negosiasi</span>
                                            </label>
                                        </div>
                                        <div class="d-flex flex-column flex-wrap fw-bold"
                                            data-kt-docs-table-filter="solusi">
                                            <label
                                                class="form-check form-check-sm form-check-custom form-check-solid mb-3 me-5">
                                                <input class="form-check-input" type="radio" name="solusi"
                                                    value="perbaikan">
                                                <span class="form-check-label text-gray-600">perbaikan</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-end">
                                        <button type="reset" class="btn btn-light btn-active-light-primary me-2"
                                            data-kt-menu-dismiss="true" data-kt-docs-table-filter="reset">Reset</button>
                                        <button type="submit" class="btn btn-primary" data-kt-menu-dismiss="true"
                                            data-kt-docs-table-filter="filter-kendala">Apply</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--end::Toolbar-->
                    </div>
                    <table id="datatable_kendala" class="table-row-bordered compact text-nowrap dataTable no-footer table-striped table align-middle"  style="width:100%">
                        <thead>
                            <tr class="fw-bold">
                                <th>SKO</th>
                                <th>Nama Customer</th>
                                <th>Tgl Masalah</th>
                                <th>Solusi</th>
                                <th>Last Update</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody> </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="flex-lg-row-auto w-100">
            <div class="card card-xl-stretch mb-5 mb-xl-8">
                <!--begin::Body-->
                <div class="card-body d-flex align-items-center pt-3 pb-0">
                    <div class="d-flex flex-column flex-grow-1 py-2 py-lg-13 me-2">
                        <a href="#" class="fw-bold text-dark fs-4 mb-2 text-hover-primary">Koordinasi</a>
                        <span class="fw-semibold text-muted fs-5">Pihak Sales dan Produksi</span>
                    </div>
                    <img src="{{ url('images/ava-boy.svg') }}" alt="" class="align-self-end h-100px">
                </div>
                <!--end::Body-->
            </div>
            <!--begin::Careers about-->
            <div class="card bg-warning">
                <!--begin::Body-->
                <div class="card-body">
                    <!--begin::Top-->
                    <div class="mb-7">
                        <!--begin::Title-->
                        <h2 class="fs-1 w-bolder mb-6">Kendala Produksi</h2>
                        <!--end::Title-->
                        <!--begin::Text-->
                        <p class="fw-semibold fs-6">Order yang berada pada tahapan produksi dan memiliki catatan seperti
                            perbaikan, reject, dan produksi ulang akan ditampilkan pada halaman ini.</p>
                        <!--end::Text-->
                    </div>
                    <!--end::Top-->
                </div>
                <!--end::Body-->
            </div>
            <!--end::Careers about-->
        </div>
    </div>
</div>
<div class="modal fade" id="modal_fpm">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Form Penanggulangan Masalah</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-fpm-close"
                    data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                </div>
            </div>
            <div class="modal-body mt-n5">
                <form action="" id="add-fpm">
                    @csrf
                    <input type="hidden" name="id_order" id="id_order_fpm">
                    <input type="hidden" name="order_key" id="order_key_fpm">
                    <input type="hidden" name="sko_key" id="sko_key_fpm">
                    <div class="row mt-10">
                        <div class="col-md-6">
                            <div class="mb-10">
                                <label for="id_customer" class="form-label">Customer</label>
                                <input type="text" name="id_customer_order_fpm" id="id_customer_order_fpm" readonly
                                    class="form-control form-control-solid" />
                            </div>
                            <div class="mb-10">
                                <label for="kode_order" class="form-label">Main Kode Order</label>
                                <input type="text" name="kode_order" id="kode_order_fpm"
                                    class="form-control form-control-solid" />
                            </div>
                            <div class="mb-10">
                                <label for="sko" class="form-label">Sub Kode Order</label>
                                <input type="text" name="sko" id="sko_fpm" readonly
                                    class="form-control form-control-solid" />
                            </div>
                            <div class="mb-10">
                                <label for="sko" class="form-label">Spesifikasi</label>
                                <textarea type="text" rows="3" name="spesifikasi_fpm" id="spesifikasi_fpm" readonly
                                    class="form-control form-control-solid"></textarea>
                            </div>
                            <div class="mb-10">
                                <label for="name" class="form-label">PIC Sales</label>
                                <input type="text" name="name" id="name_fpm" readonly
                                    class="form-control form-control-solid" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-10">
                                <label for="tgl_masalah" class="required form-label">Tanggal Masalah Dilaporkan</label>
                                <input type="date" name="tgl_masalah" id="tgl_masalah_fpm" class="form-control" />
                            </div>
                            <!-- <div class="fv-row mb-10">
                                <label for="tgl_deadline" class="required form-label">Tanggal Deadline Penyelesaian</label>
                                <input type="date" name="tgl_deadline_perbaikan" id="tgl_deadline_perbaikan" class="form-control"/>
                            </div> -->
                            <div class="mb-10">
                                <label for="kategori_masalah" class="required form-label">Kategori Masalah</label>
                                <select name="kategori_masalah" id="kategori_masalah_fpm" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih kategori</option>
                                    @foreach ($kategori_masalah as $masalah)
                                    <option value="{{ $masalah->kategori_masalah }}">{{ $masalah->kategori_masalah }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="mb-10">
                                <label for="detail_masalah" class="form-label">Detail Masalah</label>
                                <textarea name="detail_masalah" id="detail_masalah_fpm" class="form-control"
                                    rows="3"></textarea>
                            </div>
                            
                                <div class="mb-10">
                                    <div class="solusi" id="solusi">
                                        <div class="row">
                                            <div class="alert alert-danger d-flex align-items-center p-5">
                                                <div class="d-flex flex-column">
                                                    <h4 class="mb-1 text-dark">Solusi</h4>
                                                    <span>Harap pilih salah satu solusi yang tersedia dibawah.</span>
                                                        <div class="btn-group mt-3 justify-content-md-center" role="group">
                                                            <a class="btn btn-shadow btn-my-primary text-white" id="solusi-pu">Produksi Ulang</a>
                                                            <a class="btn btn-shadow btn-my-primary text-white" id="solusi-nego">Negosiasi</a>
                                                            <a class="btn btn-shadow btn-my-primary text-white" id="solusi-perbaikan">Perbaikan</a>
                                                        </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="solusi-chosen" id="solusi-chosen">
                                        <label for="solusi" class="form-label">Solusi</label>
                                        <input type="text" name="solusi" readonly id="solusi_fpm" class="form-control form-control-solid" />
                                    </div>
                                </div>
                            
                        </div>
                    </div>
                    {{-- <div class="d-flex justify-content-end">
                            <input type="submit" name="fpm_submit" value="Submit" class="btn btn-my-primary btn-shadow btn-lg text-white" id="process-fpm">
                        </div> --}}

                </form>

            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" id="close-form-fpm" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                <button type="submit" id="process-fpm" class="btn btn-primary my-primary">Save</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal_solusi">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Form Solusi</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-solusi-close"
                    data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                </div>
            </div>
            <div class="modal-body mt-n5">
                <form action="" id="add-solusi">
                    @csrf
                    <input type="hidden" name="id_order" id="id_order_solusi">
                    <input type="hidden" name="order_key" id="order_key_fsolusi">
                    <input type="hidden" name="sko_key" id="sko_key_solusi">
                    <div class="row mt-10">
                        <div class="col-md-6">
                            <div class="mb-10">
                                <label for="id_customer" class="form-label">Customer</label>
                                <input type="text" name="id_customer_order_solusi" id="id_customer_order_solusi" readonly
                                    class="form-control form-control-solid" />
                            </div>
                            <div class="mb-10">
                                <label for="kode_order" class="form-label">Main Kode Order</label>
                                <input type="text" name="kode_order" id="kode_order_solusi"
                                    class="form-control form-control-solid" />
                            </div>
                            <div class="mb-10">
                                <label for="sko" class="form-label">Sub Kode Order</label>
                                <input type="text" name="sko" id="sko_solusi" readonly
                                    class="form-control form-control-solid" />
                            </div>
                            <div class="mb-10">
                                <label for="sko" class="form-label">Spesifikasi</label>
                                <textarea type="text" rows="3" name="spesifikasi_solusi" id="spesifikasi_solusi" readonly
                                    class="form-control form-control-solid"></textarea>
                            </div>
                            <div class="mb-10">
                                <label for="name" class="form-label">PIC Sales</label>
                                <input type="text" name="name" id="name_solusi" readonly
                                    class="form-control form-control-solid" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="fv-row mb-10">
                                <label for="tgl_deadline" class="required form-label">Tanggal Deadline Penyelesaian</label>
                                <input type="date" name="tgl_deadline_solusi" id="tgl_deadline_solusi" class="form-control"/>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="solusi" class="required form-label">Solusi</label>
                                <input type="input" name="solusi" id="solusi_solusi" readonly class="form-control form-control-solid"/>
                            </div>
                            <div class="mb-10">
                                <label for="tgl_deadline" class="form-label">
                                    Detail Solusi
                                </label>
                                <textarea name="detail_solusi" id="detail_solusi_solusi" rows="3" class="form-control"></textarea>
                            </div>
                            <div class="mb-10">
                                <label for="cost" class="form-label">Cost yang ditimbulkan</label>
                                <div class="input-group">
                                    <span class="input-group-text">Rp. </span>
                                    <input type="text" name="cost" id="cost_solusi" class="form-control"/>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" id="close-form-solusi" class="btn btn-light"
                    data-bs-dismiss="modal">Close</button>
                <button type="submit" id="process-solusi" class="btn btn-primary my-primary">Save</button>
            </div>
        </div>
    </div>
</div>
