<div class="card card-flush mt-20">
    <div class="card-body pt-0">
        <div class="py-5">
            <div class="d-flex flex-stack flex-wrap mb-5">
                <!--begin::Search-->
                <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                    <!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
                    <span class="svg-icon svg-icon-1 position-absolute ms-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                            <path
                                d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                fill="black"></path>
                        </svg>
                    </span>
                    <!--end::Svg Icon-->
                    <input type="text" data-kt-docs-table-filter="search-harga-rinci"
                        class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                </div>
                <!--end::Search-->
                <!--begin::Toolbar-->
                <div class="d-flex gap-2" style="width: 280px">
                    <div class="w-100 mw-125px">
                        <select class="form-select form-select-solid filter_modal_sales" name="modal_sales">
                            <option hidden>Grading</option>
                            <option value="">All</option>
                            <option value="Reguler">Reguler</option>
                            <option value="Premium">Premium</option>
                            <option value="VIP">VIP</option>
                        </select>
                    </div>
                    <div class="w-100 mw-150px">
                        <select class="form-select form-select-solid filter-order-status-deal" name="status_deal">
                            <option hidden>Status Deal</option>
                            <option value="">All</option>
                            <option value="Follow Up">Follow Up</option>
                            <option value="Deal">Deal</option>
                            <option value="Lost">Lost</option>
                        </select>
                    </div>
                </div>
                
                <!--end::Toolbar-->
            </div>
            <table id="datatable_harga_rinci" class="table align-middle table-row-bordered compact text-nowrap dataTable no-footer">
                <thead>
                    <tr class="fw-bold">
                        <th>Waktu Kontak</th>
                        <th>Nama Customer</th>
                        <th>Grading</th>
                        <th>Status Deal</th>
                        <th>Modal Sales</th>
                        <th>Total Harga</th>
                        <th>Last Update</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody> </tbody>
            </table>
        </div>
    </div>
</div>

<!-- edit produk -->
<div class="modal fade" id="modal_harga_rinci">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Harga Rinci</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-harga-rinci-close" data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                </div>
            </div>
            <div class="modal-body mt-n5">
                <form action="" id="harga-rinci">
                    <input type="hidden" name="id_produksi" id="id_produksi_hr">
                    <div class="row mt-10">
                        <div class="col-md-6">
                            <div class="mb-10">
                                <label for="kategori_produksi" class="form-label">Kategori</label>
                                <select name="kategori_produksi" id="kategori_produksi_hr" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih Kategori</option>
                                    <option value="Packaging">Packaging</option>
                                    <option value="Packaging Support">Packaging Support</option>
                                    <option value="Non Packaging">Non Packaging</option>
                                    <option value="Dummy">Dummy</option>
                                    {{-- <option value="PPN">PPN</option> --}}
                                    <option value="Jasa Desain">Jasa Desain</option>
                                    <option value="Lain-lain">Lain-lain</option>
                                </select>
                            </div>
                            <div class="mb-10">
                                <label for="jenis_bahan" class="form-label">Jenis Bahan</label>
                                <select name="jenis_bahan" id="jenis_bahan_hr" class="form-select ">
                                    <option value="" disabled selected hidden>-- Pilih Jenis Bahan</option>
                                    @foreach ($bahan as $bahan)
                                    <option value="{{ $bahan->bahan }}">{{ $bahan->bahan }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="mb-14">
                                <label for="dimensi_produk" class="form-label">Dimensi Produk (P x L x
                                    T)</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="input-group">
                                            <input type="text" name="dp_panjang" id="dp_panjang_hr"
                                            class="form-control" placeholder="panjang" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="input-group">
                                            <input type="text" name="dp_lebar" id="dp_lebar_hr" class="form-control"
                                            placeholder="lebar" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="input-group">
                                            <input type="text" name="dp_tinggi" id="dp_tinggi_hr" class="form-control"
                                            placeholder="tinggi" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="luas_permukaan" class="form-label">Luas Permukaan (P x L)</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <input type="text" name="lp_panjang" id="lp_panjang_hr"
                                            class="form-control" placeholder="panjang" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <input type="text" name="lp_lebar" id="lp_lebar_hr" class="form-control"
                                            placeholder="lebar" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="jenis_kertas" class="form-label">Jenis Kertas</label>
                                <select name="jenis_kertas" id="jenis_kertas_hr" class="form-select ">
                                    <option value="" disabled selected hidden>-- Pilih Jenis Kertas</option>
                                    @foreach ($jenis_kertas as $jenis_kertas)
                                    <option value="{{ $jenis_kertas->jenis_kertas }}">{{ $jenis_kertas->jenis_kertas }}</option>
                                    @endforeach
                                </select>
                                <input type="text" name="jenis_kertas_lainnya" id="jenis_kertas_lainnya_hr"
                                    class="form-control" placeholder="Isi jika lainnya" />
                            </div>
                            <div class="mb-10">
                                <label for="gramasi" class="form-label">Gramasi</label>
                                <select name="gramasi" id="gramasi_hr" class="form-select ">
                                    <option value="" disabled selected hidden>-- Pilih Gramasi</option>
                                    @foreach ($gramasi as $gramasi)
                                    <option value="{{ $gramasi->gramasi }}">{{ $gramasi->gramasi }}</option>
                                    @endforeach
                                    <option value="Lainnya">Lainnya</option>
                                </select>
                                <input type="text" name="gramasi_lainnya" id="gramasi_lainnya_hr" class="form-control"
                                    placeholder="Isi jika lainnya" />
                            </div>
                            <div class="mb-10">
                                <label for="laminasi" class="form-label">Laminasi</label>
                                <select name="laminasi" id="laminasi_hr" class="form-select ">
                                    <option value="" disabled selected hidden>-- Pilih Laminasi</option>
                                    <option value="Tanpa Laminasi">Tanpa Laminasi</option>
                                    <option value="Laminasi Glossy">Laminasi Glossy</option>
                                    <option value="Laminasi Doff">Laminasi Doff</option>
                                    <option value="Laminasi Jendela Glossy">Laminasi Jendela Glossy</option>
                                </select>
                            </div>
                            <div class="mb-10">
                                <label for="sisi_laminasi" class="form-label">Sisi Laminasi</label>
                                <select name="sisi_laminasi" id="sisi_laminasi_hr" class="form-select ">
                                    <option value="" disabled selected hidden>-- Pilih sisi laminasi</option>
                                    <option value="Sisi Dalam">Sisi Dalam</option>
                                    <option value="Sisi Luar">Sisi Luar</option>
                                    <option value="2 Sisi">2 Sisi</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-5">
                                <label for="finishing" class="form-label">Finishing</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Pond/potong"
                                                name="finishing[]" id="finishing1_hr" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Pond/Potong
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Spot UV"
                                                name="finishing[]" id="finishing2_hr" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Spot UV
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Emboss"
                                                name="finishing[]" id="finishing3_hr" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Emboss
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="UV Varnish"
                                                name="finishing[]" id="finishing4_hr" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                UV Varnish
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Hot Stamp"
                                                name="finishing[]" id="finishing5_hr" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Hot Stamp
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Lem Samping"
                                                name="finishing[]" id="finishing6_hr" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Lem Samping
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Lem Lapis"
                                                name="finishing[]" id="finishing7_hr" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Lem Lapis
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Jendela Mika"
                                                name="finishing[]" id="finishing8_hr" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Jendela Mika
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Magnet/Tali"
                                                name="finishing[]" id="finishing9_hr" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Magnet/Tali
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Sablon"
                                                name="finishing[]" id="finishing10_hr" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Sablon
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Finishing Hardbox"
                                                name="finishing[]" id="finishing11_hr" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Finishing Hardbox
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Lainnya"
                                                name="finishing[]" id="finishing12_hr" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Lainnya
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <input type="text" name="finishing_lainnya" id="finishing_lainnya_hr"
                                    class="form-control" placeholder="Isi jika lainnya" />
                            </div>
                            <div class="mb-10">
                                <label for="tipe_produk" class="form-label">Tipe Produk</label>
                                <select name="tipe_produk" id="tipe_produk_hr" class="form-select ">
                                    <option value="" disabled selected hidden>-- Pilih Produk</option>
                                    @foreach ($tipe_produk as $tipe_produk)
                                    <option value="{{ $tipe_produk->tipe_produk }}">{{ $tipe_produk->tipe_produk }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="isi_kertas" class="form-label">Kertas Plano</label>
                                        <select name="isi_kertas" id="isi_kertas_hr" class="form-select ">
                                            <option value="" disabled selected hidden>-- Pilih Kertas</option>
                                            @foreach ($kertas_plano as $plano)
                                            <option value="{{ $plano->kertas_plano }}">{{ $plano->kertas_plano }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="get_plano" class="form-label">Get Plano</label>
                                        <input type="text" name="get_plano" placeholder="1-100" id="get_plano_hr"
                                            class="form-control" />
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="jumlah_produk" class="form-label">Jumlah Produk</label>
                                        <div class="input-group">
                                            <input type="text" name="jumlah_produk" id="jumlah_produk_hr"
                                            class="form-control" />
                                            <span class="input-group-text">pcs</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="harga_produk" class="form-label">Harga Produk</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp. </span>
                                            <input type="text" name="harga_produk" id="harga_produk_hr"
                                            class="form-control" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="total_harga" class="form-label">Total Harga</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp.</span>
                                            <input type="text" readonly name="total_harga" id="total_harga_hr"
                                            class="form-control form-control-solid" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="modal_sales" class="form-label">Modal Sales</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp.</span>
                                            <input type="text" name="modal_sales" id="modal_sales_hr"
                                            class="form-control" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="nama_produk_edit" class="form-label required">Nama Produk</label>
                                    <input type="text" name="nama_produk_edit" id="nama_produk_edit" class="form-control" placeholder="Nama produk" style="min-width: 260px;">
                                </div>

                                <div class="form-check mt-4 col-md-6">
                                    <input class="form-check-input" type="checkbox" value="PPN" name="ppn_edit" id="ppn_edit">
                                    <label class="form-check-label" for="ppn_edit">PPN</label>
                                </div>
                            </div>
                            <div class="row" id="ppn_form_edit">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="ppn_percent_edit" class="form-label">Persentase PPN</label>
                                        <div class="input-group">
                                            <input type="text" name="ppn_percent_edit"
                                            id="ppn_percent_edit" class="form-control" />
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="total_ppn_edit" class="form-label">Total PPN</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp.</span>
                                            <input type="text" readonly name="total_ppn_edit" id="total_ppn_edit"
                                                class="form-control  form-control-solid" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="notes" class="form-label">Catatan Produk</label>
                                <textarea name="notes" id="notes_hr" rows="6"
                                    class="form-control"></textarea>
                            </div>
                            <div class="mb-10">
                                <label for="hitung_harga_khusus" class="form-label">Hitung Harga Khusus</label>
                                <div class="form-check form-switch form-check-custom">
                                    <input class="form-check-input" name="hitung_harga_khusus" type="checkbox" value="1"
                                        id="hitung_harga_khusus_hr" />
                                    <label class="form-check-label" for="flexSwitchDefault">Tidak/Ya</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" id="close-form-harga-rinci" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                <button type="submit" id="process-form-harga-rinci" class="btn btn-primary my-primary">Save</button>
            </div>
        </div>
    </div>
</div>
