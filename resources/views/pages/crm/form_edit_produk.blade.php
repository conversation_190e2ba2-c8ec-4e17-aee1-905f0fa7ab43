<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ url()->previous() }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">Edit Produk</h3>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        @include('pages.crm.data_jscript_form_edit_produk')
    </x-slot>

    <div class="container-xxl mt-20" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-header">
                <h2 class="card-title text-dark">#{{ $data->kode_order }} - {{ $data->nama }}</h2>
            </div>
            <div class="card-body pt-0">
                <form id="edit-produk">
                    @csrf
                    @method('POST')
                    <input type="hidden" name="id_produksi" value="{{ $data->id_produksi }}" id="id_produksi_edit">
                    <input type="hidden" name="order_key" id="order_key_edit">
                    <input type="hidden" name="sko_key" value="{{ $data->sko_key }}" id="sko_key_edit">
                    <input type="hidden" name="id_cust_edit" id="id_cust_edit"/>
                    <input type="hidden" name="kode_order" id="kode_order_edit"/>
                    <div class="row mt-10">
                        <div class="col-md-6">
                            <div class="mb-10">
                                <label for="kategori_produksi" class="form-label">Kategori</label>
                                <select name="kategori_produksi" id="kategori_produksi_edit" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih Kategori</option>
                                    <option value="Packaging">Packaging</option>
                                    <option value="Packaging Support">Packaging Support</option>
                                    <option value="Non Packaging">Non Packaging</option>
                                    <option value="Dummy">Dummy</option>
                                    {{-- <option value="PPN">PPN</option> --}}
                                    <option value="Jasa Desain">Jasa Desain</option>
                                    <option value="Lain-lain">Lain-lain</option>
                                </select>
                            </div>
                            <div class="mb-10">
                                <label for="jenis_bahan" class="form-label">Jenis Bahan</label>
                                <select name="jenis_bahan" id="jenis_bahan_edit" class="form-select ">
                                    <option value="" disabled selected hidden>-- Pilih Jenis Bahan</option>
                                    @foreach ($bahan as $bahan)
                                    <option value="{{ $bahan->bahan }}">{{ $bahan->bahan }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="mb-14">
                                <label for="dimensi_produk" class="form-label">Dimensi Produk (P x L x
                                    T)</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="input-group">
                                            <input type="number" step="any" name="dp_panjang" id="dp_panjang_edit"
                                            class="form-control" placeholder="panjang" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="input-group">
                                            <input type="number" step="any" name="dp_lebar" id="dp_lebar_edit" class="form-control"
                                            placeholder="lebar" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="input-group">
                                            <input type="number" step="any" name="dp_tinggi" id="dp_tinggi_edit" class="form-control"
                                            placeholder="tinggi" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="luas_permukaan" class="form-label">Luas Permukaan (P x L)</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <input type="number" step="any" name="lp_panjang" id="lp_panjang_edit"
                                            class="form-control" placeholder="panjang" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <input type="number" step="any" name="lp_lebar" id="lp_lebar_edit" class="form-control"
                                            placeholder="lebar" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="jenis_kertas" class="form-label">Jenis Kertas</label>
                                <select name="jenis_kertas" id="jenis_kertas_edit" class="form-select ">
                                    <option value="" disabled selected hidden>-- Pilih Jenis Kertas</option>
                                    @foreach ($jenis_kertas as $jenis_kertas)
                                    <option value="{{ $jenis_kertas->jenis_kertas }}">{{ $jenis_kertas->jenis_kertas }}</option>
                                    @endforeach
                                    <option value="Lainnya">Lainnya</option>
                                </select>
                                <input type="text" name="jenis_kertas_lainnya" id="jenis_kertas_lainnya_edit"
                                    class="form-control" placeholder="Isi jika lainnya" />
                            </div>
                            <div class="mb-10">
                                <label for="gramasi" class="form-label">Gramasi</label>
                                <select name="gramasi" id="gramasi_edit" class="form-select ">
                                    <option value="" disabled selected hidden>-- Pilih Gramasi</option>
                                    @foreach ($gramasi as $gramasi)
                                    <option value="{{ $gramasi->gramasi }}">{{ $gramasi->gramasi }}</option>
                                    @endforeach
                                    <option value="Lainnya">Lainnya</option>
                                </select>
                                <input type="text" name="gramasi_lainnya" id="gramasi_lainnya_edit" class="form-control"
                                    placeholder="Isi jika lainnya" />
                            </div>
                            <div class="mb-10">
                                <label for="laminasi" class="form-label">Laminasi</label>
                                <select name="laminasi" id="laminasi_edit" class="form-select ">
                                    <option value="" disabled selected hidden>-- Pilih Laminasi</option>
                                    <option value="Tanpa Laminasi">Tanpa Laminasi</option>
                                    <option value="Laminasi Glossy">Laminasi Glossy</option>
                                    <option value="Laminasi Doff">Laminasi Doff</option>
                                    <option value="Laminasi Jendela Glossy">Laminasi Jendela Glossy</option>
                                </select>
                            </div>
                            <div class="mb-10">
                                <label for="sisi_laminasi" class="form-label">Sisi Laminasi</label>
                                <select name="sisi_laminasi" id="sisi_laminasi_edit" class="form-select ">
                                    <option value="" disabled selected hidden>-- Pilih sisi laminasi</option>
                                    <option value="Sisi Dalam">Sisi Dalam</option>
                                    <option value="Sisi Luar">Sisi Luar</option>
                                    <option value="2 Sisi">2 Sisi</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-5">
                                <label for="finishing" class="form-label">Finishing</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Pond/potong"
                                                name="finishing[]" id="finishing1_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Pond/Potong
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Spot UV"
                                                name="finishing[]" id="finishing2_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Spot UV
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Emboss"
                                                name="finishing[]" id="finishing3_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Emboss
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="UV Varnish"
                                                name="finishing[]" id="finishing4_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                UV Varnish
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Hot Stamp"
                                                name="finishing[]" id="finishing5_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Hot Stamp
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Lem Samping"
                                                name="finishing[]" id="finishing6_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Lem Samping
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Lem Lapis"
                                                name="finishing[]" id="finishing7_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Lem Lapis
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Jendela Mika"
                                                name="finishing[]" id="finishing8_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Jendela Mika
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Magnet/Tali"
                                                name="finishing[]" id="finishing9_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Magnet/Tali
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Sablon"
                                                name="finishing[]" id="finishing10_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Sablon
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Finishing Hardbox"
                                                name="finishing[]" id="finishing11_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Finishing Hardbox
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Lainnya"
                                                name="finishing[]" id="finishing12_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Lainnya
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <input type="text" name="finishing_lainnya" id="finishing_lainnya_edit"
                                    class="form-control" placeholder="Isi jika lainnya" />
                            </div>
                            <div class="mb-10">
                                <label for="tipe_produk" class="form-label">Tipe Produk</label>
                                <select name="tipe_produk" id="tipe_produk_edit" class="form-select ">
                                    <option value="" disabled selected hidden>-- Pilih Produk</option>
                                    @foreach ($tipe_produk as $tipe_produk)
                                    <option value="{{ $tipe_produk->tipe_produk }}">{{ $tipe_produk->tipe_produk }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="isi_kertas" class="form-label">Kertas Plano</label>
                                        <select name="isi_kertas" id="isi_kertas_edit" class="form-select ">
                                            <option value="" disabled selected hidden>-- Pilih Kertas</option>
                                            @foreach ($kertas_plano as $kertas_plano)
                                            <option value="{{ $kertas_plano->kertas_plano }}">{{ $kertas_plano->kertas_plano }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="get_plano" class="form-label">Get Plano</label>
                                        <input type="text" name="get_plano" placeholder="Hasil potong kertas" id="get_plano_edit"
                                            class="form-control" />
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="jumlah_produk" class="form-label">Jumlah Produk</label>
                                        <div class="input-group">
                                            <input type="text" name="jumlah_produk" id="jumlah_produk_edit"
                                            class="form-control" placeholder="Qty">
                                            <span class="input-group-text">pcs</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="harga_produk" class="form-label">Harga Produk</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp. </span>
                                            <input type="text" name="harga_produk" id="harga_produk_edit"
                                            class="form-control" placeholder="Harga satuan"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="total_harga" class="form-label">Total Harga</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp.</span>
                                            <input type="text" readonly name="total_harga" id="total_harga_edit"
                                            class="form-control form-control-solid" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="modal_sales" class="form-label">Modal Sales</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp.</span>
                                            <input type="text" name="modal_sales" id="modal_sales_edit"
                                            class="form-control" placeholder="Total modal sales"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-3 mb-3">
                                <div class="">
                                    <label for="nama_produk" class="form-label required">Nama Produk</label>
                                    <input type="text" name="nama_produk" id="nama_produk" class="form-control" placeholder="Nama produk" style="min-width: 260px;">
                                </div>

                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" value="PPN" name="ppn" id="ppn">
                                    <label class="form-check-label" for="ppn">PPN</label>
                                </div>
                            </div>
                            <div class="row" id="ppn_form">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="ppn_percent" class="form-label">Persentase PPN</label>
                                        <div class="input-group">
                                            <input type="text" name="ppn_percent"
                                            id="ppn_percent" class="form-control" />
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="total_ppn" class="form-label">Total PPN</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp.</span>
                                            <input type="text" readonly name="total_ppn" id="total_ppn"
                                                class="form-control  form-control-solid" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="notes" class="form-label">Catatan Produk</label>
                                <textarea name="notes" id="notes_edit" rows="6"
                                    class="form-control"></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="hitung_harga_khusus" class="form-label">Hitung Harga Khusus</label>
                                        <div class="form-check form-switch form-check-custom">
                                            <input class="form-check-input" name="hitung_harga_khusus" type="checkbox" value="1"
                                                id="hitung_harga_khusus_edit" />
                                            <label class="form-check-label" for="flexSwitchDefault">Tidak/Ya</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="kendala_produksi" class="form-label">Kendala Produksi</label>
                                        <div class="form-check form-switch form-check-custom">
                                            <input class="form-check-input" name="kendala_produksi" type="checkbox" value="1"
                                                id="kendala_produksi_edit" />
                                            <label class="form-check-label" for="flexSwitchDefault">Tidak/Ya</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button form="form-edit-order" id="process-form-edit-produk" class="btn my-primary text-white float-end">Edit Order</button>
                </form>
            </div>
        </div>
    </div>


</x-app-layout>
