<div class="card card-flush mt-20">
    <div class="card-body pt-0">
        <div class="py-5">
            <div class="d-flex flex-stack flex-wrap mb-5">
                <!--begin::Search-->
                <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                    <!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
                    <span class="svg-icon svg-icon-1 position-absolute ms-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                            <path
                                d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                fill="black"></path>
                        </svg>
                    </span>
                    <!--end::Svg Icon-->
                    <input type="text" data-kt-docs-table-filter="search-customer"
                        class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                </div>
                <!--end::Search-->

                <!--begin::Toolbar-->
                <div class="d-flex justify-content-end gap-3">
                    <div>
                        <select class="form-select form-select-solid filter_modal_sales_customer" name="modal_sales">
                            <option hidden>Grading</option>
                            <option value="">All</option>
                            <option value="Reguler">Reguler</option>
                            <option value="Premium">Premium</option>
                            <option value="VIP">VIP</option>
                        </select>
                    </div>
                    <div>
                        <select class="form-select form-select-solid filter_inst_type" name="tipe_instansi">
                            <option hidden>Tipe Instansi</option>
                            <option value="">All</option>
                            <option value="Komunitas">Komunitas</option>
                            <option value="Lembaga Pendidikan">Lembaga Pendidikan</option>
                            <option value="Pemerintahan">Pemerintahan</option>
                            <option value="Perseorangan/UMKM">Perseorangan/UMKM</option>
                            <option value="Swasta">Swasta</option>
                        </select>
                    </div>
                    <!--begin::Add customer-->
                    <button type="button" class="btn btn-success" data-bs-toggle="modal"
                        data-bs-target="#modal_add_customer" title="Add Customer">
                        <!--begin::Svg Icon | path: icons/duotune/arrows/arr075.svg-->
                        <span class="svg-icon svg-icon-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none">
                                <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1"
                                    transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                                <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="black"></rect>
                            </svg>
                        </span>
                        <!--end::Svg Icon-->Add Customer</button>
                    <!--end::Add customer-->
                </div>
                <!--end::Toolbar-->
            </div>
            <table id="datatable_customer" class="table-row-bordered compact text-nowrap dataTable no-footer table-striped table align-middle"  style="width:100%">
                <thead>
                    <tr class="fw-bold">
                        <th>Kode Customer</th>
                        <th>Nama</th>
                        <th>No HP</th>
                        <th>Email Bisnis</th>
                        <th>Jabatan</th>
                        {{-- <th>Bidang Usaha</th> --}}
                        <th>Grading</th>
                        <th>Tipe Instansi</th>
                        <th>Nama Instansi</th>
                        <th>Nama Brand</th>
                        <th>Last Update</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody> </tbody>
            </table>
        </div>
    </div>
</div>

<div class="modal fade" id="modal_add_customer">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tambah Customer Baru</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-customer-close"
                    data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                </div>
            </div>
            <div class="modal-body mt-n5">
                <form action="" id="add-new-customer">
                    <div class="row mt-10">
                        <div class="col-md-6">
                            <div class="fv-row mb-10">
                                <label for="nama" class="required form-label">Nama</label>
                                <input type="text" name="nama" id="nama" required class="form-control" placeholder="Nama - (3 digit akhir no hp). Ex: Fulan - 123, Anto - 599"/>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="no_hp" class="required form-label">No HP</label>
                                <input type="text" name="no_hp" id="no_hp" required class="form-control" placeholder="Ex: 6285157174545"/>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="email_bisnis" class="required form-label">Email Bisnis</label>
                                <input type="text" name="email_bisnis" id="email_bisnis" required class="form-control" placeholder="Ex: <EMAIL>, <EMAIL>"/>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="posisi_pic_cust" class="required form-label">Jabatan</label>
                                <input type="text" name="posisi_pic_cust" id="posisi_pic_cust" required class="form-control" placeholder="Ex: Owner, Purchasing"/>
                            </div>
                             <div class="fv-row mb-10">
                                <label for="jenis_usaha" class="required form-label">Bidang Usaha</label>
                                <input type="text" name="jenis_usaha" id="jenis_usaha" required class="form-control" placeholder="Ex: FnB, FMCG, Alat Medis"/>
                            </div>
                            <div class="mb-10">
                                <label for="alamat" class="form-label">Alamat Pribadi</label>
                                <textarea name="alamat" id="alamat" rows="3" class="form-control" placeholder="Alamat tempat tinggal pribadi customer"></textarea>
                            </div>
                            
                        </div>
                        <div class="col-md-6">
                            <div class="mb-10">
                                <label for="npwp" class="form-label">NPWP</label>
                                <input type="text" name="npwp" id="npwp" class="form-control" placeholder="Ex: 92.408.542.6-407.000"/>
                            </div>
                            <div class="mb-10">
                                <label for="grading" class="form-label">Grading</label>
                                <select name="grading" id="grading" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih grading</option>
                                    @foreach ($grading as $grading)
                                        <option value="{{ $grading->grading }}">{{ $grading->grading }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="tipe_instansi" class="required form-label">Tipe Instansi</label>
                                <select name="tipe_instansi" id="" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih Tipe Instansi</option>
                                    @foreach ($tipe_instansi as $ti)
                                    <option value="{{ $ti->tipe_instansi }}">{{ $ti->tipe_instansi }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="mb-10">
                                <label for="nama_instansi" class="form-label">Nama Instansi</label>
                                <input type="text" name="nama_instansi" id="nama_instansi" class="form-control" placeholder="Ex: PT Billah Berdikari Indonesia"/>
                            </div>
                            <div class="mb-10">
                                <label for="nama_brand" class="form-label">Nama Brand</label>
                                <input type="text" name="nama_brand" id="nama_brand" class="form-control" placeholder="Ex: HMNS, Coca-cola"/>
                            </div>
                            <div class="mb-10">
                                <label for="alamat_instansi" class="form-label">Alamat Instansi</label>
                                <textarea name="alamat_instansi" id="alamat_instansi" rows="10"
                                    class="form-control" placeholder="Alamat instansi/tempat kerja customer"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" id="close-form-customer" class="btn btn-light"
                    data-bs-dismiss="modal">Close</button>

                <div class="row">
                    <div class="col d-flex justify-content-center">
                        <input type="submit" form="add-new-customer" name="save" value="Save & Add Order"
                            id="process-form-customer-order" class="btn btn-primary">
                    </div>
                    {{-- <div class="col d-flex justify-content-center">
                        <input type="submit" form="add-new-customer" name="save" value="Save" id="process-form-customer"
                            class="btn btn-primary my-primary">
                    </div> --}}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal_edit_customer">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-body mt-n5">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="modal-title">Edit Customer Baru</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-edit-customer-close"
                        data-bs-dismiss="modal" aria-label="Close">
                        <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                    </div>
                </div>
                <div class="d-grid mt-3">
                    <ul class="nav nav-tabs text-nowrap flex-nowrap">
                        <li class="nav-item">
                            <a class="nav-link btn btn-active-dark btn-color-grey-600 active me-3"
                            data-bs-toggle="tab" href="#kt_tab_pane_1">Data Customer</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-active-dark btn-color-grey-600 me-3" data-bs-toggle="tab"
                            href="#kt_tab_pane_2">Profiling</a>
                        </li>
                    </ul>
                </div>

                <form action="" id="edit-customer">
                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="kt_tab_pane_1" role="tabpanel">
                            <input type="hidden" name="id_customer" id="id_customer_edit">
                            <div class="row mt-10">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="kode_kustomer" class="required form-label">Kode Kustomer</label>
                                        <input type="text" name="kode_kustomer" id="kode_kustomer_edit" readonly
                                            value="{{ old('kode_kustomer') }}" class="form-control form-control-solid" />
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="nama" class="required form-label">Nama</label>
                                        <input type="text" name="nama" id="nama_edit" value="{{ old('nama') }}" required
                                            class="form-control" placeholder="Nama - (3 digit akhir no hp). Ex: Fulan - 123, Anto - 599"/>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="no_hp" class="required form-label">No HP</label>
                                        <input type="text" name="no_hp" id="no_hp_edit" value="{{ old('no_hp') }}" required
                                            class="form-control" placeholder="Ex: 6285157174545"/>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="email_bisnis" class="required form-label">Email Bisnis</label>
                                        <input type="text" name="email_bisnis" id="email_bisnis_edit" {{old('email_bisnis')}}required class="form-control" placeholder="Ex: <EMAIL>, <EMAIL>"/>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="posisi_pic_cust" class="required form-label">Jabatan</label>
                                        <input type="text" name="posisi_pic_cust" id="posisi_pic_cust_edit" {{old('posisi_pic_cust')}} class="form-control" placeholder="Ex: Owner, Purchasing"/>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="jenis_usaha" class=" form-label">Bidang Usaha</label>
                                        <input type="text" name="jenis_usaha" id="jenis_usaha_edit" {{old('jenis_usaha')}} class="form-control" placeholder="Ex: FnB, FMCG, Alat Medis"/>
                                    </div>
                                    <div class="mb-10">
                                        <label for="alamat" class="form-label">Alamat Pribadi</label>
                                        <textarea name="alamat" value="{{ old("alamat") }}" id="alamat_edit" rows="3"
                                            class="form-control" placeholder="Alamat tempat tinggal pribadi customer"></textarea>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="npwp" class="form-label">NPWP</label>
                                        <input type="text" name="npwp" value="{{ old("npwp") }}" id="npwp_edit"
                                            class="form-control" placeholder="Ex: 92.408.542.6-407.000"/>
                                    </div>
                                    <div class="mb-10">
                                        <label for="grading" class="form-label">Grading</label>
                                        <select name="grading" id="grading_edit" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih grading</option>
                                            @foreach ($grading2 as $grading)
                                                <option value="{{ $grading->grading }}">{{ $grading->grading }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="tipe_instansi" class="required form-label">Tipe Instansi</label>
                                        <select name="tipe_instansi" id="tipe_instansi_edit" class="form-select">
                                            <option value="" disabled>-- Pilih Tipe Instansi</option>
                                            @foreach ($tipe_instansi as $ti)
                                            <option value="{{ $ti->tipe_instansi }}">{{ $ti->tipe_instansi }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="mb-10">
                                        <label for="nama_instansi" class="form-label">Nama Instansi</label>
                                        <input type="text" name="nama_instansi" value="{{ old("nama_instansi") }}"
                                            id="nama_instansi_edit" class="form-control" placeholder="Ex: PT Billah Berdikari Indonesia"/>
                                    </div>
                                    <div class="mb-10">
                                        <label for="nama_brand" class="form-label">Nama Brand</label>
                                        <input type="text" name="nama_brand" value="{{ old("nama_brand") }}"
                                            id="nama_brand_edit" class="form-control" placeholder="Ex: HMNS, Coca-cola"/>
                                    </div>
                                    <div class="mb-10">
                                        <label for="alamat_instansi" class="form-label">Alamat Instansi</label>
                                        <textarea name="alamat_instansi" value="{{ old("alamat_instansi") }}"
                                            id="alamat_instansi_edit" rows="3" class="form-control" placeholder="Alamat instansi/tempat kerja customer"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane fade" id="kt_tab_pane_2" role="tabpanel">
                            <label for="" class="form-label mt-10">Profiling Marketing & Sales</label>
                            <hr class="mb-3">
                            <div class="row mt-5">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="durasi_berdiri" class=" form-label">Durasi Berdiri</label>
                                        <select name="durasi_berdiri" id="durasi_berdiri_edit" class="form-select">
                                            <option value="" hidden>-- Pilih durasi --</option>
                                            <option value="< 6 Bulan"> < 6 Bulan</option>
                                            <option value="6 Bulan - 5 Tahun"> 6 Bulan - 5 Tahun</option>
                                            <option value="> 5 Tahun"> > 5 Tahun</option>
                                        </select>
                                    </div>
                                    <div class="mb-10">
                                        <label for="skala_bisnis" class=" form-label">Skala Bisnis</label>
                                        <select name="skala_bisnis" id="skala_bisnis_edit" class="form-select">
                                            <option value="" hidden>-- Pilih skala bisnis --</option>
                                            <option value="Mikro">Mikro (<300jt/th)</option>
                                            <option value="Kecil">Kecil (300jt - 2.5M/th)</option>
                                            <option value="Menengah">Menengah (2.5 - 50M/th)</option>
                                            <option value="Besar">Besar (>50M/th)</option>
                                        </select>
                                    </div>
                                    <div class="mb-10">
                                        <label for="tingkat_penjualan" class=" form-label">Tingkat Penjualan Bisnis/Perusahaan</label>
                                        <select name="tingkat_penjualan" id="tingkat_penjualan_edit" class="form-select">
                                            <option value="" hidden>-- Pilih tingkat penjualan --</option>
                                            <option value="Sedikit">Sedikit</option>
                                            <option value="Stabil">Stabil</option>
                                            <option value="Sedang">Sedang</option>
                                        </select>
                                    </div>
                                    
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="segmen_pasar" class=" form-label">Segmen Pasar</label>
                                        <select name="segmen_pasar" id="segmen_pasar_edit" class="form-select">
                                            <option value="" hidden>-- Pilih segmen --</option>
                                            <option value="A">A</option>
                                            <option value="B">B</option>
                                            <option value="C">C</option>
                                        </select>
                                    </div>
                                    <div class="mb-10">
                                        <label for="channel_marketing" class=" form-label">Jenis Channel Marketing</label>
                                        <select name="channel_marketing" id="channel_marketing_edit" class="form-select">
                                            <option value="" hidden>-- Pilih channel --</option>
                                            <option value="Paid Search">Paid Search</option>
                                            <option value="Organic Search">Organic Search</option>
                                            <option value="Display">Display</option>
                                            <option value="Direct">Direct</option>
                                            <option value="Social">Social</option>
                                            <option value="Referral">Referral</option>
                                            <option value="Email">Email</option>
                                            <option value="Meta Ads">Meta Ads</option>
                                        </select>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="nama_campaign" class=" form-label">Nama Campaign</label>
                                        <input type="text" name="nama_campaign" id="nama_campaign_edit" value="{{ old('email_bisnis') }}" 
                                            class="form-control" placeholder="Nama Campaign"/>
                                    </div>
                                </div>
                            </div>

                            <label for="" class="form-label mt-10">Profiling CRM</label>
                            <hr class="mb-3">
                            <div class="row mt-5 mb-3">
                                <div class="col-md-6">
                                    <div class="fv-row mb-10">
                                        <label for="anniv_usaha" class=" form-label">Anniversary Usaha</label>
                                        <input type="date" name="anniv_usaha" id="anniv_usaha_edit" value="" 
                                            class="form-control" placeholder="xx-xx-xxxx"/>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="hari_besar" class=" form-label">Hari Besar yang di Rayakan</label>
                                        <div class="row px-3">
                                            <div class="form-check form-check-custom mb-2 col">
                                                <input class="form-check-input" type="checkbox" value="1" name="imlek" id="imlek" />
                                                <label class="form-check-label" for="imlek">
                                                    Imlek
                                                </label>
                                            </div>
                                            <div class="form-check form-check-custom mb-2 col">
                                                <input class="form-check-input" type="checkbox" value="1" name="idul_fitri" id="idul_fitri" />
                                                <label class="form-check-label" for="idul_fitri">
                                                    Idul Fitri
                                                </label>
                                            </div>
                                            <div class="form-check form-check-custom mb-2 col">
                                                <input class="form-check-input" type="checkbox" value="1" name="natal" id="natal" />
                                                <label class="form-check-label" for="natal">
                                                    Natal
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="fv-row mb-10">
                                        <label for="keb_packaging" class="form-label">Kebutuhan Packaging per Bulan</label>
                                        <div class="input-group">
                                            <input type="number" name="keb_packaging" id="keb_packaging_edit" value="" class="form-control" placeholder="ex: 1000 pcs"/>
                                            <span class="input-group-text">pcs</span>
                                        </div>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="potensi_freq" class="form-label">Potensi Frekuensi Order 1 Tahun</label>
                                        <div class="input-group">
                                            <input type="number" name="potensi_freq" id="potensi_freq_edit" value="" class="form-control" placeholder="ex: 5x"/>
                                            <span class="input-group-text">kali</span>
                                        </div>
                                    </div>
                                    <div class="row mb-10">
                                        <div class="col-6">
                                            <label for="profiling" class="form-label">Form Profiling</label>
                                            <div class="form-check form-switch form-check-custom">
                                                <input class="form-check-input" name="profiling"
                                                    type="checkbox" value="1" id="profiling" />
                                                <label class="form-check-label"
                                                    for="flexSwitchDefault">Tidak/Ya</label>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <label for="direct_meeting" class="form-label">Direct Meeting</label>
                                            <div class="form-check form-switch form-check-custom">
                                                <input class="form-check-input" name="direct_meeting"
                                                    type="checkbox" value="1" id="direct_meeting" />
                                                <label class="form-check-label"
                                                    for="flexSwitchDefault">Tidak/Ya</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer justify-content-between">
                                <button type="button" id="close-edit-customer" class="btn btn-light"
                                    data-bs-dismiss="modal">Close</button>
                                <button type="submit" id="process-edit-customer" class="btn btn-primary my-primary">Save</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal-edit-cust-company">
    <div class="modal-dialog modal-dialog-centered modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Company</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary" id="modal-edit-cust-company-close"
                    data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                </div>
            </div>
            <div class="modal-body">
                <form action="" id="edit-cust-comp">
                <input type="hidden" name="id_cust_comp" id="id-cust-comp-edit">
                <div class="fv-row mb-2">
                    <label for="company_id" class="required form-label">Nama Instansi</label>
                    <select class="form-select" name="company_id_edit" id="company_id_edit"
                        data-control="select2" data-dropdown-parent="#modal-edit-cust-company"
                        data-placeholder="Pilih Company" data-allow-clear="true">
                        <option></option>
                        @foreach ($company as $c)
                            <option value="{{$c->id}}">{{$c->name}}</option>
                        @endforeach
                    </select>
                </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="edit-comp-cust-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                <button type="button" id="submit-edit-cust-company" class="btn btn-primary my-primary">Save
                    changes</button>
            </div>
        </div>
    </div>
</div>
