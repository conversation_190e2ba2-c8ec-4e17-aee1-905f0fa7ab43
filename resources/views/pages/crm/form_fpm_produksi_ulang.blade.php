<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ url()->previous() }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">Form Penanggulangan Masalah</h3>
                    <small class="text-white fs-6 fw-normal pt-2">Produksi <PERSON></small>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">

    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-header">
                <h3 class="card-title text-dark"><i>#{{ $data->kode_order }} - {{ $data->nama }}</i></h3>
            </div>
            <div class="card-body pt-0">
                <form method="POST" id="form-faw" action="{{ route('crm.store_faw') }}" enctype="multipart/form-data">
                    @csrf
                    @method('POST')
                    <input type="hidden" name="id_order" value="{{ $data->id_order }}">
                    <input type="hidden" name="order_key" value="{{ $data->order_key }}">
                    <input type="hidden" name="sko_key" value="{{ $data->sko_key }}">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-10">
                                <label for="sko" class="form-label">Sub Kode Order</label>
                                <input type="text" name="sko" value="{{ $data->sko }}" readonly id="sko" class="form-control form-control-solid"/>
                            </div>
                            <div class="mb-10">
                                <label for="nama" class="form-label">Nama Customer</label>
                                <input type="text" name="nama" id="nama" value="{{ $data->nama }}" readonly class="form-control form-control-solid"/>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="pic" class="form-label">PIC</label>
                                <input type="text" name="pic" id="pic" value="{{ $data->name }}" readonly class="form-control form-control-solid"/>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="tgl_deadline" class="required form-label">Tanggal Deadline</label>
                                <input type="date" name="tgl_deadline" id="tgl_deadline" class="form-control"/>
                            </div>
                        </div>
                    </div>
                    <input type="submit" form="form-faw" value="Terbitkan FAW" id="store-faw" class="btn my-primary text-white float-end">
                </form>
            </div>
        </div>
    </div>

    
</x-app-layout>