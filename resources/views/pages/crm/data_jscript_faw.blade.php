<script>
    const dataTableLanguage = {
        loadingRecords: `
                    <div class="d-flex justify-content-center align-items-center">
                        <div class="spinner-grow text-warning" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>`,
        emptyTable: `
                    <div class="d-flex justify-content-center align-items-center">
                        <div role="status">
                            <span class="text-danger">Data tidak ditemukan</span>
                        </div>
                    </div>`,
        zeroRecords: `
                    <div class="d-flex justify-content-center align-items-center">
                        <div role="status">
                            <span class="text-danger">Data tidak ditemukan</span>
                        </div>
                    </div>`,
    };

    $(document).ready(function () {        

        //filter page cust
        const filterSearch = document.querySelector('[data-kt-docs-table-filter="search-faw"]')
        filterSearch.addEventListener('keyup', function (e) {
            window.throttleDTSearch(window.dt_faw, e.target.value)
        })

        const filterStatus = document.querySelector('.filter-status-faw')
        filterStatus.addEventListener('change', function(e) {
            window.dt_faw.draw()
        })

        $('#filter_by_date').on('click', function() {
            const startDate = $('#filter-dummy-from').val();
            const endDate = $('#filter-dummy-to').val();

            // Convert string dates to Date objects
            const startDateValue = new Date(startDate);
            const endDateValue = new Date(endDate);

            if (endDateValue < startDateValue)
                return

            window.dt_faw.draw()
        });

        $('#reset_date_filter').on('click', function() {
            // Clear date inputs
            $('#filter-dummy-from').val('');
            $('#filter-dummy-to').val('');

            // Redraw the DataTable
            window.dt_faw.draw();
        });
        
    }); 
    function approveConfirmation(id) {
        swal.fire({
            title: "Ubah Status FAW ?",
            text: "Harap pastikan",
            icon: "warning",
            showCancelButton: !0,
            confirmButtonText: "Ya!",
            cancelButtonText: "Tidak, batal!",
            reverseButtons: !0
        }).then(function (e) {
            if (e.value === true) {
                $.ajax({
                    url: "{{ route('crm.approve_faw','') }}" + '/' + id,
                    type: 'POST',
                    data: {
                        "id": id
                        // "_token": token,
                    },
                    success: function (data) {
                        if (data) {
                            window.dt_faw.ajax.reload()
                            swal.fire("Done!", data.success, "success");
                        } else {
                            swal("Error!", results.message, "error");
                        }
                    }
                });

            } else {
                e.dismiss;
            }
        }, function (dismiss) {
            return false;
        })
    }
</script>
