<x-app-layout>
    <x-slot name="script">
        @include('pages.crm.data_jscript_order_detail')
    </x-slot>
    <x-slot name="header">
        {{-- <a href="{{ url()->previous() }}">
        <span class="fs-1"> --}}

            {{-- </a> --}}
            {{-- Detail Customer</span> --}}
            <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
                <li class="breadcrumb-item pe-3"><a href="{{ route('crm').'#tab_faw'; }}" class="pe-3">
                        <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                    </a></li>
                <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                        <h1 class="display-4 text-white">Detail Order</h1>
                    </a></li>
            </ol>
    </x-slot>

    <div class="container-xxl mt-20" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-body pt-0 mt-5">
                <div class="py-5">
                    @if(session('success'))
                        <div class="alert alert-success">
                            <p>{{session('success')}}</p>
                        </div>
                    @endif
                    <h4><strong>{{ $data_order->sko }}</strong></h4>
                    <table>
                        <tr>
                            <td><i class="fa-solid fa-user"></i></td>
                            <td>{{ $data_order->name }}</td>
                            <td></td>
                            <td></td>
                            <td><i class="fa-regular fa-calendar-days"></i></td>
                            <td>{{ date('F', strtotime($data_order->tgl_order)) }}</td>
                        </tr>
                    </table>
                    <div class="order-content mt-10">
                        <ul class="nav nav-tabs nav-line-tabs nav-line-tabs-2x mb-5 fs-6">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#kt_tab_pane_1">Data Order</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_pane_2">Data Produk</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_pane_3">Data Harga</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_pane_4">|</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_pane_5">FAW</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#kt_tab_pane_6">SPK</a>
                            </li>
                        </ul>
                        
                        <div class="tab-content" id="myTabContent">
                            <div class="tab-pane fade show active" id="kt_tab_pane_1" role="tabpanel">
                                <h3>Data Order</h3>
                                <p>
                                    Data order adalah data umum customer yang diisikan oleh divisi Sales saat mendapatkan order pertama kali.
                                </p>
                                <table class="table mt-3">
                                    <tr>
                                        <td><strong>Main Kode Order</strong></td>
                                        <td>:</td>
                                        <td>{{ $data_order->kode_order }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Sub Kode Order</strong></td>
                                        <td>:</td>
                                        <td>{{ $data_order->sko }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Produk Ke</strong></td>
                                        <td>:</td>
                                        <td>{{ $data_order->produk_ke }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Waktu Kontak</strong></td>
                                        <td>:</td>
                                        <td>{{ date('d F Y H:i', strtotime($data_order->waktu_kontak)) }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Sumber</strong></td>
                                        <td>:</td>
                                        <td>{{ $data_order->sumber }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Nama Customer</strong></td>
                                        <td>:</td>
                                        <td>{{ $data_order->nama }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>No Hp</strong></td>
                                        <td>:</td>
                                        <td>{{ $data_order->no_hp }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status Deal</strong></td>
                                        <td>:</td>
                                        <td>{{ $data_order->status_deal }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status Order</strong></td>
                                        <td>:</td>
                                        <td>{{ $data_order->status_order }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Tanggal Order</strong></td>
                                        <td>:</td>
                                        <td>{{ date('d F Y', strtotime($data_order->tgl_order)) }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Instansi/Tipe Instansi</strong></td>
                                        <td>:</td>
                                        <td>{{ $data_order->nama_instansi.' / '.$data_order->tipe_instansi }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>DP</strong></td>
                                        <td>:</td>
                                        <td>Rp. {{ number_format($data_order->DP,0,'',',') }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Tahap Pelunasan</strong></td>
                                        <td>:</td>
                                        <td>{{ $data_order->flag_lunas }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Tipe Order</strong></td>
                                        <td>:</td>
                                        <td>{{ $data_order->flag_dummy }}</td>
                                    </tr>
                                </table>
                                {{-- <div class="d-flex justify-content-end">
                                    <a class="btn btn-my-primary text-white" href="{{ route('crm.form_edit_order', $param_encrypt) }}">Edit</a>
                                </div> --}}
                            </div>
                            <div class="tab-pane fade" id="kt_tab_pane_2" role="tabpanel">
                                <h3>Data Produksi</h3>
                                <p>
                                    Data produksi adalah data lebih lanjut terkait informasi produksi.
                                </p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table mt-3">
                                            <tr>
                                                <td><strong>Kategori Produksi</strong></td>
                                                <td>:</td>
                                                <td>{{ $data_order->kategori_produksi }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Jenis Bahan</strong></td>
                                                <td>:</td>
                                                <td>{{ $data_order->jenis_bahan }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Dimensi Produk</strong></td>
                                                <td>:</td>
                                                <td>{{ $data_order->dp_panjang.'X'.$data_order->dp_lebar.'X'.$data_order->dp_tinggi }} cm</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Luas Permukaan</strong></td>
                                                <td>:</td>
                                                <td>{{ $data_order->lp_panjang.'X'.$data_order->lp_lebar }} cm</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Jenis Kertas</strong></td>
                                                <td>:</td>
                                                <td>{{ $data_order->jenis_kertas }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Gramasi</strong></td>
                                                <td>:</td>
                                                <td>{{ $data_order->gramasi }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Laminasi</strong></td>
                                                <td>:</td>
                                                <td>{{ $data_order->laminasi }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Sisi Laminasi</strong></td>
                                                <td>:</td>
                                                <td>{{ $data_order->sisi_laminasi }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Finishing</strong></td>
                                                <td>:</td>
                                                <td>{{ $data_order->finishing }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Tipe Produk</strong></td>
                                                <td>:</td>
                                                <td>{{ $data_order->tipe_produk }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Kertas Plano</strong></td>
                                                <td>:</td>
                                                <td>Tipe {{ $data_order->isi_kertas }} - {{ $data_order->get_plano }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Catatan Produk</strong></td>
                                                <td>:</td>
                                                <td>{{ $data_order->notes }}</td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table mt-3">
                                            <tr>
                                                <td><strong>Harga Produk</strong></td>
                                                <td>:</td>
                                                <td>Rp. {{ number_format($data_order->harga_produk, 0,'',',') }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Jumlah Produk</strong></td>
                                                <td>:</td>
                                                <td>{{ $data_order->jumlah_produk }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Total Harga</strong></td>
                                                <td>:</td>
                                                <td>Rp. {{ number_format($data_order->total_harga, 0,'',',') }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>Hitung Harga Rinci</strong></td>
                                                <td>:</td>
                                                <td>
                                                    @if ($data_order->hitung_harga_khusus == '1')
                                                    Ya
                                                    @else
                                                    Tidak
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Modal Sales</strong></td>
                                                <td>:</td>
                                                <td>Rp. {{ number_format($data_order->modal_sales,0,'',',') }}</td>
                                            </tr>
                                            <tr>
                                                <td><strong>PPN</strong></td>
                                                <td>:</td>
                                                <td>
                                                    @if($data_order->ppn)
                                                        <div class="badge badge-light-success">Ada</div>
                                                    @else
                                                        <div class="badge badge-light-danger">Tidak Ada</div>
                                                    @endif
                                                </td>
                                            </tr>
                                            @if($data_order->ppn)
                                            <tr>
                                                <td><strong>Total PPN</strong></td>
                                                <td>:</td>
                                                <td>Rp. {{ number_format($data_order->total_ppn,0,'',',') }}</td>
                                            </tr>
                                            @endif
                                                <tr>
                                                <td><strong>Grading Customer</strong></td>
                                                <td>:</td>
                                                <td>{{ $data_order->grading }}</td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                {{-- <div class="d-flex justify-content-end">
                                    <a class="btn btn-my-primary text-white" id="btn_edit_produk" href="{{ route('crm.form_edit_produk', $param_encrypt) }}">Edit</a>
                                </div> --}}
                            </div>
                            <div class="tab-pane fade" id="kt_tab_pane_3" role="tabpanel">
                                <h3>Data Harga</h3>
                                <div class="notice d-flex bg-light-success rounded border-success border border-dashed p-6 mt-3">
                                    <!--begin::Icon-->
                                    <!--begin::Svg Icon | path: icons/duotune/finance/fin001.svg-->
                                    <span class="svg-icon svg-icon-2tx svg-icon-primary me-4">
                                        <i class="fa-solid fa-receipt fs-2x"></i>
                                    </span>
                                    <!--end::Svg Icon-->
                                    <!--end::Icon-->
                                    <!--begin::Wrapper-->
                                    <div class="d-flex flex-stack flex-grow-1 flex-wrap flex-md-nowrap">
                                        <!--begin::Content-->
                                        <div class="mb-3 mb-md-0 fw-semibold">
                                            <h4 class="text-gray-900 fw-bold">Surat Invoice</h4>
                                        </div>
                                        <!--end::Content-->
                                        <!--begin::Action-->
                                        <div class="justify-content-end">
                                            @if (!empty($data_order->flag_lunas))
                                            <a href="{{ route('crm.generate_invoice', $data_order->sko_key) }}" class="btn btn-my-primary px-6 align-self-center text-nowrap text-white">Pelunasan</a>
                                            @else
                                            <a href="{{ route('crm.generate_invoice_dp', $data_order->sko_key) }}" class="btn btn-my-primary px-6 align-self-center text-nowrap text-white">DP</a>
                                            @endif
                                        </div>
                                        <!--end::Action-->
                                    </div>
                                    <!--end::Wrapper-->
                                </div>
                                <div class="notice d-flex bg-light-success rounded border-primary border border-dashed p-6 mt-3">
                                    <!--begin::Icon-->
                                    <!--begin::Svg Icon | path: icons/duotune/finance/fin001.svg-->
                                    <span class="svg-icon svg-icon-2tx svg-icon-primary me-4">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M20 19.725V18.725C20 18.125 19.6 17.725 19 17.725H5C4.4 17.725 4 18.125 4 18.725V19.725H3C2.4 19.725 2 20.125 2 20.725V21.725H22V20.725C22 20.125 21.6 19.725 21 19.725H20Z" fill="currentColor"></path>
                                            <path opacity="0.3" d="M22 6.725V7.725C22 8.325 21.6 8.725 21 8.725H18C18.6 8.725 19 9.125 19 9.725C19 10.325 18.6 10.725 18 10.725V15.725C18.6 15.725 19 16.125 19 16.725V17.725H15V16.725C15 16.125 15.4 15.725 16 15.725V10.725C15.4 10.725 15 10.325 15 9.725C15 9.125 15.4 8.725 16 8.725H13C13.6 8.725 14 9.125 14 9.725C14 10.325 13.6 10.725 13 10.725V15.725C13.6 15.725 14 16.125 14 16.725V17.725H10V16.725C10 16.125 10.4 15.725 11 15.725V10.725C10.4 10.725 10 10.325 10 9.725C10 9.125 10.4 8.725 11 8.725H8C8.6 8.725 9 9.125 9 9.725C9 10.325 8.6 10.725 8 10.725V15.725C8.6 15.725 9 16.125 9 16.725V17.725H5V16.725C5 16.125 5.4 15.725 6 15.725V10.725C5.4 10.725 5 10.325 5 9.725C5 9.125 5.4 8.725 6 8.725H3C2.4 8.725 2 8.325 2 7.725V6.725L11 2.225C11.6 1.925 12.4 1.925 13.1 2.225L22 6.725ZM12 3.725C11.2 3.725 10.5 4.425 10.5 5.225C10.5 6.025 11.2 6.725 12 6.725C12.8 6.725 13.5 6.025 13.5 5.225C13.5 4.425 12.8 3.725 12 3.725Z" fill="currentColor"></path>
                                        </svg>
                                    </span>
                                    <!--end::Svg Icon-->
                                    <!--end::Icon-->
                                    <!--begin::Wrapper-->
                                    <div class="d-flex flex-stack flex-grow-1 flex-wrap flex-md-nowrap">
                                        <!--begin::Content-->
                                        <div class="mb-3 mb-md-0 fw-semibold">
                                            <h4 class="text-gray-900 fw-bold">Upload Dokumen PO</h4>
                                        </div>
                                        <!--end::Content-->
                                        <!--begin::Action-->
                                        {{-- <div class="justify-content-end">
                                            <a href="{{ route('crm.generate_offering_letter', $data_order->sko_key) }}" class="btn btn-my-primary text-white px-6 align-self-center text-nowrap" disabled>Download</a>
                                        </div> --}}
                                        <!--end::Action-->
                                        <input type="file" name="lampiran-filename" class="form-control"/>
                                        @if (isset($data_order))
                                            <a href="{{ asset($data_order->lampiran_filename) }}" target="_blank" class="btn btn-link">
                                                Lihat Lampiran
                                            </a>
                                        @endif
                                    </div>
                                    <!--end::Wrapper-->
                                </div>
                                <div class="notice d-flex bg-light-success rounded border-warning border border-dashed p-6 mt-3">
                                    <!--begin::Icon-->
                                    <!--begin::Svg Icon | path: icons/duotune/finance/fin001.svg-->
                                    <span class="svg-icon svg-icon-2tx svg-icon-primary me-4">
                                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M20 19.725V18.725C20 18.125 19.6 17.725 19 17.725H5C4.4 17.725 4 18.125 4 18.725V19.725H3C2.4 19.725 2 20.125 2 20.725V21.725H22V20.725C22 20.125 21.6 19.725 21 19.725H20Z" fill="currentColor"></path>
                                            <path opacity="0.3" d="M22 6.725V7.725C22 8.325 21.6 8.725 21 8.725H18C18.6 8.725 19 9.125 19 9.725C19 10.325 18.6 10.725 18 10.725V15.725C18.6 15.725 19 16.125 19 16.725V17.725H15V16.725C15 16.125 15.4 15.725 16 15.725V10.725C15.4 10.725 15 10.325 15 9.725C15 9.125 15.4 8.725 16 8.725H13C13.6 8.725 14 9.125 14 9.725C14 10.325 13.6 10.725 13 10.725V15.725C13.6 15.725 14 16.125 14 16.725V17.725H10V16.725C10 16.125 10.4 15.725 11 15.725V10.725C10.4 10.725 10 10.325 10 9.725C10 9.125 10.4 8.725 11 8.725H8C8.6 8.725 9 9.125 9 9.725C9 10.325 8.6 10.725 8 10.725V15.725C8.6 15.725 9 16.125 9 16.725V17.725H5V16.725C5 16.125 5.4 15.725 6 15.725V10.725C5.4 10.725 5 10.325 5 9.725C5 9.125 5.4 8.725 6 8.725H3C2.4 8.725 2 8.325 2 7.725V6.725L11 2.225C11.6 1.925 12.4 1.925 13.1 2.225L22 6.725ZM12 3.725C11.2 3.725 10.5 4.425 10.5 5.225C10.5 6.025 11.2 6.725 12 6.725C12.8 6.725 13.5 6.025 13.5 5.225C13.5 4.425 12.8 3.725 12 3.725Z" fill="currentColor"></path>
                                        </svg>
                                    </span>
                                    <!--end::Svg Icon-->
                                    <!--end::Icon-->
                                    <!--begin::Wrapper-->
                                    <div class="d-flex flex-stack flex-grow-1 flex-wrap flex-md-nowrap">
                                        <!--begin::Content-->
                                        <div class="mb-3 mb-md-0 fw-semibold">
                                            <h4 class="text-gray-900 fw-bold">Surat Jalan</h4>
                                        </div>
                                        <!--end::Content-->
                                        <!--begin::Action-->
                                        <a href="{{ route('crm.generate_surat_jalan', $data_order->sko_key) }}" class="btn btn-my-primary text-white px-6 align-self-center text-nowrap">Download</a>
                                        <!--end::Action-->
                                    </div>
                                    <!--end::Wrapper-->
                                </div>
                            </div>
                            <div class="tab-pane fade" id="kt_tab_pane_5" role="tabpanel">
                                @if (!empty($data_order->id_faw))
                                <div class="row g-5 g-xl-8">
                                    <div class="col-md-12">
                                        <div class="card card-xl-stretch mb-xl-8">
                                            <div class="card-body">
                                                <table class="table table-bordered">
                                                    <tbody>
                                                        <tr>
                                                            <td><strong>Waktu Produksi</strong></td>
                                                            <td>:</td>
                                                            <td><strong>{{ $data_faw->waktu_produksi }}</strong></td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>Tanggal Deadline</strong></td>
                                                            <td>:</td>
                                                            <td><strong>{{ date('d F Y', strtotime($data_faw->tgl_deadline)) }}</strong></td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>Tanggal FAW</strong></td>
                                                            <td>:</td>
                                                            <td><strong>{{ date('d F Y', strtotime($data_faw->tgl_faw)) }}</strong></td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>Link File Final</strong></td>
                                                            <td>:</td>
                                                            <td><a class="badge badge-light-primary" href="{{ $data_faw->file_final }}" target="_blank">Open Link</a></td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>Lampiran</strong></td>
                                                            <td>:</td>
                                                            <td><img id="zoomable-image" width="300" src="{{ url('storage/'.$data_faw->sko_key.'/faw/lampiran/'.$data_faw->path_lampiran); }}" alt=""></td>
                                                        </tr>
                                                        <tr>
                                                            <td><strong>Keterangan</strong></td>
                                                            <td>:</td>
                                                            <td>{{ $data_faw->keterangan_tambahan }}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    
                                </div>
                                <div class="row g-5 g-xl-8">
                                    <!--begin::Col-->
                                    <div class="col-xl-6">
                                        <!--begin::Mixed Widget 4-->
                                        <div class="card card-xl-stretch mb-xl-8">
                                            <!--begin::Body-->
                                            <div class="card-body d-flex flex-column">
                                                <div class="pt-5">
                                                    <p class="text-center fs-6 pb-5">
                                                    <span class="badge badge-light-danger fs-8">Notes:</span>&nbsp; File dalam format ms. word (.docx)</p>
                                                    <a href="{{ route('crm.generate_faw', $data_order->sko_key) }}" class="btn btn-primary w-100 py-3"><i class="fa-solid fa-file-word"></i> Download</a>
                                                </div>
                                            </div>
                                            <!--end::Body-->
                                        </div>
                                        <!--end::Mixed Widget 4-->
                                    </div>
                                    <!--end::Col-->
                                    <!--begin::Col-->
                                    <div class="col-xl-6">
                                        <!--begin::Mixed Widget 4-->
                                        <div class="card card-xl-stretch mb-xl-8">
                                            <!--begin::Body-->
                                            <div class="card-body d-flex flex-column">
                                                <div class="pt-5">
                                                    <p class="text-center fs-6 pb-5">
                                                    <span class="badge badge-light-danger fs-8">Notes:</span>&nbsp; Ubah data FAW</p>
                                                    <a href="{{ route('crm.edit_faw', $data_order->sko_key) }}" class="btn btn-success w-100 py-3"><i class="fa-solid fa-pen-to-square"></i> Edit</a>
                                                </div>
                                            </div>
                                            <!--end::Body-->
                                        </div>
                                        <!--end::Mixed Widget 4-->
                                    </div>
                                    <!--end::Col-->
                                </div>
                                @else
                                    <div class="row justify-content-md-center">
                                        <div class="col-md-8">
                                            <a href="{{ route('crm.form_add_faw', $param_encrypt) }}" class="btn btn-lg btn-my-primary text-white w-100">Buat FAW</a>
                                        </div>
                                    </div>
                                @endif
                            </div>
                            <div class="tab-pane fade" id="kt_tab_pane_6" role="tabpanel">
                                @if (!empty($data_order->id_spk))
                                <div class="row g-5 g-xl-8">
                                    <h3>Surat Perintah Kerja</h3>
                                    <div class="col-md-12">
                                        @if(!empty($data_spk->progress_produksi_1))
                                        <div class="table-responsive mb-3" id="spk-pracetak">
                                            <table class="table table-bordered table-rounded table-striped border gy-7 gs-7">
                                                <thead class="fw-bolder">
                                                  <tr>
                                                    <th scope="col">#</th>
                                                    <th scope="col">Jumlah Plano</th>
                                                    <th scope="col">Vendor</th>
                                                    <th scope="col">Tgl produksi</th>
                                                    <th scope="col">PIC</th>
                                                    <th scope="col">Checklist 1</th>
                                                    <th scope="col">Checklist 2</th>
                                                    <th scope="col">Checklist 3</th>
                                                    <th scope="col">Checklist 4</th>
                                                    <th scope="col">Selesai</th>
                                                  </tr>
                                                </thead>
                                                <tbody>
                                                  <tr>
                                                    <th scope="row" class="fw-bolder">Pra Cetak</th>
                                                    <td>2</td>
                                                    <td>{{ $data_spk->vendor_1 }}</td>
                                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_1)) }}</td>
                                                    <td>{{ $data_spk->pic_validasi_1 }}
                                                    <td>{!! $data_spk->check_1_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_1_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_1_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_1_4 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->flag_status_1 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                        </div>
                                        @endif

                                        @if(!empty($data_spk->progress_produksi_2))
                                        <div class="table-responsive mb-3" id="spk-cetak">
                                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                                <thead class="fw-bolder">
                                                  <tr>
                                                    <th scope="col">#</th>
                                                    <th scope="col">Jumlah Awal</th>
                                                    <th scope="col">Jumlah Akhir</th>
                                                    <th scope="col">Jumlah Reject</th>
                                                    <th scope="col">Vendor</th>
                                                    <th scope="col">Tgl produksi</th>
                                                    <th scope="col">PIC</th>
                                                    <th scope="col">Checklist 1</th>
                                                    <th scope="col">Checklist 2</th>
                                                    <th scope="col">Checklist 3</th>
                                                    <th scope="col">Checklist 4</th>
                                                    <th scope="col">Selesai</th>
                                                  </tr>
                                                </thead>
                                                <tbody>
                                                  <tr>
                                                    <th scope="row" class="fw-bolder">Cetak</th>
                                                    <td>{{ $data_spk->jumlah_awal_pp_2 }}</td>
                                                    <td>{{ $data_spk->jumlah_hasil_pp_2 }}</td>
                                                    <td>{{ $data_spk->reject_pp_2 }}</td>
                                                    <td>{{ $data_spk->vendor_2 }}</td>
                                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_2)) }}</td>
                                                    <td>{{ $data_spk->pic_validasi_2 }}
                                                    <td>{!! $data_spk->check_2_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_2_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_2_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_2_4 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->flag_status_2 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                        </div>
                                        @endif

                                        @if(!empty($data_spk->progress_produksi_3))
                                        <div class="table-responsive mb-3" id="spk-laminasi">
                                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                                <thead class="fw-bolder">
                                                  <tr>
                                                    <th scope="col">#</th>
                                                    <th scope="col">Jumlah Awal</th>
                                                    <th scope="col">Jumlah Akhir</th>
                                                    <th scope="col">Jumlah Reject</th>
                                                    <th scope="col">Vendor</th>
                                                    <th scope="col">Tgl produksi</th>
                                                    <th scope="col">PIC</th>
                                                    <th scope="col">Checklist 1</th>
                                                    <th scope="col">Checklist 2</th>
                                                    <th scope="col">Checklist 3</th>
                                                    <th scope="col">Selesai</th>
                                                  </tr>
                                                </thead>
                                                <tbody>
                                                  <tr>
                                                    <th scope="row" class="fw-bolder">Laminasi</th>
                                                    <td>{{ $data_spk->jumlah_awal_pp_3 }}</td>
                                                    <td>{{ $data_spk->jumlah_hasil_pp_3 }}</td>
                                                    <td>{{ $data_spk->reject_pp_3 }}</td>
                                                    <td>{{ $data_spk->vendor_3 }}</td>
                                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_3)) }}</td>
                                                    <td>{{ $data_spk->pic_validasi_3 }}
                                                    <td>{!! $data_spk->check_3_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_3_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_3_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->flag_status_3 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                        </div>
                                        @endif

                                        @if(!empty($data_spk->progress_produksi_4))
                                        <div class="table-responsive mb-3" id="spk-poly">
                                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                                <thead class="fw-bolder">
                                                  <tr>
                                                    <th scope="col">#</th>
                                                    <th scope="col">Jumlah Awal</th>
                                                    <th scope="col">Jumlah Akhir</th>
                                                    <th scope="col">Jumlah Reject</th>
                                                    <th scope="col">Vendor</th>
                                                    <th scope="col">Tgl produksi</th>
                                                    <th scope="col">PIC</th>
                                                    <th scope="col">Checklist 1</th>
                                                    <th scope="col">Checklist 2</th>
                                                    <th scope="col">Checklist 3</th>
                                                    <th scope="col">Checklist 4</th>
                                                    <th scope="col">Checklist 5</th>
                                                    <th scope="col">Selesai</th>
                                                  </tr>
                                                </thead>
                                                <tbody>
                                                  <tr>
                                                    <th scope="row" class="fw-bolder">Poly</th>
                                                    <td>{{ $data_spk->jumlah_awal_pp_4 }}</td>
                                                    <td>{{ $data_spk->jumlah_hasil_pp_4 }}</td>
                                                    <td>{{ $data_spk->reject_pp_4 }}</td>
                                                    <td>{{ $data_spk->vendor_4 }}</td>
                                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_4)) }}</td>
                                                    <td>{{ $data_spk->pic_validasi_4 }}
                                                    <td>{!! $data_spk->check_4_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_4_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_4_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_4_4 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_4_5 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->flag_status_4 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                        </div>
                                        @endif

                                        @if(!empty($data_spk->progress_produksi_5))
                                        <div class="table-responsive mb-3" id="spk-emboss">
                                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                                <thead class="fw-bolder">
                                                  <tr>
                                                    <th scope="col">#</th>
                                                    <th scope="col">Jumlah Awal</th>
                                                    <th scope="col">Jumlah Akhir</th>
                                                    <th scope="col">Jumlah Reject</th>
                                                    <th scope="col">Vendor</th>
                                                    <th scope="col">Tgl produksi</th>
                                                    <th scope="col">PIC</th>
                                                    <th scope="col">Checklist 1</th>
                                                    <th scope="col">Checklist 2</th>
                                                    <th scope="col">Checklist 3</th>
                                                    <th scope="col">Checklist 4</th>
                                                    <th scope="col">Checklist 5</th>
                                                    <th scope="col">Selesai</th>
                                                  </tr>
                                                </thead>
                                                <tbody>
                                                  <tr>
                                                    <th scope="row" class="fw-bolder">Emboss</th>
                                                    <td>{{ $data_spk->jumlah_awal_pp_5 }}</td>
                                                    <td>{{ $data_spk->jumlah_hasil_pp_5 }}</td>
                                                    <td>{{ $data_spk->reject_pp_5 }}</td>
                                                    <td>{{ $data_spk->vendor_5 }}</td>
                                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_5)) }}</td>
                                                    <td>{{ $data_spk->pic_validasi_5 }}
                                                    <td>{!! $data_spk->check_5_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_5_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_5_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_5_4 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_5_5 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->flag_status_5 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                        </div>
                                        @endif

                                        @if(!empty($data_spk->progress_produksi_6))
                                        <div class="table-responsive mb-3" id="spk-spotuv">
                                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                                <thead class="fw-bolder">
                                                  <tr>
                                                    <th scope="col">#</th>
                                                    <th scope="col">Jumlah Awal</th>
                                                    <th scope="col">Jumlah Akhir</th>
                                                    <th scope="col">Jumlah Reject</th>
                                                    <th scope="col">Vendor</th>
                                                    <th scope="col">Tgl produksi</th>
                                                    <th scope="col">PIC</th>
                                                    <th scope="col">Checklist 1</th>
                                                    <th scope="col">Checklist 2</th>
                                                    <th scope="col">Checklist 3</th>
                                                    <th scope="col">Selesai</th>
                                                  </tr>
                                                </thead>
                                                <tbody>
                                                  <tr>
                                                    <th scope="row" class="fw-bolder">Spot UV</th>
                                                    <td>{{ $data_spk->jumlah_awal_pp_6 }}</td>
                                                    <td>{{ $data_spk->jumlah_hasil_pp_6 }}</td>
                                                    <td>{{ $data_spk->reject_pp_6 }}</td>
                                                    <td>{{ $data_spk->vendor_6 }}</td>
                                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_6)) }}</td>
                                                    <td>{{ $data_spk->pic_validasi_6 }}
                                                    <td>{!! $data_spk->check_6_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_6_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_6_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->flag_status_6 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                        </div>
                                        @endif
                                        
                                        @if(!empty($data_spk->progress_produksi_7))
                                        <div class="table-responsive mb-3" id="spk-lapis">
                                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                                <thead class="fw-bolder">
                                                  <tr>
                                                    <th scope="col">#</th>
                                                    <th scope="col">Jumlah Awal</th>
                                                    <th scope="col">Jumlah Akhir</th>
                                                    <th scope="col">Jumlah Reject</th>
                                                    <th scope="col">Vendor</th>
                                                    <th scope="col">Tgl produksi</th>
                                                    <th scope="col">PIC</th>
                                                    <th scope="col">Checklist 1</th>
                                                    <th scope="col">Checklist 2</th>
                                                    <th scope="col">Selesai</th>
                                                  </tr>
                                                </thead>
                                                <tbody>
                                                  <tr>
                                                    <th scope="row" class="fw-bolder">Lapis</th>
                                                    <td>{{ $data_spk->jumlah_awal_pp_7 }}</td>
                                                    <td>{{ $data_spk->jumlah_hasil_pp_7 }}</td>
                                                    <td>{{ $data_spk->reject_pp_7 }}</td>
                                                    <td>{{ $data_spk->vendor_7 }}</td>
                                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_7)) }}</td>
                                                    <td>{{ $data_spk->pic_validasi_7 }}
                                                    <td>{!! $data_spk->check_7_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_7_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->flag_status_7 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                        </div>
                                        @endif

                                        @if(!empty($data_spk->progress_produksi_8))
                                        <div class="table-responsive mb-3" id="spk-jendelamika">
                                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                                <thead class="fw-bolder">
                                                  <tr>
                                                    <th scope="col">#</th>
                                                    <th scope="col">Jumlah Awal</th>
                                                    <th scope="col">Jumlah Akhir</th>
                                                    <th scope="col">Jumlah Reject</th>
                                                    <th scope="col">Vendor</th>
                                                    <th scope="col">Tgl produksi</th>
                                                    <th scope="col">PIC</th>
                                                    <th scope="col">Selesai</th>
                                                  </tr>
                                                </thead>
                                                <tbody>
                                                  <tr>
                                                    <th scope="row" class="fw-bolder">Jendela Mika</th>
                                                    <td>{{ $data_spk->jumlah_awal_pp_8 }}</td>
                                                    <td>{{ $data_spk->jumlah_hasil_pp_8 }}</td>
                                                    <td>{{ $data_spk->reject_pp_8 }}</td>
                                                    <td>{{ $data_spk->vendor_8 }}</td>
                                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_8)) }}</td>
                                                    <td>{{ $data_spk->pic_validasi_8 }}
                                                    <td>{!! $data_spk->flag_status_8 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                        </div>
                                        @endif

                                        @if(!empty($data_spk->progress_produksi_9))
                                        <div class="table-responsive mb-3" id="spk-pond">
                                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                                <thead class="fw-bolder">
                                                  <tr>
                                                    <th scope="col">#</th>
                                                    <th scope="col">Jumlah Awal</th>
                                                    <th scope="col">Jumlah Akhir</th>
                                                    <th scope="col">Jumlah Reject</th>
                                                    <th scope="col">Vendor</th>
                                                    <th scope="col">Tgl produksi</th>
                                                    <th scope="col">PIC</th>
                                                    <th scope="col">Checklist 1</th>
                                                    <th scope="col">Checklist 2</th>
                                                    <th scope="col">Checklist 3</th>
                                                    <th scope="col">Checklist 4</th>
                                                    <th scope="col">Checklist 5</th>
                                                    <th scope="col">Selesai</th>
                                                  </tr>
                                                </thead>
                                                <tbody>
                                                  <tr>
                                                    <th scope="row" class="fw-bolder">Pond</th>
                                                    <td>{{ $data_spk->jumlah_awal_pp_9 }}</td>
                                                    <td>{{ $data_spk->jumlah_hasil_pp_9 }}</td>
                                                    <td>{{ $data_spk->reject_pp_9 }}</td>
                                                    <td>{{ $data_spk->vendor_9 }}</td>
                                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_9)) }}</td>
                                                    <td>{{ $data_spk->pic_validasi_9 }}
                                                    <td>{!! $data_spk->check_9_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_9_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_9_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_9_4 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_9_5 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->flag_status_9 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                        </div>
                                        @endif

                                        @if(!empty($data_spk->progress_produksi_10))
                                        <div class="table-responsive mb-3" id="spk-finishing">
                                            <table class="table table-rounded table-striped border gy-7 gs-7">
                                                <thead class="fw-bolder">
                                                  <tr>
                                                    <th scope="col">#</th>
                                                    <th scope="col">Jumlah Awal</th>
                                                    <th scope="col">Jumlah Akhir</th>
                                                    <th scope="col">Jumlah Reject</th>
                                                    <th scope="col">Vendor</th>
                                                    <th scope="col">Tgl produksi</th>
                                                    <th scope="col">PIC</th>
                                                    <th scope="col">Checklist 1</th>
                                                    <th scope="col">Checklist 2</th>
                                                    <th scope="col">Checklist 3</th>
                                                    <th scope="col">Checklist 4</th>
                                                    <th scope="col">Checklist 5</th>
                                                    <th scope="col">Selesai</th>
                                                  </tr>
                                                </thead>
                                                <tbody>
                                                  <tr>
                                                    <th scope="row" class="fw-bolder">Finishing</th>
                                                    <td>{{ $data_spk->jumlah_awal_pp_10 }}</td>
                                                    <td>{{ $data_spk->jumlah_hasil_pp_10 }}</td>
                                                    <td>{{ $data_spk->reject_pp_10 }}</td>
                                                    <td>{{ $data_spk->vendor_10 }}</td>
                                                    <td>{{ date('d/m/y', strtotime($data_spk->tgl_produksi_10)) }}</td>
                                                    <td>{{ $data_spk->pic_validasi_10 }}
                                                    <td>{!! $data_spk->check_10_1 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_10_2 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_10_3 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_10_4 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->check_10_5 ? '<i class="fa-solid fa-check"></i>' : '-' !!}</td>
                                                    <td>{!! $data_spk->flag_status_10 ? '<i class="fa-solid fa-check text-success"></i>' : '-' !!}</td>
                                                  </tr>
                                                </tbody>
                                              </table>
                                        </div>
                                        @endif

                                    </div>
                                </div>
                                {{-- <div class="row g-5 g-xl-8">
                                    <div class="col-xl-6">
                                        <div class="card card-xl-stretch mb-xl-8">
                                            <div class="card-body d-flex flex-column">
                                                <div class="pt-5">
                                                    <p class="text-center fs-6 pb-5">
                                                    <span class="badge badge-light-danger fs-8">Notes:</span>&nbsp; File dalam format ms. word (.docx)</p>
                                                    <a href="{{ route('production.generate_spk', Crypt::encryptString($data_order->sko_key)) }}" class="btn btn-primary w-100 py-3"><i class="fa-solid fa-file-word"></i> Download</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="card card-xl-stretch mb-xl-8">
                                            <div class="card-body d-flex flex-column">
                                                <div class="pt-5">
                                                    <p class="text-center fs-6 pb-5">
                                                    <span class="badge badge-light-danger fs-8">Notes:</span>&nbsp; Update data SPK</p>
                                                    <a href="{{ route('production.show_production', Crypt::encryptString($data_spk->id_spk)) }}" class="btn btn-success w-100 py-3"><i class="fa-solid fa-pen-to-square"></i> Edit</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div> --}}
                                @else
                                    <div class="row justify-content-md-center">
                                        <div class="col-md-8 text-center">
                                            <i class="fa-solid fa-triangle-exclamation fa-2xl "></i>
                                            <h3 class="mt-3">Belum ada data SPK</h3>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
