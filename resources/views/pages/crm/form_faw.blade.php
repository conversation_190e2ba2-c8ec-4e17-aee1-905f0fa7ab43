<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ route('crm').'#tab_faw'; }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">Buat FAW (Form Approval Work)</h3>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        @include('pages.crm.data_jscript_form_faw')
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-header">
                <h3 class="card-title text-dark"><i>#{{ $data_order->kode_order }} - {{ $data_order->nama }}</i></h3>
            </div>
            <div class="card-body pt-0">
                @if(session('errors'))
                    <div class="alert alert-danger">
                        <p>{{session('errors')}}</p>
                    </div>
                @endif
                <form method="POST" id="form-faw" action="{{ route('crm.store_faw') }}" enctype="multipart/form-data">
                    @csrf
                    @method('POST')
                    <input type="hidden" name="id_order" value="{{ $data_order->id_order }}">
                    <input type="hidden" name="order_key" value="{{ $data_order->order_key }}">
                    <input type="hidden" name="sko_key" value="{{ $data_order->sko_key }}">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-10">
                                <label for="sko" class="form-label">Sub Kode Order</label>
                                <input type="text" name="sko" value="{{ $data_order->sko }}" readonly id="sko" class="form-control form-control-solid"/>
                            </div>
                            <div class="mb-10">
                                <label for="nama" class="form-label">Nama Customer</label>
                                <input type="text" name="nama" id="nama" value="{{ $data_order->nama }}" readonly class="form-control form-control-solid"/>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="waktu_produksi" class="required form-label">Waktu Produksi</label>
                                <input type="text" name="waktu_produksi" id="waktu_produksi" class="form-control"/>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="tgl_deadline" class="required form-label">Tanggal Deadline</label>
                                <input type="date" name="tgl_deadline" id="tgl_deadline" class="form-control"/>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="tgl_deadline_konsumen" class="required form-label">Tanggal Deadline Konsumen</label>
                                <input type="date" name="tgl_deadline_konsumen" id="tgl_deadline_konsumen" class="form-control"/>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="fv-row mb-10">
                                <label for="link_file_final" class="required form-label">File Final (Gdrive)</label>
                                <div class="input-group">
                                    <input type="text" name="link_file_final" id="link_file_final" value="{{ old('link_file_final') }}" class="form-control"/>
                                    <span class="input-group-text" id="basic-addon2"><i class="fa-solid fa-link"></i></span>
                                </div>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="lampiran" class="required form-label">lampiran</label>
                                <input type="file" name="lampiran" class="form-control"/>
                            </div>
                            <div class="mb-10">
                                <label for="keterangan_tambahan" class="form-label">Keterangan Tambahan</label>
                                <textarea type="text" name="keterangan_tambahan" id="keterangan_tambahan" rows="6" class="form-control"></textarea>
                            </div>
                        </div>
                    </div>
                    <input type="submit" form="form-faw" value="Terbitkan FAW" id="store-faw" class="btn my-primary text-white float-end">
                </form>
            </div>
        </div>
    </div>

    
</x-app-layout>