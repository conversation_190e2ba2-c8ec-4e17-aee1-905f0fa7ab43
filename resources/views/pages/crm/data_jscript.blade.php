<script type="text/javascript">

    // SweetAlert Alert Example
    @if(session()->has('message'))
    function showAlert(title, message, type) {
        window.swal.fire({
            title: "Response Message",
            text: title,
            icon: 'success',
            type: type,
            confirmButtonText: "OK",
            html: message,
            // cancelButtonText: "Cancel",
            showCancelButton: false,
        }).then(console.log)
            .catch(console.error);
    }
    showAlert('{{ strtolower(session('message')['type']) }}', '{{ session('message')['text'] }}');
    @endif
    

    $(document).ready(function(){
        window.throttleDTSearch = DataTable.util.throttle(function (dt, val) {
            dt.search(val).draw();
        }, 450);

        function fetchActiveTab (tab) {
            switch (tab) {
                case '#tab_company':
                    if (!window.dt_company) {
                        window.dt_company = $('#datatable_company').DataTable({
                            processing: true,
                            serverSide: true,
                            language: dataTableLanguage,
                            paging: true, //Aktifkan pagination
                            pageLength: 10, //Tentukan jumlah data yang akan ditampilkan dalam satu halaman
                            ajax: {
                                url: "{{ route('company.table_company') }}"
                            },
                            deferRender: true,
                            order: [[ 1, 'desc' ]],
                            columns: [
                                // {
                                //     data: 'no',
                                //     name: 'no',
                                //     render: function (data, type, row, meta) {
                                //         if (type === 'display') {
                                //             // meta.row gives the current row index (starting from 0)
                                //             return meta.row + 1; // Add 1 to make it start from 1
                                //         }
                                //         return meta.row + 1; // For other types (like export), you might want the number as well
                                //     }
                                // },
                                {
                                    data: 'name',
                                    name: 'name'
                                },
                                {
                                    data: 'business_sector',
                                    name: 'business_sector'
                                },
                                {
                                    data: 'province_name',
                                    name: 'province_name'
                                },
                                {
                                    data: 'total_pic_customer',
                                    name: 'total_pic_customer'
                                },
                                {
                                    data: 'created_at',
                                    name: 'created_at',
                                    searchable: false
    
                                },
                                {
                                    data: 'updated_at',
                                    name: 'updated_at',
                                    searchable: false
    
                                },
                                {
                                    data: 'action',
                                    name: 'action',
                                    orderable: false,
                                    searchable: false,
                                }
    
                            ]
                        });
                    } else {
                        window.dt_company.ajax.reload();
                    }
                    break;
                case '#tab_customer':
                    if (!window.dt_cust) {
                        window.dt_cust = $('#datatable_customer').DataTable({
                            processing: true,
                            serverSide: true,
                            language: dataTableLanguage,
                            paging: true, //Aktifkan pagination
                            pageLength: 10, //Tentukan jumlah data yang akan ditampilkan dalam satu halaman
                            ajax: {
                                url: "{{ route('crm.table_customer') }}"
                            },
                            deferRender: true,
                            columnDefs: [ { type: 'date', 'targets': [8] } ],
                            order: [[ 9, 'desc' ]],
                            columns: [{
                                    data: 'kode_kustomer',
                                    name: 'kode_kustomer'
                                },
                                {
                                    data: 'nama',
                                    name: 'nama'
                                },
                                {
                                    data: 'no_hp',
                                    name: 'no_hp',
                                },
                                {
                                    data: 'email_bisnis',
                                    name: 'email_bisnis',
                                    searchable: false
                                },
                                {
                                    data: 'posisi_pic_cust',
                                    name: 'posisi_pic_cust',
                                    searchable: false
                                },
                                {
                                    data: 'grading',
                                    name: 'grading'
                                },
                                {
                                    data: 'tipe_instansi',
                                    name: 'tipe_instansi',
                                },
                                {
                                    data: 'nama_instansi',
                                    name: 'nama_instansi',
                                },
                                {
                                    data: 'nama_brand',
                                    name: 'nama_brand',
                                    searchable: false
    
                                },
                                {
                                    data: 'updated_at',
                                    name: 'updated_at',
                                    searchable: false
    
                                },
                                {
                                    data: 'action',
                                    name: 'action',
                                    orderable: false,
                                    searchable: false,
                                }
    
                            ]
                        });
                    } else {
                        window.dt_cust.ajax.reload();
                    }
                    break;
                case '#tab_faw':
                    if (!window.dt_faw) {
                        window.dt_faw = $('#datatable_faw').DataTable({
                            processing: true,
                            serverSide: true,
                            paging: true,
                            pageLength: 10,
                            language: dataTableLanguage,
                            ajax: {
                                url: "{{ route('crm.table_faw') }}",
                                data: function (d) {
                                    d.from_date = $('#filter-dummy-from').val();
                                    d.to_date = $('#filter-dummy-to').val();
                                    d.status_faw = $('.filter-status-faw').val();
                                }
                            },
                            deferRender: true,
                            columnDefs: [ { type: 'date', targets: [2] } ],
                            order: [[ 2, 'desc' ]],
                            columns: [
                                {
                                    data: 'sko',
                                    name: 'tb_orders.sko'
                                },
                                {
                                    data: 'nama',
                                    name: 'nama'
                                },
                                {
                                    data: 'tgl_order',
                                    name: 'tgl_order',
                                    searchable: false
                                },
                                {
                                    data: 'tgl_update_faw',
                                    name: 'tgl_update_faw',
                                    searchable: false
                                },
                                {
                                    data: 'status_faw_disp',
                                    name: 'tb_faws.status_faw',
                                    searchable: false
                                },
                                {
                                    data: 'action',
                                    name: 'action',
                                    orderable: false,
                                    searchable: false,
                                }
                            ],

                        });
                    } else {
                        window.dt_faw.ajax.reload();
                    }
                    break;
                case '#tab_harga':
                    if (!window.dt_hr) {
                        window.dt_hr = $('#datatable_harga_rinci').DataTable({
                            processing: true,
                            serverSide: true,
                            paging: true,
                            pageLength: 10,
                            language: dataTableLanguage,
                            ajax: {
                                url: "{{ route('crm.table_harga_rinci') }}"
                            },
                            deferRender: true,
                            columnDefs: [ 
                                {  
                                    type: 'date', 
                                    targets: [6],
                                    render: function (data, type, row) {
                                        // Check if the date is not empty
                                        if (data && data.trim() !== '' && data.trim() !== '30 Nov -0001') {
                                            return data;
                                        } else {
                                            // Return an empty string for empty dates
                                            return '';
                                        }
                                    }
                                },
                            ],
                            order: [[ 6, 'desc' ]],
                            columns: [
                                {
                                    data: 'waktu_kontak',
                                    name: 'waktu_kontak',
                                    searchable: false
                                },
                                {
                                    data: 'nama',
                                    name: 'tb_customers.nama'
                                },
                                {
                                    data: 'grading',
                                    name: 'tb_customers.grading'
                                },
                                {
                                    data: 'status_deal',
                                    name: 'status_deal'
                                },
                                {
                                    data: 'modal_sales',
                                    name: 'tb_produksis.modal_sales',
                                    searchable: false
                                },
                                {
                                    data: 'total_harga',
                                    name: 'tb_produksis.total_harga',
                                    searchable: false
                                },
                                {
                                    data: 'hk_tgl_updated',
                                    name: 'hk_tgl_updated',
                                    searchable: false
                                },
                                {
                                    data: 'action',
                                    name: 'action',
                                    orderable: false,
                                    searchable: false,
                                }
                            ],
                        });
                    } else {
                        window.dt_hr.ajax.reload();
                    }
                    break;
                case '#tab_kendala':
                    if (!window.dt_kendala) {
                        window.dt_kendala = $('#datatable_kendala').DataTable({
                            processing: true,
                            serverSide: true,
                            paging: true,
                            pageLength: 10,
                            language: dataTableLanguage,
                            ajax: {
                                url: "{{ route('crm.table_kendala') }}"
                            },
                            deferRender: true,
                            columnDefs: [ { type: 'date', 'targets': [4] } ],
                            order: [[ 4, 'desc' ]],
                            columns: [
                                {
                                    data: 'sko',
                                    name: 'sko'
                                },
                                {
                                    data: 'nama',
                                    name: 'tb_customers.nama'
                                },
                                {
                                    data: 'tgl_masalah',
                                    name: 'tb_fpms.tgl_masalah',
                                    searchable: false,
                                },
                                {
                                    data: 'solusi',
                                    name: 'tb_fpms.solusi',
                                    searchable: true,
                                },
                                {
                                    data: 'last_update',
                                    name: 'tb_fpms.updated_at',
                                    searchable: false,
                                },
                                {
                                    data: 'action',
                                    name: 'action',
                                    orderable: false,
                                    searchable: false,
                                }
                            ],

                        });
                    } else {
                        window.dt_kendala.ajax.reload();
                    }
                    break;
                case '#tab_order':
                    if (!window.dt_order) {
                        window.dt_order = $('#datatable_order').DataTable({
                            processing: true,
                            serverSide: true,
                            paging: true,
                            pageLength: 10,
                            language: dataTableLanguage,
                            ajax: {
                                url: "{{ route('crm.table_order') }}",
                                data: function(d) {
                                    d.tgl_created = $('.filter-order-year').val()
                                    // d.tgl_order_from_date = $('#filter-tgl-order-from').val();
                                    // d.tgl_order_to_date = $('#filter-tgl-order-to').val();
                                }
                            },
                            deferRender: true,
                            columnDefs: [ 
                                {  
                                    type: 'date', 
                                    targets: [1, 2, 12],
                                    render: function (data, type, row) {
                                        // Check if the date is not empty
                                        if (data && data.trim() !== '' && data.trim() !== '30 Nov -0001') {
                                            return data;
                                        } else {
                                            // Return an empty string for empty dates
                                            return '';
                                        }
                                    }
                                },
                            ],
                            order: [[ 12, 'desc' ]],
                            columns: [
                                // {
                                //     data: 'kode_order',
                                //     name: 'kode_order',
                                //     orderable: false
                                // },
                                {
                                    data: 'sko',
                                    name: 'sko',
                                    orderable: false
                                },
                                {
                                    data: 'tgl_order',
                                    name: 'tgl_order',
                                    className: 'dt-body-center',
                                    searchable: false
                                },
                                {
                                    data: 'waktu_kontak',
                                    name: 'waktu_kontak',
                                    searchable: false
                                },
                                {
                                    data: 'selisih_hari',
                                    name: 'selisih_hari',
                                    searchable: false
                                },
                                {
                                    data: 'pic',
                                    name: 'users.name',
                                },
                                {
                                    data: 'nama',
                                    name: 'tb_customers.nama'
                                },
                                {
                                    data: 'total_harga',
                                    name: 'tb_produksis.total_harga',
                                    searchable: false
                                },
                                {
                                    data: 'total_ppn',
                                    name: 'total_ppn',
                                    searchable: false
                                },
                                {
                                    data: 'modal_sales',
                                    name: 'modal_sales',
                                    searchable: false,
                                },
                                {
                                    data: 'status_deal',
                                    name: 'status_deal'
                                },
                                {
                                    data: 'status_order',
                                    name: 'status_order'
                                },
                                {
                                    data: 'kategori_produksi',
                                    name: 'kategori_produksi',
                                    searchable: false
                                },
                                {
                                    data: 'last_update',
                                    name: 'last_update',
                                    searchable: false
                                },
                                {
                                    data: 'action',
                                    name: 'action',
                                    orderable: false,
                                    searchable: false,
                                },
                                {
                                    data: 'sumber',
                                    name: 'sumber',
                                    visible: false
                                },
                                {
                                    data: 'tipe_order',
                                    name: 'tb_orders.flag_dummy',
                                    visible: false
                                },
                            ],
                        });
                    } else {
                        window.dt_order.ajax.reload();
                    }
                    break;
            }
        }

        const initActiveTabElem = $('a[data-bs-toggle="tab"][class*=active][class*=nav-link]').attr('data-bs-target')
        fetchActiveTab(initActiveTabElem)

        $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
            const activeTab = $(e.target).attr('data-bs-target')
            fetchActiveTab(activeTab)
        })

        if(window.location.hash == '#tab_customer' ) { 
            const triggerEl = document.querySelector('#myTab a[data-bs-target="#tab_customer"]')
            var tab1 = new bootstrap.Tab(triggerEl);
            tab1.show();
        }
        if(window.location.hash == '#tab_order' ) { 
            const triggerEl = document.querySelector('#myTab a[data-bs-target="#tab_order"]')
            var tab2 = new bootstrap.Tab(triggerEl);
            tab2.show();
        }
        if(window.location.hash == '#tab_faw' ) { 
            const triggerEl = document.querySelector('#myTab a[data-bs-target="#tab_faw"]')
            var tab3 = new bootstrap.Tab(triggerEl);
            tab3.show();
        }
        if(window.location.hash == '#tab_harga' ) { 
            const triggerEl = document.querySelector('#myTab a[data-bs-target="#tab_harga"]')
            var tab4 = new bootstrap.Tab(triggerEl);
            tab4.show();
        }
        if(window.location.hash == '#tab_kendala' ) { 
            const triggerEl = document.querySelector('#myTab a[data-bs-target="#tab_kendala"]')
            var tab5 = new bootstrap.Tab(triggerEl);
            tab5.show();
        }
    });

    $(".modal").on("hidden.bs.modal",function(){
        $('#edit-order').trigger("reset");
        $('#main_order_key_edit').val('');
    });
    

</script>

@include('pages.crm.data_jscript_crm')

@include('pages.crm.data_jscript_order')

@include('pages.crm.data_jscript_faw')

@include('pages.crm.data_jscript_harga_rinci')

@include('pages.crm.data_jscript_kendala')

@include('pages.crm.company.data_jscript_company')

