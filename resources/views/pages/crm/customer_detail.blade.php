<x-app-layout>
    <x-slot name="script">
        <script>
            $(document).ready(function () {
                //datatable page cust
                var id = '{{ $data_cust->id_customer }}';
                dt_cust = $('#datatable_detail_customer').DataTable({
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: "{{ url('crm/table_transaksi_customer') }}/"+id
                    },
                    columns: [{
                            data: 'tgl_order',
                            name: 'tgl_order'
                        },
                        {
                            data: 'kode_order',
                            name: 'tb_kode_orders.kode_order'
                        },
                        {
                            data: 'sko',
                            name: 'sko'
                        },
                        {
                            data: 'tgl_jatuh_tempo',
                            name: 'tgl_jatuh_tempo'
                        },
                        {
                            data: 'status_deal',
                            name: 'status_deal'
                        },
                        {
                            data: 'status_order',
                            name: 'status_order'
                        },
                        {
                            data: 'total_harga',
                            name: 'total_harga'

                        }
                    ],

                });

            });

        </script>

    </x-slot>
    <x-slot name="header">
        {{-- <a href="{{ url()->previous() }}">
        <span class="fs-1"> --}}

            {{-- </a> --}}
            {{-- Detail Customer</span> --}}
            <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
                <li class="breadcrumb-item pe-3"><a href="{{ request()->has('ccode') ? route('company.detail_company', ['id' => $data_cust->company->code]).'?pic_customer=true' : route('crm').'#tab_customer' }}" class="pe-3">
                        <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                    </a></li>
                <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                        <h1 class="display-4 text-white">Detail Customer</h1>
                    </a></li>
            </ol>
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-body pt-0 mt-5">
                <div class="py-5">
                    <div class="row">
                        <div class="col-md-4 d-flex">
                            <h1 class="text-center">{{ $data_cust->nama }}</h1>
                        </div>
                        <div class="col-md-8">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td><b>Hp</b></td>
                                        <td><b>:</b></td>
                                        <td>{{ $data_cust->no_hp }}</td>
                                    </tr>
                                    <tr>
                                        <td><b>Instansi/Tipe Instansi</b></td>
                                        <td><b>:</b></td>
                                        <td>{{ $data_cust->nama_instansi }} / {{ $data_cust->tipe_instansi }}</td>
                                    </tr>
                                    @if (isset($data_cust->company))
                                        <tr>
                                            <td><b>Nama Perusahaan</b></td>
                                            <td><b>:</b></td>
                                            <td>{{ $data_cust->company->name ?? '-' }}</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card card-flush mt-2">
            <div class="card-body pt-0 mt-5">
                <div class="py-5">
                    <div class="row">
                        <div class="col-md-3">
                            <h3 class="mb-3">Summary Transaksi</h3>
                            <div class="notice bg-light-warning rounded border-warning border border-dashed p-6 mt-2">
                                <!--begin::Wrapper-->
                                <div class="d-flex flex-stack flex-grow-1">
                                    <!--begin::Content-->
                                    <div class="fw-semibold">
                                        <h4 class="text-gray-900 fw-bold">Total Omzet</h4>
                                        <div class="fs-6 text-gray-700">Rp. {{isset($total_omzet) ? number_format($total_omzet,0,'','.') : 0}}</div>
                                    </div>
                                    <!--end::Content-->
                                </div>
                                <hr>
                                <div class="d-flex flex-stack flex-grow-1">
                                    <!--begin::Content-->
                                    <div class="fw-semibold">
                                        <h4 class="text-gray-900 fw-bold">Potential Order</h4>
                                        <div class="fs-6 text-gray-700">Rp. {{isset($total_potential) ? number_format($total_potential,0,'','.') : 0}}</div>
                                    </div>
                                    <!--end::Content-->
                                </div>
                                <!--end::Wrapper-->
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="table-responsive">
                                <table class="table table-striped gy-7 gs-7" id="datatable_detail_customer">
                                    <thead>
                                        <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                                            <th nowrap>Tanggal order</th>
                                            <th>Kode Order</th>
                                            <th>SKO</th>
                                            <th>Jatuh Tempo</th>
                                            <th>Status Deal</th>
                                            <th>Status Order</th>
                                            <th nowrap>Total Harga</th>
                                        </tr>
                                    </thead>
                                    <tbody></tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</x-app-layout>
