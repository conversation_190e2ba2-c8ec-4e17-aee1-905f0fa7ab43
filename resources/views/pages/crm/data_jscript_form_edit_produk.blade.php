<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/css/tempus-dominus.css" rel="stylesheet" type="text/css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/js/tempus-dominus.js"></script>
<script src="{{ url('js/simple.money.format.js') }}"></script>
<link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">
<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script>
    $(document).ready(function(){
        $('#ppn_form').hide();

        $('#jenis_kertas_edit').on('change', function(){
            if($('#jenis_kertas_edit').val() == 'Lainnya'){
                $('#jenis_kertas_lainnya_edit').show();
            }else{
                $('#jenis_kertas_lainnya_edit').hide();
            }
        });

        $('#gramasi_edit').on('change', function(){
            if($('#gramasi_edit').val() == 'Lainnya'){
                $('#gramasi_lainnya_edit').show();
            }else{
                $('#gramasi_lainnya_edit').hide();
            }
        });

        $('#finishing12_edit').on('change', function(){
            if($('#finishing12_edit').is(':checked')){
                $('#finishing_lainnya_edit').show();
            }else{
                $('#finishing_lainnya_edit').hide();
            }
        });

        $('#ppn').on('change', function() {
            if ($('#ppn').is(':checked')) {
                $('#ppn_form').show();
            } else {
                $('#ppn_form').hide();
            }
        });

        $('#total_harga, #ppn_percent').on("keyup change", function() {
            $('#total_ppn').simpleMoneyFormat();
            total_harga = $("#total_harga").val().replace(/[A-Za-z$. ,-]/g, "");
            ppn_percent = $("#ppn_percent").val();
            $('#total_ppn').val(total_harga * (ppn_percent / 100)).simpleMoneyFormat();
        })

        $('#jenis_kertas_lainnya_edit').hide();
        $('#gramasi_lainnya_edit').hide();
        $('#finishing_lainnya_edit').hide();
        $('#total_ppn').simpleMoneyFormat();
        
        var key = $('#sko_key_edit').val();
        $.get("{{ url('crm/show_produk/') }}/" + key, function (data) {
            //autocalctotal
            if (data[0].ppn === 1) {
                $("#ppn_edit").prop('checked', true);
                $('#ppn_form_edit').show();
                $('#ppn_percent_edit').val(data[0].ppn_percent);
                $('#total_ppn_edit').val(data[0].total_ppn);
            }
            $('#harga_produk_edit, #jumlah_produk_edit').on("keyup change", function(){
                $('#total_harga_edit').simpleMoneyFormat();
                jumlah_produk = $("#jumlah_produk_edit").val();
                harga_produk = $("#harga_produk_edit").val().replace(/[A-Za-z$. ,-]/g, "");
                $('#total_harga_edit').val(jumlah_produk*harga_produk).simpleMoneyFormat();
            });

            var jenis_kertas_arr = Array('Art Paper',
                                    'Art Carton',
                                    'Ivory',
                                    'Duplex',
                                    'Brown Kraft Liner',
                                    'Kraft PE',
                                    'Ivory Food Grade',
                                    'E Flute',
                                    'B Flute',
                                    'C Flute',
                                    'CB Flute',
                                    'Lainnya');
            var gramasi_arr = Array('K125/M125/M125 (SF COKLAT)',
                                    'K125/M125/K125 (COKLAT-COKLAT)',
                                    'K150/M125/K150 (COKLAT-COKLAT)',
                                    'WK140/M125/M125 (SF PUTIH)',
                                    'WK140/M1215/K125 (PUTIH-COKLAT)',
                                    'WK140/M125/K150 (PUTIH-COKLAT)',
                                    'WK140/M125/WK140 (PUTIH-PUTIH)',
                                    'Lainnya');
            
            $('#modal_edit_produk').modal('show');
            $('#id_produksi_edit').val(data[0].id_produksi);
            $('#kategori_produksi_edit').find('option[value="' + data[0].kategori_produksi + '"]').prop('selected', true);
            $('#jenis_bahan_edit').find('option[value="' + data[0].jenis_bahan + '"]').prop('selected', true);
            $('#dp_panjang_edit').val(data[0].dp_panjang);
            $('#dp_lebar_edit').val(data[0].dp_lebar);
            $('#dp_tinggi_edit').val(data[0].dp_tinggi);
            $('#lp_panjang_edit').val(data[0].lp_panjang);
            $('#lp_lebar_edit').val(data[0].lp_lebar);
            if(jenis_kertas_arr.includes(data[0].jenis_kertas)){
                $('#jenis_kertas_lainnya_edit').hide();
                $('#jenis_kertas_edit').find('option[value="' + data[0].jenis_kertas + '"]').prop('selected', true);
            }else{
                $('#jenis_kertas_lainnya_edit').show();
                $('#jenis_kertas_lainnya_edit').val(data[0].jenis_kertas);
                $('#jenis_kertas_edit').find('option[value="Lainnya"]').prop('selected', true);
            }
            if(gramasi_arr.includes(data[0].gramasi)){
                $('#gramasi_lainnya_edit').hide();
                $('#gramasi_edit').find('option[value="' + data[0].gramasi + '"]').prop('selected', true);
            }else{
                $('#gramasi_lainnya_edit').show();
                $('#gramasi_lainnya_edit').val(data[0].gramasi);
                $('#gramasi_edit').find('option[value="Lainnya"]').prop('selected', true);
            }
            $('#laminasi_edit').find('option[value="' + data[0].laminasi + '"]').prop('selected', true);
            $('#sisi_laminasi_edit').find('option[value="' + data[0].sisi_laminasi + '"]').prop('selected', true);
            $('#tipe_produk_edit').find('option[value="' + data[0].tipe_produk + '"]').prop('selected', true);
            $('#isi_kertas_edit').find('option[value="' + data[0].isi_kertas + '"]').prop('selected', true);
            $('#get_plano_edit').val(data[0].get_plano);
            $('#jumlah_produk_edit').val(data[0].jumlah_produk);
            $('#harga_produk_edit').val(data[0].harga_produk).simpleMoneyFormat();
            $('#total_harga_edit').val(data[0].total_harga).simpleMoneyFormat();
            $('#modal_sales_edit').val(data[0].modal_sales).simpleMoneyFormat();
            $("#hitung_harga_khusus_edit[value='" + data[0].hitung_harga_khusus + "']").prop('checked', true);
            $("#kendala_produksi_edit[value='" + data[0].kendala_produksi + "']").prop('checked', true);
            $('#nama_produk_edit').val(data[0].nama_produk);
            $('#notes_edit').val(data[0].notes);
            $.each(data.selesai, function(i, val){
                $("input[value='" + val + "']").prop('checked', true);
                if(data.selesai == 'Lainnya'){
                    $('#finishing_lainnya_edit').show();
                    $('#finishing_lainnya_edit').val(data.selesai);
                }else{
                    $('#finishing_lainnya_edit').hide();
                }
            });
            
        })

        $('#process-form-edit-produk').click(function (e) {
            e.preventDefault();
                        $.ajax({
                            data: $('#edit-produk').serialize(),
                            url: "{{ route('crm.edit_produk') }}",
                            type: "POST",
                            dataType: 'json',
                            success: function (data) {
                                Swal.fire({
                                    title: 'SUCCESS!',
                                    text: "Order berhasil diedit/update",
                                    icon: 'success',
                                });
                                window.location = "{{ route('crm.detail_order', $param_encrypt) }}";
                            },
                            error: function (data) {
                                console.log('Error:', data);
                                $('#process-form-edit-produk').html('Error');
                            }
                        });
        });

    });
</script>