<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/css/tempus-dominus.css" rel="stylesheet" type="text/css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/js/tempus-dominus.js"></script>
<script src="{{ url('js/simple.money.format.js') }}"></script>
<link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">
<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script>
    $(document).ready(function(){

        //form validation cust page
        const form = document.getElementById('form-direct-order');

        var validator = FormValidation.formValidation(
            form,
            {
                fields: {
                    'sumber': {
                        validators: {
                            notEmpty: {
                                message: 'Sumber wajib diisi!'
                            }
                        }
                    },
                    'waktu_kontak': {
                        validators: {
                            notEmpty: {
                                message: 'Waktu Kontak wajib diisi!'
                            }
                        }
                    },
                    'tipe_kontak': {
                        validators: {
                            notEmpty: {
                                message: 'Tipe Kontak wajib diisi!'
                            }
                        }
                    },
                    'id_pic': {
                        validators: {
                            notEmpty: {
                                message: 'Waktu Kontak wajib diisi!'
                            }
                        }
                    },
                    'status_deal': {
                        validators: {
                            notEmpty: {
                                message: 'Status Deal wajib dipilih!'
                            }
                        }
                    },
                    'status_order': {
                        validators: {
                            notEmpty: {
                                message: 'Status Order wajib dipilih!'
                            }
                        }
                    },
                    'kategori_produksi': {
                        validators: {
                            notEmpty: {
                                message: 'Kategori Produksi wajib dipilih!'
                            }
                        }
                    },
                    'flag_dummy': {
                        validators: {
                            notEmpty: {
                                message: 'Tipe Order wajib dipilih!'
                            }
                        }
                    }
                },

                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: '.fv-row',
                        eleInvalidClass: '',
                        eleValidClass: ''
                    })
                }
            }
        );

        $("#main_kode_order").autocomplete({
            source: function (request, response) {
                var listData = [];
                $.ajax({
                    url: "{{ route('crm.main_kode_order_get') }}",
                    dataType: "json",
                    method: "GET",
                    headers: {
                        'X-api-token': 'f9854c56b995ac7e09f207c07a656750ba80b63e'
                    },
                    data: {
                        kode_order: request.term,
                        id_customer: $('#id_customer').val()
                    },
                    success: function (data) {
                        response($.map(data, function (item) {
                            return {
                                label: item.kode_order,
                                value: item.order_key,
                                nama_customer: item.nama,
                            };
                        }));
                    },
                    error: function (response) {
                        console.log('Error:', response);
                    }
                });
            },
            select: function (event, ui) {
                $("#main_kode_order").val(ui.item.label);
                $("#order_key").val(ui.item.value);
                return false;
            }
        })
        $('#main_kode_order').on('click', function() {
            $("#main_kode_order").autocomplete( "search", "%" );
        })

        //show main kode order only for 2nd, more produk
        $('.kode_order_group').hide();
        $('#produk_ke').on('change', function(){
            if($('#produk_ke').val() != '1'){
                $('.kode_order_group').show();
            }else{
                $('.kode_order_group').hide();
            }
        })

        //waktu_kontak_add_order
        $(function() {
            const picker = new tempusDominus.TempusDominus(document.getElementById('waktu_kontak'),
            {
                display: {
                sideBySide: true,
                calendarWeeks: false,
                viewMode: 'calendar',
                toolbarPlacement: 'top',
                components: {
                    calendar: true,
                    date: true,
                    month: true,
                    year: true,
                    decades: true,
                    clock: true,
                    hours: true,
                    minutes: true,
                    seconds: false,
                    useTwentyfourHour: true
                },
                theme: 'light'
                }
            });
            picker.dates.formatInput = date => moment(date).format('DD/MM/YYYY HH:mm')
            picker.dates.setValue();
        });

        //bulan page order
        $('.input-bulan').hide();
        $('.input-bulan').attr('disabled');

        $('form#add-new-order').not('.form-select-solid').change(function(){
            var waktu_kontak = document.getElementById('waktu_kontak').value;
            var tgl_order = document.getElementById('tgl_order').value;

            if(waktu_kontak.length > 1 && tgl_order.length === 0){
                $('.input-bulan').show();
                $('#bulan').val(moment(waktu_kontak, 'YYYY-MM-DD').format('MMMM'));
                $('#bulan').prop('disabled', true);
            }else if(waktu_kontak.length > 1 && tgl_order.length > 1){
                $('.input-bulan').show();
                $('#bulan').val(moment(tgl_order, 'YYYY-MM-DD').format('MMMM'));
                $('#bulan').prop('disabled', true);
            }else if(waktu_kontak.length === 0 && tgl_order.length > 1){
                $('.input-bulan').show();
                $('#bulan').val(moment(tgl_order, 'YYYY-MM-DD').format('MMMM'));
                $('#bulan').prop('disabled', true);
            }else if(waktu_kontak.length === 0 && tgl_order.length === 0){
                $('.input-bulan').hide();
            }
        });

        //currency format
        $('#harga_produk').simpleMoneyFormat();
        $('#total_harga').simpleMoneyFormat();
        $('#modal_sales').simpleMoneyFormat();
        $('#total_ppn').simpleMoneyFormat();
        $('#DP').simpleMoneyFormat();

        //autocalctotal
        $('#harga_produk, #jumlah_produk').on("keyup change", function(){
            $('#total_harga').simpleMoneyFormat();
            jumlah_produk = $("#jumlah_produk").val();
            harga_produk = $("#harga_produk").val().replace(/[A-Za-z$. ,-]/g, "");
            $('#total_harga').val(jumlah_produk*harga_produk).simpleMoneyFormat();
        })

        $('#total_harga, #ppn_percent').on("keyup change", function() {
            $('#total_ppn').simpleMoneyFormat();
            total_harga = $("#total_harga").val().replace(/[A-Za-z$. ,-]/g, "");
            ppn_percent = $("#ppn_percent").val();
            $('#total_ppn').val(total_harga * (ppn_percent / 100)).simpleMoneyFormat();
        })

        $('#status_order1').hide();
        $('#status_order2').hide();
        $('#status_order3').hide();
        $('#tanggal_order').hide();
        $('#section_dp').hide();

        $('#status_deal').not('.form-select-solid').change(function(){
            // console.info("test");
            var status_deal = $(this).val();

            if(status_deal == 'Follow Up'){
                $('#status_order1').show();
                $('#status_order2').hide();
                $('#status_order3').hide();
                $('#tanggal_order').hide();
                $('#section_dp').hide();
            }else if(status_deal == 'Deal'){
                $('#status_order1').hide();
                $('#status_order2').show();
                $('#status_order3').hide();
                $('#tanggal_order').show();
                $('#section_dp').show();
            }else if(status_deal == 'Lost'){
                $('#status_order1').hide();
                $('#status_order2').hide();
                $('#status_order3').show();
                $('#tanggal_order').hide();
                $('#section_dp').hide();
            }
        });

        $('#jenis_kertas_lainnya').hide();
        $('#gramasi_lainnya').hide();
        $('#finishing_lainnya').hide();

        $('#flag_lunas').on('change', function() {
            if ($('#flag_lunas').is(':checked')) {
                $('#section_pelunasan').attr('disabled', false);
            } else {
                $('#section_pelunasan').attr('disabled', true);
            }
        });

        $('#jenis_kertas').on('change', function(){
            if($('#jenis_kertas').val() == 'Lainnya'){
                $('#jenis_kertas_lainnya').show();
            }else{
                $('#jenis_kertas_lainnya').hide();
            }
        });

        $('#gramasi').on('change', function(){
            if($('#gramasi').val() == 'Lainnya'){
                $('#gramasi_lainnya').show();
            }else{
                $('#gramasi_lainnya').hide();
            }
        });

        $('#finishing12').on('change', function(){
            if($('#finishing12').is(':checked')){
                $('#finishing_lainnya').show();
            }else{
                $('#finishing_lainnya').hide();
            }
        });

        $('#ppn').on('change', function() {
            if ($('#ppn').is(':checked')) {
                $('#ppn_form').show();
            } else {
                $('#ppn_form').hide();
            }
        });

    //add new order
    $('#direct-order').click(function (e) {
            e.preventDefault();
            $('#direct-order').attr('disabled', true);
            if(validator) {
                validator.validate().then(function (status) {
                    if (status == 'Valid') {
                        $.ajax({
                            data: $('#form-direct-order').serialize(),
                            url: "{{ route('crm.store_new_order') }}",
                            type: "POST",
                            dataType: 'json',
                            success: function(data) {
                                Swal.fire({
                                    title: 'SUCCESS!',
                                    text: "Order berhasil ditambah",
                                    icon: 'success',
                                });
                                window.location = "{{ route('crm') }}";
                            },
                            error: function(data) {
                                console.log('Error:', data);
                                $('#process-form-order').html('Error');
                                $('#direct-order').attr('disabled', false);
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Failed!',
                            text: "Lengkapi kolom yang wajib diisi",
                            icon: 'error',
                        }).then(() => {
                            $('#direct-order').attr('disabled', false);
                        })
                    }
                });
            }
        });
    });

    //select2 pic page order
    $(document).ready(function(){
        $('#ppn_form').hide();

        const format = (item) => {
            if (!item.id) {
                return item.text;
            }
            var img = "<span class='menu-icon me-3'><svg xmlns='http://www.w3.org/2000/svg' width='26' height='26' viewBox='0 0 24 24' fill='none'><path opacity='0.3' d='M3 6C2.4 6 2 5.6 2 5V3C2 2.4 2.4 2 3 2H5C5.6 2 6 2.4 6 3C6 3.6 5.6 4 5 4H4V5C4 5.6 3.6 6 3 6ZM22 5V3C22 2.4 21.6 2 21 2H19C18.4 2 18 2.4 18 3C18 3.6 18.4 4 19 4H20V5C20 5.6 20.4 6 21 6C21.6 6 22 5.6 22 5ZM6 21C6 20.4 5.6 20 5 20H4V19C4 18.4 3.6 18 3 18C2.4 18 2 18.4 2 19V21C2 21.6 2.4 22 3 22H5C5.6 22 6 21.6 6 21ZM22 21V19C22 18.4 21.6 18 21 18C20.4 18 20 18.4 20 19V20H19C18.4 20 18 20.4 18 21C18 21.6 18.4 22 19 22H21C21.6 22 22 21.6 22 21ZM16 11V9C16 6.8 14.2 5 12 5C9.8 5 8 6.8 8 9V11C7.2 11 6.5 11.7 6.5 12.5C6.5 13.3 7.2 14 8 14V15C8 17.2 9.8 19 12 19C14.2 19 16 17.2 16 15V14C16.8 14 17.5 13.3 17.5 12.5C17.5 11.7 16.8 11 16 11ZM13.4 15C13.7 15 14 15.3 13.9 15.6C13.6 16.4 12.9 17 12 17C11.1 17 10.4 16.5 10.1 15.7C10 15.4 10.2 15 10.6 15H13.4Z' fill='black'/><path d='M9.2 12.9C9.1 12.8 9.10001 12.7 9.10001 12.6C9.00001 12.2 9.3 11.7 9.7 11.6C10.1 11.5 10.6 11.8 10.7 12.2C10.7 12.3 10.7 12.4 10.7 12.5L9.2 12.9ZM14.8 12.9C14.9 12.8 14.9 12.7 14.9 12.6C15 12.2 14.7 11.7 14.3 11.6C13.9 11.5 13.4 11.8 13.3 12.2C13.3 12.3 13.3 12.4 13.3 12.5L14.8 12.9ZM16 7.29998C16.3 6.99998 16.5 6.69998 16.7 6.29998C16.3 6.29998 15.8 6.30001 15.4 6.20001C15 6.10001 14.7 5.90001 14.4 5.70001C13.8 5.20001 13 5.00002 12.2 4.90002C9.9 4.80002 8.10001 6.79997 8.10001 9.09997V11.4C8.90001 10.7 9.40001 9.8 9.60001 9C11 9.1 13.4 8.69998 14.5 8.29998C14.7 9.39998 15.3 10.5 16.1 11.4V9C16.1 8.5 16 8 15.8 7.5C15.8 7.5 15.9 7.39998 16 7.29998Z' fill='black'/></svg></span>"
            var span = $("<span>", {
                text: " " + item.text
            });
            span.prepend(img)
            return span
        }
        $('#id_pic').select2({ 
            allowClear: true,
            debug: true,
            ajax: {
                // url: baseUrl + 'customer/getCustomer',
                url: "{{ route('crm.get_pic') }}",
                processResults: function (data) {
                    var resultsData = []

                    $.each(data, function(index, item){
                        resultsData.push({
                            id: item.id,
                            text: item.name
                        })
                    })

                    return {
                    results: resultsData
                    };
                },
                // cache: true
            },        
            templateResult: function (item) {
                return format(item)
            }    
        })
        .on('select2:opening', function(e) {
            $(this).data('select2').$dropdown.find(':input.select2-search__field').attr('placeholder', 'Cari Nama PIC')
        })
    })
</script>