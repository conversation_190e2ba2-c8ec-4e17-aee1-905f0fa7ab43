<script>
    const form = document.getElementById('form-faw');

    var validator = FormValidation.formValidation(
            form,
            {
                fields: {
                    'waktu_produksi': {
                        validators: {
                            notEmpty: {
                                message: 'Waktu produksi wajib diisi!'
                            }
                        }
                    },
                    'tgl_deadline': {
                        validators: {
                            notEmpty: {
                                message: 'Tanggal deadline wajib diisi!'
                            }
                        }
                    },
                    'tgl_deadline_konsumen': {
                        validators: {
                            notEmpty: {
                                message: 'Tanggal deadline konsumen wajib diisi!'
                            }
                        }
                    },
                    'link_file_final': {
                        validators: {
                            notEmpty: {
                                message: 'Link final wajib diisi!'
                            }
                        }
                    },
                    'lampiran': {
                        validators: {
                            notEmpty: {
                                message: 'Lampiran wajib diisi!'
                            },
                            file: {
                                extension: 'jpeg,jpg,png',
                                type: 'image/jpeg,image/png',
                                message: 'The selected file is not valid',
                            }
                        }
                    }
                },

                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: '.fv-row',
                        eleInvalidClass: '',
                        eleValidClass: ''
                    }),
                    // Validate fields when clicking the Submit button
                    submitButton: new FormValidation.plugins.SubmitButton(),
                    // Submit the form when all fields are valid
                    defaultSubmit: new FormValidation.plugins.DefaultSubmit(),
                }
            }
        );
    
    //add new cust
    // $('#store-faw').click(function (e) {
    //     e.preventDefault();
    //     if(validator) {
    //         validator.validate().then(function (status) {
    //             // if (status == 'Valid') {
    //                 // Swal.fire({
    //                 //     title: 'SUCCESS!',
    //                 //     text: "FAW berhasil diterbitkan",
    //                 //     icon: 'success',
    //                 // })
    //                 // return TRUE;
    //                 //
    //                 $.ajax({
    //                     headers: {
    //                         'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    //                     },
    //                     data: $('#form-faw').serialize(),
    //                     url: "{{ route('crm.store_faw') }}",
    //                     type: "POST",
    //                     dataType: 'json',
    //                     success: function (data) {
    //                         console.log(data);
    //                         Swal.fire({
    //                             title: 'SUCCESS!',
    //                             text: "Customer berhasil ditambah",
    //                             icon: 'success',
    //                         });
    //                         window.location = "/crm/detail_order/" + data.param;
    //                     },
    //                     error: function (data) {
    //                         console.log('Error:', data);
    //                         Swal.fire({
    //                             title: 'Failed!',
    //                             text: "Gagal input",
    //                             icon: 'error',
    //                         })
    //                     }
    //                 });
    //             // }if (status == 'Invalid'){
    //             //     Swal.fire({
    //             //         title: 'Failed!',
    //             //         text: "Lengkapi kolom yang wajib diisi",
    //             //         icon: 'error',
    //             //     })
    //             // }
    //         });
    //     }
    // });
</script>