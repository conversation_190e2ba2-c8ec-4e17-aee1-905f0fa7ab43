<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/css/tempus-dominus.css" rel="stylesheet" type="text/css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/js/tempus-dominus.js"></script>
<script src="{{ url('js/simple.money.format.js') }}"></script>
<link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">
<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script>
    $(document).ready(function(){
        //CSRF cust page
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        window.throttleDTSearch = DataTable.util.throttle(function (dt, val) {
            dt.search(val).draw();
        }, 450);

        const dataTableLanguage = {
            loadingRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div class="spinner-grow text-warning" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>`,
            emptyTable: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
            zeroRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
        };

        //datatable page order
        dt_price_null = $('#datatable_price_null').DataTable({
            processing: true,
            serverSide: true,
            language: dataTableLanguage,
            paging: true,
            pageLength: 10,
            ajax: {
                url: "{{ route('crm.table_price_null') }}",
                data: function(d) {
                    d.waktu_kontak_from_date = $('input[name=waktu_kontak_from_date]').val();
                    d.waktu_kontak_to_date = $('input[name=waktu_kontak_to_date]').val();
                },
            },
            deferRender: true,
            columnDefs: [ { type: 'date', targets: [0] } ],
            order: [[ 5, 'desc' ]],
            columns: [
                {
                    data: 'waktu_kontak',
                    name: 'waktu_kontak'
                },
                {
                    data: 'name',
                    name: 'users.name'

                },
                {
                    data: 'nama',
                    name: 'tb_customers.nama'
                },
                {
                    data: 'grading',
                    name: 'tb_customers.grading'
                },
                {
                    data: 'status_order',
                    name: 'status_order',
                    searchable: false
                },
                {
                    data: 'id_order',
                    name: 'id_order',
                    searchable: false,
                    visible: false
                }
            ],
            
        });

        //filter page order
        const filterSearch = document.querySelector('[data-kt-docs-table-filter="search-order"]')
        filterSearch.addEventListener('keyup', function (e) {
            window.throttleDTSearch(dt_price_null, e.target.value)
        })

        @if(Auth::user()->roles == 'SALES SPV' ||Auth::user()->roles == 'SUPERADMIN')
        const filterPIC = document.querySelector('.filter-order-pic')
        filterPIC.addEventListener('change', function (e) {
            dt_price_null.search(e.target.value).draw()
        })
        @endif

        const filterStatusOrder = document.querySelector('.filter-order-status-order')
        filterStatusOrder.addEventListener('change', function (e) {
            dt_price_null.columns(3).search(e.target.value).draw();
        })

        const filterTglOrder = document.querySelector('#filter-order-tgl-order')
        filterTglOrder.addEventListener('click', function (e) {
            const startDate = $('input[name=waktu_kontak_from_date]').val();
            const endDate = $('input[name=waktu_kontak_to_date]').val();

            // Convert string dates to Date objects
            const startDateValue = new Date(startDate);
            const endDateValue = new Date(endDate);

            if (endDateValue < startDateValue)
                return
            dt_price_null.draw();
        })

        const resetTglOrder = document.querySelector('#reset-order-tgl-order')
        resetTglOrder.addEventListener('click', function (e) {
            $('input[name=waktu_kontak_from_date]').val('');
            $('input[name=waktu_kontak_to_date]').val('');
            dt_price_null.draw();
        })
    });

</script>