<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ url()->previous() }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">Edit FAW (Form Approval Works)</h3>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        @include('pages.crm.data_jscript_form_edit_faw')
        <script>
                function deleteConfirmation() {
                    Swal.fire({
                    title: 'Are you sure?',
                    text: "You won't be able to revert this!",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, delete it!'
                    }).then((result) => {
                    if (result.isConfirmed) {
                        Swal.fire(
                        'Deleted!',
                        'Your file has been deleted.',
                        'success'
                        ),
                        window.location = "{{ route('crm.delete_lampiran', $data->id_faw) }}";
                    }
                    })
                }
        </script>
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-header">
                <h3 class="card-title text-dark"><i>#{{ $data->kode_order }} - {{ $data->nama }}</i></h3>
            </div>
            <div class="card-body pt-0">
                <form method="POST" id="form-edit-faw" action="{{ route('crm.update_faw') }}" enctype="multipart/form-data">
                    @csrf
                    @method('POST')
                    <input type="hidden" name="id_order" value="{{ $data->id_order }}">
                    <input type="hidden" name="order_key" value="{{ $data->order_key }}">
                    <input type="hidden" name="sko_key" value="{{ $data->sko_key }}">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-10">
                                <label for="sko" class="form-label">Sub Kode Order</label>
                                <input type="text" name="sko" value="{{ $data->sko }}" readonly id="sko" class="form-control form-control-solid"/>
                            </div>
                            <div class="mb-10">
                                <label for="nama" class="form-label">Nama Customer</label>
                                <input type="text" name="nama" id="nama" value="{{ $data->nama }}" readonly class="form-control form-control-solid"/>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="waktu_produksi" class="required form-label">Waktu Produksi</label>
                                <input type="text" name="waktu_produksi" value="{{ old('waktu_produksi') ?? $data->waktu_produksi}}" id="waktu_produksi" class="form-control"/>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="tgl_deadline" class="required form-label">Tanggal Deadline</label>
                                <input type="date" name="tgl_deadline" value="{{ old('tgl_deadline') ?? $data->tgl_deadline }}" id="tgl_deadline" class="form-control"/>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="tgl_faw" class="required form-label">Tanggal FAW</label>
                                <input type="date" name="tgl_faw" value="{{ old('tgl_faw') ?? $data->tgl_faw }}" id="tgl_faw" class="form-control"/>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="fv-row mb-10">
                                <label for="link_file_final" class="required form-label">File Final (Gdrive)</label>
                                <div class="input-group">
                                    <input type="text" name="link_file_final" id="link_file_final" value="{{ old('file_final') ?? $data->file_final }}" class="form-control"/>
                                    <span class="input-group-text" id="basic-addon2"><i class="fa-solid fa-link"></i></span>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="lampiran" class="form-label">lampiran</label>
                                <input type="file" name="lampiran" class="form-control"/>
                                @if (!empty($data->path_lampiran))
                                <div class="notice d-flex bg-light-warning rounded border-warning border border-dashed p-6 mt-3">
                                    <!--begin::Wrapper-->
                                    <div class="d-flex flex-stack flex-grow-1">
                                        <!--begin::Content-->
                                        <div class="fw-semibold">
                                            <div class="fs-6 text-gray-700"><i class="fa-solid fa-paperclip"></i> {{ $data->path_lampiran }}</div>
                                        </div>
                                        <a href="javascript:void(0)" onclick="deleteConfirmation();"><i class="fa-solid fa-trash-can"></i></a>
                                        <!--end::Content-->
                                    </div>
                                    <!--end::Wrapper-->
                                </div>
                                @endif
                            </div>
                            <div class="mb-10">
                                <label for="keterangan_tambahan" class="form-label">Keterangan Tambahan</label>
                                <textarea type="text" name="keterangan_tambahan" id="notes" rows="6" class="form-control">{{ old('keterangan_tambahan') ?? $data->keterangan_tambahan }}</textarea>
                            </div>
                        </div>
                    </div>
                    <input type="submit" form="form-edit-faw" id="update-faw" value="Update FAW" class="btn my-primary text-white float-end">
                </form>
            </div>
        </div>
    </div>

    
</x-app-layout>