<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/css/tempus-dominus.css" rel="stylesheet"
    type="text/css" />

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/js/tempus-dominus.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script src="{{ url('js/simple.money.format.js') }}"></script>
<style>
    .swal-button {
        border: 0;
        border-radius: 0.25em;
        background: initial;
        background-color: #dc3741;
        color: #fff;
        font-size: 1em;
        padding: 10px 17.6px;
        margin: 5px;
    }
    .swal-button:hover {
        border: 0;
        border-radius: 0.25em;
        background: initial;
        background-color: #790c32;
        color: #fff;
        font-size: 1em;
        padding: 10px 17.6px;
        margin: 5px;
    }
    .swal-button.cancel {
        border: 0;
        border-radius: 0.25em;
        background: initial;
        background-color: #6e7881;
        color: #fff;
        font-size: 1em;
        padding: 10px 17.6px;
        margin: 5px;
    }
    .swal-button.cancel:hover {
        border: 0;
        border-radius: 0.25em;
        background: initial;
        background-color: #52585c;
        color: #fff;
        font-size: 1em;
        padding: 10px 17.6px;
        margin: 5px;
    }
</style>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">
<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<script>

    $(document).ready(function() {
        var isCollapsed = true; // Initialize the collapse state

        $('#collapseButton').on('click', function () {
            var collapse = $('#collapseExample');
            
            if (isCollapsed) {
                collapse.collapse('show');
                $("#table_div").removeClass("mt-2")
            } else {
                collapse.collapse('hide');
                $("#table_div").addClass("mt-2")
            }
            
            isCollapsed = !isCollapsed; // Toggle the collapse state
        });

        $('#data_year').each(function() {

            var year = (new Date()).getFullYear();
            var current = year;
            year -= 5;
            for (var i = 0; i < 6; i++) {
                if ((year + i) == current)
                    $(this).append('<option selected value="' + (year + i) + '">' + (year + i) +
                        '</option>');
                else
                    $(this).append('<option value="' + (year + i) + '">' + (year + i) + '</option>');
            }

        })

        //form validation page order
        const form_order = document.getElementById('add-new-order');
        const form_edit_order = document.getElementById('edit-order');
        const form_edit_produk = document.getElementById('edit-produk');

        var validator_order = FormValidation.formValidation(
            form_order, {
                fields: {
                    'id_customer': {
                        validators: {
                            notEmpty: {
                                message: 'Customer wajib diisi!'
                            }
                        }
                    },
                    'sumber': {
                        validators: {
                            notEmpty: {
                                message: 'Sumber wajib diisi!'
                            }
                        }
                    },
                    'tipe_kontak': {
                        validators: {
                            notEmpty: {
                                message: 'Tipe Kontak wajib diisi!'
                            }
                        }
                    },
                    'waktu_kontak': {
                        validators: {
                            notEmpty: {
                                message: 'Waktu Kontak wajib diisi!'
                            }
                        }
                    },
                    'id_pic': {
                        validators: {
                            notEmpty: {
                                message: 'Waktu Kontak wajib diisi!'
                            }
                        }
                    },
                    'status_deal': {
                        validators: {
                            notEmpty: {
                                message: 'Status Deal wajib dipilih!'
                            }
                        }
                    },
                    'status_order': {
                        validators: {
                            notEmpty: {
                                message: 'Status Order wajib dipilih!'
                            }
                        }
                    },
                    'flag_dummy': {
                        validators: {
                            notEmpty: {
                                message: 'Tipe Order wajib dipilih!'
                            }
                        }
                    },
                    'kategori_produksi': {
                        validators: {
                            notEmpty: {
                                message: 'Kategori Produksi wajib dipilih!'
                            }
                        }
                    }
                },

                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: '.fv-row',
                        eleInvalidClass: '',
                        eleValidClass: ''
                    })
                }
            }
        );

        var validator_edit_order = FormValidation.formValidation(
            form_edit_order, {
                fields: {
                    'sumber': {
                        validators: {
                            notEmpty: {
                                message: 'Sumber wajib diisi!'
                            }
                        }
                    },
                    'waktu_kontak': {
                        validators: {
                            notEmpty: {
                                message: 'Waktu Kontak wajib diisi!'
                            }
                        }
                    },
                    'id_pic': {
                        validators: {
                            notEmpty: {
                                message: 'Waktu Kontak wajib diisi!'
                            }
                        }
                    },
                    'status_deal': {
                        validators: {
                            notEmpty: {
                                message: 'Status Deal wajib dipilih!'
                            }
                        }
                    },
                    'status_order': {
                        validators: {
                            notEmpty: {
                                message: 'Status Order wajib dipilih!'
                            }
                        }
                    }
                },

                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: '.fv-row',
                        eleInvalidClass: '',
                        eleValidClass: ''
                    })
                }
            }
        );

        var validator_edit_produk = FormValidation.formValidation(
            form_edit_produk, {
                fields: {
                    'kategori_produksi': {
                        validators: {
                            notEmpty: {
                                message: 'Kategori Produksi wajib dipilih!'
                            }
                        }
                    }
                },

                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: '.fv-row',
                        eleInvalidClass: '',
                        eleValidClass: ''
                    })
                }
            }
        );

        //datatable page order
        // tgl_order_from_date = '';
        // tgl_order_to_date = '';
        // waktu_kontak_from_date = '';
        // waktu_kontak_to_date = '';
        // data_year = $('#data_year').val();
        const dataTableLanguage = {
            loadingRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div class="spinner-grow text-warning" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>`,
            emptyTable: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
            zeroRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
        };

        //filter page order
        const filterSearch = document.querySelector('[data-kt-docs-table-filter="search-order"]')
        filterSearch.addEventListener('keyup', function(e) {
            window.throttleDTSearch(window.dt_order, e.target.value)
        })

        // const filterDataYear = document.querySelector('.filter-data-year')
        // filterDataYear.addEventListener('change', function (e) {
        //     window.dt_order.search(e.target.value).draw()
        // })

        @if (Auth::user()->roles == 'SALES SPV' || Auth::user()->roles == 'SUPERADMIN')
            const filterPIC = document.querySelector('.filter-order-pic');
            filterPIC.addEventListener('change', function(e) {
                window.dt_order.columns(4).search(e.target.value).draw();
            });
        @endif

        const filterStatusDeal = document.querySelector('.filter-order-status-deal')
        filterStatusDeal.addEventListener('change', function(e) {
            window.dt_order.columns(9).search(e.target.value).draw();
        })

        $('.filter-order-flag-dummy').on('change', function(e) {
            window.dt_order.columns(15).search(e.target.value).draw();
        });

        const filterYear = document.querySelector('.filter-order-year')
        filterYear.addEventListener('change', function(e) {
            window.dt_order.ajax.reload();
        })

        const filterStatusOrder = document.querySelector('.filter-order-status-order')
        filterStatusOrder.addEventListener('change', function(e) {
            window.dt_order.columns(10).search(e.target.value).draw();

        })

        $('.filter-sumber').on('change', function(e) {
            window.dt_order.columns(14).search(e.target.value).draw();
        });

        $('#filter-order-tgl-order').click(function() {
            $('#kt-toolbar-filter-order').removeClass('show');
            window.dt_order.ajax.reload();
        });

        $('#filter-tgl-order-reset').click(function() {
            $('#filter-tgl-order-from').val('');
            $('#filter-tgl-order-to').val('');
            $('#kt-toolbar-filter-order').removeClass('show');
            window.dt_order.ajax.reload();
        });

        $("#main_kode_order").autocomplete({
            source: function(request, response) {
                var listData = [];
                $.ajax({
                    url: "{{ route('crm.main_kode_order_get') }}",
                    dataType: "json",
                    method: "GET",
                    headers: {
                        'X-api-token': 'f9854c56b995ac7e09f207c07a656750ba80b63e'
                    },
                    data: {
                        kode_order: request.term,
                        id_customer: $('#id_customer').val()
                    },
                    success: function(data) {
                        response($.map(data, function(item) {
                            return {
                                label: item.kode_order,
                                value: item.order_key,
                                nama_customer: item.nama,
                            };
                        }));
                    },
                    error: function(response) {
                        console.log(response);
                    }
                });
            },
            select: function(event, ui) {
                $("#main_kode_order").val(ui.item.label);
                $("#order_key").val(ui.item.value);
                return false;
            },
            classes: {
                "ui-autocomplete": "highlight"
            },
            appendTo: '#add-new-order'
        })
        $('#main_kode_order').on('click', function() {
            $("#main_kode_order").autocomplete("search", "%");
        })

        $("#kode_order_edit").autocomplete({
            source: function(request, response) {
                var listData = [];
                $.ajax({
                    url: "{{ route('crm.main_kode_order_get') }}",
                    dataType: "json",
                    method: "GET",
                    headers: {
                        'X-api-token': 'f9854c56b995ac7e09f207c07a656750ba80b63e'
                    },
                    data: {
                        kode_order: request.term,
                        id_customer: $('#id_cust_edit').val()
                    },
                    success: function(data) {
                        response($.map(data, function(item) {
                            return {
                                label: item.kode_order,
                                value: item.order_key,
                                nama_customer: item.nama,
                            };
                        }));
                    },
                    error: function(response) {
                        console.log(response);
                    }
                });
            },
            select: function(event, ui) {
                $("#kode_order_edit").val(ui.item.label);
                $("#main_order_key_edit").val(ui.item.value);
                return false;
            },
            appendTo: '#edit-order'
        });

        $('#kode_order_edit').on('click', function() {
            $("#kode_order_edit").autocomplete("search", "%");
        })


        // //show main kode order only for 2nd, more produk
        // $('.kode_order_group').hide();
        // $('#produk_ke').on('change', function(){
        //     if($('#produk_ke').val() != '1'){
        //         $('.kode_order_group').show();
        //     }else{
        //         $('.kode_order_group').hide();
        //     }
        // })

        //waktu_kontak_add_order

        $(function() {
            const picker = new tempusDominus.TempusDominus(document.getElementById('waktu_kontak'), {
                display: {
                    sideBySide: true,
                    calendarWeeks: false,
                    viewMode: 'calendar',
                    toolbarPlacement: 'top',
                    components: {
                        calendar: true,
                        date: true,
                        month: true,
                        year: true,
                        decades: true,
                        clock: true,
                        hours: true,
                        minutes: true,
                        seconds: false,
                        useTwentyfourHour: true
                    },
                    theme: 'light'
                }
            });

            picker.dates.formatInput = date => moment(date).format('DD/MM/YYYY HH:mm')
            picker.dates.setValue();

            $('#forecast').on('change', function() {
                if ($(this).prop('checked')) {
                    $('#div-forecast').removeClass('d-none');
                } else {
                    $('#div-forecast').addClass('d-none');
                }
            });

            $('#forecast_edit').on('change', function() {
                if ($(this).prop('checked')) {
                    $('#div-forecast-edit').removeClass('d-none');
                } else {
                    $('#div-forecast-edit').addClass('d-none');
                }
            });
        });

        // flatpickr('#exampleDateInput', {
        //     dateFormat: "d/m/Y",
        // });

        //bulan page order
        $('.input-bulan').hide();
        $('.input-bulan').attr('disabled');



        $('form#add-new-order').not('.form-select-solid').change(function() {
            var waktu_kontak = document.getElementById('waktu_kontak').value;
            var tgl_order = document.getElementById('tgl_order').value;

            if (waktu_kontak.length > 1 && tgl_order.length === 0) {
                $('.input-bulan').show();
                $('#bulan').val(moment(waktu_kontak, 'YYYY-MM-DD').format('MMMM'));
                $('#bulan').prop('disabled', true);
            } else if (waktu_kontak.length > 1 && tgl_order.length > 1) {
                $('.input-bulan').show();
                $('#bulan').val(moment(tgl_order, 'YYYY-MM-DD').format('MMMM'));
                $('#bulan').prop('disabled', true);
            } else if (waktu_kontak.length === 0 && tgl_order.length > 1) {
                $('.input-bulan').show();
                $('#bulan').val(moment(tgl_order, 'YYYY-MM-DD').format('MMMM'));
                $('#bulan').prop('disabled', true);
            } else if (waktu_kontak.length === 0 && tgl_order.length === 0) {
                $('.input-bulan').hide();
            }
        });



        //currency format
        $('#harga_produk').simpleMoneyFormat();
        $('#total_harga').simpleMoneyFormat();
        $('#total_ppn').simpleMoneyFormat();
        $('#modal_sales').simpleMoneyFormat();
        $('#DP').simpleMoneyFormat();
        $('#pelunasan').simpleMoneyFormat();
        $('#expected-revenue').simpleMoneyFormat();
        $('#expected-revenue-edit').simpleMoneyFormat();

        //autocalctotal
        $('#harga_produk, #jumlah_produk').on("keyup change", function() {
            $('#total_harga').simpleMoneyFormat();
            jumlah_produk = $("#jumlah_produk").val();
            harga_produk = $("#harga_produk").val().replace(/[A-Za-z$. ,-]/g, "");
            $('#total_harga').val(jumlah_produk * harga_produk).simpleMoneyFormat();
        })

        $('#total_harga, #ppn_percent').on("keyup change", function() {
            $('#total_ppn').simpleMoneyFormat();
            total_harga = $("#total_harga").val().replace(/[A-Za-z$. ,-]/g, "");
            ppn_percent = $("#ppn_percent").val();
            $('#total_ppn').val(total_harga * (ppn_percent / 100)).simpleMoneyFormat();
        })

        $('#total_harga_edit, #ppn_percent_edit').on("keyup change", function() {
            $('#total_ppn_edit').simpleMoneyFormat();
            total_harga_edit = $("#total_harga_edit").val().replace(/[A-Za-z$. ,-]/g, "");
            ppn_percent_edit = $("#ppn_percent_edit").val();
            $('#total_ppn_edit').val(total_harga_edit * (ppn_percent_edit / 100)).simpleMoneyFormat();
        })

        $('#status_order1').hide();
        $('#status_order2').hide();
        $('#status_order3').hide();
        $('#tanggal_order').hide();
        $('#section_dp').hide();
        $('#follow_up_terakhir').hide();

        $('#status_deal').not('.form-select-solid').change(function() {
            console.info("test");
            var status_deal = $(this).val();

            if (status_deal == 'Follow Up') {
                $('#status_order1').show();
                $('#status_order2').hide();
                $('#status_order3').hide();
                $('#tanggal_order').hide();
                $('#section_dp').hide();
            } else if (status_deal == 'Deal') {
                $('#status_order1').hide();
                $('#status_order2').show();
                $('#status_order3').hide();
                $('#tanggal_order').show();
                $('#section_dp').show();
            } else if (status_deal == 'Lost') {
                $('#status_order1').hide();
                $('#status_order2').hide();
                $('#status_order3').show();
                $('#tanggal_order').hide();
                $('#section_dp').hide();
            }
        });
        $('#status_deal_edit').not('.form-select-solid').change(function() {
            var status_deal = $(this).val();

            if (status_deal == 'Follow Up') {
                $('#status_order1_edit').show();
                $('#status_order2_edit').hide();
                $('#status_order3_edit').hide();
                $('#tanggal_order_edit').hide();
                $('#section_dp_edit').hide();
                $('#follow_up_terakhir').hide();
            } else if (status_deal == 'Deal') {
                $('#status_order1_edit').hide();
                $('#status_order2_edit').show();
                $('#status_order3_edit').hide();
                $('#tanggal_order_edit').show();
                $('#section_dp_edit').show();
                $('#follow_up_terakhir').hide();
            } else if (status_deal == 'Lost') {
                $('#status_order1_edit').hide();
                $('#status_order2_edit').hide();
                $('#status_order3_edit').show();
                $('#tanggal_order_edit').hide();
                $('#section_dp_edit').hide();
                $('#follow_up_terakhir').show();
            }
        });

        $('#jenis_kertas_lainnya').hide();
        $('#gramasi_lainnya').hide();
        $('#finishing_lainnya').hide();
        // $('#section_pelunasan').hide();

        $('#flag_lunas').on('change', function() {
            if ($('#flag_lunas').is(':checked')) {
                $('#section_pelunasan').attr('disabled', false);
            } else {
                $('#section_pelunasan').attr('disabled', true);
            }
        });

        $('#flag_lunas_edit').on('change', function() {
            if ($('#flag_lunas_edit').is(':checked')) {
                $('#section_pelunasan_edit').attr('disabled', false);
            } else {
                $('#section_pelunasan_edit').attr('disabled', true);
            }
        });

        $('#jenis_kertas').on('change', function() {
            if ($('#jenis_kertas').val() == 'Lainnya') {
                $('#jenis_kertas_lainnya').show();
            } else {
                $('#jenis_kertas_lainnya').hide();
            }
        });

        $('#jenis_kertas_edit').on('change', function() {
            if ($('#jenis_kertas_edit').val() == 'Lainnya') {
                $('#jenis_kertas_lainnya_edit').show();
            } else {
                $('#jenis_kertas_lainnya_edit').hide();
            }
        });

        $('#gramasi').on('change', function() {
            if ($('#gramasi').val() == 'Lainnya') {
                $('#gramasi_lainnya').show();
            } else {
                $('#gramasi_lainnya').hide();
            }
        });

        $('#gramasi_edit').on('change', function() {
            if ($('#gramasi_edit').val() == 'Lainnya') {
                $('#gramasi_lainnya_edit').show();
            } else {
                $('#gramasi_lainnya_edit').hide();
            }
        });

        $('#finishing12').on('change', function() {
            if ($('#finishing12').is(':checked')) {
                $('#finishing_lainnya').show();
            } else {
                $('#finishing_lainnya').hide();
            }
        });

        $('#ppn').on('change', function() {
            if ($('#ppn').is(':checked')) {
                $('#ppn_form').show();
            } else {
                $('#ppn_form').hide();
            }
        });

        $('#ppn_edit').on('change', function() {
            if ($('#ppn_edit').is(':checked')) {
                $('#ppn_form_edit').show();
            } else {
                $('#ppn_form_edit').hide();
            }
        });

        $('#finishing12_edit').on('change', function() {
            if ($('#finishing12_edit').is(':checked')) {
                $('#finishing_lainnya_edit').show();
            } else {
                $('#finishing_lainnya_edit').hide();
            }
        });

        // add order
        $('#add_order_btn').click(function(e) {
            e.preventDefault();
            // var add = document.getElementById('error_add_flag_dummy');
            // add.style.display = 'none';
            $('#ppn_form').hide();
        })

        $('#process-form-order').click(function(e) {
            e.preventDefault();
            $('#process-form-order').attr('disabled', true);
            // var flag_dummy_value = document.getElementsByName('flag_dummy')
            // var error = document.getElementsByName('error_add_flag_dummy')
            // var status_flag = false
            // for (var i = 0; i < flag_dummy_value.length; i++) {
            //     if (flag_dummy_value[i].checked){
            //         status_flag = true
            //     }
            // }
            if (validator_order) {
                validator_order.validate().then(function(status) {
                    if (status == 'Valid') {
                        if($('#nama_produk').val() == '') {
                            Swal.fire({
                                title: 'Failed!',
                                text: "Lengkapi kolom yang wajib diisi",
                                icon: 'error',
                            }).then(() => {
                                $('#process-form-order').attr('disabled', false);
                                $('#nama_produk').addClass('is-invalid');
                                $('#nama_produk').after('<span class="invalid-feedback">Nama Produk wajib diisi</span>');
                                return false;
                            });
                        }
                        $.ajax({
                            data: $('#add-new-order').serialize(),
                            url: "{{ route('crm.store_new_order') }}",
                            type: "POST",
                            dataType: 'json',
                            success: function(data) {
                                document.getElementById('add-new-order').reset()
                                setTimeout(function() {
                                    self.$("#close-form-order").trigger(
                                        "click");
                                }, 1200);
                                window.dt_order.ajax.reload();
                                $('#id_customer').val("").trigger("change.select2");
                                $('#id_pic').val("").trigger("change.select2");
                                $("#count_price_null").load(window.location.href +
                                    " #count_price_null");
                                $("#count_prioritas").load(window.location.href +
                                    " #count_prioritas");
                                Swal.fire({
                                    title: 'SUCCESS!',
                                    text: "Order berhasil ditambah",
                                    icon: 'success',
                                }).then(() => {
                                    $('#process-form-order').attr('disabled', false);
                                })
                            },
                            error: function(data) {
                                console.log('Error:', data);
                                $('#process-form-order').attr('disabled', false);
                                $('#process-form-order').html('Error');
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Failed!',
                            text: "Lengkapi kolom yang wajib diisi",
                            icon: 'error',
                        }).then(() => {
                            $('#process-form-order').attr('disabled', false);
                            if($('#nama_produk').val() == '') {
                                $('#nama_produk').addClass('is-invalid');
                                $('#nama_produk').after('<span class="invalid-feedback">Nama Produk wajib diisi</span>');
                                return false;
                            }
                            // if (!status_flag) {
                            //     error_add_flag_dummy.style.display = 'block'
                            //     return document.getElementById("error_add_flag_dummy").innerHTML = "Tipe Order harus dipilih!";
                            // }
                        });
                    }
                });
            }
        });

        //modal-edit-order
        $('body').on('click', '.edit-order', function(event) {
            event.preventDefault();
            var edit_error_text = document.getElementById('error_edit_flag_dummy');
            edit_error_text.style.display = 'none';

            var id_order = $(this).data('id');
            $.get("{{ url('crm/show_order/') }}/" + id_order, function(data) {
                //show main kode order only for 2nd, more produk
                // $('.kode_order_group_edit').hide();
                // $('#produk_ke_edit').on('change', function(){
                //     if($('#produk_ke_edit').val() != '1'){
                //         $('.kode_order_group_edit').show();
                //     }else{
                //         $('.kode_order_group_edit').hide();
                //     }
                // })

                $('#modal_edit_order').modal('show');
                // $('<a href="{!! url("crm/generate_offering_letter/'+data[0].sko_key+'") !!}"  class="btn btn-my-primary text-white">Download</a>')
                //     .appendTo($('.download_surat_penawaran'));
                if (data[0].invoice_count < 1) {
                    $('<a href="{!! url("invoice/create/?type=sales&sko_key='+data[0].sko_key+'") !!}"  class="btn btn-info text-white">Buat Invoice</a>')
                    .appendTo($('.download_surat_penawaran'));
                } 
                $('#id_order_edit').val(data[0].id_order);
                $('#order_key_edit').val(data[0].order_key);
                $('#id_cust_edit').val(data[0].id_customer);
                $('#id_customer_order_edit').val(data[0].nama);
                $('#kode_order_edit').val(data[0].kode_order);
                if (data[0].kode_order != null) {
                    $('#kode_order_edit').prop('readonly', true);
                    $('#kode_order_edit').addClass('form-select-solid', true);
                } else {
                    $('#kode_order_edit').prop('readonly', false);
                    $('#kode_order_edit').removeClass('form-select-solid', true);
                }
                $('#sko_edit').val(data[0].sko);
                $('#produk_ke_edit').find('option[value="' + data[0].produk_ke + '"]').prop(
                    'selected', true);
                $('#sumber_edit').val(data[0].sumber);
                // $('#waktu_kontak_edit').val(data[0].waktu_kontak);moment(waktu_kontak, 'YYYY-MM-DD').format('MMMM')
                $('#waktu_kontak_edit').val(moment(data[0].waktu_kontak).format(
                    'DD/MM/YYYY HH:mm'));
                $('#tgl_order_edit').val(data[0].tgl_order);
                $('#tipe_kontak_edit').val(data[0].tipe_kontak);
                $('#id_pic_edit').val(data[0].id_pic);
                $('#status_deal_edit').find('option[value="' + data[0].status_deal + '"]').prop(
                    'selected',
                    true);
                $("#flag_lunas_edit[value='" + data[0].flag_lunas + "']").prop('checked', true);
                if (data[0].follow_up_terakhir) {
                    $('#follow_up_terakhir_edit').val(data[0].follow_up_terakhir);
                }
                if (data[0].status_deal == 'Follow Up') {
                    $('#status_order1_edit').show();
                    $('#status_order1_edit').find('option[value="' + data[0].status_order +
                        '"]').prop('selected',
                        true);
                    $('#status_order2_edit').hide();
                    $('#status_order3_edit').hide();
                    $('#tanggal_order_edit').hide();
                    $('#section_dp_edit').hide();
                    $('#follow_up_terakhir').hide();
                } else if (data[0].status_deal == 'Deal') {
                    $('#status_order1_edit').hide();
                    $('#status_order2_edit').show();
                    $('#status_order2_edit').find('option[value="' + data[0].status_order +
                        '"]').prop('selected',
                        true);
                    $('#status_order3_edit').hide();
                    $('#tanggal_order_edit').show();
                    $('#section_dp_edit').show();
                    $('#follow_up_terakhir').hide();
                } else if (data[0].status_deal == 'Lost') {
                    $('#status_order1_edit').hide();
                    $('#status_order2_edit').hide();
                    $('#status_order3_edit').show();
                    $('#status_order3_edit').find('option[value="' + data[0].status_order +
                        '"]').prop('selected',
                        true);
                    $('#tanggal_order_edit').hide();
                    $('#section_dp_edit').hide();
                    $('#follow_up_terakhir').show();
                }
                $('#catatan_order_edit').val(data[0].catatan_order);
                $('#no_po_edit').val(data[0].no_po);
                $('#link_dokumen_order_edit').val(data[0].link_dokumen_order);
                // if (data[0].flag_lunas) {
                //     $('#section_pelunasan_edit').attr('disabled', false);
                //     $('#section_pelunasan_edit').val(data[0].tgl_pelunasan);
                // } else {
                //     $('#section_pelunasan_edit').attr('disabled', true);
                // }
                $("#survey_order_edit[value='" + data[0].survey_order + "']").prop('checked', true);
                $("#forecast_edit[value='" + data[0].is_forecast + "']").prop('checked', true);
                $("#pain_pont1_edit[value='" + data[0].pp_harga + "']").prop('checked', true);
                $("#pain_pont2_edit[value='" + data[0].pp_waktu + "']").prop('checked', true);
                $("#pain_pont3_edit[value='" + data[0].pp_pengiriman + "']").prop('checked', true);
                $("#pain_pont4_edit[value='" + data[0].pp_kualitas + "']").prop('checked', true);
                $("#pain_pont5_edit[value='" + data[0].pp_pembayaran + "']").prop('checked', true);
                $("#flag_dummy_d_edit[value='" + data[0].flag_dummy + "']").prop('checked', true);
                $("#flag_dummy_s_edit[value='" + data[0].flag_dummy + "']").prop('checked', true);
                $("#flag_dummy_r_edit[value='" + data[0].flag_dummy + "']").prop('checked', true);
                $("#flag_dummy_f_edit[value='" + data[0].flag_dummy + "']").prop('checked', true);
                $("#flag_dummy_j_edit[value='" + data[0].flag_dummy + "']").prop('checked', true);
                $("#flag_dummy_n_edit[value='" + data[0].flag_dummy + "']").prop('checked', true);
                if (data[0].is_forecast == true) {
                    $('#div-forecast-edit').removeClass('d-none');
                    $('#forecast-month-edit').find('option[value="' + data[0].forecast_month +
                        '"]').prop('selected',
                        true);
                    $('#forecast-year-edit').find('option[value="' + data[0].forecast_year +
                    '"]').prop('selected',
                    true);
                    $('#forecast-week-edit').find('option[value="' + data[0].forecast_week +
                    '"]').prop('selected',
                    true);
                    $('#expected-revenue-edit').val(data[0].expected_revenue).simpleMoneyFormat();
                }
            })
        });

        $('#jenis_kertas_lainnya_edit').hide();
        $('#gramasi_lainnya_edit').hide();
        $('#finishing_lainnya_edit').hide();

        //waktu_kontak_add_order
        $(function() {
            picker_edit = new tempusDominus.TempusDominus(document.getElementById(
                'waktu_kontak_edit'), {
                display: {
                    sideBySide: true,
                    calendarWeeks: false,
                    viewMode: 'calendar',
                    toolbarPlacement: 'top',
                    components: {
                        calendar: true,
                        date: true,
                        month: true,
                        year: true,
                        decades: true,
                        clock: true,
                        hours: true,
                        minutes: true,
                        seconds: false,
                        useTwentyfourHour: true
                    },
                    theme: 'light'
                }
            })
            picker_edit.dates.formatInput = date => moment(date).format('DD/MM/YYYY HH:mm')
            picker_edit.dates.setValue();
        });

        //modal-edit-produk
        $('body').on('click', '.edit-produk', function(event) {
            $('#ppn_form_edit').hide();
            event.preventDefault();
            var key = $(this).data('key');
            $.get("{{ url('crm/show_produk/') }}/" + key, function(data) {
                if (data[0].ppn == 1) {
                    console.log('adsas');
                    $("#ppn_edit").prop('checked', true);
                    $('#ppn_form_edit').show();
                    $('#ppn_percent_edit').val(data[0].ppn_percent);
                    $('#total_ppn_edit').val(data[0].total_ppn);
                }
                //autocalctotal
                $('#harga_produk_edit, #jumlah_produk_edit').on("keyup change", function() {
                    $('#total_harga_edit').simpleMoneyFormat();
                    jumlah_produk = $("#jumlah_produk_edit").val();
                    harga_produk = $("#harga_produk_edit").val().replace(
                        /[A-Za-z$. ,-]/g, "");
                    $('#total_harga_edit').val(jumlah_produk * harga_produk)
                        .simpleMoneyFormat();
                });

                var jenis_kertas_arr = Array('Art Paper',
                    'Art Carton',
                    'Ivory',
                    'Duplex',
                    'Brown Kraft Liner',
                    'Kraft PE',
                    'Ivory Food Grade',
                    'E Flute',
                    'B Flute',
                    'C Flute',
                    'CB Flute',
                    'Lainnya');
                var gramasi_arr = Array('K125/M125/M125 (SF COKLAT)',
                    'K125/M125/K125 (COKLAT-COKLAT)',
                    'K150/M125/K150 (COKLAT-COKLAT)',
                    'WK140/M125/M125 (SF PUTIH)',
                    'WK140/M1215/K125 (PUTIH-COKLAT)',
                    'WK140/M125/K150 (PUTIH-COKLAT)',
                    'WK140/M125/WK140 (PUTIH-PUTIH)',
                    'Lainnya');

                $('#modal_edit_produk').modal('show');
                $('#id_produksi_edit').val(data[0].id_produksi);
                $('#kategori_produksi_edit').find('option[value="' + data[0].kategori_produksi +
                    '"]').prop('selected', true);
                $('#jenis_bahan_edit').find('option[value="' + data[0].jenis_bahan + '"]').prop(
                    'selected', true);
                $('#dp_panjang_edit').val(data[0].dp_panjang);
                $('#dp_lebar_edit').val(data[0].dp_lebar);
                $('#dp_tinggi_edit').val(data[0].dp_tinggi);
                $('#lp_panjang_edit').val(data[0].lp_panjang);
                $('#lp_lebar_edit').val(data[0].lp_lebar);
                if (jenis_kertas_arr.includes(data[0].jenis_kertas)) {
                    $('#jenis_kertas_lainnya_edit').hide();
                    $('#jenis_kertas_edit').find('option[value="' + data[0].jenis_kertas + '"]')
                        .prop('selected', true);
                } else {
                    $('#jenis_kertas_lainnya_edit').show();
                    $('#jenis_kertas_lainnya_edit').val(data[0].jenis_kertas);
                    $('#jenis_kertas_edit').find('option[value="Lainnya"]').prop('selected',
                        true);
                }
                if (gramasi_arr.includes(data[0].gramasi)) {
                    $('#gramasi_lainnya_edit').hide();
                    $('#gramasi_edit').find('option[value="' + data[0].gramasi + '"]').prop(
                        'selected', true);
                } else {
                    $('#gramasi_lainnya_edit').show();
                    $('#gramasi_lainnya_edit').val(data[0].gramasi);
                    $('#gramasi_edit').find('option[value="Lainnya"]').prop('selected', true);
                }
                $('#laminasi_edit').find('option[value="' + data[0].laminasi + '"]').prop(
                    'selected', true);
                $('#sisi_laminasi_edit').find('option[value="' + data[0].sisi_laminasi + '"]')
                    .prop('selected', true);
                $('#tipe_produk_edit').find('option[value="' + data[0].tipe_produk + '"]').prop(
                    'selected', true);
                $('#isi_kertas_edit').find('option[value="' + data[0].isi_kertas + '"]').prop(
                    'selected', true);
                $('#get_plano_edit').val(data[0].get_plano);
                $('#jumlah_produk_edit').val(data[0].jumlah_produk);
                $('#harga_produk_edit').val(data[0].harga_produk).simpleMoneyFormat();
                $('#total_harga_edit').val(data[0].total_harga).simpleMoneyFormat();
                $('#modal_sales_edit').val(data[0].modal_sales).simpleMoneyFormat();
                $("#hitung_harga_khusus_edit[value='" + data[0].hitung_harga_khusus + "']")
                    .prop('checked', true);
                $("#kendala_produksi_edit[value='" + data[0].kendala_produksi + "']").prop(
                    'checked', true);
                $('#notes_edit').val(data[0].notes);
                $('#nama_produk_edit').val(data[0].nama_produk);
                $.each(data.selesai, function(i, val) {
                    $("input[value='" + val + "']").prop('checked', true);
                    if (data.selesai == 'Lainnya') {
                        $('#finishing_lainnya_edit').show();
                        $('#finishing_lainnya_edit').val(data.selesai);
                    } else {
                        $('#finishing_lainnya_edit').hide();
                    }
                });
            })
        });

        $('#process-form-edit-order').click(function(e) {
            e.preventDefault();
            var flag_dummy_edit_value = document.getElementsByName('flag_dummy_edit')
            var error = document.getElementsByName('error_edit_flag_dummy')
            var status_flag_edit = false
            for (var i = 0; i < flag_dummy_edit_value.length; i++) {
                if (flag_dummy_edit_value[i].checked){
                    status_flag_edit = true
                }
            }
            if (validator_edit_order) {
                validator_edit_order.validate().then(function(status) {
                    if (status == 'Valid' && status_flag_edit) {
                        $.ajax({
                            data: $('#edit-order').serialize(),
                            url: "{{ route('crm.edit_order') }}",
                            type: "POST",
                            dataType: 'json',
                            success: function(data) {
                                error_edit_flag_dummy.style.display = 'none'
                                $('#edit-order').trigger("reset");
                                setTimeout(function() {
                                    self.$("#close-form-edit-order")
                                        .trigger("click");
                                }, 1200);
                                window.dt_order.ajax.reload();
                                $('.download_surat_penawaran').html("");
                                $("#count_price_null").load(window.location.href + " #count_price_null");
                                $("#count_prioritas").load(window.location.href + " #count_prioritas");
                                Swal.fire({
                                    title: 'SUCCESS!',
                                    text: "Order berhasil diedit/update",
                                    icon: 'success',
                                })
                            },
                            error: function(data) {
                                console.log('Error:', data);
                                $('#process-form-edit-order').html('Error');
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Failed!',
                            text: "Lengkapi kolom yang wajib diisi",
                            icon: 'error',
                        })
                        if (!status_flag_edit) {
                            error_edit_flag_dummy.style.display = 'block'
                            return document.getElementById("error_edit_flag_dummy").innerHTML = "Tipe Order harus dipilih!";
                        }

                    }
                });
            }
        });

        $('#process-form-edit-produk').click(function(e) {
            e.preventDefault();
            if($('#nama_produk_edit').val() == '') {
                $('#nama_produk_edit').addClass('is-invalid');
                $('#nama_produk_edit').after('<span class="invalid-feedback">Nama Produk wajib diisi</span>');
                return false;
            }
            e.preventDefault();
            if (validator_edit_produk) {
                validator_edit_produk.validate().then(function(status) {
                    if (status == 'Valid') {
                        $.ajax({
                            data: $('#edit-produk').serialize(),
                            url: "{{ route('crm.edit_produk') }}",
                            type: "POST",
                            dataType: 'json',
                            success: function(data) {
                                $('#edit-produk').trigger("reset");
                                setTimeout(function() {
                                    self.$("#close-form-edit-produk").trigger("click");
                                }, 1200);
                                window.dt_order.ajax.reload();
                                $("#count_price_null").load(window.location.href +
                                    " #count_price_null");
                                $("#count_prioritas").load(window.location.href + " #count_prioritas");
                                Swal.fire({
                                    title: 'SUCCESS!',
                                    text: "Order berhasil diedit/update",
                                    icon: 'success',
                                })
                            },
                            error: function(data) {
                                console.log('Error:', data);
                                $('#process-form-edit-produk').html('Error');
                            }
                        });
                    } else {
                        Swal.fire({
                            title: 'Failed!',
                            text: "Lengkapi kolom yang wajib diisi",
                            icon: 'error',
                        })
                    }
                });
            }
        });

        $('.modal').on('hidden.bs.modal', function() {
            $(this).find('form').trigger('reset');
            $('.download_surat_penawaran').html("");
        })
    });

    function deleteConfirmation(id) {
        swal.fire({
            title: "Hapus ?",
            text: "Harap pastikan",
            icon: "warning",
            showCancelButton: !0,
            confirmButtonText: "Ya, Hapus!",
            cancelButtonText: "Tidak, batal!",
            reverseButtons: !0,
            buttonsStyling: false,
            customClass: {
                confirmButton: 'swal-button',
                cancelButton: 'swal-button cancel', // Apply the custom CSS class to the confirm button
            },
        }).then(function(e) {
            if (e.value === true) {
                // var token = $("meta[name='csrf-token']").attr("content");
                // var id = $('.delete-link').data('key');
                $.ajax({
                    url: "{{ route('crm.delete_order', '') }}" + '/' + id,
                    type: 'DELETE',
                    data: {
                        "id": id,
                        // "_token": token,
                    },
                    success: function(data) {
                        if (data) {
                            window.dt_order.ajax.reload();
                            $("#count_price_null").load(window.location.href +
                                " #count_price_null");
                            $("#count_prioritas").load(window.location.href + " #count_prioritas");
                            Swal.fire({
                                title: 'SUCCESS!',
                                text: "grading berhasil dihapus",
                                icon: 'success',
                            })
                        } else {
                            Swal.fire({
                                title: 'FAILED!',
                                text: "grading gagal dihapus",
                                icon: 'error',
                            })
                        }
                    }
                });

            } else {
                e.dismiss;
            }
        }, function(dismiss) {
            return false;
        })
    };

    //select2 cust page order
    $(document).ready(function() {

        const format = (item) => {
            if (!item.id) {
                return item.text;
            }
            var img =
                "<span class='menu-icon me-3'><svg xmlns='http://www.w3.org/2000/svg' width='26' height='26' viewBox='0 0 24 24' fill='none'><path opacity='0.3' d='M3 6C2.4 6 2 5.6 2 5V3C2 2.4 2.4 2 3 2H5C5.6 2 6 2.4 6 3C6 3.6 5.6 4 5 4H4V5C4 5.6 3.6 6 3 6ZM22 5V3C22 2.4 21.6 2 21 2H19C18.4 2 18 2.4 18 3C18 3.6 18.4 4 19 4H20V5C20 5.6 20.4 6 21 6C21.6 6 22 5.6 22 5ZM6 21C6 20.4 5.6 20 5 20H4V19C4 18.4 3.6 18 3 18C2.4 18 2 18.4 2 19V21C2 21.6 2.4 22 3 22H5C5.6 22 6 21.6 6 21ZM22 21V19C22 18.4 21.6 18 21 18C20.4 18 20 18.4 20 19V20H19C18.4 20 18 20.4 18 21C18 21.6 18.4 22 19 22H21C21.6 22 22 21.6 22 21ZM16 11V9C16 6.8 14.2 5 12 5C9.8 5 8 6.8 8 9V11C7.2 11 6.5 11.7 6.5 12.5C6.5 13.3 7.2 14 8 14V15C8 17.2 9.8 19 12 19C14.2 19 16 17.2 16 15V14C16.8 14 17.5 13.3 17.5 12.5C17.5 11.7 16.8 11 16 11ZM13.4 15C13.7 15 14 15.3 13.9 15.6C13.6 16.4 12.9 17 12 17C11.1 17 10.4 16.5 10.1 15.7C10 15.4 10.2 15 10.6 15H13.4Z' fill='black'/><path d='M9.2 12.9C9.1 12.8 9.10001 12.7 9.10001 12.6C9.00001 12.2 9.3 11.7 9.7 11.6C10.1 11.5 10.6 11.8 10.7 12.2C10.7 12.3 10.7 12.4 10.7 12.5L9.2 12.9ZM14.8 12.9C14.9 12.8 14.9 12.7 14.9 12.6C15 12.2 14.7 11.7 14.3 11.6C13.9 11.5 13.4 11.8 13.3 12.2C13.3 12.3 13.3 12.4 13.3 12.5L14.8 12.9ZM16 7.29998C16.3 6.99998 16.5 6.69998 16.7 6.29998C16.3 6.29998 15.8 6.30001 15.4 6.20001C15 6.10001 14.7 5.90001 14.4 5.70001C13.8 5.20001 13 5.00002 12.2 4.90002C9.9 4.80002 8.10001 6.79997 8.10001 9.09997V11.4C8.90001 10.7 9.40001 9.8 9.60001 9C11 9.1 13.4 8.69998 14.5 8.29998C14.7 9.39998 15.3 10.5 16.1 11.4V9C16.1 8.5 16 8 15.8 7.5C15.8 7.5 15.9 7.39998 16 7.29998Z' fill='black'/></svg></span>"
            var span = $("<span>", {
                text: " " + item.text
            });
            span.prepend(img)
            return span
        }
        $('#id_customer').select2({
                allowClear: true,
                debug: true,
                dropdownParent: $('#modal_add_order .modal-content'),
                ajax: {
                    // url: baseUrl + 'customer/getCustomer',
                    url: "{{ route('crm.get_customer') }}",
                    processResults: function(data) {
                        var resultsData = []

                        $.each(data, function(index, item) {
                            resultsData.push({
                                id: item.id_customer,
                                text: item.nama
                            })
                        })

                        return {
                            results: resultsData
                        };
                    },
                    // cache: true
                },
                templateResult: function(item) {
                    return format(item)
                }
            })
            .on('select2:opening', function(e) {
                $(this).data('select2').$dropdown.find(':input.select2-search__field').attr('placeholder',
                    'Cari Nama Customer')
            })
    })

    //select2 pic page order
    $(document).ready(function() {

        const format = (item) => {
            if (!item.id) {
                return item.text;
            }
            var img =
                "<span class='menu-icon me-3'><svg xmlns='http://www.w3.org/2000/svg' width='26' height='26' viewBox='0 0 24 24' fill='none'><path opacity='0.3' d='M3 6C2.4 6 2 5.6 2 5V3C2 2.4 2.4 2 3 2H5C5.6 2 6 2.4 6 3C6 3.6 5.6 4 5 4H4V5C4 5.6 3.6 6 3 6ZM22 5V3C22 2.4 21.6 2 21 2H19C18.4 2 18 2.4 18 3C18 3.6 18.4 4 19 4H20V5C20 5.6 20.4 6 21 6C21.6 6 22 5.6 22 5ZM6 21C6 20.4 5.6 20 5 20H4V19C4 18.4 3.6 18 3 18C2.4 18 2 18.4 2 19V21C2 21.6 2.4 22 3 22H5C5.6 22 6 21.6 6 21ZM22 21V19C22 18.4 21.6 18 21 18C20.4 18 20 18.4 20 19V20H19C18.4 20 18 20.4 18 21C18 21.6 18.4 22 19 22H21C21.6 22 22 21.6 22 21ZM16 11V9C16 6.8 14.2 5 12 5C9.8 5 8 6.8 8 9V11C7.2 11 6.5 11.7 6.5 12.5C6.5 13.3 7.2 14 8 14V15C8 17.2 9.8 19 12 19C14.2 19 16 17.2 16 15V14C16.8 14 17.5 13.3 17.5 12.5C17.5 11.7 16.8 11 16 11ZM13.4 15C13.7 15 14 15.3 13.9 15.6C13.6 16.4 12.9 17 12 17C11.1 17 10.4 16.5 10.1 15.7C10 15.4 10.2 15 10.6 15H13.4Z' fill='black'/><path d='M9.2 12.9C9.1 12.8 9.10001 12.7 9.10001 12.6C9.00001 12.2 9.3 11.7 9.7 11.6C10.1 11.5 10.6 11.8 10.7 12.2C10.7 12.3 10.7 12.4 10.7 12.5L9.2 12.9ZM14.8 12.9C14.9 12.8 14.9 12.7 14.9 12.6C15 12.2 14.7 11.7 14.3 11.6C13.9 11.5 13.4 11.8 13.3 12.2C13.3 12.3 13.3 12.4 13.3 12.5L14.8 12.9ZM16 7.29998C16.3 6.99998 16.5 6.69998 16.7 6.29998C16.3 6.29998 15.8 6.30001 15.4 6.20001C15 6.10001 14.7 5.90001 14.4 5.70001C13.8 5.20001 13 5.00002 12.2 4.90002C9.9 4.80002 8.10001 6.79997 8.10001 9.09997V11.4C8.90001 10.7 9.40001 9.8 9.60001 9C11 9.1 13.4 8.69998 14.5 8.29998C14.7 9.39998 15.3 10.5 16.1 11.4V9C16.1 8.5 16 8 15.8 7.5C15.8 7.5 15.9 7.39998 16 7.29998Z' fill='black'/></svg></span>"
            var span = $("<span>", {
                text: " " + item.text
            });
            span.prepend(img)
            return span
        }
        $('#id_pic').select2({
            allowClear: true,
            debug: true,
            dropdownParent: $('#modal_add_order .modal-content'),
            ajax: {
                // url: baseUrl + 'customer/getCustomer',
                url: "{{ route('crm.get_pic') }}",
                processResults: function(data) {
                    var resultsData = []

                    $.each(data, function(index, item) {
                        resultsData.push({
                            id: item.id,
                            text: item.name
                        })
                    })

                    return {
                        results: resultsData
                    };
                },
                // cache: true
            },
            templateResult: function(item) {
                return format(item)
            }
        })
        .on('select2:opening', function(e) {
            $(this).data('select2').$dropdown.find(':input.select2-search__field').attr('placeholder',
                'Cari Nama PIC')
        })
    })
</script>
