<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ url()->previous() }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">Edit Order</h3>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        @include('pages.crm.data_jscript_form_edit_order')
    </x-slot>

    <div class="container-xxl mt-20" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-header">
                <h2 class="card-title text-dark">#{{ $data->kode_order }} - {{ $data->nama }}</h2>
            </div>
            <div class="card-body pt-0">
                <form id="form-edit-order">
                    @csrf
                    @method('POST')
                    <input type="hidden" name="id_order" value="{{ $data->id_order }}" id="id_order_edit">
                    <input type="hidden" name="order_key" id="order_key_edit">
                    <input type="hidden" name="sko_key" id="sko_key_edit">
                    <input type="hidden" name="id_cust_edit" id="id_cust_edit"/>
                    <input type="hidden" name="kode_order" id="kode_order_edit"/>
                    <div class="row mt-10">
                        <div class="col-md-6">
                            <div class="mb-10">
                                <div class="row">
                                    <div class="col-md-8">
                                        <label for="sko" class="required form-label">Sub Kode Order</label>
                                        <input type="text" name="sko" id="sko_edit" readonly required
                                            class="form-control form-control-solid" />
                                    </div>
                                    <div class="col-md-4">
                                            <label for="produk_ke" class="required form-label">Produk ke-</label>
                                            <select name="produk_ke" id="produk_ke_edit" class="form-select form-select-solid">
                                                <option disabled>--------------------</option>
                                                <option value="1">1</option>
                                                <option value="2">2</option>
                                                <option value="3">3</option>
                                                <option value="4">4</option>
                                                <option value="5">5</option>
                                                <option value="6">6</option>
                                                <option value="7">7</option>
                                                <option value="8">8</option>
                                                <option value="9">9</option>
                                                <option value="10">10</option>
                                            </select>
                                    </div>
                                </div>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="sumber" class="required form-label">Sumber</label>
                                <select name="sumber" id="sumber_edit" class="form-select">
                                    <option value="" disabled>-- Pilih sumber</option>
                                    @foreach ($sumber as $sumber)
                                    <option value="{{ $sumber->sumber }}">{{ $sumber->sumber }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="waktu_kontak" class="required form-label">Waktu Kontak</label>
                                <div class="input-group mb-5" id="waktu_kontak_form_edit">
                                    <input type="text" class="form-control" name="waktu_kontak" id="waktu_kontak_edit" required/>
                                    <span class="input-group-text" id="basic-addon2">
                                        <i class="fa fa-calendar"></i>
                                    </span>
                                </div>
                            </div>

                            <div class="mb-10">
                                <label for="tipe_kontak" class="form-label required">Tipe Kontak</label>
                                <select name="tipe_kontak" id="tipe_kontak_edit" class="form-select" required>
                                    <option value="" disabled selected hidden>-- Pilih tipe kontak</option>
                                    <option value="Sampah">Sampah</option>
                                    <option value="Bukan Sampah">Bukan Sampah</option>
                                </select>
                            </div>
                            <div class="mb-10">
                                <label for="grading" class="form-label">Grading</label>
                                <select name="grading" id="grading_edit" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih grading</option>
                                    @foreach ($grading as $grading)
                                    <option value="{{ $grading->grading }}">{{ $grading->grading }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="fv-row mb-10">
                                <label for="id_pic" class="required form-label">PIC</label>
                                <select class="form-select " name="id_pic" id="id_pic_edit" data-control="select"
                                    data-placeholder="Nama PIC">
                                    <option disabled>--------------------</option>
                                    @foreach ($pic as $nama)
                                    <option value="{{ $nama->id }}">{{ $nama->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="fv-row mb-10">
                                        <label for="status_deal" class="required form-label">Status Deal</label>
                                        <select name="status_deal" id="status_deal_edit" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih status deal</option>
                                            <option value="Follow Up">Follow Up</option>
                                            <option value="Deal">Deal</option>
                                            <option value="Lost">Lost</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="fv-row mb-10">
                                        <label for="status_order" class="required form-label">Status Order</label>
                                        <select name="status_order1" id="status_order1_edit" class="form-select">
                                            <option value="" disabled hidden>-- Pilih status deal</option>
                                            @foreach ($follow_up as $follow_up)
                                            <option value="{{ $follow_up->follow_up }}">{{ $follow_up->follow_up }}</option>
                                            @endforeach
                                        </select>
                                        <select name="status_order2" id="status_order2_edit" class="form-select">
                                            <option value="" disabled hidden>-- Pilih status deal</option>
                                            <option value="Design">Design</option>
                                            <option value="FAW">FAW</option>
                                            <option value="Selesai Produksi">Selesai Produksi</option>
                                        </select>
                                        <select name="status_order3" id="status_order3_edit" class="form-select">
                                            <option value="" disabled hidden>-- Pilih status deal</option>
                                            @foreach ($lost as $lost)
                                            <option value="{{ $lost->lost }}">{{ $lost->lost }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10" id="follow_up_terakhir">
                                <label for="follow_up_terakhir_edit" class="form-label">Status Follow Up Terakhir</label>
                                <select name="follow_up_terakhir" id="follow_up_terakhir_edit" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih status follow up</option>
                                    @foreach ($follow_up2 as $follow_up)
                                    <option value="{{ $follow_up->follow_up }}">{{ $follow_up->follow_up }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="mb-10" id="tanggal_order_edit">
                                <label for="tgl_order" class="form-label">Tanggal Order</label>
                                <input type="date" name="tgl_order" id="tgl_order_edit" class="form-control" />
                            </div>
                            <div class="row" id="section_dp_edit">
                                <div class="col-md-12">
                                    <div class="mb-10">
                                        <label for="DP" class="form-label">DP</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp.</span>
                                            <input type="text" name="DP" id="DP_edit"
                                            class="form-control" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 d-none">
                                    <div class="mb-10 mt-5">
                                        <label for="flag_lunas" class="form-label"></label>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="pelunasan" name="flag_lunas" id="flag_lunas_edit">
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Pelunasan
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="catatan_order" class="form-label">Catatan Order</label>
                                <textarea name="catatan_order" id="catatan_order_edit" rows="6"
                                    class="form-control"></textarea>
                            </div>
                            <div class="mb-10 mt-5">
                                <label for="flag_dummy" class="form-label">Tipe Order</label>
                                <div class="row gap-5">
                                    <div class="col-12 row">
                                        <div class="col">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="flag_dummy"
                                                    value="Dummy" id="flag_dummy_d_edit">
                                                <label class="form-check-label" for="flag_dummy_d">
                                                    Dummy
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="flag_dummy"
                                                    value="Produksi Massal" id="flag_dummy_s_edit">
                                                <label class="form-check-label" for="flag_dummy_s">
                                                    Produksi Massal
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="flag_dummy"
                                                    value="Produksi Ulang" id="flag_dummy_r_edit">
                                                <label class="form-check-label" for="flag_dummy_r">
                                                    Produksi Ulang
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 row">
                                        <div class="col-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="flag_dummy"
                                                    value="FU/Lost" id="flag_dummy_f_edit">
                                                <label class="form-check-label" for="flag_dummy_f">
                                                    FU/Lost
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="flag_dummy"
                                                    value="Jasa Lainnya" id="flag_dummy_j_edit">
                                                <label class="form-check-label" for="flag_dummy_j">
                                                    Jasa Lainnya
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button form="form-edit-order" id="proccess-edit-order" class="btn my-primary text-white float-end">Edit Order</button>
                </form>
            </div>
        </div>
    </div>


</x-app-layout>
