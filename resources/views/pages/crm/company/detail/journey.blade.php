<div>
    <div class="d-flex justify-content-end mb-4">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
            data-bs-target="#modal-add-note" title="Add Notes">
            <span class="svg-icon svg-icon-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    fill="none">
                    <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1"
                        transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                    <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="black"></rect>
                </svg>
            </span>
            Add Note</button>
    </div>
    @if (count($data) > 0)
        @foreach ($data as $dt)
            <div class="card border rounded-md bg-light mb-4">
                <div class="card-body">
                <blockquote class="blockquote mb-0">
                    <div class="d-flex justify-content-between align-items-top mb-5">
                        <div>{{$dt->message ?? ''}}</div>
                        <div>
                            <span class="fa fa-clock text-my-primary fs-5"></span> 
                            <span class="text-my-primary fs-5">{{date('d M Y H:i', strtotime($dt->updated_at))}}</span>
                        </div>
                    </div>
                    <footer class="blockquote-footer">{{$dt->user->name ?? ''}} as <cite title="Source Title">{{$dt->user->roles ?? ''}}</cite></footer>
                </blockquote>
                </div>
            </div>
        @endforeach
    @else
        <span>Belum ada tambahan catatan</span>
    @endif
</div>

<div class="modal fade" id="modal-add-note">
    <div class="modal-dialog modal-dialog-centered modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Note</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary" id="modal-add-note-close"
                    data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                </div>
            </div>
            <div class="modal-body">
                <form action="" id="add-note">
                <input type="hidden" name="company_id" id="company_id" value="{{$company_id??null}}">
                <div class="fv-row mb-2">
                    <label for="company_id" class="required form-label">Catatan</label>
                    <textarea class="form-control" name="message"></textarea>
                </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="add-note-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                <button type="button" id="submit-add-note" class="btn btn-primary my-primary">Save
                    New Note</button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        //CSRF cust page
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        $('#submit-add-note').click(function(e) {
            e.preventDefault();
            $.ajax({
                data: $('#add-note').serialize(),
                url: "{{ route('company.add_note') }}",
                type: "POST",
                dataType: 'json',
                success: function(data) {
                    $('#add-note').trigger("reset");
                    Swal.fire({
                        title: 'SUCCESS!',
                        text: "Berhasil tambahkan catatan",
                        icon: 'success',
                    })
                    setTimeout(function() {
                        self.$("#add-note-close").trigger("click");
                        self.$('.nav-tabs .nav-link:first').trigger('click');
                    }, 600);
                    
                },
                error: function(data) {
                    console.log('Error:', data);
                    Swal.fire({
                        title: 'FAILED!',
                        text: "Mohon maaf gagal menambahkan catatan",
                        icon: 'error',
                    })
                    $('#submit-add-note').html('Error');
                }
            });
        });
    })
    
</script>