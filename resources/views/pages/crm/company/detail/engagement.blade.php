<div>
    <div class="d-flex justify-content-end mb-4 d-none">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
            data-bs-target="#modal-add-engagement" title="Add Engagement">
            <span class="svg-icon svg-icon-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    fill="none">
                    <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1"
                        transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                    <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="black"></rect>
                </svg>
            </span>
            Add Engagement</button>
    </div>
    <div>
        <table class="table my-5">
            <thead class="thead-dark border bg-secondary">
                <th width="3%" class="text-center">No</th>
                <th width="7%" class="text-center">PIC</th>
                <th width="15%">Summary</th>
                <th>Detail</th>
                <th width="10%" class="text-center">Type</th>
                <th width="10%" class="text-center">Create Date</th>
                <th width="10%" class="text-center">Due Date</th>
            </thead>
            <tbody class="border">
                @php
                    $no = 1;
                @endphp
                @if (count($data) > 0)
                    @foreach ($data as $engagement)
                        <tr>
                            <td class="text-center">{{$no}}</td>
                            <td>{{$engagement->user->name ?? ''}}</td>
                            <td>{{$engagement->title ?? ''}}</td>
                            <td>{{$engagement->detail ?? ''}}</td>
                            <td class="text-center">{{typeEngagement($engagement->type) ?? ''}}</td>
                            <td class="text-center">{{date('d-M-Y', strtotime($engagement->created_at)) ?? ''}}</td>
                            <td class="text-center">{{date('d-M-Y', strtotime($engagement->due_date)) ?? ''}}</td>
                        </tr>
                        @php
                            $no++;
                        @endphp
                    @endforeach
                @else
                    <tr>
                        <td colspan="7" class="text-center">Belum ada agenda</td>
                    </tr>
                @endif
            </tbody>
        </table>
    </div>
</div>

<div class="modal fade" id="modal-add-engagement">
    <div class="modal-dialog modal-dialog-centered modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Engagement</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary" id="modal-add-engagement-close"
                    data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                </div>
            </div>
            <div class="modal-body">
                <form action="" id="add-engagement">
                <input type="hidden" name="company_id" id="company_id" value="{{$company_id??null}}">
                <div class="fv-row mb-2">
                    <label for="type" class="required form-label">Tipe</label>
                    <select class="form-select" name="type">
                        @if (count(typeEngagement()) > 0)
                            @foreach (typeEngagement() as $key => $type)
                                <option value="{{$key}}">{{$type}}</option>
                            @endforeach
                        @endif
                    </select>
                </div>
                <div class="fv-row mb-2">
                    <label for="title" class="required form-label">Title</label>
                    <input class="form-control" name="title"/>
                </div>
                <div class="fv-row mb-2">
                    <label for="detail" class="required form-label">Detail</label>
                    <textarea class="form-control" name="detail"></textarea>
                </div>
                <div class="fv-row mb-2" id="due_date">
                    <label for="due_date" class="form-label">Due Date</label>
                    <input type="date" name="due_date" id="due_date" class="form-control" />
                </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="add-engagement-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                <button type="button" id="submit-add-engagement" class="btn btn-primary my-primary">Save
                    New Engagement</button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        //CSRF cust page
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        $('#submit-add-engagement').click(function(e) {
            e.preventDefault();
            $.ajax({
                data: $('#add-engagement').serialize(),
                url: "{{ route('company.add_engagement') }}",
                type: "POST",
                dataType: 'json',
                success: function(data) {
                    $('#add-engagement').trigger("reset");
                    Swal.fire({
                        title: 'SUCCESS!',
                        text: "Berhasil tambahkan catatan",
                        icon: 'success',
                    })
                    setTimeout(function() {
                        self.$("#add-engagement-close").trigger("click");
                        self.$('[data-tab="engagement#{{$company_id}}"]').trigger('click');
                    }, 600);
                    
                },
                error: function(data) {
                    console.log('Error:', data);
                    Swal.fire({
                        title: 'FAILED!',
                        text: "Mohon maaf gagal menambahkan catatan",
                        icon: 'error',
                    })
                    $('#submit-add-engagement').html('Error');
                }
            });
        });
    })
    
</script>