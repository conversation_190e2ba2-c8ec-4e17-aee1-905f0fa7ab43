<div>
    <div class="d-flex justify-content-end mb-4 d-none">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
            data-bs-target="#modal-add-pic-cust" title="Add Notes">
            <span class="svg-icon svg-icon-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    fill="none">
                    <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1"
                        transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                    <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="black"></rect>
                </svg>
            </span>
            Add PIC Customer</button>
    </div>
    <div>
        <table class="table my-5">
            <thead class="thead-dark border bg-secondary">
                <th width="3%" class="text-center"><b>No</b></th>
                <th><b>Name</b></th>
                {{-- <th width="20%" class="text-center">Bulan</th>
                <th width="20%" class="text-center">Tahun</th> --}}
                <th width="10%" class="text-center"><b>Total Order</b></th>
                <th width="20%" class="text-center"><b>Total Omzet</b></th>
                <th width="10%" class="text-center"><b>Aksi</b></th>
            </thead>
            <tbody class="border">
                @php
                    $no = 1;
                    $total_omzet = 0;
                    $total_order = 0;
                @endphp
                @if (count($data) > 0)
                    @foreach ($data as $key_cs => $customer)
                        @php
                            $total_omzet += $customer['sum_total_harga'] ?? 0;
                            $total_order += $customer['total_order'] ?? 0;
                        @endphp
                        <tr>
                            <td class="text-center">{{$no++}}</td>
                            <td>{{$key_cs ?? ''}}</td>
                            {{-- <td class="text-center"></td> --}}
                            {{-- <td class="text-center"></td> --}}
                            <td width="5%" class="text-center">{{$customer['total_order'] ?? 0}}</td>
                            <td class="text-center">Rp. {{number_format($customer['sum_total_harga'], 0, ',', '.')}}</td>
                            <td width="5%" class="text-center"> 
                                <a class="btn btn-sm btn-primary" data-bs-toggle="collapse" href="#collapseOrder-{{$customer['customer_id']}}" role="button" aria-expanded="false" aria-controls="collapseExample">
                                    <i class="fa fa-eye"></i> Detail
                                </a>
                            </td>
                        </tr>
                        <tr class="collapse" id="collapseOrder-{{$customer['customer_id']}}">
                            <td colspan="5">
                                <div>
                                    <div class="card card-body p-1">
                                        @if (count($customer['orders']) > 0)
                                            <table class="table table-striped">
                                                <thead class="border bg-secondary">
                                                    <tr>
                                                        <th width="5%" class="text-center"><b>No</b></th>
                                                        <th width="10%" class="text-center"><b>Tanggal Order</b></th>
                                                        <th>Kode Order</th>
                                                        <th>SKO</th>
                                                        <th width="10%" class="text-center"><b>Status Deal</b></th>
                                                        <th width="10%" class="text-center"><b>Status Order</b></th>
                                                        <th width="20%" class="text-center"><b>Total Harga</b></th>
                                                    </tr>
                                                </thead>
                                                <tbody class="border">
                                                    @foreach ($customer['orders'] as $key => $order)
                                                        <tr>
                                                            <td class="text-center">{{$key + 1}}</td>
                                                            <td class="text-center">{{\Carbon\Carbon::parse($order->tgl_order)->format('d-m-Y')}}</td>
                                                            <td class="text-left">{{$order->kode_order ?? '-'}}</td>
                                                            <td class="text-left">{{$order->sko ?? '-'}}</td>
                                                            <td class="text-center">{{$order->status_deal ?? '-'}}</td>
                                                            <td class="text-center">{{$order->status_order ?? '-'}}</td>
                                                            <td class="text-center">Rp. {{number_format($order->total_harga ?? 0, 0, ',', '.')}}</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        @else
                                            No orders found for this customer.
                                        @endif
                                    </div>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                    <tr>
                        <td colspan="2" class="text-center"><b>Total</b></td>
                        <td class="text-center"><b>{{$total_order ?? 0}}</b></td>
                        <td class="text-center"><b>Rp. {{number_format($total_omzet, 0, ',', '.')}}</b></td>
                        <td></td>
                    </tr>
                @else
                    <tr>
                        <td colspan="3">Belum memiliki PIC Customer</td>
                    </tr>
                @endif
            </tbody>
        </table>
    </div>
</div>