<div>
    <div class="d-flex justify-content-end mb-4">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal"
            data-bs-target="#modal-add-pic-cust" title="Add Notes">
            <span class="svg-icon svg-icon-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                    fill="none">
                    <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1"
                        transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                    <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="black"></rect>
                </svg>
            </span>
            Add PIC Customer</button>
    </div>
    <div>
        <table class="table my-5">
            <thead class="thead-dark border bg-secondary">
                <th width="3%" class="text-center">No</th>
                <th>Name</th>
                <th width="20%" class="text-center">Contact</th>
                <th width="20%" class="text-center">Action</th>
            </thead>
            <tbody class="border">
                @php
                    $no = 1;
                @endphp
                @if (count($data) > 0)
                    @foreach ($data as $dt)
                        <tr>
                            <td class="text-center">{{$no}}</td>
                            <td><a href="{{route('crm.detail_customer', $dt->kode_kustomer).'?ccode='.$dt->company->code??''}}" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">{{$dt->nama}}</a></td>
                            <td class="text-center">{{$dt->no_hp}}</td>
                            <td class="text-center">
                                <a href="{{url('/crm/detail_customer/'.$dt->kode_kustomer)}}">
                                    <button class="btn btn-sm btn-info" data-toggle="tooltip" data-placement="bottom" title="Riwayat Order"><i class="fa fa-eye"></i></button>
                                </a>
                                <button class="btn btn-sm btn-danger" id="btn-remove-{{$dt->id_customer}}" onclick="removeCust('{{$dt->id_customer}}')" type="button" data-toggle="tooltip" data-placement="bottom" title="Remove"><i class="fa fa-trash"></i></button>
                            </td>
                        </tr>
                        @php
                            $no++;
                        @endphp
                    @endforeach
                @else
                    <tr>
                        <td colspan="3">Belum memiliki PIC Customer</td>
                    </tr>
                @endif
            </tbody>
        </table>
    </div>
</div>
<div class="modal fade" id="modal-add-pic-cust">
    <div class="modal-dialog modal-dialog-centered modal-md">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add PIC Customer</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary" id="modal-add-pic-cust-close"
                    data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                </div>
            </div>
            <div class="modal-body">
                <form action="" id="add-pic-cust">
                <input type="hidden" name="company_id" id="company_id" value="{{$company_id??null}}">
                <div class="fv-row mb-2">
                    <label for="company_id" class="required form-label">Customer</label>
                    <select name="customer_id" id="customer_id" class="form-select">
                        <option value="" disabled selected hidden>-- Pilih Customer</option>
                    </select>
                </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" id="add-pic-cust-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                <button type="button" id="submit-add-pic-cust" class="btn btn-primary my-primary">Save</button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(document).ready(function() {
        $('[data-toggle="tooltip"]').tooltip()
        $('#customer_id').select2({
            allowClear: false,
            debug: true,
            dropdownParent: $('#add-pic-cust'),
            ajax: {
                url: "{{ route('company.get_customer') }}",
                processResults: function(data) {
                    var resultsData = []

                    $.each(data, function(index, item) {
                        resultsData.push({
                            id: item.id,
                            text: item.name
                        })
                    })

                    return {
                        results: resultsData
                    };
                },
                // cache: true
            }
        })

        //CSRF cust page
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });
        
        $('#submit-add-pic-cust').click(function(e) {
            e.preventDefault();
            $.ajax({
                data: $('#add-pic-cust').serialize(),
                url: "{{ route('company.add_pic_cust') }}",
                type: "POST",
                dataType: 'json',
                success: function(data) {
                    $('#add-pic-cust').trigger("reset");
                    Swal.fire({
                        title: 'SUCCESS!',
                        text: "Berhasil tambahkan PIC customer",
                        icon: 'success',
                    })
                    setTimeout(function() {
                        self.$("#add-pic-cust-close").trigger("click");
                        self.$('[data-tab="pic-customer#{{$company_id}}"]').trigger('click');
                    }, 600);
                    
                },
                error: function(data) {
                    console.log('Error:', data);
                    Swal.fire({
                        title: 'FAILED!',
                        text: "Mohon maaf gagal menambahkan PIC Customer",
                        icon: 'error',
                    })
                    $('#submit-add-pic-cust').html('Error');
                }
            });
        });

    });

    function removeCust(customer_id) {
        $("#btn-remove-"+customer_id).html('<div class="spinner-border text-light" role="status"><span class="sr-only">Loading...</span></div>')
        $("#btn-remove-"+customer_id).attr('disabled', true);
        $.ajax({
            data: {
                id_customer: customer_id
            },
            url: "{{ route('company.remove_pic_cust') }}",
            type: "POST",
            dataType: 'json',
            success: function(data) {
                Swal.fire({
                    title: 'SUCCESS!',
                    text: "Berhasil hapus PIC customer",
                    icon: 'success',
                })
                setTimeout(function() {
                    self.$('[data-tab="pic-customer#{{$company_id}}"]').trigger('click');
                }, 600);
                
            },
            error: function(data) {
                console.log('Error:', data);
                Swal.fire({
                    title: 'FAILED!',
                    text: "Mohon maaf gagal hapus PIC Customer",
                    icon: 'error',
                })
                $('#submit-add-pic-cust').html('Error');
            }
        });
    }
</script>
<style>
    .spinner-border {
        width: 1.2rem !important;
        height: 1.2rem !important;
    }
</style>