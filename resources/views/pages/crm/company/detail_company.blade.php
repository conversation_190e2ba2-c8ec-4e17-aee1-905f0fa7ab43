
<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">{{$data->name ?? ''}}</span>
        <div class="d-flex align-items-center mt-3"><a href="{{route('crm')}}" class="text-decoration-none pb-2"><small class="text-white fs-6 fw-normal me-2">CRM</small></a><small class="text-white fs-6 fw-normal"> / Detail Company</small></div>
    </x-slot>
    <x-slot name="script">
    </x-slot>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush">
            <div class="card-body pt-0">
                <div class="py-5 mt-4">
                    <table class="table table-striped border">
                        <tr>
                            <td width="10%" class="ps-3"><strong>ID</strong></td>
                            <td width="1%">:</td>
                            <td>{{$data->code ?? '-'}}</td>
                            <td width="10%" class="ps-3"><b>NPWP</b></td>
                            <td width="1%">:</td>
                            <td>{{$data->npwp ?? '-'}}</td>
                        </tr>
                        <tr>
                            <td width="10%" class="ps-3"><b>Bidang Usaha</b></td>
                            <td width="1%">:</td>
                            <td>{{$data->business_sector ?? '-'}}</td>
                            <td width="10%" class="ps-3"><b>Alamat</b></td>
                            <td width="1%">:</td>
                            <td>{{$data->address ?? '-'}}</td>
                        </tr>
                        <tr>
                            <td width="10%"></td>
                            <td width="1%"></td>
                            <td></td>
                            <td width="10%"></td>
                            <td width="1%"></td>
                            <td>{{$data->village->name ?? '-'}}, {{$data->subdistrict->name ?? '-'}}</td>
                        </tr>
                        <tr>
                            <td width="10%"></td>
                            <td width="1%"></td>
                            <td></td>
                            <td width="10%"></td>
                            <td width="1%"></td>
                            <td>{{$data->district->name ?? '-'}}, {{$data->province->name ?? '-'}}</td>
                        </tr>
                    </table>
                </div>
                <div class="d-grid my-3">
                    <ul class="nav nav-tabs text-nowrap flex-nowrap border rounded-md">
                        <li class="nav-item">
                            <a class="nav-link btn btn-active-dark btn-color-grey-600 active me-3"
                            data-bs-toggle="tab" href="#kt_tab_pane_1" data-tab="journey#{{$data->id}}">Journey Notes</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-active-dark btn-color-grey-600 me-3" data-bs-toggle="tab"
                            href="#kt_tab_pane_2" data-tab="pic-customer#{{$data->id}}">PIC Customer</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-active-dark btn-color-grey-600 me-3" data-bs-toggle="tab"
                            href="#kt_tab_pane_3" data-tab="forecast#{{$data->id}}">History Deal</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-active-dark btn-color-grey-600 me-3" data-bs-toggle="tab"
                            href="#kt_tab_pane_4" data-tab="engagement#{{$data->id}}">Engagement</a>
                        </li>
                    </ul>
                </div>
                <div class="tab-content" id="myTabContent">
                    <div class="tab-pane fade show active" id="kt_tab_pane_1" role="tabpanel"></div>
                    <div class="tab-pane fade" id="kt_tab_pane_2" role="tabpanel"></div>
                    <div class="tab-pane fade" id="kt_tab_pane_3" role="tabpanel"></div>
                    <div class="tab-pane fade" id="kt_tab_pane_4" role="tabpanel"></div>
                </div>
                <div id="content"></div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            $('.nav-tabs .nav-link').on('click', function(event) {
                var clickedNavLink = $(this);
                var tabData = clickedNavLink.data('tab'); 
                console.log("Clicked tab's data-tab value:", tabData);  

                const target = $('#content');
                target.html('<div class="spinner-grow text-dark" role="status"><span class="sr-only">Loading...</span></div>');
                $.ajax({
                    data: {
                        tab: tabData
                    },
                    url: "{{ route('company.detail') }}",
                    type: "GET",
                    success: function(data) {
                        target.html(data);
                    },
                    error: function(data) {
                    }
                });
            });

            $('.nav-tabs .nav-link:first').trigger('click');
            
            @if (request()->has('pic_customer'))
                $('.nav-tabs .nav-link').removeClass('active');
                $('.nav-tabs .nav-link:eq(1)').addClass('active');
                $('.nav-tabs .nav-link:eq(1)').trigger('click');
            @endif
        });
        
        
        function loadContentForTab(tabName) {
            console.log("Loading content for:", tabName);            
        }
    </script>
</x-app-layout>