
<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">{{$data->name ?? ''}}</span>
        <small class="text-white fs-6 fw-normal pt-2">Edit Company</small>
    </x-slot>
    <x-slot name="script">
    </x-slot>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush">
            <div class="card-body pt-0">
                <div class="">
                    <form id="edit-company">
                        <div class="row mt-10">
                            <div class="border-bottom mb-5 pb-2 d-flex justify-content-end">
                                <a href="{{route('crm')}}"><button type="button" id="close-form-company" class="btn btn-light me-3">Cancel</button></a>
                                <button type="button" id="process-form-company" class="btn btn-primary">Save</button>
                            </div>
                            <input name="id" value="{{$data->id}}" type="hidden"/>
                            <div class="col-md-6">
                                <div class="fv-row mb-10">
                                    <label for="nama" class="required form-label">Nama Instansi</label>
                                    <input type="text" name="name" id="name" value="{{$data->name ?? null}}" required class="form-control" placeholder="Nama Perusahaan"/>
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="tipe_instansi" class="required form-label">Tipe Instansi</label>
                                    <select name="instansi_id" id="instansi_id" class="form-select">
                                        <option value="" disabled selected hidden>-- Pilih Tipe Instansi</option>
                                        @foreach ($tipe_instansi as $ti)
                                        <option value="{{ $ti->id_instansi }}" {{$data->instansi_id == $ti->id_instansi ? 'selected' : ''}}>{{ $ti->tipe_instansi }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="jenis_usaha" class="required form-label">Bidang Usaha</label>
                                    <select name="business_sector" id="business_sector" class="form-select">
                                        <option value="" disabled selected hidden>-- Pilih Bidang Usaha</option>
                                        @foreach (business_sectors() as $key => $sector)
                                        <option value="{{ $key }}" {{$data->business_sector == $key ? 'selected' : ''}}>{{ $sector }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="email" class="form-label">Email Perusahaan</label>
                                    <input type="email" name="email" id="email" value="{{$data->email ?? null}}" class="form-control" placeholder="Email"/>
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="website" class="form-label">Website Perusahaan</label>
                                    <input type="text" name="website" id="website" value="{{$data->website ?? null}}" class="form-control" placeholder="website"/>
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="NPWP" class="form-label">NPWP Perusahaan</label>
                                    <input type="text" name="npwp" id="npwp" value="{{$data->npwp ?? null}}" class="form-control" placeholder="NPWP"/>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="fv-row mb-10">
                                    <label for="no_hp" class="form-label">Provinsi</label>
                                    @if ($data->province_id)
                                        <select name="province_id" id="province_id" class="form-select" data-control="select2">
                                            @foreach ($province as $pr)
                                                <option value="{{$pr->id}}" selected>{{$pr->name}}</option>
                                            @endforeach
                                        </select>
                                    @else
                                        <select name="province_id" id="province_id" class="form-select" data-control="select2">
                                            <option value="" disabled selected hidden>-- Pilih Provinsi</option>
                                        </select>
                                    @endif
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="no_hp" class="form-label">Kota/Kabupaten</label>
                                    @if ($data->district_id)
                                        <select name="district_id" id="district_id" class="form-select" data-control="select2">
                                            @foreach ($district as $dr)
                                                <option value="{{$dr->id}}" selected>{{$dr->name}}</option>
                                            @endforeach
                                        </select>
                                    @else
                                        <select name="district_id" id="district_id" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Kota/Kabupaten</option>
                                        </select>
                                    @endif
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="no_hp" class="form-label">Kecamatan</label>
                                    @if ($data->sub_district_id)
                                        <select name="sub_district_id" id="sub_district_id" class="form-select" data-control="select2">
                                            @foreach ($subdistrict as $sdr)
                                                <option value="{{$sdr->id}}" selected>{{$sdr->name}}</option>
                                            @endforeach
                                        </select>
                                    @else
                                        <select name="sub_district_id" id="sub_district_id" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Kecamatan</option>
                                        </select>
                                    @endif
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="no_hp" class="form-label">Kelurahan</label>
                                    @if ($data->village_id)
                                        <select name="village_id" id="village_id" class="form-select" data-control="select2">
                                            @foreach ($village as $v)
                                                <option value="{{$v->id}}" selected>{{$v->name}}</option>
                                            @endforeach
                                        </select>
                                    @else
                                        <select name="village_id" id="village_id" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Kelurahan</option>
                                        </select>
                                    @endif
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="address" class="form-label required">Alamat Instansi</label>
                                    <textarea name="address" id="address" rows="3" class="form-control" required placeholder="Alamat">{{$data->address ?? null}}</textarea>
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="post_code" class="form-label">Kode Pos</label>
                                    <input type="text" name="zip_code" id="zip_code" class="form-control" placeholder="Kode Pos" value="{{$data->zip_code ?? null}}"/>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script>
        // If Select2 needs jQuery passed explicitly (check its documentation):
        $(document).ready(function() {
            // $('.another-select').select2(); // Now '$' works within this scope
            $('#province_id').select2({
                allowClear: true,
                placeholder:"-- Pilih Provinsi",
                debug: true,
                dropdownParent: $('#edit-company'),
                ajax: {
                    url: "{{ route('crm.get_province') }}",
                    processResults: function(data) {
                        var resultsData = []
    
                        $.each(data, function(index, item) {
                            resultsData.push({
                                id: item.id,
                                text: item.name
                            })
                        })
    
                        return {
                            results: resultsData
                        };
                    },
                    // cache: true
                }
            })

            $('#province_id').on('change', function(){
                var province_id = $('#province_id').val();
                $('#district_id').select2({
                    placeholder:"-- Pilih Kota/Kabupaten",
                    debug: true,
                    dropdownParent: $('#edit-company'),
                    ajax: {
                        url: "{{ route('crm.get_district') }}",
                        data: function(params) {
                            var query = {
                                q: params.term,
                                type: 'public',
                                province_id: province_id, // Assuming you have an element with ID 'province_id'
                                raw: true // Add the 'raw' parameter
                            }

                            // Query parameters will be ?search=[term]&type=public&province_id=[value]&raw=true
                            return query;
                        },
                        processResults: function(data) {
                            var resultsData = []
        
                            $.each(data, function(index, item) {
                                resultsData.push({
                                    id: item.id,
                                    text: item.name
                                })
                            })
        
                            return {
                                results: resultsData
                            };
                        },
                        // cache: true
                    }
                })
            })

            $('#district_id').on('change', function(){
                var district_id = $('#district_id').val();
                if (district_id === null || district_id === 'undefined') {
                    Swal.fire({
                        title: 'Warning!',
                        text: "Pilih Kecamatan terlebih dahulu",
                        icon: 'info',
                    })
                    return false
                } else {
                    $('#sub_district_id').select2({
                        allowClear: true,
                        placeholder:"-- Pilih Kecamatan",
                        debug: true,
                        dropdownParent: $('#edit-company'),
                        ajax: {
                            url: "{{ route('crm.get_sub_district') }}",
                            data: function(params) {
                                var query = {
                                    q: params.term,
                                    type: 'public',
                                    district_id: district_id, // Assuming you have an element with ID 'province_id'
                                    raw: true // Add the 'raw' parameter
                                }

                                // Query parameters will be ?search=[term]&type=public&province_id=[value]&raw=true
                                return query;
                            },
                            processResults: function(data) {
                                var resultsData = []
        
                                $.each(data, function(index, item) {
                                    resultsData.push({
                                        id: item.id,
                                        text: item.name
                                    })
                                })
        
                                return {
                                    results: resultsData
                                };
                            },
                            cache: true
                        }
                    })
                }
            })

            $('#sub_district_id').on('change', function(){
                var sub_district_id = $('#sub_district_id').val();
                if (sub_district_id === null || sub_district_id === 'undefined') {
                    Swal.fire({
                        title: 'Warning!',
                        text: "Pilih Kecamatan terlebih dahulu",
                        icon: 'info',
                    })
                    return false
                } else {
                    $('#village_id').select2({
                        allowClear: true,
                        placeholder:"-- Pilih Kelurahan",
                        debug: true,
                        dropdownParent: $('#edit-company'),
                        ajax: {
                            url: "{{ route('crm.get_village') }}",
                            data: function(params) {
                                var query = {
                                    q: params.term,
                                    type: 'public',
                                    sub_district_id: sub_district_id, // Assuming you have an element with ID 'province_id'
                                    raw: true // Add the 'raw' parameter
                                }

                                // Query parameters will be ?search=[term]&type=public&province_id=[value]&raw=true
                                return query;
                            },
                            processResults: function(data) {
                                var resultsData = []
        
                                $.each(data, function(index, item) {
                                    resultsData.push({
                                        id: item.id,
                                        text: item.name
                                    })
                                })
        
                                return {
                                    results: resultsData
                                };
                            },
                            // cache: true
                        }
                    })
                }
            })

            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            $('#process-form-company').click(function (e) {
                e.preventDefault();
                swal.fire({
                    title: "Ubah Data Company ?",
                    text: "Harap pastikan",
                    icon: "warning",
                    showCancelButton: !0,
                    confirmButtonText: "Ya!",
                    cancelButtonText: "Tidak, batal!",
                    reverseButtons: !0
                }).then(function (e) {
                    if (e.value === true) {
                        var province_id = $('#province_id').val();
                        var district_id = $('#district_id').val();
                        var sub_district_id = $('#sub_district_id').val();
                        var village_id = $('#village_id').val();
                        $.ajax({
                            data: $('#edit-company').serialize()+"&province_id="+province_id+"&district_id="+district_id+"&village_id="+village_id,
                            url: "{{ route('company.store_company') }}",
                            type: "POST",
                            dataType: 'json',
                            success: function (data) {
                                $('#add-new-company').trigger("reset");
                                Swal.fire({
                                    title: 'SUCCESS!',
                                    text: "Perusahaan baru berhasil dibuat",
                                    icon: 'success',
                                })
                                window.location = "{{route('crm')}}";
                            },
                            error: function (data) {
                                $('#process-form-company').html('Save Changes');
                                Swal.fire({
                                    title: 'ERROR!',
                                    text: "Harap Lengkapi form yang ada",
                                    icon: 'error',
                                })
                            }
                        });
                    } else {
                        e.dismiss;
                    }
                }, function (dismiss) {
                    return false;
                })
            });
        });
    </script>
</x-app-layout>