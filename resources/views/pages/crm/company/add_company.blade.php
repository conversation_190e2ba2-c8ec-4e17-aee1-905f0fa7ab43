<div class="modal-dialog modal-dialog-centered modal-xl">
    <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title">Tambah Company Baru</h5>
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-company-close"
                data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
            </div>
        </div>
        <div class="modal-body mt-n5">
            <form id="add-new-company">
                <div class="row mt-10">
                    <div class="col-md-6">
                        <div class="fv-row mb-10">
                            <label for="nama" class="required form-label">Nama Instansi</label>
                            <input type="text" name="name" id="name-company" required class="form-control" placeholder="<PERSON>a Peru<PERSON>ha<PERSON>"/>
                        </div>
                        <div class="fv-row mb-10">
                            <label for="tipe_instansi" class="required form-label">Tipe Instansi</label>
                            <select name="instansi_id" id="instansi_id" class="form-select">
                                <option value="" disabled selected hidden>-- Pilih Tipe Instansi</option>
                                @foreach ($tipe_instansi as $ti)
                                <option value="{{ $ti->id_instansi }}">{{ $ti->tipe_instansi }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="fv-row mb-10">
                            <label for="jenis_usaha" class="required form-label">Bidang Usaha</label>
                            <select name="business_sector" id="business_sector" class="form-select">
                                <option value="" disabled selected hidden>-- Pilih Bidang Usaha</option>
                                @foreach (business_sectors() as $key => $sector)
                                <option value="{{ $key }}">{{ $sector }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="fv-row mb-10">
                            <label for="email" class="form-label">Email Perusahaan</label>
                            <input type="email" name="email" id="email" class="form-control" placeholder="Email"/>
                        </div>
                        <div class="fv-row mb-10">
                            <label for="website" class="form-label">Website Perusahaan</label>
                            <input type="text" name="website" id="website" class="form-control" placeholder="website"/>
                        </div>
                        <div class="fv-row mb-10">
                            <label for="NPWP" class="form-label">NPWP Perusahaan</label>
                            <input type="text" name="npwp" id="npwp" class="form-control" placeholder="NPWP"/>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="fv-row mb-10">
                            <label for="no_hp" class="form-label">Provinsi</label>
                            <select name="province_id" id="province_id" class="form-select" data-control="select2">
                                <option value="" disabled selected hidden>-- Pilih Provinsi</option>
                            </select>
                        </div>
                        <div class="fv-row mb-10">
                            <label for="no_hp" class="form-label">Kota/Kabupaten</label>
                            <select name="district_id" id="district_id" class="form-select">
                                <option value="" disabled selected hidden>-- Pilih Kota/Kabupaten</option>
                            </select>
                        </div>
                        <div class="fv-row mb-10">
                            <label for="no_hp" class="form-label">Kecamatan</label>
                            <select name="sub_district_id" id="sub_district_id" class="form-select">
                                <option value="" disabled selected hidden>-- Pilih Kecamatan</option>
                            </select>
                        </div>
                        <div class="fv-row mb-10">
                            <label for="no_hp" class="form-label">Kelurahan</label>
                            <select name="village_id" id="village_id" class="form-select">
                                <option value="" disabled selected hidden>-- Pilih Kelurahan</option>
                            </select>
                        </div>
                        <div class="fv-row mb-10">
                            <label for="address" class="required form-label">Alamat Instansi</label>
                            <textarea name="address" id="address" rows="3" class="form-control" placeholder="Alamat"></textarea>
                        </div>
                        <div class="fv-row mb-10">
                            <label for="post_code" class="form-label">Kode Pos</label>
                            <input type="text" name="zip_code" id="zip_code" class="form-control" placeholder="Kode Pos"/>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer justify-content-between">
            <button type="button" id="close-form-company" class="btn btn-light"
                data-bs-dismiss="modal">Close</button>

            <div class="row">
                <div class="col d-flex justify-content-center">
                    <button type="button" id="process-form-company" class="btn btn-primary">Save</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Make sure jQuery is loaded before this script -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $('#name-company').on('input', function() {
        // Only allow letters, numbers, and spaces
        $(this).val($(this).val().replace(/[^a-zA-Z0-9\s]/g, ''));
    });

    // Check company name on blur or when moving to another input
    $('#name-company').on('blur', function() {
        var companyName = $(this).val();
        if (companyName.trim() !== '') {
            $.ajax({
                url: '/company/get-exist-company',
                method: 'GET',
                data: { name: companyName },
                success: function(response) {
                    if (response.message === 'exist') {
                        $('#name-company').addClass('is-invalid');
                        if (!$('#name-company-note').length) {
                            $('#name-company').after('<small id="name-company-note" class="text-danger">Nama perusahaan sudah terdaftar.</small>');
                        }
                    } else {
                        $('#name-company').removeClass('is-invalid').addClass('border-success');
                        if (!$('#name-company-note').length) {
                            $('#name-company').after('<small id="name-company-note" class="text-success">Nama perusahaan tersedia.</small>');
                        } else {
                            $('#name-company-note').text('Nama perusahaan tersedia.').removeClass('text-danger').addClass('text-success');
                        }
                    }
                },
                error: function() {
                    alert('Terjadi kesalahan saat memeriksa nama perusahaan.');
                }
            });
        }
    });
</script>