<script>
    $(document).ready(function() {

        $('#province_id').select2({
            allowClear: false,
            debug: true,
            dropdownParent: $('#add-new-company'),
            ajax: {
                url: "{{ route('crm.get_province') }}",
                processResults: function(data) {
                    var resultsData = []

                    $.each(data, function(index, item) {
                        resultsData.push({
                            id: item.id,
                            text: item.name
                        })
                    })

                    return {
                        results: resultsData
                    };
                },
                // cache: true
            }
        })
        
        $('#province_id').on('change', function(){
            var province_id = $('#province_id').val();
            $('#district_id').select2({
                allowClear: false,
                debug: true,
                dropdownParent: $('#add-new-company'),
                ajax: {
                    url: "{{ route('crm.get_district') }}",
                    data: function(params) {
                        var query = {
                            q: params.term,
                            type: 'public',
                            province_id: province_id, // Assuming you have an element with ID 'province_id'
                            raw: true // Add the 'raw' parameter
                        }

                        // Query parameters will be ?search=[term]&type=public&province_id=[value]&raw=true
                        return query;
                    },
                    processResults: function(data) {
                        var resultsData = []
    
                        $.each(data, function(index, item) {
                            resultsData.push({
                                id: item.id,
                                text: item.name
                            })
                        })
    
                        return {
                            results: resultsData
                        };
                    },
                    // cache: true
                }
            })
        })

        $('#district_id').on('change', function(){
            var district_id = $('#district_id').val();
            if (district_id === null || district_id === 'undefined') {
                Swal.fire({
                    title: 'Warning!',
                    text: "Pilih Kecamatan terlebih dahulu",
                    icon: 'info',
                })
                return false
            } else {
                $('#sub_district_id').select2({
                    allowClear: false,
                    debug: true,
                    dropdownParent: $('#add-new-company'),
                    ajax: {
                        url: "{{ route('crm.get_sub_district') }}",
                        data: function(params) {
                            var query = {
                                q: params.term,
                                type: 'public',
                                district_id: district_id, // Assuming you have an element with ID 'province_id'
                                raw: true // Add the 'raw' parameter
                            }

                            // Query parameters will be ?search=[term]&type=public&province_id=[value]&raw=true
                            return query;
                        },
                        processResults: function(data) {
                            var resultsData = []
    
                            $.each(data, function(index, item) {
                                resultsData.push({
                                    id: item.id,
                                    text: item.name
                                })
                            })
    
                            return {
                                results: resultsData
                            };
                        },
                        cache: true
                    }
                })
            }
        })

        $('#sub_district_id').on('change', function(){
            var sub_district_id = $('#sub_district_id').val();
            if (sub_district_id === null || sub_district_id === 'undefined') {
                Swal.fire({
                    title: 'Warning!',
                    text: "Pilih Kecamatan terlebih dahulu",
                    icon: 'info',
                })
                return false
            } else {
                $('#village_id').select2({
                    allowClear: true,
                    debug: true,
                    dropdownParent: $('#add-new-company'),
                    ajax: {
                        url: "{{ route('crm.get_village') }}",
                        data: function(params) {
                            var query = {
                                q: params.term,
                                type: 'public',
                                sub_district_id: sub_district_id, // Assuming you have an element with ID 'province_id'
                                raw: true // Add the 'raw' parameter
                            }

                            // Query parameters will be ?search=[term]&type=public&province_id=[value]&raw=true
                            return query;
                        },
                        processResults: function(data) {
                            var resultsData = []
    
                            $.each(data, function(index, item) {
                                resultsData.push({
                                    id: item.id,
                                    text: item.name
                                })
                            })
    
                            return {
                                results: resultsData
                            };
                        },
                        // cache: true
                    }
                })
            }
        })
        
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $('#process-form-company').click(function (e) {
            e.preventDefault();
            var province_id = $('#province_id').val();
            var district_id = $('#district_id').val();
            var sub_district_id = $('#sub_district_id').val();
            var village_id = $('#village_id').val();
            $.ajax({
                data: $('#add-new-company').serialize()+"&province_id="+province_id+"&district_id="+district_id+"&village_id="+village_id,
                url: "{{ route('company.store_company') }}",
                type: "POST",
                dataType: 'json',
                success: function (data) {
                    $('#add-new-company').trigger("reset");
                    Swal.fire({
                        title: 'SUCCESS!',
                        text: "Perusahaan baru berhasil dibuat",
                        icon: 'success',
                        confirmButtonText: 'OK'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = "{{ route('crm') }}";
                        }
                    });
                    window.dt_company.draw();
                    $('#datatable_company').DataTable().ajax.reload();
                    $('#modal-add-company-close').trigger('click');
                },
                error: function (data) {
                    $('#process-form-company').html('Save Changes');
                    Swal.fire({
                        title: 'ERROR!',
                        text: "Harap Lengkapi form yang ada",
                        icon: 'error',
                    })
                }
            });
        });

        const dataTableLanguage = {
            loadingRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div class="spinner-grow text-warning" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>`,
            emptyTable: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
            zeroRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
        };

        //filter page cust
        const filterSearch = document.querySelector('[data-kt-docs-table-filter="search-company"]')
        filterSearch.addEventListener('keyup', function(e) {
            window.throttleDTSearch(window.dt_company, e.target.value)
        })
    });
</script>
