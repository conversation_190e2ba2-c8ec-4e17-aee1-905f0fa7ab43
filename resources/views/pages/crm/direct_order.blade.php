<x-app-layout>
    <x-slot name="header">
        <span class="fs-1 text-white">CRM</span>
        <small class="fs-6 fw-normal pt-2 text-white">Tambah Order Langsung</small>
    </x-slot>
    <x-slot name="script">
        @include('pages.crm.data_jscript_direct_order')
    </x-slot>

    <div class="container-xxl mt-20" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-header">
                <h3 class="card-title text-dark"><i>#{{ $data_cust->kode_kustomer }} - {{ $data_cust->nama }}</i></h3>
            </div>
            <div class="card-body pt-0">
                <form method="POST" id="form-direct-order" enctype="multipart/form-data">
                    @csrf
                    @method('POST')
                    <input type="hidden" name="id_customer" id="id_customer" value="{{ $data_cust->id_customer }}">
                    <div class="d-grid mt-3">
                        <ul class="nav nav-tabs text-nowrap flex-nowrap">
                            <li class="nav-item">
                                <a class="nav-link btn btn-active-dark btn-color-grey-600 active me-3"
                                    data-bs-toggle="tab" href="#kt_tab_pane_1">Data Order</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link btn btn-active-dark btn-color-grey-600" data-bs-toggle="tab"
                                    href="#kt_tab_pane_2">Data Produk</a>
                            </li>
                        </ul>
                    </div>

                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="kt_tab_pane_1" role="tabpanel">
                            <div class="row mt-10">
                                <div class="col-md-6">
                                    <div class="kode_order_group mb-10">
                                        <label for="main_kode_order" class="form-label">Main Kode Order (kosongkan jika
                                            order pertama)</label>
                                        <input type="text" name="main_kode_order" id="main_kode_order"
                                            class="form-select" />
                                        <input type="hidden" name="order_key" id="order_key" class="form-control">
                                    </div>
                                    <div class="mb-10">
                                        <label for="produk_ke" class="required form-label">Produk ke-</label>
                                        <select name="produk_ke" id="produk_ke" class="form-select">
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                            <option value="6">6</option>
                                            <option value="7">7</option>
                                            <option value="8">8</option>
                                            <option value="9">9</option>
                                            <option value="10">10</option>
                                        </select>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="sumber" class="required form-label">Sumber</label>
                                        <select name="sumber" id="sumber" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih sumber</option>
                                            @foreach ($sumber as $sumber)
                                                <option value="{{ $sumber->sumber }}">{{ $sumber->sumber }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="waktu_kontak" class="required form-label">Waktu Kontak</label>
                                        <div class="input-group mb-5" id="waktu_kontak_form">
                                            <input type="text" class="form-control" name="waktu_kontak"
                                                id="waktu_kontak" />
                                            <span class="input-group-text" id="basic-addon2">
                                                <i class="fa fa-calendar"></i>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="tipe_kontak" class="form-label required">Tipe Kontak</label>
                                        <select name="tipe_kontak" id="tipe_kontak" class="form-select" required>
                                            <option value="" disabled selected hidden>-- Pilih tipe kontak
                                            </option>
                                            <option value="Sampah">Sampah</option>
                                            <option value="Bukan Sampah">Bukan Sampah</option>
                                        </select>
                                    </div>
                                    <div class="fv-row mb-5">
                                        <label for="pain_pont" class="form-label">Pain Point</label>
                                        <div class="d-flex flex-wrap gap-3">
                                            <div class="form-check form-check-custom mb-2" style="width: 172px">
                                                <input class="form-check-input" type="checkbox"
                                                    value="1" name="pp_harga" id="pain_pont1" />
                                                <label class="form-check-label" for="pain_pont1">
                                                    Harga/Biaya
                                                </label>
                                            </div>
                                            <div class="form-check form-check-custom mb-2" style="width: 172px">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                    name="pp_waktu" id="pain_pont2" />
                                                <label class="form-check-label" for="pain_pont2">
                                                    Kualitas Produk
                                                </label>
                                            </div>
                                            <div class="form-check form-check-custom mb-2" style="width: 172px">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                    name="pp_pengiriman" id="pain_pont3" />
                                                <label class="form-check-label" for="pain_pont3">
                                                    Waktu Produksi
                                                </label>
                                            </div>
                                            <div class="form-check form-check-custom mb-2" style="width: 172px">
                                                <input class="form-check-input" type="checkbox" value="1" 
                                                    name="pp_kualitas" id="pain_pont4" />
                                                <label class="form-check-label" for="pain_pont4">
                                                    Term of Payment
                                                </label>
                                            </div>
                                            <div class="form-check form-check-custom mb-2" style="width: 173px">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                    name="pp_pembayaran" id="pain_pont5" />
                                                <label class="form-check-label" for="pain_pont5">
                                                    Packing & Pengiriman
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="fv-row mb-10">
                                        <label for="id_pic" class="required form-label">PIC</label>
                                        <select class="form-select" name="id_pic" id="id_pic"
                                            data-control="select2" data-placeholder="Nama PIC">
                                            <option></option>
                                        </select>
                                    </div>
                                    {{-- <div class="mb-10 input-bulan">
                                        <label for="bulan" class="form-label">Bulan</label>
                                        <input type="text" name="bulan" id="bulan" class="form-control"/>
                                    </div> --}}
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="fv-row mb-10">
                                                <label for="status_deal" class="required form-label">Status
                                                    Deal</label>
                                                <select name="status_deal" id="status_deal" class="form-select">
                                                    <option value="" disabled selected hidden>-- Pilih status
                                                        deal</option>
                                                    <option value="Follow Up">Follow Up</option>
                                                    <option value="Deal">Deal</option>
                                                    <option value="Lost">Lost</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="fv-row mb-10">
                                                <label for="status_order" class="required form-label">Status
                                                    Order</label>
                                                <select name="status_order1" id="status_order1" class="form-select">
                                                    <option value="" disabled selected hidden>-- Pilih status
                                                        deal</option>
                                                    @foreach ($follow_up as $follow_up)
                                                        <option value="{{ $follow_up->follow_up }}">
                                                            {{ $follow_up->follow_up }}</option>
                                                    @endforeach
                                                </select>
                                                <select name="status_order2" id="status_order2" class="form-select">
                                                    <option value="" disabled selected hidden>-- Pilih status
                                                        deal</option>
                                                    <option value="Design">Design</option>
                                                    <option value="FAW">FAW</option>
                                                    <option value="Selesai Produksi">Selesai Produksi</option>
                                                </select>
                                                <select name="status_order3" id="status_order3" class="form-select">
                                                    <option value="" disabled selected hidden>-- Pilih status
                                                        deal</option>
                                                    @foreach ($lost as $lost)
                                                        <option value="{{ $lost->lost }}">{{ $lost->lost }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-10" id="tanggal_order">
                                        <label for="tgl_order" class="form-label">Tanggal Order</label>
                                        <input type="date" name="tgl_order" id="tgl_order"
                                            class="form-control" />
                                    </div>
                                    <div class="row gy-5 mb-10" id="section_dp">
                                        <div class="fv-row col-md-6" id="tanggal_order">
                                            <label for="tgl_order" class="form-label">Tanggal Order</label>
                                            <input type="date" name="tgl_order" id="tgl_order"
                                                class="form-control"/>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="tgl_pelunasan" class="form-label">Tanggal Pelunasan</label>
                                            <input id="section_pelunasan" type="date" name="tgl_pelunasan" id="tgl_pelunasan"
                                                class="form-control" disabled/>
                                        </div>
                                        <div class="col-md-8">
                                            <label for="DP" class="form-label">DP</label>
                                            <div class="input-group">
                                                <span class="input-group-text">Rp.</span>
                                                <input type="text" name="DP" id="DP"
                                                    class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-md-4 align-self-center">
                                            <div class="form-check form-check-custom mt-7">
                                                <input class="form-check-input" type="checkbox" value="pelunasan"
                                                    name="flag_lunas" id="flag_lunas">
                                                <label class="form-check-label" for="flexCheckDefault">
                                                    Pelunasan
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-10">
                                        <label for="catatan_order" class="form-label">Catatan Order</label>
                                        <textarea name="catatan_order" id="catatan_order" rows="6" class="form-control"></textarea>
                                    </div>
                                    <div class="fv-row mb-10 mt-5">
                                        <label for="flag_dummy" class="required form-label">Tipe Order</label>
                                        <div class="d-flex flex-column">
                                            <div class="d-flex">
                                                <div class="m-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="flag_dummy"
                                                            value="Dummy" id="flag_dummy_d">
                                                        <label class="form-check-label" for="flag_dummy_d">
                                                            Dummy
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="m-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="flag_dummy"
                                                            value="Produksi Massal" id="flag_dummy_s">
                                                        <label class="form-check-label" for="flag_dummy_s">
                                                            Produksi Massal
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="m-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="flag_dummy"
                                                            value="Produksi Ulang" id="flag_dummy_r">
                                                        <label class="form-check-label" for="flag_dummy_r">
                                                            Produksi Ulang
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="m-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="flag_dummy"
                                                            value="FU/Lost" id="flag_dummy_f">
                                                        <label class="form-check-label" for="flag_dummy_f">
                                                            FU/Lost
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="m-4">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="flag_dummy"
                                                            value="Jasa Lainnya" id="flag_dummy_j">
                                                        <label class="form-check-label" for="flag_dummy_j">
                                                            Jasa Lainnya
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {{-- <p id="error_add_flag_dummy" class="text-danger mt-3" style="font-size: 0.9em;"></p> --}}
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="survey_order" class="form-label">Survey Hasil Order</label>
                                                <div class="form-check form-switch form-check-custom">
                                                    <input class="form-check-input" name="survey_order"
                                                        type="checkbox" value="1" id="survey_order" />
                                                    <label class="form-check-label"
                                                        for="flexSwitchDefault">Tidak/Ya</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-10">
                                                <label for="forecast" class="form-label">Forecast</label>
                                                <div class="form-check form-switch form-check-custom">
                                                    <input class="form-check-input" name="forecast"
                                                        type="checkbox" value="1" id="forecast"/>
                                                    <label class="form-check-label"
                                                        for="flexSwitchDefault">Tidak/Ya</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="kt_tab_pane_2" role="tabpanel">
                            <div class="row mt-10">
                                <div class="col-md-6">
                                    <div class="fv-row mb-10">
                                        <label for="kategori_produksi" class="required form-label">Kategori</label>
                                        <select name="kategori_produksi" id="kategori_produksi" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Kategori</option>
                                            <option value="Packaging">Packaging</option>
                                            <option value="Packaging Support">Packaging Support</option>
                                            <option value="Non Packaging">Non Packaging</option>
                                            <option value="Dummy">Dummy</option>
                                            {{-- <option value="PPN">PPN</option> --}}
                                            <option value="Jasa Desain">Jasa Desain</option>
                                            <option value="Lain-lain">Lain-lain</option>
                                        </select>
                                    </div>
                                    <div class="mb-10">
                                        <label for="jenis_bahan" class="form-label">Jenis Bahan</label>
                                        <select name="jenis_bahan" id="jenis_bahan" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Jenis Bahan
                                            </option>
                                            @foreach ($bahan as $bahan)
                                                <option value="{{ $bahan->bahan }}">{{ $bahan->bahan }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="mb-14">
                                        <label for="dimensi_produk" class="form-label">Dimensi Produk (P x L x
                                            T)</label>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="input-group">
                                                    <input type="number" step="any" name="dp_panjang"
                                                        id="dp_panjang" class="form-control" placeholder="panjang" />
                                                    <span class="input-group-text">cm</span>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="input-group">
                                                    <input type="number" step="any" name="dp_lebar"
                                                        id="dp_lebar" class="form-control" placeholder="lebar" />
                                                    <span class="input-group-text">cm</span>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="input-group">
                                                    <input type="number" step="any" name="dp_tinggi"
                                                        id="dp_tinggi" class="form-control" placeholder="tinggi" />
                                                    <span class="input-group-text">cm</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-10">
                                        <label for="luas_permukaan" class="form-label">Luas Permukaan (P x L)</label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="input-group">
                                                    <input type="number" step="any" name="lp_panjang"
                                                        id="lp_panjang" class="form-control" placeholder="panjang" />
                                                    <span class="input-group-text">cm</span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="input-group">
                                                    <input type="number" step="any" name="lp_lebar"
                                                        id="lp_lebar" class="form-control" placeholder="lebar" />
                                                    <span class="input-group-text">cm</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-10">
                                        <label for="jenis_kertas" class="form-label">Jenis Kertas</label>
                                        <select name="jenis_kertas" id="jenis_kertas" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Jenis Kertas
                                            </option>
                                            @foreach ($jenis_kertas as $jenis_kertas)
                                                <option value="{{ $jenis_kertas->jenis_kertas }}">
                                                    {{ $jenis_kertas->jenis_kertas }}</option>
                                            @endforeach
                                            <option value="Lainnya">Lainnya</option>
                                        </select>
                                        <input type="text" name="jenis_kertas_lainnya" id="jenis_kertas_lainnya"
                                            class="form-control" placeholder="Isi jika lainnya" />
                                    </div>
                                    <div class="mb-10">
                                        <label for="gramasi" class="form-label">Gramasi</label>
                                        <select name="gramasi" id="gramasi" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Gramasi</option>
                                            @foreach ($gramasi as $gramasi)
                                                <option value="{{ $gramasi->gramasi }}">{{ $gramasi->gramasi }}
                                                </option>
                                            @endforeach
                                            <option value="Lainnya">Lainnya</option>
                                        </select>
                                        <input type="text" name="gramasi_lainnya" id="gramasi_lainnya"
                                            class="form-control" placeholder="Isi jika lainnya" />
                                    </div>
                                    <div class="mb-10">
                                        <label for="laminasi" class="form-label">Laminasi</label>
                                        <select name="laminasi" id="laminasi" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Laminasi</option>
                                            <option value="Tanpa Laminasi">Tanpa Laminasi</option>
                                            <option value="Laminasi Glossy">Laminasi Glossy</option>
                                            <option value="Laminasi Doff">Laminasi Doff</option>
                                            <option value="Laminasi Jendela Glossy">Laminasi Jendela Glossy</option>
                                        </select>
                                    </div>
                                    <div class="mb-10">
                                        <label for="sisi_laminasi" class="form-label">Sisi Laminasi</label>
                                        <select name="sisi_laminasi" id="sisi_laminasi" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih sisi laminasi
                                            </option>
                                            <option value="Tanpa Laminasi">Tanpa Laminasi</option>
                                            <option value="Sisi Dalam">Sisi Dalam</option>
                                            <option value="Sisi Luar">Sisi Luar</option>
                                            <option value="2 Sisi">2 Sisi</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-5">
                                        <label for="finishing" class="form-label">Finishing</label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox"
                                                        value="Pond/potong" name="finishing[]" id="finishing1" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Pond/Potong
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox" value="Spot UV"
                                                        name="finishing[]" id="finishing2" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Spot UV
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox" value="Emboss"
                                                        name="finishing[]" id="finishing3" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Emboss
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox"
                                                        value="UV Varnish" name="finishing[]" id="finishing4" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        UV Varnish
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox" value="Hot Stamp"
                                                        name="finishing[]" id="finishing5" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Hot Stamp
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox"
                                                        value="Lem Samping" name="finishing[]" id="finishing6" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Lem Samping
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox" value="Lem Lapis"
                                                        name="finishing[]" id="finishing7" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Lem Lapis
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox"
                                                        value="Jendela Mika" name="finishing[]" id="finishing8" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Jendela Mika
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox"
                                                        value="Magnet/Tali" name="finishing[]" id="finishing9" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Magnet/Tali
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox" value="Sablon"
                                                        name="finishing[]" id="finishing10" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Sablon
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox"
                                                        value="Finishing Hardbox" name="finishing[]"
                                                        id="finishing11" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Finishing Hardbox
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox" value="Lainnya"
                                                        name="finishing[]" id="finishing12" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Lainnya
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <input type="text" name="finishing_lainnya" id="finishing_lainnya"
                                            class="form-control" placeholder="Isi jika lainnya" />
                                    </div>
                                    <div class="mb-10">
                                        <label for="tipe_produk" class="form-label">Tipe Produk</label>
                                        <select name="tipe_produk" id="tipe_produk" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Produk</option>
                                            @foreach ($tipe_produk as $tipe_produk)
                                                <option value="{{ $tipe_produk->tipe_produk }}">
                                                    {{ $tipe_produk->tipe_produk }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="isi_kertas" class="form-label">Kertas Plano</label>
                                                <select name="isi_kertas" id="isi_kertas" class="form-select">
                                                    <option value="" disabled selected hidden>-- Pilih Kertas
                                                    </option>
                                                    @foreach ($kertas_plano as $kertas_plano)
                                                        <option value="{{ $kertas_plano->kertas_plano }}">
                                                            {{ $kertas_plano->kertas_plano }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="get_plano" class="form-label">Get Plano</label>
                                                <input type="text" name="get_plano" placeholder="Hasil potong kertas"
                                                    id="get_plano" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="jumlah_produk" class="form-label">Jumlah Produk</label>
                                                <div class="input-group">
                                                    <input type="text" name="jumlah_produk" id="jumlah_produk"
                                                        class="form-control" placeholder="Qty"/>
                                                    <span class="input-group-text">pcs</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="harga_produk" class="form-label">Harga Produk</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">Rp. </span>
                                                    <input type="text" name="harga_produk" id="harga_produk"
                                                        class="form-control" placeholder="Harga satuan"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="total_harga" class="form-label">Total Harga</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">Rp.</span>
                                                    <input type="text" readonly name="total_harga"
                                                        id="total_harga" class="form-control form-control-solid" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="modal_sales" class="form-label">Modal Sales</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">Rp.</span>
                                                    <input type="text" name="modal_sales" id="modal_sales"
                                                        class="form-control" placeholder="Total modal sales"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-3 mb-3">
                                        <div class="">
                                            <label for="nama_produk" class="form-label required">Nama Produk</label>
                                            <input type="text" name="nama_produk" id="nama_produk" class="form-control" placeholder="Nama produk" style="min-width: 260px;">
                                        </div>

                                        <div class="form-check mt-4">
                                            <input class="form-check-input" type="checkbox" value="PPN" name="ppn" id="ppn">
                                            <label class="form-check-label" for="ppn">PPN</label>
                                        </div>
                                    </div>
                                    <div class="row" id="ppn_form">
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="ppn_percent" class="form-label">Persentase PPN</label>
                                                <div class="input-group">
                                                    <input type="text" name="ppn_percent"
                                                    id="ppn_percent" class="form-control" />
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="total_ppn" class="form-label">Total PPN</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">Rp.</span>
                                                    <input type="text" readonly name="total_ppn" id="total_ppn"
                                                        class="form-control  form-control-solid" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-10">
                                        <label for="notes" class="form-label">Catatan Produk</label>
                                        <textarea name="notes" id="notes" rows="6" class="form-control"></textarea>
                                    </div>
                                    <div class="mb-10">
                                        <label for="hitung_harga_khusus" class="form-label">Hitung Harga
                                            Khusus</label>
                                        <div class="form-check form-switch form-check-custom">
                                            <input class="form-check-input" name="hitung_harga_khusus"
                                                type="checkbox" value="1" id="hitung_harga_khusus" />
                                            <label class="form-check-label" for="flexSwitchDefault">Tidak/Ya</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" id="direct-order" class="btn my-primary float-end text-white">Submit</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>


</x-app-layout>
