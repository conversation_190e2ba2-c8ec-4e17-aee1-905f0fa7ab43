<div class="row g-6 g-xl-9" style="margin-top: 50px">
    <!--begin::Col-->
    <div class="col-lg-6">
        <!--begin::Summary-->
        <div class="card card-flush hover-elevate-up h-lg-100">
            <a href="{{ route('target') }}">
                <!--begin::Card header-->
                <div class="card-header mt-6">
                    <!--begin::Card title-->
                    <div class="flex-column justify-content-start">
                        <h3 class="fw-bolder mb-1">Total Target</h3>
                        <h1 class="fw-bolder text-danger mb-1"><b>Rp. {{ number_format($total_target, 0, '', ',') }}</b>
                        </h1>
                    </div>
                    <!--end::Card title-->
                    <!--begin::Card title-->
                    <div class="flex-column justify-content-end">
                        <h3 class="fw-bolder mb-1 text-end">Capaian ({{ date('F') }})</h3>
                        <h1 class="text-success mb-1 text-end">Rp. {{ number_format($total_realisasi, 0, '', ',') }}
                        </h1>
                    </div>
                    <!--end::Card title-->
                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body p-9 pt-5">
                    <div class="d-flex flex-column">
                        <div class="d-flex justify-content-between w-100 fs-6 mb-3">
                            <span>Progress To Target</span>
                            <span>{{ round($target_progress, 2) }}%</span>
                        </div>
                        <div class="h-8px bg-light mb-3 rounded">
                            <div class="bg-danger h-8px rounded" role="progressbar"
                                style="width: {{ $target_progress }}%;" aria-valuenow="0" aria-valuemin="0"
                                aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </a>
            <!--end::Card body-->
        </div>
        <!--end::Summary-->
    </div>
    <!--end::Col-->

    <!--begin::Col-->
    <div class="col-lg-3">
        <!--begin::Summary-->
        <div class="card bg-primary hover-elevate-up h-lg-100" id="count_price_null">
            <a href="{{ route('crm.price_null') }}">
                <span class="menu-icon m-6 mb-0">
                    <!--begin::Svg Icon | path: assets/media/icons/duotune/communication/com014.svg-->
                    <svg xmlns="http://www.w3.org/2000/svg" width="70" height="70" viewBox="0 0 24 24"
                        fill="none">
                        <path
                            d="M16.0173 9H15.3945C14.2833 9 13.263 9.61425 12.7431 10.5963L12.154 11.7091C12.0645 11.8781 12.1072 12.0868 12.2559 12.2071L12.6402 12.5183C13.2631 13.0225 13.7556 13.6691 14.0764 14.4035L14.2321 14.7601C14.2957 14.9058 14.4396 15 14.5987 15H18.6747C19.7297 15 20.4057 13.8774 19.912 12.945L18.6686 10.5963C18.1487 9.61425 17.1285 9 16.0173 9Z"
                            fill="white"></path>
                        <rect opacity="0.3" x="14" y="4" width="4" height="4" rx="2"
                            fill="white"></rect>
                        <path
                            d="M4.65486 14.8559C5.40389 13.1224 7.11161 12 9 12C10.8884 12 12.5961 13.1224 13.3451 14.8559L14.793 18.2067C15.3636 19.5271 14.3955 21 12.9571 21H5.04292C3.60453 21 2.63644 19.5271 3.20698 18.2067L4.65486 14.8559Z"
                            fill="white"></path>
                        <rect opacity="0.3" x="6" y="5" width="6" height="6" rx="3"
                            fill="white"></rect>
                    </svg>
                    <!--end::Svg Icon-->
                </span>

                <div class="card-body d-flex flex-column">
                    <!--begin::Amount-->
                    <span class="fs-2hx fw-bolder me-2 lh-1 ls-n2 text-white">{{ $count_price_null }}</span>
                    <!--end::Amount-->
                    <!--begin::Subtitle-->
                    <span class="fw-semibold fs-6 pt-1 text-white">Customer Belum Diberi Harga</span>
                    <!--end::Subtitle-->
                </div>
            </a>

        </div>
        <!--end::Summary-->
    </div>
    <!--end::Col-->

    <!--begin::Col-->
    <div class="col-lg-3">
        <!--begin::Summary-->
        <div class="card bg-warning hover-elevate-up h-lg-100" id="count_prioritas">
            <a href="{{ route('crm.prioritas') }}">
                <span class="menu-icon m-6 mb-0">
                    <!--begin::Svg Icon | path: assets/media/icons/duotune/communication/com014.svg-->
                    <svg xmlns="http://www.w3.org/2000/svg" width="70" height="70" viewBox="0 0 24 24"
                        fill="none">
                        <rect x="8" y="9" width="3" height="10" rx="1.5" fill="white">
                        </rect>
                        <rect opacity="0.5" x="13" y="5" width="3" height="14" rx="1.5"
                            fill="white"></rect>
                        <rect x="18" y="11" width="3" height="8" rx="1.5" fill="white">
                        </rect>
                        <rect x="3" y="13" width="3" height="6" rx="1.5" fill="white">
                        </rect>
                    </svg>
                    <!--end::Svg Icon-->
                </span>

                <div class="card-body d-flex flex-column">
                    <!--begin::Amount-->
                    <span class="fs-2hx fw-bolder me-2 lh-1 ls-n2 text-white">{{ $count_prioritas }}</span>
                    <!--end::Amount-->
                    <!--begin::Subtitle-->
                    <span class="fw-semibold fs-6 pt-1 text-white">Customer Prioritas</span>
                    <!--end::Subtitle-->
                </div>
            </a>
        </div>
        <!--end::Summary-->
    </div>
    <!--end::Col-->
</div>


<div class="card card-flush mt-10">
    <div class="card-body pt-0">
        <div class="py-5">
            <div class="d-flex flex-row-fluid justify-content-between gap-3">
                {{-- <div class="w-100 mw-100px">
                    <select class="form-select form-select-solid filter-data-year" id="data_year" name="data_year" >
                    </select>
                </div> --}}
                <div class="d-flex align-items-center position-relative">
                    <!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
                    <span class="svg-icon svg-icon-1 position-absolute ms-6">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none">
                            <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2"
                                rx="1" transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                            <path
                                d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                fill="black"></path>
                        </svg>
                    </span>
                    <!--end::Svg Icon-->
                    <input type="text" data-kt-docs-table-filter="search-order"
                        class="form-control form-control-solid w-250px ps-15" placeholder="Search ...">
                </div>
                
                {{-- <button type="button" class="btn btn-light-primary" data-kt-menu-trigger="click"
                    data-kt-menu-placement="bottom-end">
                    <span class="svg-icon svg-icon-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                            viewBox="0 0 24 24" fill="none">
                            <path
                                d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                                fill="black"></path>
                        </svg>
                    </span>
                    Filter
                </button>
                <div class="menu menu-sub menu-sub-dropdown w-700px w-md-725px" data-kt-menu="true"
                    id="kt-toolbar-filter-order">
                    <div class="px-7 py-5">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-10">
                                    <label class="form-label fs-5 fw-bold mb-3">Waktu Kontak</label>
                                    <div class="row align-items-start">
                                        <div class="col-md-6">
                                            <input type="date" id="filter-tgl-order-from"
                                                name="tgl_order_from_date"
                                                class="form-control form-control-solid fw-bold">
                                            <small>From</small>
                                        </div>
                                        <div class="col-md-6">
                                            <input type="date" id="filter-tgl-order-to"
                                                name="tgl_order_to_date"
                                                class="form-control form-control-solid fw-bold">
                                            <small>To</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <button type="button" name="filter-tgl-order-reset"
                                            id="filter-tgl-order-reset" class="btn btn-primary">Reset</button>
                                    <button type="button" name="filter-order-tgl-order"
                                        id="filter-order-tgl-order" class="btn btn-primary">Search</button>
                                </div>
                            </div>
                        </div>

                    </div>
                </div> --}}
                <!--begin::Add order-->
                <div class="d-flex gap-3">
                    <button type="button" class="btn btn-light-primary" id="collapseButton">
                        <span class="svg-icon svg-icon-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none">
                                <path
                                    d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                                    fill="black"></path>
                            </svg>
                        </span>
                        Filter
                    </button>
                    <button type="button" class="btn btn-success text-nowrap" data-bs-toggle="modal"
                        data-bs-target="#modal_add_order" title="Add Order" id="add_order_btn">
                        <!--begin::Svg Icon | path: icons/duotune/arrows/arr075.svg-->
                        <span class="svg-icon svg-icon-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                viewBox="0 0 24 24" fill="none">
                                <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2"
                                    rx="1" transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                                <rect x="4.36396" y="11.364" width="16" height="2" rx="1"
                                    fill="black"></rect>
                            </svg>
                        </span>
                        <!--end::Svg Icon-->Add Order & Product
                    </button>
                </div>
                <!--end::Add customer-->
            </div>
            <div class="collapse" id="collapseExample">
                <hr>
                <div class=" bg-secondary rounded p-2 d-flex flex-row-fluid justify-content-between gap-3">
                    <div>
                        <select class="form-select form-select-solid filter-order-flag-dummy" name="flag_dummy_order">
                            <option hidden>Tipe Order</option>
                            <option value="">All</option>
                            <option value="Dummy">Dummy</option>
                            <option value="Produksi Massal">Produksi Massal</option>
                            <option value="Produksi Ulang">Produksi Ulang</option>
                            <option value="FU/Lost">FU/Lost</option>
                            <option value="Jasa Lainnya">Jasa Lainnya</option>
                        </select>
                    </div>
                    
                    <div class="w-100 mw-100px">
                        <select class="form-select form-select-solid filter-order-year" name="year">
                            <option hidden>Tahun</option>
                            <option value="" selected>All</option>
                            <option value="2023">2023</option>
                            <option value="2024">2024</option>
                            <option value="2025">2025</option>
                        </select>
                    </div>
                    @if (Auth::user()->roles == 'SALES SPV' || Auth::user()->roles == 'SUPERADMIN')
                        <div class="w-100 mw-100px">
                            <select class="form-select form-select-solid filter-order-pic" name="name">
                                <option hidden>PIC</option>
                                <option value="">All</option>
                                @foreach ($pic as $list)
                                    <option value="{{ $list->name }}">{{ $list->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    @endif
                    <div class="w-100 mw-150px">
                        <select class="form-select form-select-solid filter-order-status-deal" name="name">
                            <option hidden>Status Deal</option>
                            <option value="">All</option>
                            <option value="Follow Up">Follow Up</option>
                            <option value="Deal">Deal</option>
                            <option value="Lost">Lost</option>
                        </select>
                    </div>
                    <div class="w-100 mw-200px">
                        <select class="form-select form-select-solid filter-order-status-order" name="name">
                            <option hidden>Status Order</option>
                            <option value="">All</option>
                            <option value="" disabled>-- FU</option>
                            @foreach ($follow_up as $fu)
                                <option value="{{ $fu->follow_up }}">{{ $fu->follow_up }}</option>
                            @endforeach
                            <option value="" disabled>-- Deal</option>
                            <option value="Design">Design </option>
                            <option value="FAW">FAW</option>
                            <option value="Selesai Produksi">Selesai Produksi</option>
                            <option value="" disabled>-- Lost</option>
                            @foreach ($lost as $fu_lost)
                                <option value="{{ $fu_lost->lost }}">{{ $fu_lost->lost }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="w-100 mw-150px">
                        <select class="form-select form-select-solid filter-sumber" name="name">
                            <option hidden>Sumber</option>
                            <option value="">All</option>
                            @foreach ($sumber as $list)
                                <option value="{{ $list->sumber }}">{{ $list->sumber }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <hr>
            </div>
                
                <!--end::Toolbar-->
            <div class="mt-2" id="table_div">
                <table id="datatable_order"
                class="table-row-bordered compact text-nowrap dataTable no-footer table-striped table align-middle">
                <thead>
                    <tr class="fw-bold">
                        {{-- <th class="text-center align-middle">Kode Order</th> --}}
                        <th>SKO</th>
                        <th>Tanggal Order</th>
                        <th>Waktu Kontak</th>
                        <th>Waktu Closing</th>
                        <th>PIC</th>
                        <th>Nama Customer</th>
                        <th>Total harga</th>
                        <th>Total PPN</th>
                        <th>Modal Sales</th>
                        <th>Status Deal</th>
                        <th>Status Order</th>
                        <th>Kategori</th>
                        <th>Last Update</th>
                        <th class="text-center">Action</th>
                        <th>Sumber</th>
                        <th>Tipe Order</th>
                    </tr>
                </thead>
                <tbody> </tbody>
            </table>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="modal_add_order">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <form action="" id="add-new-order" enctype="multipart/form-data">
                <div class="modal-body mt-n5">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="modal-title">Tambah Order Baru</h5>
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-order-close"
                            data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                        </div>
                    </div>
                    <div class="d-grid mt-3">
                        <ul class="nav nav-tabs text-nowrap flex-nowrap">
                            <li class="nav-item">
                                <a class="nav-link btn btn-active-dark btn-color-grey-600 active me-3"
                                data-bs-toggle="tab" href="#kt_tab_pane_order">Data Order</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link btn btn-active-dark btn-color-grey-600" data-bs-toggle="tab"
                                href="#kt_tab_pane_produk">Data Produk</a>
                            </li>
                        </ul>
                    </div>
                
                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="kt_tab_pane_order" role="tabpanel">
                            <div class="row mt-10">
                                <div class="col-md-6">
                                    <div class="fv-row mb-10">
                                        <label for="id_customer" class="required form-label">Customer</label>
                                        <select class="form-select" name="id_customer" id="id_customer"
                                            data-control="select2" data-placeholder="Nama Customer">
                                            <option></option>
                                        </select>
                                    </div>
                                    <div class="kode_order_group mb-10">
                                        <label for="main_kode_order" class="form-label">Main Kode Order (kosongkan
                                            jika order pertama)</label>
                                        <input type="text" name="main_kode_order" id="main_kode_order"
                                            class="form-select" />
                                        <input type="hidden" name="order_key" id="order_key" class="form-control">
                                    </div>
                                    <div class="mb-10">
                                        <label for="produk_ke" class="required form-label">Produk ke-</label>
                                        <select name="produk_ke" id="produk_ke" class="form-select">
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                            <option value="6">6</option>
                                            <option value="7">7</option>
                                            <option value="8">8</option>
                                            <option value="9">9</option>
                                            <option value="10">10</option>
                                        </select>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="sumber" class="required form-label">Sumber</label>
                                        <select name="sumber" id="sumber" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih sumber</option>
                                            @foreach ($sumber_order as $sumber)
                                                <option value="{{ $sumber->sumber }}">{{ $sumber->sumber }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="waktu_kontak" class="required form-label">Waktu Kontak</label>
                                        <div class="input-group mb-5" id="waktu_kontak_form">
                                            <input type="text" class="form-control" name="waktu_kontak"
                                                id="waktu_kontak" required />
                                            <span class="input-group-text" id="basic-addon2">
                                                <i class="fa fa-calendar"></i>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="tipe_kontak" class="required form-label">Tipe Kontak</label>
                                        <select name="tipe_kontak" id="tipe_kontak" class="form-select" required>
                                            <option value="" disabled selected hidden>-- Pilih tipe kontak
                                            </option>
                                            <option value="Sampah">Sampah</option>
                                            <option value="Bukan Sampah">Bukan Sampah</option>
                                        </select>
                                    </div>
                                    <div class="fv-row mb-5">
                                        <label for="pain_pont" class="form-label">Pain Point</label>
                                        <div class="d-flex flex-wrap gap-3">
                                            <div class="form-check form-check-custom mb-2" style="width: 172px">
                                                <input class="form-check-input" type="checkbox"
                                                    value="1" name="pp_harga" id="pain_pont1" />
                                                <label class="form-check-label" for="pain_pont1">
                                                    Harga/Biaya
                                                </label>
                                            </div>
                                            <div class="form-check form-check-custom mb-2" style="width: 172px">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                    name="pp_waktu" id="pain_pont2" />
                                                <label class="form-check-label" for="pain_pont2">
                                                    Kualitas Produk
                                                </label>
                                            </div>
                                            <div class="form-check form-check-custom mb-2" style="width: 172px">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                    name="pp_pengiriman" id="pain_pont3" />
                                                <label class="form-check-label" for="pain_pont3">
                                                    Waktu Produksi
                                                </label>
                                            </div>
                                            <div class="form-check form-check-custom mb-2" style="width: 172px">
                                                <input class="form-check-input" type="checkbox" value="1" 
                                                    name="pp_kualitas" id="pain_pont4" />
                                                <label class="form-check-label" for="pain_pont4">
                                                    Term of Payment
                                                </label>
                                            </div>
                                            <div class="form-check form-check-custom mb-2" style="width: 173px">
                                                <input class="form-check-input" type="checkbox" value="1"
                                                    name="pp_pembayaran" id="pain_pont5" />
                                                <label class="form-check-label" for="pain_pont5">
                                                    Packing & Pengiriman
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="fv-row mb-10">
                                        <label for="id_pic" class="required form-label">PIC</label>
                                        @if (Auth::user()->roles == 'SALES')
                                            <select class="form-select" name="id_pic">
                                                @foreach ($pic as $loggedpic)
                                                    <option value="{{ $loggedpic->id }}">{{ $loggedpic->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        @else
                                            <select class="form-select" name="id_pic" id="id_pic"
                                                data-control="select2" data-placeholder="Nama PIC">
                                                <option></option>
                                            </select>
                                        @endif
                                    </div>
                                    {{-- <div class="mb-10 input-bulan">
                                        <label for="bulan" class="required form-label">Bulan</label>
                                        <input type="text" name="bulan" id="bulan" class="form-control" />
                                    </div> --}}
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="fv-row mb-10">
                                                <label for="status_deal" class="required form-label">Status
                                                    Deal</label>
                                                <select name="status_deal" id="status_deal" class="form-select">
                                                    <option value="" disabled selected hidden>-- Pilih status
                                                        deal
                                                    </option>
                                                    <option value="Follow Up">Follow Up</option>
                                                    <option value="Deal">Deal</option>
                                                    <option value="Lost">Lost</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="fv-row mb-10">
                                                <label for="status_order" class="required form-label">Status
                                                    Order</label>
                                                <select name="status_order1" id="status_order1" class="form-select">
                                                    <option value="" disabled selected hidden>-- Pilih status
                                                        deal
                                                    </option>
                                                    @foreach ($follow_up as $fu)
                                                        <option value="{{ $fu->follow_up }}">{{ $fu->follow_up }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                                <select name="status_order2" id="status_order2" class="form-select">
                                                    <option value="" disabled selected hidden>-- Pilih status
                                                        deal
                                                    </option>
                                                    <option value="Design">Design</option>
                                                    <option value="FAW">FAW</option>
                                                    <option value="Selesai Produksi">Selesai Produksi</option>
                                                </select>
                                                <select name="status_order3" id="status_order3" class="form-select">
                                                    <option value="" disabled selected hidden>-- Pilih status
                                                        deal
                                                    </option>
                                                    @foreach ($lost as $lost)
                                                        <option value="{{ $lost->lost }}">{{ $lost->lost }}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row gy-5 mb-10" id="section_dp">
                                        <div class="fv-row col-md-6" id="tanggal_order">
                                            <label for="tgl_order" class="form-label">Tanggal Order</label>
                                            <input type="date" name="tgl_order" id="tgl_order"
                                                class="form-control"/>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="no_po" class="form-label">No. PO</label>
                                            <input id="section_link_dokumen_order" type="text" name="no_po" id="no_po"
                                                class="form-control"/>
                                        </div>
                                        <div class="col-md-12">
                                            <label for="link_dokumen_order" class="form-label">Link Dokumen Order(Gdrive)</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fa fa-link"></i></span>
                                                <input type="text" name="link_dokumen_order" id="link_dokumen_order" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="col-md-4 align-self-center d-none">
                                            <div class="form-check form-check-custom mt-7">
                                                <input class="form-check-input" type="checkbox" value="pelunasan"
                                                    name="flag_lunas" id="flag_lunas">
                                                <label class="form-check-label" for="flexCheckDefault">
                                                    Pelunasan
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-10">
                                        <label for="catatan_order" class="form-label">Catatan Order</label>
                                        <textarea name="catatan_order" id="catatan_order" rows="6" class="form-control"></textarea>
                                    </div>
                                    <div class="fv-row mb-10 mt-5">
                                        <label for="flag_dummy" class="required form-label">Tipe Order</label>
                                        <div class="d-flex flex-column">
                                            <div class="d-flex">
                                                <div class="m-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="flag_dummy"
                                                            value="Dummy" id="flag_dummy_d">
                                                        <label class="form-check-label" for="flag_dummy_d">
                                                            Dummy
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="m-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="flag_dummy"
                                                            value="Produksi Massal" id="flag_dummy_s">
                                                        <label class="form-check-label" for="flag_dummy_s">
                                                            Produksi Massal
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="m-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="flag_dummy"
                                                            value="Produksi Ulang" id="flag_dummy_r">
                                                        <label class="form-check-label" for="flag_dummy_r">
                                                            Produksi Ulang
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="m-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="flag_dummy"
                                                            value="FU/Lost" id="flag_dummy_f">
                                                        <label class="form-check-label" for="flag_dummy_f">
                                                            FU/Lost
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="m-4">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="flag_dummy"
                                                            value="Jasa Lainnya" id="flag_dummy_j">
                                                        <label class="form-check-label" for="flag_dummy_j">
                                                            Jasa Lainnya
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {{-- <p id="error_add_flag_dummy" class="text-danger mt-3" style="font-size: 0.9em;"></p> --}}
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="forecast" class="form-label">Forecast</label>
                                                <div class="form-check form-switch form-check-custom">
                                                    <input class="form-check-input" name="forecast"
                                                        type="checkbox" value="1" id="forecast"/>
                                                    <label class="form-check-label"
                                                        for="flexSwitchDefault">Tidak/Ya</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="lead_prospek" class="form-label">Potential Lead</label>
                                                <div class="form-check form-switch form-check-custom">
                                                    <input class="form-check-input" name="lead_prospek"
                                                        type="checkbox" value="1" id="lead_prospek" />
                                                    <label class="form-check-label"
                                                        for="flexSwitchDefault">Tidak/Ya</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row border p-2 d-none" id="div-forecast">
                                        <b class="mb-3">Forecast Section</b>
                                        <div class="col-md-4">
                                            <label for="foracast_month" class="form-label">Bulan</label>
                                            <select class="form-select" id="forecast-month" name="forecast_month">
                                                @foreach (month() as $key => $mt)
                                                    <option value="{{$key}}" {{$key < date('m') ? 'disabled':''}}>{{$mt}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="foracast_year" class="form-label">Tahun</label>
                                            <select class="form-select" id="forecast-year" name="forecast_year">
                                                @php
                                                    $y = date('Y');
                                                @endphp
                                                @for ($i = $y; $i <= $y+5 ; $i++)
                                                    <option value="{{$i}}">{{$i}}</option>
                                                @endfor
                                            </select>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <label for="forecast_week" class="form-label">Week</label>
                                            <select class="form-select" id="forecast-week" name="forecast_week">
                                                <option value="1">Week 1</option>
                                                <option value="2">Week 2</option>
                                                <option value="3">Week 3</option>
                                                <option value="4">Week 4</option>
                                                <option value="5">Week 5</option>
                                            </select>
                                        </div>
                                        <div class="col-md-12">
                                            <label for="expected_revenue" class="form-label">Expected Revenue</label>
                                            <div class="input-group">
                                                <span class="input-group-text">Rp. </span>
                                                <input type="text" name="expected_revenue" id="expected-revenue"
                                                        class="form-control" placeholder="Expected Revenue"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="kt_tab_pane_produk" role="tabpanel">
                            <div class="row mt-10">
                                <div class="col-md-6">
                                    <div class="fv-row mb-10">
                                        <label for="kategori_produksi" class="required form-label">Kategori</label>
                                        <select name="kategori_produksi" id="kategori_produksi" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Kategori</option>
                                            <option value="Packaging">Packaging</option>
                                            <option value="Packaging Support">Packaging Support</option>
                                            <option value="Non Packaging">Non Packaging</option>
                                            <option value="Dummy">Dummy</option>
                                            {{-- <option value="PPN">PPN</option> --}}
                                            <option value="Jasa Desain">Jasa Desain</option>
                                            <option value="Lain-lain">Lain-lain</option>
                                            </option>
                                        </select>
                                    </div>
                                    <div class="mb-10">
                                        <label for="jenis_bahan" class="form-label">Jenis Bahan</label>
                                        <select name="jenis_bahan" id="jenis_bahan" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Jenis Bahan
                                            </option>
                                            @foreach ($bahan as $bahan)
                                                <option value="{{ $bahan->bahan }}">{{ $bahan->bahan }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="mb-14">
                                        <label for="dimensi_produk" class="form-label">Dimensi Produk (P x L x
                                            T)</label>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="input-group">
                                                    <input type="number" step="any" name="dp_panjang"
                                                        id="dp_panjang" class="form-control" placeholder="panjang" />
                                                    <span class="input-group-text">cm</span>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="input-group">
                                                    <input type="number" step="any" name="dp_lebar"
                                                        id="dp_lebar" class="form-control" placeholder="lebar" />
                                                    <span class="input-group-text">cm</span>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="input-group">
                                                    <input type="number" step="any" name="dp_tinggi"
                                                        id="dp_tinggi" class="form-control" placeholder="tinggi" />
                                                    <span class="input-group-text">cm</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-10">
                                        <label for="luas_permukaan" class="form-label">Luas Permukaan (P x
                                            L)</label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="input-group">
                                                    <input type="number" step="any" name="lp_panjang"
                                                        id="lp_panjang" class="form-control" placeholder="panjang" />
                                                    <span class="input-group-text">cm</span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="input-group">
                                                    <input type="number" step="any" name="lp_lebar"
                                                        id="lp_lebar" class="form-control" placeholder="lebar" />
                                                    <span class="input-group-text">cm</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-10">
                                        <label for="jenis_kertas" class="form-label">Jenis Kertas</label>
                                        <select name="jenis_kertas" id="jenis_kertas" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Jenis Kertas
                                            </option>
                                            @foreach ($jenis_kertas as $jenis_kertas)
                                                <option value="{{ $jenis_kertas->jenis_kertas }}">
                                                    {{ $jenis_kertas->jenis_kertas }}</option>
                                            @endforeach
                                            <option value="Lainnya">Lainnya</option>
                                        </select>
                                        <input type="text" name="jenis_kertas_lainnya"
                                            id="jenis_kertas_lainnya" class="form-control"
                                            placeholder="Isi jika lainnya" />
                                    </div>
                                    <div class="mb-10">
                                        <label for="gramasi" class="form-label">Gramasi</label>
                                        <select name="gramasi" id="gramasi" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Gramasi</option>
                                            @foreach ($gramasi as $gramasi)
                                                <option value="{{ $gramasi->gramasi }}">{{ $gramasi->gramasi }}
                                                </option>
                                            @endforeach
                                            <option value="Lainnya">Lainnya</option>
                                        </select>
                                        <input type="text" name="gramasi_lainnya" id="gramasi_lainnya"
                                            class="form-control" placeholder="Isi jika lainnya" />
                                    </div>
                                    <div class="mb-10">
                                        <label for="laminasi" class="form-label">Laminasi</label>
                                        <select name="laminasi" id="laminasi" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Laminasi</option>
                                            <option value="Tanpa Laminasi">Tanpa Laminasi</option>
                                            <option value="Laminasi Glossy">Laminasi Glossy</option>
                                            <option value="Laminasi Doff">Laminasi Doff</option>
                                            <option value="Laminasi Jendela Glossy">Laminasi Jendela Glossy</option>
                                        </select>
                                    </div>
                                    <div class="mb-10">
                                        <label for="sisi_laminasi" class="form-label">Sisi Laminasi</label>
                                        <select name="sisi_laminasi" id="sisi_laminasi" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih sisi laminasi
                                            </option>
                                            <option value="Tanpa Laminasi">Tanpa Laminasi</option>
                                            <option value="Sisi Dalam">Sisi Dalam</option>
                                            <option value="Sisi Luar">Sisi Luar</option>
                                            <option value="2 Sisi">2 Sisi</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-5">
                                        <label for="finishing" class="form-label">Finishing</label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox"
                                                        value="Pond/potong" name="finishing[]" id="finishing1" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Pond/Potong
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox" value="Spot UV"
                                                        name="finishing[]" id="finishing2" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Spot UV
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox" value="Emboss"
                                                        name="finishing[]" id="finishing3" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Emboss
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox"
                                                        value="UV Varnish" name="finishing[]" id="finishing4" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        UV Varnish
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox" value="Hot Stamp"
                                                        name="finishing[]" id="finishing5" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Hot Stamp
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox"
                                                        value="Lem Samping" name="finishing[]" id="finishing6" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Lem Samping
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox" value="Lem Lapis"
                                                        name="finishing[]" id="finishing7" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Lem Lapis
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox"
                                                        value="Jendela Mika" name="finishing[]" id="finishing8" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Jendela Mika
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox"
                                                        value="Magnet/Tali" name="finishing[]" id="finishing9" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Magnet/Tali
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox" value="Sablon"
                                                        name="finishing[]" id="finishing10" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Sablon
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox"
                                                        value="Finishing Hardbox" name="finishing[]"
                                                        id="finishing11" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Finishing Hardbox
                                                    </label>
                                                </div>
                                                <div class="form-check form-check-custom mb-2">
                                                    <input class="form-check-input" type="checkbox" value="Lainnya"
                                                        name="finishing[]" id="finishing12" />
                                                    <label class="form-check-label" for="flexCheckDefault">
                                                        Lainnya
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <input type="text" name="finishing_lainnya" id="finishing_lainnya"
                                            class="form-control" placeholder="Isi jika lainnya" />
                                    </div>
                                    <div class="mb-10">
                                        <label for="tipe_produk" class="form-label">Tipe Produk</label>
                                        <select name="tipe_produk" id="tipe_produk" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Produk</option>
                                            @foreach ($tipe_produk as $tipe_produk)
                                                <option value="{{ $tipe_produk->tipe_produk }}">
                                                    {{ $tipe_produk->tipe_produk }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="isi_kertas" class="form-label">Kertas Plano</label>
                                                <select name="isi_kertas" id="isi_kertas" class="form-select">
                                                    <option value="" disabled selected hidden>-- Pilih Kertas
                                                    </option>
                                                    @foreach ($kertas_plano as $kertas_plano)
                                                        <option value="{{ $kertas_plano->kertas_plano }}">
                                                            {{ $kertas_plano->kertas_plano }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="get_plano" class="form-label">Get Plano</label>
                                                <input type="text" name="get_plano" placeholder="Hasil potong kertas"
                                                    id="get_plano" class="form-control" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="jumlah_produk" class="form-label">Jumlah Produk</label>
                                                <div class="input-group">
                                                    <input type="text" name="jumlah_produk" id="jumlah_produk"
                                                        class="form-control" placeholder="Qty"/>
                                                    <span class="input-group-text">pcs</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="harga_produk" class="form-label">Harga Produk</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">Rp. </span>
                                                    <input type="text" name="harga_produk" id="harga_produk"
                                                        class="form-control" placeholder="Harga satuan"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="total_harga" class="form-label">Total Harga</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">Rp.</span>
                                                    <input type="text" readonly name="total_harga"
                                                        id="total_harga" class="form-control form-control-solid" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="modal_sales" class="form-label">Modal Sales</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">Rp.</span>
                                                    <input type="text" name="modal_sales" id="modal_sales"
                                                        class="form-control" placeholder="Total modal sales"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center gap-3 mb-3">
                                        <div class="">
                                            <label for="nama_produk" class="form-label required">Nama Produk</label>
                                            <input type="text" name="nama_produk" id="nama_produk" class="form-control w-50" placeholder="Nama produk" style="min-width: 260px;">
                                        </div>

                                        <div class="form-check mt-4">
                                            <input class="form-check-input" type="checkbox" value="PPN" name="ppn" id="ppn">
                                            <label class="form-check-label" for="ppn">PPN</label>
                                        </div>
                                    </div>
                                    <div class="row" id="ppn_form">
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="ppn_percent" class="form-label">Persentase PPN</label>
                                                <div class="input-group">
                                                    <input type="text" name="ppn_percent"
                                                    id="ppn_percent" class="form-control" />
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="total_ppn" class="form-label">Total PPN</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">Rp.</span>
                                                    <input type="text" readonly name="total_ppn" id="total_ppn"
                                                        class="form-control  form-control-solid" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-10">
                                        <label for="notes" class="form-label">Catatan Produk</label>
                                        <textarea name="notes" id="notes" rows="6" class="form-control"></textarea>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="hitung_harga_khusus" class="form-label">Hitung Harga
                                                    Khusus</label>
                                                <div class="form-check form-switch form-check-custom">
                                                    <input class="form-check-input" name="hitung_harga_khusus"
                                                        type="checkbox" value="1" id="hitung_harga_khusus" />
                                                    <label class="form-check-label"
                                                        for="flexSwitchDefault">Tidak/Ya</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-10">
                                                <label for="kendala_produksi" class="form-label">Kendala
                                                    Produksi</label>
                                                <div class="form-check form-switch form-check-custom">
                                                    <input class="form-check-input" name="kendala_produksi"
                                                        type="checkbox" value="1" id="kendala_produksi" />
                                                    <label class="form-check-label"
                                                        for="flexSwitchDefault">Tidak/Ya</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer justify-content-between">
                                <button type="button" id="close-form-order" class="btn btn-light"
                                    data-bs-dismiss="modal">Close</button>
                                <button type="submit" id="process-form-order" class="btn btn-primary my-primary">Save</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- edit order -->
<div class="modal fade" id="modal_edit_order">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Order</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-edit-order-close"
                    data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                </div>
            </div>
            <div class="modal-body mt-n5">
                <form action="" id="edit-order">
                    <input type="hidden" name="id_order" id="id_order_edit">
                    <input type="hidden" name="order_key" id="order_key_edit">
                    <div class="row mt-10">
                        <div class="col-md-6">
                            <div class="mb-10">
                                <label for="id_customer" class="required form-label">Customer</label>
                                <input type="hidden" name="id_cust_edit" id="id_cust_edit" readonly
                                    class="form-control form-control-solid" />
                                <input type="text" name="id_customer_order_edit" id="id_customer_order_edit"
                                    readonly class="form-control form-control-solid" />
                            </div>
                            <div class="kode_order_group_edit mb-10">
                                <label for="kode_order" class="required form-label">Main Kode Order</label>
                                <input type="text" name="kode_order" id="kode_order_edit" class="form-select" />
                                <input type="hidden" name="main_order_key" id="main_order_key_edit"
                                    class="form-control-solid" />
                            </div>
                            <div class="mb-10">
                                <div class="row">
                                    <div class="col-md-8">
                                        <label for="sko" class="required form-label">Sub Kode Order</label>
                                        <input type="text" name="sko" id="sko_edit" readonly
                                            class="form-control form-control-solid" />
                                    </div>
                                    <div class="col-md-4">
                                        <label for="produk_ke" class="required form-label">Produk ke-</label>
                                        <select name="produk_ke" id="produk_ke_edit"
                                            class="form-select form-select-solid">
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                            <option value="6">6</option>
                                            <option value="7">7</option>
                                            <option value="8">8</option>
                                            <option value="9">9</option>
                                            <option value="10">10</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="sumber" class="required form-label">Sumber</label>
                                <select name="sumber" id="sumber_edit" class="form-select">
                                    <option value="" disabled>-- Pilih sumber</option>
                                    @foreach ($sumber_order as $sumber)
                                        <option value="{{ $sumber->sumber }}">{{ $sumber->sumber }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="fv-row mb-10">
                                <label for="waktu_kontak" class="required form-label">Waktu Kontak</label>
                                <div class="input-group mb-5" id="waktu_kontak_form_edit">
                                    <input type="text" class="form-control" name="waktu_kontak"
                                        id="waktu_kontak_edit" required />
                                    <span class="input-group-text" id="basic-addon2">
                                        <i class="fa fa-calendar"></i>
                                    </span>
                                </div>
                            </div>

                            <div class="mb-10">
                                <label for="tipe_kontak" class="form-label required">Tipe Kontak</label>
                                <select name="tipe_kontak" id="tipe_kontak_edit" class="form-select" required>
                                    <option value="" disabled selected hidden>-- Pilih tipe kontak</option>
                                    <option value="Sampah">Sampah</option>
                                    <option value="Bukan Sampah">Bukan Sampah</option>
                                </select>
                            </div>
                            <div class="fv-row mb-5">
                                <label for="pain_pont" class="form-label">Pain Point</label>
                                <div class="d-flex flex-wrap gap-3">
                                    <div class="form-check form-check-custom mb-2" style="width: 172px">
                                        <input class="form-check-input" type="checkbox"
                                            value="1" name="pp_harga" id="pain_pont1_edit" />
                                        <label class="form-check-label" for="pain_pont1_edit">
                                            Harga/Biaya
                                        </label>
                                    </div>
                                    <div class="form-check form-check-custom mb-2" style="width: 172px">
                                        <input class="form-check-input" type="checkbox" value="1"
                                            name="pp_waktu" id="pain_pont2_edit" />
                                        <label class="form-check-label" for="pain_pont2_edit">
                                            Kualitas Produk
                                        </label>
                                    </div>
                                    <div class="form-check form-check-custom mb-2" style="width: 172px">
                                        <input class="form-check-input" type="checkbox" value="1"
                                            name="pp_pengiriman" id="pain_pont3_edit" />
                                        <label class="form-check-label" for="pain_pont3_edit">
                                            Waktu Produksi
                                        </label>
                                    </div>
                                    <div class="form-check form-check-custom mb-2" style="width: 172px">
                                        <input class="form-check-input" type="checkbox" value="1" 
                                            name="pp_kualitas" id="pain_pont4_edit" />
                                        <label class="form-check-label" for="pain_pont4_edit">
                                            Term of Payment
                                        </label>
                                    </div>
                                    <div class="form-check form-check-custom mb-2" style="width: 173px">
                                        <input class="form-check-input" type="checkbox" value="1"
                                            name="pp_pembayaran" id="pain_pont5_edit" />
                                        <label class="form-check-label" for="pain_pont5_edit">
                                            Packing & Pengiriman
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="fv-row mb-10">
                                <label for="id_pic" class="required form-label">PIC</label>
                                <select class="form-select" name="id_pic" id="id_pic_edit" data-control="select"
                                    data-placeholder="Nama PIC">
                                    @foreach ($pic as $nama)
                                        <option value="{{ $nama->id }}">{{ $nama->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            {{-- <div class="mb-10 input-bulan">
                                <label for="bulan" class="required form-label">Bulan</label>
                                <input type="text" name="bulan" id="bulan_edit"
                                    class="form-control form-control-solid" />
                            </div> --}}
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="fv-row mb-10">
                                        <label for="status_deal" class="required form-label">Status Deal</label>
                                        <select name="status_deal" id="status_deal_edit" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih status deal
                                            </option>
                                            <option value="Follow Up">Follow Up</option>
                                            <option value="Deal">Deal</option>
                                            <option value="Lost">Lost</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="fv-row mb-10">
                                        <label for="status_order" class="required form-label">Status Order</label>
                                        <select name="status_order1" id="status_order1_edit" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih status deal
                                            </option>
                                            @foreach ($follow_up as $follow_up)
                                                <option value="{{ $follow_up->follow_up }}">{{ $follow_up->follow_up }}</option>
                                            @endforeach
                                        </select>
                                        <select name="status_order2" id="status_order2_edit" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih status deal
                                            </option>
                                            <option value="Design">Design</option>
                                            <option value="FAW">FAW</option>
                                            <option value="Selesai Produksi">Selesai Produksi</option>
                                        </select>
                                        <select name="status_order3" id="status_order3_edit" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih status deal
                                            </option>
                                            @foreach ($lost2 as $lost)
                                                <option value="{{ $lost->lost }}">{{ $lost->lost }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10" id="follow_up_terakhir">
                                <label for="follow_up_terakhir_edit" class="form-label">Status Follow Up
                                    Terakhir</label>
                                <select name="follow_up_terakhir" id="follow_up_terakhir_edit" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih status follow up</option>
                                    @foreach ($follow_up2 as $follow_up)
                                        <option value="{{ $follow_up->follow_up }}">{{ $follow_up->follow_up }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="row gy-5 mb-10" id="section_dp_edit">
                                <div class="fv-row col-md-6" id="tanggal_order_edit">
                                    <label for="tgl_order" class="form-label">Tanggal Order</label>
                                    <input type="date" name="tgl_order" id="tgl_order_edit" class="form-control" />
                                </div>
                                <div class="col-md-6">
                                    <label for="tgl_pelunasan" class="form-label">No PO</label>
                                    <input type="text" name="no_po" id="no_po_edit" class="form-control"/>
                                </div>
                                <div class="col-md-12">
                                    <label for="link_dokumen_order" class="form-label">Link Dokumen Order(Gdrive)</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fa fa-link"></i></span>
                                        <input type="text" name="link_dokumen_order" id="link_dokumen_order_edit" class="form-control" />
                                    </div>
                                </div>
                                <div class="col-md-4 align-self-center d-none">
                                    <div class="form-check form-check-custom mt-7">
                                        <input class="form-check-input" type="checkbox" value="pelunasan"
                                            name="flag_lunas" id="flag_lunas_edit">
                                        <label class="form-check-label" for="flexCheckDefault">
                                            Pelunasan
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="catatan_order_edit" class="form-label">Catatan Order</label>
                                <textarea name="catatan_order_edit" id="catatan_order_edit" rows="6" class="form-control"></textarea>
                            </div>
                            <div class="mb-10 mt-5">
                                <label for="flag_dummy_edit" class="required form-label">Tipe Order</label>
                                <div class="row gap-5">
                                    <div class="col-12 row">
                                        <div class="col">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="flag_dummy_edit"
                                                    value="Dummy" id="flag_dummy_d_edit">
                                                <label class="form-check-label" for="flag_dummy_d_edit">
                                                    Dummy
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="flag_dummy_edit"
                                                    value="Produksi Massal" id="flag_dummy_s_edit">
                                                <label class="form-check-label" for="flag_dummy_s_edit">
                                                    Produksi Massal
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="flag_dummy_edit"
                                                    value="Produksi Ulang" id="flag_dummy_r_edit">
                                                <label class="form-check-label" for="flag_dummy_r_edit">
                                                    Produksi Ulang
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 row">
                                        <div class="col-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="flag_dummy_edit"
                                                    value="FU/Lost" id="flag_dummy_f_edit">
                                                <label class="form-check-label" for="flag_dummy_f_edit">
                                                    FU/Lost
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="flag_dummy_edit"
                                                    value="Jasa Lainnya" id="flag_dummy_j_edit">
                                                <label class="form-check-label" for="flag_dummy_j_edit">
                                                    Jasa Lainnya
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <p id="error_edit_flag_dummy" class="text-danger mt-3" style="font-size: 0.9em;"></p>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="forecast" class="form-label">Forecast</label>
                                        <div class="form-check form-switch form-check-custom">
                                            <input class="form-check-input" name="forecast"
                                                type="checkbox" value="1" id="forecast_edit"/>
                                            <label class="form-check-label"
                                                for="flexSwitchDefault">Tidak/Ya</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="lead_prospek" class="form-label">Potential Lead</label>
                                        <div class="form-check form-switch form-check-custom">
                                            <input class="form-check-input" name="lead_prospek"
                                                type="checkbox" value="1" id="lead_prospek_edit" />
                                            <label class="form-check-label"
                                                for="flexSwitchDefault">Tidak/Ya</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row border p-2 d-none" id="div-forecast-edit">
                                <b class="mb-3">Forecast Section</b>
                                <div class="col-md-4">
                                    <label for="foracast_month" class="form-label">Bulan</label>
                                    <select class="form-select" id="forecast-month-edit" name="forecast_month">
                                        @foreach (month() as $key => $mt)
                                            <option value="{{$key}}" {{$key < date('m') ? 'disabled':''}}>{{$mt}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="foracast_year" class="form-label">Tahun</label>
                                    <select class="form-select" id="forecast-year-edit" name="forecast_year">
                                        @php
                                            $y = date('Y');
                                        @endphp
                                        @for ($i = $y; $i <= $y+5 ; $i++)
                                            <option value="{{$i}}">{{$i}}</option>
                                        @endfor
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="forecast_week" class="form-label">Week</label>
                                    <select class="form-select" id="forecast-week-edit" name="forecast_week">
                                        <option value="1">Week 1</option>
                                        <option value="2">Week 2</option>
                                        <option value="3">Week 3</option>
                                        <option value="4">Week 4</option>
                                        <option value="5">Week 5</option>
                                    </select>
                                </div>
                                <div class="col-md-12">
                                    <label for="expected_revenue" class="form-label">Expected Revenue</label>
                                    <div class="input-group">
                                        <span class="input-group-text">Rp. </span>
                                        <input type="text" name="expected_revenue" id="expected-revenue-edit"
                                                class="form-control" placeholder="Expected Revenue"/>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10 mt-8">
                                {{-- <label for="generate_surat_penawaran" class="form-label fw-bold">Surat
                                    Penawaran</label> --}}
                                <div class="download_surat_penawaran">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" id="close-form-edit-order" class="btn btn-light"
                    data-bs-dismiss="modal">Close</button>
                <button type="submit" id="process-form-edit-order"
                    class="btn btn-primary my-primary">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- edit produk -->
<div class="modal fade" id="modal_edit_produk">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Produk</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-edit-produk-close"
                    data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                </div>
            </div>
            <div class="modal-body mt-n5">
                <form action="" id="edit-produk">
                    <input type="hidden" name="id_produksi" id="id_produksi_edit">
                    <div class="row mt-10">
                        <div class="col-md-6">
                            <div class="fv-row mb-10">
                                <label for="kategori_produksi" class="form-label required">Kategori</label>
                                <select name="kategori_produksi" id="kategori_produksi_edit" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih Kategori</option>
                                    <option value="Packaging">Packaging</option>
                                    <option value="Packaging Support">Packaging Support</option>
                                    <option value="Non Packaging">Non Packaging</option>
                                    <option value="Dummy">Dummy</option>
                                    {{-- <option value="PPN">PPN</option> --}}
                                    <option value="Jasa Desain">Jasa Desain</option>
                                    <option value="Lain-lain">Lain-lain</option>
                                </select>
                            </div>
                            <div class="mb-10">
                                <label for="jenis_bahan" class="form-label">Jenis Bahan</label>
                                <select name="jenis_bahan" id="jenis_bahan_edit" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih Jenis Bahan</option>
                                    @foreach ($bahan2 as $bahan)
                                        <option value="{{ $bahan->bahan }}">{{ $bahan->bahan }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="mb-14">
                                <label for="dimensi_produk" class="form-label">Dimensi Produk (P x L x
                                    T)</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="input-group">
                                            <input type="number" step="any" name="dp_panjang"
                                                id="dp_panjang_edit" class="form-control" placeholder="panjang" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="input-group">
                                            <input type="number" step="any" name="dp_lebar"
                                                id="dp_lebar_edit" class="form-control" placeholder="lebar" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="input-group">
                                            <input type="number" step="any" name="dp_tinggi"
                                                id="dp_tinggi_edit" class="form-control" placeholder="tinggi" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="luas_permukaan" class="form-label">Luas Permukaan (P x L)</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <input type="number" step="any" name="lp_panjang"
                                                id="lp_panjang_edit" class="form-control" placeholder="panjang" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-group">
                                            <input type="number" step="any" name="lp_lebar"
                                                id="lp_lebar_edit" class="form-control" placeholder="lebar" />
                                            <span class="input-group-text">cm</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="jenis_kertas" class="form-label">Jenis Kertas</label>
                                <select name="jenis_kertas" id="jenis_kertas_edit" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih Jenis Kertas</option>
                                    @foreach ($jenis_kertas2 as $jenis_kertas)
                                        <option value="{{ $jenis_kertas->jenis_kertas }}">
                                            {{ $jenis_kertas->jenis_kertas }}</option>
                                    @endforeach
                                    <option value="Lainnya">Lainnya</option>
                                </select>
                                <input type="text" name="jenis_kertas_lainnya" id="jenis_kertas_lainnya_edit"
                                    class="form-control" placeholder="Isi jika lainnya" />
                            </div>
                            <div class="mb-10">
                                <label for="gramasi" class="form-label">Gramasi</label>
                                <select name="gramasi" id="gramasi_edit" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih Gramasi</option>
                                    @foreach ($gramasi2 as $gramasi)
                                        <option value="{{ $gramasi->gramasi }}">{{ $gramasi->gramasi }}</option>
                                    @endforeach
                                    <option value="Lainnya">Lainnya</option>
                                </select>
                                <input type="text" name="gramasi_lainnya" id="gramasi_lainnya_edit"
                                    class="form-control" placeholder="Isi jika lainnya" />
                            </div>
                            <div class="mb-10">
                                <label for="laminasi" class="form-label">Laminasi</label>
                                <select name="laminasi" id="laminasi_edit" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih Laminasi</option>
                                    <option value="Tanpa Laminasi">Tanpa Laminasi</option>
                                    <option value="Laminasi Glossy">Laminasi Glossy</option>
                                    <option value="Laminasi Doff">Laminasi Doff</option>
                                    <option value="Laminasi Jendela Glossy">Laminasi Jendela Glossy</option>
                                </select>
                            </div>
                            <div class="mb-10">
                                <label for="sisi_laminasi" class="form-label">Sisi Laminasi</label>
                                <select name="sisi_laminasi" id="sisi_laminasi_edit" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih sisi laminasi</option>
                                    <option value="Tanpa Laminasi">Tanpa Laminasi</option>
                                    <option value="Sisi Dalam">Sisi Dalam</option>
                                    <option value="Sisi Luar">Sisi Luar</option>
                                    <option value="2 Sisi">2 Sisi</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-5">
                                <label for="finishing" class="form-label">Finishing</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Pond/potong"
                                                name="finishing[]" id="finishing1_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Pond/Potong
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Spot UV"
                                                name="finishing[]" id="finishing2_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Spot UV
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Emboss"
                                                name="finishing[]" id="finishing3_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Emboss
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="UV Varnish"
                                                name="finishing[]" id="finishing4_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                UV Varnish
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Hot Stamp"
                                                name="finishing[]" id="finishing5_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Hot Stamp
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Lem Samping"
                                                name="finishing[]" id="finishing6_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Lem Samping
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Lem Lapis"
                                                name="finishing[]" id="finishing7_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Lem Lapis
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Jendela Mika"
                                                name="finishing[]" id="finishing8_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Jendela Mika
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Magnet/Tali"
                                                name="finishing[]" id="finishing9_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Magnet/Tali
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Sablon"
                                                name="finishing[]" id="finishing10_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Sablon
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox"
                                                value="Finishing Hardbox" name="finishing[]"
                                                id="finishing11_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Finishing Hardbox
                                            </label>
                                        </div>
                                        <div class="form-check form-check-custom mb-2">
                                            <input class="form-check-input" type="checkbox" value="Lainnya"
                                                name="finishing[]" id="finishing12_edit" />
                                            <label class="form-check-label" for="flexCheckDefault">
                                                Lainnya
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <input type="text" name="finishing_lainnya" id="finishing_lainnya_edit"
                                    class="form-control" placeholder="Isi jika lainnya" />
                            </div>
                            <div class="mb-10">
                                <label for="tipe_produk" class="form-label">Tipe Produk</label>
                                <select name="tipe_produk" id="tipe_produk_edit" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih Produk</option>
                                    @foreach ($tipe_produk2 as $tipe_produk)
                                        <option value="{{ $tipe_produk->tipe_produk }}">
                                            {{ $tipe_produk->tipe_produk }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="isi_kertas" class="form-label">Kertas Plano</label>
                                        <select name="isi_kertas" id="isi_kertas_edit" class="form-select">
                                            <option value="" disabled selected hidden>-- Pilih Kertas</option>
                                            @foreach ($kertas_plano2 as $kertas_plano)
                                                <option value="{{ $kertas_plano->kertas_plano }}">
                                                    {{ $kertas_plano->kertas_plano }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="get_plano" class="form-label">Get Plano</label>
                                        <input type="text" name="get_plano" placeholder="Hasil potong kertas"
                                            id="get_plano_edit" class="form-control" />
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="jumlah_produk" class="form-label">Jumlah Produk</label>
                                        <div class="input-group">
                                            <input type="text" name="jumlah_produk" id="jumlah_produk_edit"
                                                class="form-control" placeholder="Qty"/>
                                            <span class="input-group-text">pcs</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="harga_produk" class="form-label">Harga Produk</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp. </span>
                                            <input type="text" name="harga_produk" id="harga_produk_edit"
                                                class="form-control" placeholder="Harga satuan"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="total_harga" class="form-label">Total Harga</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp.</span>
                                            <input type="text" readonly name="total_harga"
                                                id="total_harga_edit" class="form-control form-control-solid" />
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="modal_sales" class="form-label">Modal Sales</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp.</span>
                                            <input type="text" name="modal_sales" id="modal_sales_edit"
                                                class="form-control" placeholder="Total modal sales"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-3 mb-3">
                                <div class="">
                                    <label for="nama_produk_edit" class="form-label required">Nama Produk</label>
                                    <input type="text" name="nama_produk_edit" id="nama_produk_edit" class="form-control" placeholder="Nama produk" style="min-width: 260px;">
                                </div>

                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" value="PPN" name="ppn_edit" id="ppn_edit">
                                    <label class="form-check-label" for="ppn_edit">PPN</label>
                                </div>
                            </div>
                            <div class="row" id="ppn_form_edit">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="ppn_percent_edit" class="form-label">Persentase PPN</label>
                                        <div class="input-group">
                                            <input type="text" name="ppn_percent_edit"
                                            id="ppn_percent_edit" class="form-control" />
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="total_ppn_edit" class="form-label">Total PPN</label>
                                        <div class="input-group">
                                            <span class="input-group-text">Rp.</span>
                                            <input type="text" readonly name="total_ppn_edit" id="total_ppn_edit"
                                                class="form-control  form-control-solid" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-10">
                                <label for="notes" class="form-label">Catatan Produk</label>
                                <textarea name="notes" id="notes_edit" rows="6" class="form-control"></textarea>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="hitung_harga_khusus" class="form-label">Hitung Harga
                                            Khusus</label>
                                        <div class="form-check form-switch form-check-custom">
                                            <input class="form-check-input" name="hitung_harga_khusus"
                                                type="checkbox" value="1" id="hitung_harga_khusus_edit" />
                                            <label class="form-check-label" for="flexSwitchDefault">Tidak/Ya</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-10">
                                        <label for="kendala_produksi" class="form-label">Kendala Produksi</label>
                                        <div class="form-check form-switch form-check-custom">
                                            <input class="form-check-input" name="kendala_produksi"
                                                type="checkbox" value="1" id="kendala_produksi_edit" />
                                            <label class="form-check-label" for="flexSwitchDefault">Tidak/Ya</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" id="close-form-edit-produk" class="btn btn-light"
                    data-bs-dismiss="modal">Close</button>
                <button type="submit" id="process-form-edit-produk"
                    class="btn btn-primary my-primary">Save</button>
            </div>
        </div>
    </div>
</div>