<script>
    $(document).ready(function() {
        //CSRF cust page
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });



        //form validation cust page
        const form = document.getElementById('add-new-customer');
        const form_edit = document.getElementById('edit-customer');

        var validator = FormValidation.formValidation(
            form, {
                fields: {
                    'nama': {
                        validators: {
                            notEmpty: {
                                message: 'Nama wajib diisi!'
                            }
                        }
                    },
                    'email_bisnis': {
                        validators: {
                            notEmpty: {
                                message: 'Email Bisnis wajib diisi!'
                            }
                        }
                    },
                    'no_hp': {
                        validators: {
                            notEmpty: {
                                message: 'Nomor HP wajib diisi!'
                            },
                            numeric: {
                                message: 'Nomor HP wajib angka!'
                            },
                            remote: {
                                url: "{{ route('checkPhoneNumber') }}",
                                message: 'Nomor HP sudah terdaftar!',
                                method: 'POST',
                                data: function() {
                                    return {
                                        _token: $('input[name="_token"]').val(),
                                        no_hp: $('input[name="no_hp"]').val()
                                    };
                                },
                                delay: 500 // Menambahkan delay 0,5 detik agar tidak terlalu banyak request ke server
                            }



                        }
                    },
                    'tipe_instansi': {
                        validators: {
                            notEmpty: {
                                message: 'Tipe instansi wajib diisi!'
                            }
                        }
                    },
                    'posisi_pic_cust': {
                        validators: {
                            notEmpty: {
                                message: 'Jabatan wajib diisi!'
                            }
                        }
                    },
                    'jenis_usaha': {
                        validators: {
                            notEmpty: {
                                message: 'Bidang Usaha wajib diisi!'
                            }
                        }
                    }
                },

                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: '.fv-row',
                        eleInvalidClass: '',
                        eleValidClass: ''
                    })
                }
            }
        );

        var validator_edit = FormValidation.formValidation(
            form_edit, {
                fields: {
                    'nama': {
                        validators: {
                            notEmpty: {
                                message: 'Nama wajib diisi!'
                            }
                        }
                    },
                    'no_hp': {
                        validators: {
                            notEmpty: {
                                message: 'Nomor HP wajib diisi!'
                            }
                        }
                    },
                    'email_bisnis': {
                        validators: {
                            notEmpty: {
                                message: 'Email Bisnis wajib diisi!'
                            }
                        }
                    },
                    'tipe_instansi': {
                        validators: {
                            notEmpty: {
                                message: 'Tipe instansi wajib diisi!'
                            }
                        }
                    },
                    'posisi_pic_cust': {
                        validators: {
                            notEmpty: {
                                message: 'Jabatan wajib diisi!'
                            }
                        }
                    },
                },

                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: '.fv-row',
                        eleInvalidClass: '',
                        eleValidClass: ''
                    })
                }
            }
        );

        const dataTableLanguage = {
            loadingRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div class="spinner-grow text-warning" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>`,
            emptyTable: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
            zeroRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
        };

        //filter page cust
        const filterSearch = document.querySelector('[data-kt-docs-table-filter="search-customer"]')
        filterSearch.addEventListener('keyup', function(e) {
            window.throttleDTSearch(window.dt_cust, e.target.value)
        })

        $('.filter_modal_sales_customer').on('change', function(e) {
            window.dt_cust.column(5).search(e.target.value).draw()
        });
        $('.filter_inst_type').on('change', function(e) {
            window.dt_cust.column(6).search(e.target.value).draw()
        });

        //add new cust
        $('#process-form-customer').click(function(e) {
            e.preventDefault();
            if (validator) {
                validator.validate().then(function(status) {
                    if (status == 'Valid') {
                        $.ajax({
                            data: $('#add-new-customer').serialize(),
                            url: "{{ route('crm.store_new_cust') }}",
                            type: "POST",
                            dataType: 'json',
                            success: function(data) {
                                $('#add-new-customer').trigger("reset");
                                setTimeout(function() {
                                    self.$("#close-form-customer").trigger(
                                        "click");
                                }, 1200);
                                Swal.fire({
                                    title: 'SUCCESS!',
                                    text: "Customer berhasil ditambah",
                                    icon: 'success',
                                })
                            },
                            error: function(data) {
                                console.log('Error:', data);
                                $('#process-form-customer').html('Error');
                            }
                        });
                    }
                    if (status == 'Invalid') {
                        Swal.fire({
                            title: 'Failed!',
                            text: "Lengkapi kolom yang wajib diisi",
                            icon: 'error',
                        })
                    }
                });
            }
        });
        $('#process-form-customer-order').click(function(e) {
            e.preventDefault();
            if (validator) {
                validator.validate().then(function(status) {
                    if (status == 'Valid') {
                        $.ajax({
                            data: $('#add-new-customer').serialize(),
                            url: "{{ route('crm.store_new_cust') }}",
                            type: "POST",
                            dataType: 'json',
                            success: function(data) {
                                Swal.fire({
                                    title: 'SUCCESS!',
                                    text: "Customer berhasil ditambah",
                                    icon: 'success',
                                });
                                window.location.replace(
                                    "{{ url('crm/direct_order') }}/" + data
                                    .kode_kustomer);
                            },
                            error: function(data) {
                                console.log('Error:', data);
                                $('#process-form-customer').html('Error');
                            }
                        });
                    }
                    if (status == 'Invalid') {
                        Swal.fire({
                            title: 'Failed!',
                            text: "Lengkapi kolom yang wajib diisi",
                            icon: 'error',
                        })
                    }
                });
            }
        });

        //modal editor cust
        $('body').on('click', '.edit-customer', function(event) {
            event.preventDefault();
            var id_customer = $(this).data('id');
            $.get("{{ url('crm/show_cust/') }}/" + id_customer, function(data) {
                $('#modal_edit_customer').modal('show');
                $('#id_customer_edit').val(data[0].id_customer);
                $('#kode_kustomer_edit').val(data[0].kode_kustomer);
                $('#nama_edit').val(data[0].nama);
                $('#no_hp_edit').val(data[0].no_hp);
                $('#email_bisnis_edit').val(data[0].email_bisnis);
                $('#posisi_pic_cust_edit').val(data[0].posisi_pic_cust);
                $('#jenis_usaha_edit').val(data[0].jenis_usaha);
                $('#alamat_edit').val(data[0].alamat);
                $('#nama_instansi_edit').val(data[0].nama_instansi);
                $('#grading_edit').val(data[0].grading);
                $('#tipe_instansi_edit').find('option[value="' + data[0].tipe_instansi + '"]')
                    .prop('selected',
                        true);
                $('#alamat_instansi_edit').val(data[0].alamat_instansi);
                $('#npwp_edit').val(data[0].npwp);
                $('#nama_brand_edit').val(data[0].nama_brand);
                // Profiling
                $('#durasi_berdiri_edit').val(data[1].durasi_berdiri);
                $('#skala_bisnis_edit').val(data[1].skala_bisnis);
                $('#segmen_pasar_edit').val(data[1].segmen_pasar);
                $('#channel_marketing_edit').val(data[1].channel_marketing);
                $('#tingkat_penjualan_edit').val(data[1].tingkat_penjualan);
                $('#nama_campaign_edit').val(data[1].nama_campaign);
                $('#anniv_usaha_edit').val(data[1].anniv_usaha);
                if (data[1].imlek == 1) {
                    $('#imlek').prop('checked', true);
                } else {
                    $('#imlek').prop('checked', false);
                }

                if (data[1].idul_fitri == 1) {
                    $('#idul_fitri').prop('checked', true);
                } else {
                    $('#idul_fitri').prop('checked', false);
                }

                if (data[1].natal == 1) {
                    $('#natal').prop('checked', true);
                } else {
                    $('#natal').prop('checked', false);
                }
                $('#keb_packaging_edit').val(data[1].keb_packaging);
                $('#potensi_freq_edit').val(data[1].potensi_freq);
                if (data[1].profiling == 1) {
                    $('#profiling').prop('checked', true);
                } else {
                    $('#profiling').prop('checked', false);
                }
                if (data[1].direct_meeting == 1) {
                    $('#direct_meeting').prop('checked', true);
                } else {
                    $('#direct_meeting').prop('checked', false);
                }
            })
        });

        $('#process-edit-customer').click(function(e) {
            e.preventDefault();
            if (validator_edit) {
                validator_edit.validate().then(function(status) {
                    if (status == 'Valid') {
                        $.ajax({
                            data: $('#edit-customer').serialize(),
                            url: "{{ route('crm.edit_cust') }}",
                            type: "POST",
                            dataType: 'json',
                            success: function(data) {
                                $('#edit-customer').trigger("reset");
                                setTimeout(function() {
                                    self.$("#close-edit-customer").trigger(
                                        "click");
                                }, 1200);
                                window.dt_cust.ajax.reload();
                                Swal.fire({
                                    title: 'SUCCESS!',
                                    text: "Customer berhasil diedit",
                                    icon: 'success',
                                })
                            },
                            error: function(data) {
                                console.log('Error:', data);
                                $('#process-edit-customer').html('Error');
                            }
                        });
                    }
                    if (status == 'Invalid') {
                        Swal.fire({
                            title: 'Failed!',
                            text: "Lengkapi kolom yang wajib diisi",
                            icon: 'error',
                        })
                    }
                });
            }
        });

        //modal editor
        $('body').on('click', '.edit-cust-comp', function (event) {
            event.preventDefault();
            var id = $(this).data('id');
            $.get("{{ url('crm/company/') }}/" + id, function (data) {
                $('#modal-edit-cust-company').modal('show');
                $('#id-cust-comp-edit').val(data[0].id_customer);
                $('#company_id_edit option[value='+data[0].company_id+']').attr('selected','selected');
                $('[aria-controls="select2-company_id_edit-container"] .select2-selection__placeholder').html(data[0].company.name);
            })
        });

        $('#submit-edit-cust-company').click(function(e) {
            e.preventDefault();
            $.ajax({
                data: $('#edit-cust-comp').serialize(),
                url: "{{ route('crm.edit_cust_comp') }}",
                type: "POST",
                dataType: 'json',
                success: function(data) {
                    $('#edit-cust-comp').trigger("reset");
                    setTimeout(function() {
                        self.$("#edit-comp-cust-close").trigger(
                            "click");
                    }, 1200);
                    window.dt_cust.ajax.reload();
                    Swal.fire({
                        title: 'SUCCESS!',
                        text: "Nama Instansi berhasil diedit",
                        icon: 'success',
                    })
                },
                error: function(data) {
                    console.log('Error:', data);
                    Swal.fire({
                        title: 'FAILED!',
                        text: "Mohon maaf edit nama instansi gagal",
                        icon: 'error',
                    })
                    $('#submit-edit-cust-company').html('Error');
                }
            });
        });
    });
</script>
