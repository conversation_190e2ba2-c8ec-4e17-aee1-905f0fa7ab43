<script src="{{ url('js/simple.money.format.js') }}"></script>
<script>
    $(document).ready(function () {
        //datatable page cust
        const dataTableLanguage = {
            loadingRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div class="spinner-grow text-warning" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>`,
            emptyTable: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
            zeroRecords: `
                        <div class="d-flex justify-content-center align-items-center">
                            <div role="status">
                                <span class="text-danger">Data tidak ditemukan</span>
                            </div>
                        </div>`,
        };

        //filter page cust
        const filterSearch = document.querySelector('[data-kt-docs-table-filter="search-kendala"]')
        filterSearch.addEventListener('keyup', function (e) {
            window.throttleDTSearch(window.dt_kendala, e.target.value)
        })

        // Select filter options
        const filterKendala = document.querySelectorAll('[data-kt-docs-table-filter="solusi"] [name="solusi"]')
        const resetButton = document.querySelector('[data-kt-docs-table-filter="reset"]')
        const filterButton = document.querySelector('[data-kt-docs-table-filter="filter-kendala"]')

        resetButton.addEventListener('click', function() {
            document.querySelector('[data-kt-docs-table-filter="solusi"] [name="solusi"][value="all"]').checked = true
            let solusi = ''
            window.dt_kendala.search(solusi).draw()
        })

        // Filter datatable on submit
        filterButton.addEventListener('click', function () {
            // Get filter values
            let solusi = ''

            // Get payment value
            filterKendala.forEach(r => {
                if (r.checked) {
                    solusi = r.value
                }

                // Reset payment value if "All" is selected
                if (solusi === 'all') {
                    solusi = ''
                }
            })

            // Filter datatable --- official docs reference: https://datatables.net/reference/api/search()
            window.dt_kendala.search(solusi).draw()
        })

        //modal-edit-order
        $('body').on('click', '.fpm', function (event) {
            event.preventDefault();
            var sko_key = $(this).data('key');
            $.get("{{ url('crm/show_fpm/') }}/" + sko_key, function (data) {
                $('#modal_fpm').modal('show');
                $('#id_order_fpm').val(data[0].id_order);
                $('#order_key_fpm').val(data[0].order_key);
                $('#sko_key_fpm').val(data[0].sko_key);
                $('#id_customer_order_fpm').val(data[0].nama);
                $('#kode_order_fpm').val(data[0].kode_order);
                $('#sko_fpm').val(data[0].sko);
                var spesifikasi_fpm = data[0].lp_panjang + 'X' + data[0].lp_lebar + ', ' +
                    data[0].jenis_kertas + ', ' +
                    data[0].gramasi + ', ' +
                    data[0].laminasi + ', ' +
                    data[0].sisi_laminasi + ', ' +
                    data[0].finishing + ', ' +
                    data[0].jumlah_produk + ' pcs, ' +
                    data[0].notes;
                $('#spesifikasi_fpm').val(spesifikasi_fpm);
                $('#name_fpm').val(data[0].name);
                $('#tgl_masalah_fpm').val(data[0].tgl_masalah);
                $('#kategori_masalah_fpm').find('option[value="' + data[0].kategori_masalah + '"]').prop('selected', true);
                $('#detail_masalah_fpm').val(data[0].detail_masalah);
                $('#solusi_fpm').val(data[0].solusi);
                if (data[0].id_fpms !== null) {
                    $('#solusi').show();
                } else {
                    $('#solusi').hide();
                }

                if(data[0].solusi !== null) {
                    $('#solusi-chosen').show();
                    $('#solusi').hide();
                }else{
                    $('#solusi-chosen').hide();
                }

                $('#solusi-pu').click(function (e) {
                    window.location.replace("{{ url('crm/form_solusi') }}?sko_key="+data[0].sko_key+"&solusi=produksi-ulang");
                });
                $('#solusi-nego').click(function (e) {
                    window.location.replace("{{ url('crm/form_solusi') }}?sko_key="+data[0].sko_key+"&solusi=negosiasi");
                });
                $('#solusi-perbaikan').click(function (e) {
                    window.location.replace("{{ url('crm/form_solusi') }}?sko_key="+data[0].sko_key+"&solusi=perbaikan");
                });
                
            })
        });

        //modal-edit-order
        $('body').on('click', '.solusi', function (event) {
            event.preventDefault();
            var sko_key = $(this).data('key');
            $.get("{{ url('crm/show_fpm/') }}/" + sko_key, function (data) {
                console.log(data[0]);
                $('#modal_solusi').modal('show');
                $('#id_order_solusi').val(data[0].id_order);
                $('#order_key_solusi').val(data[0].order_key);
                console.log(data[0]);
                $('#sko_key_solusi').val(data[0].sko_key);
                $('#id_customer_order_solusi').val(data[0].nama);
                $('#kode_order_solusi').val(data[0].kode_order);
                $('#sko_solusi').val(data[0].sko);
                var spesifikasi_fpm = data[0].lp_panjang + 'X' + data[0].lp_lebar + ', ' +
                    data[0].jenis_kertas + ', ' +
                    data[0].gramasi + ', ' +
                    data[0].laminasi + ', ' +
                    data[0].sisi_laminasi + ', ' +
                    data[0].finishing + ', ' +
                    data[0].jumlah_produk + ' pcs, ' +
                    data[0].notes;
                $('#spesifikasi_solusi').val(spesifikasi_fpm);
                $('#name_solusi').val(data[0].name);
                $('#tgl_deadline_solusi').val(data[0].tgl_deadline_perbaikan);
                $('#solusi_solusi').val(data[0].solusi);
                $('#detail_solusi_solusi').val(data[0].detail_solusi);
                $('#cost_solusi').val(data[0].cost).simpleMoneyFormat();
                
            })
        });

        $('#process-fpm').click(function (e) {
            e.preventDefault();
            $.ajax({
                data: $('#add-fpm').serialize(),
                url: "{{ route('crm.form_fpm') }}",
                type: "POST",
                dataType: 'json',
                success: function (data) {
                    $('#add-fpm').trigger("reset");
                    setTimeout(function () {
                        self.$("#close-form-fpm").trigger("click");
                    }, 1200);
                    window.dt_kendala.ajax.reload();
                    $("#count_price_null").load(window.location.href + " #count_price_null");
                    $("#count_prioritas").load(window.location.href + " #count_prioritas");
                    Swal.fire({
                        title: 'SUCCESS!',
                        text: "FPM Berhasil diisi!",
                        icon: 'success',
                    })
                },
                error: function (data) {
                    console.log('Error:', data);
                    $('#process-fpm').html('Error');
                }
            });
        });

        $('#process-solusi').click(function (e) {
            e.preventDefault();
            $.ajax({
                data: $('#add-solusi').serialize(),
                url: "{{ route('crm.store_table_solusi') }}",
                type: "POST",
                dataType: 'json',
                success: function (data) {
                    $('#add-solusi').trigger("reset");
                    setTimeout(function () {
                        self.$("#close-form-solusi").trigger("click");
                    }, 1200);
                    window.dt_kendala.ajax.reload();
                    $("#count_price_null").load(window.location.href + " #count_price_null");
                    $("#count_prioritas").load(window.location.href + " #count_prioritas");
                    Swal.fire({
                        title: 'SUCCESS!',
                        text: "Solusi berhasil diubah!",
                        icon: 'success',
                    })
                },
                error: function (data) {
                    console.log('Error:', data);
                    $('#process-solusi').html('Error');
                }
            });
        });

        
            
    }); 
</script>
