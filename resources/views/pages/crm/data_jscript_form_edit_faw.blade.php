<script>
    const form = document.getElementById('form-edit-faw');

    var validator = FormValidation.formValidation(
            form,
            {
                fields: {
                    'waktu_produksi': {
                        validators: {
                            notEmpty: {
                                message: 'Waktu produksi wajib diisi!'
                            }
                        }
                    },
                    'tgl_deadline': {
                        validators: {
                            notEmpty: {
                                message: 'Tanggal deadline wajib diisi!'
                            }
                        }
                    },
                    'tgl_faw': {
                        validators: {
                            notEmpty: {
                                message: 'Tanggal FAW wajib diisi!'
                            }
                        }
                    },
                    'link_file_final': {
                        validators: {
                            notEmpty: {
                                message: 'Link final wajib diisi!'
                            }
                        }
                    }
                },

                plugins: {
                    trigger: new FormValidation.plugins.Trigger(),
                    bootstrap: new FormValidation.plugins.Bootstrap5({
                        rowSelector: '.fv-row',
                        eleInvalidClass: '',
                        eleValidClass: ''
                    }),
                    // Validate fields when clicking the Submit button
                    submitButton: new FormValidation.plugins.SubmitButton(),
                    // Submit the form when all fields are valid
                    defaultSubmit: new FormValidation.plugins.DefaultSubmit(),
                }
            }
        );

    $('#update-faw').click(function (e) {
            e.preventDefault();
            if(validator) {
                validator.validate().then(function (status) {
                    if (status == 'Valid') {
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "Customer berhasil ditambah",
                            icon: 'success',
                        })
                        return TRUE;
                    }if (status == 'Invalid'){
                        Swal.fire({
                            title: 'Failed!',
                            text: "Lengkapi kolom yang wajib diisi",
                            icon: 'error',
                        })
                    }
                });
            }
        });
</script>