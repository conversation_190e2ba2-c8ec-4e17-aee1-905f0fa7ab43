<link href = "https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/css/tempus-dominus.css"
    rel = "stylesheet" type = "text/css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/js/tempus-dominus.js"></script>
<script src="{{ url('js/simple.money.format.js') }}"></script>
<script>
    $(document).ready(function() {
        // SweetAlert Alert Example
        @if (session()->has('message'))
            function showAlert(title, message, type) {
                window.swal.fire({
                        title: "Response Message",
                        text: title,
                        icon: 'success',
                        type: type,
                        confirmButtonText: "OK",
                        html: message,
                        // cancelButtonText: "Cancel",
                        showCancelButton: false,
                    }).then(console.log)
                    .catch(console.error);
            }
            showAlert('{{ strtolower(session('message')['type']) }}', '{{ session('message')['text'] }}');
        @endif

        function chart_target(labels, data_target, data_realisasi, data_line) {
            // Define chart element
            var ctx = document.getElementById('kt_chartjs_1');

            // Chart config
            const config = {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                            label: 'CR (%)',
                            data: data_line,
                            borderColor: '#ffc700',
                            backgroundColor: '#ffc700',
                            type: 'line',
                            yAxisID: 'y2', // Use the second y-axis
                        },
                        {
                            label: 'Target',
                            data: data_target,
                            backgroundColor: '#E4620C',
                            stack: 'Stack 0',
                        },
                        {
                            label: 'Realisasi',
                            data: data_realisasi,
                            backgroundColor: '#50cd89',
                            stack: 'Stack 1',
                        },
                    ],
                },
                options: {
                    plugins: {
                        title: {
                            display: false,
                        },
                    },
                    responsive: true,
                    interaction: {
                        intersect: false,
                    },
                    scales: {
                        x: {
                            stacked: true,
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true, // Start the y-axis from zero
                            suggestedMax: 100, // Set the maximum value to 100%
                        },
                        y2: {
                            type: 'linear',
                            position: 'right',
                            beginAtZero: true, // Start the y-axis from zero
                            suggestedMax: 100, // Set the maximum value to 100%
                        },
                    },
                },
            };

            return {
                'ctx' : ctx,
                'config' : config
            }

        }

        $(function() {
            new tempusDominus.TempusDominus(document.getElementById('tahun_target'), {
                display: {
                    viewMode: 'calendar',
                    toolbarPlacement: 'top',
                    components: {
                        calendar: true,
                        date: false,
                        month: false,
                        year: true,
                        decades: true,
                        clock: false,
                        hours: false,
                        minutes: false,
                        seconds: false
                    },
                    theme: 'light'
                }
            })
        });

        // Chart labels
        var labels = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September',
            'October', 'November', 'December'
        ];

        // Chart data
        var data_target = [
            {{ $annual_target->jan ?? 0}},
            {{ $annual_target->feb ?? 0}},
            {{ $annual_target->mar ?? 0}},
            {{ $annual_target->apr ?? 0}},
            {{ $annual_target->mei ?? 0}},
            {{ $annual_target->jun ?? 0}},
            {{ $annual_target->jul ?? 0}},
            {{ $annual_target->aug ?? 0}},
            {{ $annual_target->sep ?? 0}},
            {{ $annual_target->okt ?? 0}},
            {{ $annual_target->nov ?? 0}},
            {{ $annual_target->des ?? 0}}
        ];

        var data_realisasi = [
            {{ $annual_realisasi->jan ?? 0 }},
            {{ $annual_realisasi->feb ?? 0 }},
            {{ $annual_realisasi->mar ?? 0 }},
            {{ $annual_realisasi->apr ?? 0 }},
            {{ $annual_realisasi->mei ?? 0 }},
            {{ $annual_realisasi->jun ?? 0 }},
            {{ $annual_realisasi->jul ?? 0 }},
            {{ $annual_realisasi->aug ?? 0 }},
            {{ $annual_realisasi->sep ?? 0 }},
            {{ $annual_realisasi->okt ?? 0 }},
            {{ $annual_realisasi->nov ?? 0 }},
            {{ $annual_realisasi->des ?? 0 }}
        ];

        var data_line = data_realisasi.map((value, index) => {
            let targetValue = data_target[index];
            let cr = (value / targetValue) * 100
            if (isFinite(cr) && cr > 0) {
                return cr
            } else {
                return 0
            }
        });

        let chart_val = chart_target(labels, data_target, data_realisasi, data_line)
        
        // Create the chart
        var myChart = new Chart(chart_val.ctx, chart_val.config);

        jQuery.extend(jQuery.fn.dataTableExt.oSort, {
            "total-harga-asc": function(a, b) {
                a = parseFloat(a);
                b = parseFloat(b);
                return a - b;
            },

            "total-harga-desc": function(a, b) {
                a = parseFloat(a);
                b = parseFloat(b);
                return b - a;
            }
        });

        function table_detail_deal() {
            dt_onrepeat = $('#list_customer_repeat_onreq').DataTable({
                processing: true,
                serverSide: true,
                lengthChange: false,
                bPaginate: false,
                info: false,
                ajax: {
                    url: "{{ route('target.table_detail_deals') }}",
                    data: function(d) {
                        d.pic_sales = $('#pic_target').val();
                        d.year = $('#tahun_target').val();
                    }
                },
                columns: [{
                        data: 'nama',
                        name: 'nama'
                    },
                    {
                        data: 'cro',
                        name: 'cro'
                    },
                    {
                        data: 'sumber',
                        name: 'sumber',
                    },
                    {
                        data: 'grading',
                        name: 'grading'
                    },
                    {
                        data: 'total_harga',
                        name: 'total_harga',
                        orderable: true,
                        render: function(data, type, full, meta) {
                            if (type === 'display' || type === 'filter') {
                                return 'Rp' + parseFloat(data).toLocaleString();
                            }
                            return data;
                        }
                    },
                    {
                        data: 'modal_sales',
                        name: 'modal_sales',
                        orderable: true,
                        render: function (data, type, full, meta) {
                            if (type === 'display' || type === 'filter') {
                                return 'Rp' + parseFloat(data).toLocaleString();
                            }
                            return data;
                        }
                    },
                ],
                columnDefs: [{
                        type: 'total-harga',
                        targets: 4
                    } // Use the custom sorting for total_harga column
                ],
                order: [
                    [4, 'desc']
                ],
            });
        }

        table_detail_deal()

        $('#sumber').on('change', function(e) {
            if (e.target.value.length < 1) {
                dt_onrepeat.columns(2).search(e.target.value).draw()
            } else {
                dt_onrepeat.columns(2).search("^" + e.target.value + "$", true, false).draw()
            }
        })

        $('#tahun_target').on("change", function(event) {
            dt_onrepeat.destroy();
            table_detail_deal()
            $('#sumber').val("")
        })

        function updateProgressBar(month_progress, month_real, month_target) {
            // Update the width of the progress bar
            $('#myProgressBar').css('width', month_progress + '%');

            // Update other attributes as needed
            $('#myProgressBar').attr('aria-valuenow', month_real);
            $('#myProgressBar').attr('aria-valuemax', month_target);
        }

        $('#pic_target, #tahun_target, #sumber').on("change", function(event) {
            event.preventDefault();
            myChart.destroy();
            $.ajax({
                data: $('#pic_target, #tahun_target, #sumber').serialize(),
                url: "{{ route('target.get_target') }}",
                success: function(data) {
                    total_target = Number(data[0].total_target);
                    $('.total_customer').html(data[0].total_customer);
                    $('.total_target').html(total_target.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                    $('.total_realisasi').html(data[0].total_realisasi.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                    $('#month_target').html(data[0].this_month_year_target.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                    $('#month_real').html(data[0].this_month_year_realisasi.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                    updateProgressBar(data[0].this_month_year_progress, data[0].this_month_year_realisasi, data[0].this_month_year_target)

                    // Chart data
                    data_target = [
                        data[0].annual_target.jan, 
                        data[0].annual_target.feb, 
                        data[0].annual_target.mar, 
                        data[0].annual_target.apr, 
                        data[0].annual_target.mei, 
                        data[0].annual_target.jun, 
                        data[0].annual_target.jul, 
                        data[0].annual_target.aug, 
                        data[0].annual_target.sep, 
                        data[0].annual_target.okt, 
                        data[0].annual_target.nov, 
                        data[0].annual_target.des
                    ];

                    data_realisasi = [
                        data[0].annual_realisasi.jan, data[0].annual_realisasi.feb,
                        data[0].annual_realisasi.mar, data[0].annual_realisasi.apr,
                        data[0].annual_realisasi.mei, data[0].annual_realisasi.jun,
                        data[0].annual_realisasi.jul, data[0].annual_realisasi.aug,
                        data[0].annual_realisasi.sep, data[0].annual_realisasi.okt,
                        data[0].annual_realisasi.nov, data[0].annual_realisasi.des
                    ];

                    data_line = data_realisasi.map((value, index) => {
                        const targetValue = data_target[index];
                        let cr = (value / targetValue) * 100
                        if (isFinite(cr) && cr > 0) {
                            return cr
                        } else {
                            return 0
                        }
                    });

                    let chart_val = chart_target(labels, data_target, data_realisasi, data_line)
        
                    // Create the chart
                    myChart = new Chart(chart_val.ctx, chart_val.config);

                    $('#sum_total_price').html('Rp' + Number(data[0].list_customer_repeat)
                        .toLocaleString('en-EN'))
                },
                error: function(data) {
                    console.log('Error:', data);
                }
            });
        });

    });
</script>
