<link href = "https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/css/tempus-dominus.css"
    rel = "stylesheet" type = "text/css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/js/tempus-dominus.js"></script>
<script src="{{ url('js/simple.money.format.js') }}"></script>
<script>
    $(document).ready(function () {
        $(function() {
            new tempusDominus.TempusDominus(document.getElementById('tahun_target'), {
                display: {
                    viewMode: 'calendar',
                    toolbarPlacement: 'top',
                    components: {
                        calendar: true,
                        date: false,
                        month: false,
                        year: true,
                        decades: true,
                        clock: false,
                        hours: false,
                        minutes: false,
                        seconds: false
                    },
                    theme: 'light'
                }
            })
        });

        $('#edit_target').on("click", function(e){
            var param = $('#pic option:selected').val();
            var url = "{{ route('target.edit_target').'?pic=' }}"+param;
            $.ajax({
                    data: $('#pic').serialize(),
                    url: "{{ route('target.edit_target') }}",
                    success: function (data) {
                        location.replace(url);
                    },
                    error: function (data) {
                        console.log('Error:', data);
                    }
            })
        })

        $('#pic, #tahun_target').on("change", function(event){
            event.preventDefault();

            if ($('#pic').val() != "All") {
                $("#edit_target").attr('disabled', false)
            } else {
                $("#edit_target").attr('disabled', true)
            }

            $.ajax({
                    data: $('#pic, #tahun_target').serialize(),
                    url: "{{ route('target.get_detail') }}",
                    success: function (data) {
                        if(data[0].data_target.jan == null){
                            target_jan = 0;
                        }else{
                            target_jan = data[0].data_target.jan;
                        }

                        if(data[0].data_target.feb == null){
                            target_feb = 0;
                        }else{
                            target_feb = data[0].data_target.feb;
                        }

                        if(data[0].data_target.mar == null){
                            target_mar = 0;
                        }else{
                            target_mar = data[0].data_target.mar;
                        }

                        if(data[0].data_target.apr == null){
                            target_apr = 0;
                        }else{
                            target_apr = data[0].data_target.apr;
                        }

                        if(data[0].data_target.mei == null){
                            target_mei = 0;
                        }else{
                            target_mei = data[0].data_target.mei;
                        }

                        if(data[0].data_target.jun == null){
                            target_jun = 0;
                        }else{
                            target_jun = data[0].data_target.jun;
                        }

                        if(data[0].data_target.jul == null){
                            target_jul = 0;
                        }else{
                            target_jul = data[0].data_target.jul;
                        }

                        if(data[0].data_target.aug == null){
                            target_aug = 0;
                        }else{
                            target_aug = data[0].data_target.aug;
                        }

                        if(data[0].data_target.sep == null){
                            target_sep = 0;
                        }else{
                            target_sep = data[0].data_target.sep;
                        }

                        if(data[0].data_target.okt == null){
                            target_okt = 0;
                        }else{
                            target_okt = data[0].data_target.okt;
                        }

                        if(data[0].data_target.nov == null){
                            target_nov = 0;
                        }else{
                            target_nov = data[0].data_target.nov;
                        }

                        if(data[0].data_target.des == null){
                            target_des = 0;
                        }else{
                            target_des = data[0].data_target.des;
                        }

                        if(data[0].data_order.jan == null){
                            order_jan = 0;
                        }else{
                            order_jan = data[0].data_order.jan;
                        }

                        if(data[0].data_order.feb == null){
                            order_feb = 0;
                        }else{
                            order_feb = data[0].data_order.feb;
                        }

                        if(data[0].data_order.mar == null){
                            order_mar = 0;
                        }else{
                            order_mar = data[0].data_order.mar;
                        }

                        if(data[0].data_order.apr == null){
                            order_apr = 0;
                        }else{
                            order_apr = data[0].data_order.apr;
                        }

                        if(data[0].data_order.mei == null){
                            order_mei = 0;
                        }else{
                            order_mei = data[0].data_order.mei;
                        }

                        if(data[0].data_order.junn == null){
                            order_jun = 0;
                        }else{
                            order_jun = data[0].data_order.jun;
                        }

                        if(data[0].data_order.jul == null){
                            order_jul = 0;
                        }else{
                            order_jul = data[0].data_order.jul;
                        }

                        if(data[0].data_order.aug == null){
                            order_aug = 0;
                        }else{
                            order_aug = data[0].data_order.aug;
                        }

                        if(data[0].data_order.sep == null){
                            order_sep = 0;
                        }else{
                            order_sep = data[0].data_order.sep;
                        }

                        if(data[0].data_order.okt == null){
                            order_okt = 0;
                        }else{
                            order_okt = data[0].data_order.okt;
                        }

                        if(data[0].data_order.nov == null){
                            order_nov = 0;
                        }else{
                            order_nov = data[0].data_order.nov;
                        }

                        if(data[0].data_order.des == null){
                            order_des = 0;
                        }else{
                            order_des = data[0].data_order.des;
                        }

                        $('#count_total_customer_1').html(data[0].count_total_customer_1)
                        $('#count_total_customer_2').html(data[0].count_total_customer_2)
                        $('#count_total_customer_3').html(data[0].count_total_customer_3)
                        $('#count_total_customer_4').html(data[0].count_total_customer_4)
                        $('#count_total_customer_5').html(data[0].count_total_customer_5)
                        $('#count_total_customer_6').html(data[0].count_total_customer_6)
                        $('#count_total_customer_7').html(data[0].count_total_customer_7)
                        $('#count_total_customer_8').html(data[0].count_total_customer_8)
                        $('#count_total_customer_9').html(data[0].count_total_customer_9)
                        $('#count_total_customer_10').html(data[0].count_total_customer_10)
                        $('#count_total_customer_11').html(data[0].count_total_customer_11)
                        $('#count_total_customer_12').html(data[0].count_total_customer_12)
                        $('#data_target_jan').html(target_jan.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_target_feb').html(target_feb.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_target_mar').html(target_mar.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_target_apr').html(target_apr.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_target_mei').html(target_mei.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_target_jun').html(target_jun.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_target_jul').html(target_jul.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_target_aug').html(target_aug.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_target_sep').html(target_sep.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_target_okt').html(target_okt.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_target_nov').html(target_nov.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_target_des').html(target_des.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_cr_jan').html(target_jan != 0 ? ((order_jan/target_jan) * 100).toFixed() + '%' : 0 + '%');
                        $('#data_cr_feb').html(target_feb != 0 ? ((order_feb/target_feb) * 100).toFixed() + '%' : 0 + '%');
                        $('#data_cr_mar').html(target_mar != 0 ? ((order_mar/target_mar) * 100).toFixed() + '%' : 0 + '%');
                        $('#data_cr_apr').html(target_apr != 0 ? ((order_apr/target_apr) * 100).toFixed() + '%' : 0 + '%');
                        $('#data_cr_mei').html(target_mei != 0 ? ((order_mei/target_mei) * 100).toFixed() + '%' : 0 + '%');
                        $('#data_cr_jun').html(target_jun != 0 ? ((order_jun/target_jun) * 100).toFixed() + '%' : 0 + '%');
                        $('#data_cr_jul').html(target_jul != 0 ? ((order_jul/target_jul) * 100).toFixed() + '%' : 0 + '%');
                        $('#data_cr_aug').html(target_aug != 0 ? ((order_aug/target_aug) * 100).toFixed() + '%' : 0 + '%');
                        $('#data_cr_sep').html(target_sep != 0 ? ((order_sep/target_sep) * 100).toFixed() + '%' : 0 + '%');
                        $('#data_cr_okt').html(target_okt != 0 ? ((order_okt/target_okt) * 100).toFixed() + '%' : 0 + '%');
                        $('#data_cr_nov').html(target_nov != 0 ? ((order_nov/target_nov) * 100).toFixed() + '%' : 0 + '%');
                        $('#data_cr_des').html(target_des != 0 ? ((order_des/target_des) * 100).toFixed() + '%' : 0 + '%');
                        $('#data_order_jan').html(order_jan.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_order_feb').html(order_feb.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_order_mar').html(order_mar.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_order_apr').html(order_apr.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_order_mei').html(order_mei.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_order_jun').html(order_jun.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_order_jul').html(order_jul.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_order_aug').html(order_aug.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_order_sep').html(order_sep.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_order_okt').html(order_okt.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_order_nov').html(order_nov.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        $('#data_order_des').html(order_des.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                        
                        if(data[0].margin_1 < 0){
                            $('#margin_1').html(data[0].margin_1.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-success').addClass('text-danger');
                        }else{
                            $('#margin_1').html(data[0].margin_1.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-danger').addClass('text-success');
                        }

                        if(data[0].margin_2 < 0){
                            $('#margin_2').html(data[0].margin_2.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-success').addClass('text-danger');
                        }else{
                            $('#margin_2').html(data[0].margin_2.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-danger').addClass('text-success');
                        }

                        if(data[0].margin_3 < 0){
                            $('#margin_3').html(data[0].margin_3.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-success').addClass('text-danger');
                        }else{
                            $('#margin_3').html(data[0].margin_3.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-danger').addClass('text-success');
                        }

                        if(data[0].margin_4 < 0){
                            $('#margin_4').html(data[0].margin_4.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-success').addClass('text-danger');
                        }else{
                            $('#margin_4').html(data[0].margin_4.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-danger').addClass('text-success');
                        }

                        if(data[0].margin_5 < 0){
                            $('#margin_5').html(data[0].margin_5.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-success').addClass('text-danger');
                        }else{
                            $('#margin_5').html(data[0].margin_5.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-danger').addClass('text-success');
                        }

                        if(data[0].margin_6 < 0){
                            $('#margin_6').html(data[0].margin_6.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-success').addClass('text-danger');
                        }else{
                            $('#margin_6').html(data[0].margin_6.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-danger').addClass('text-success');
                        }

                        if(data[0].margin_7 < 0){
                            $('#margin_7').html(data[0].margin_7.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-success').addClass('text-danger');
                        }else{
                            $('#margin_7').html(data[0].margin_7.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-danger').addClass('text-success');
                        }

                        if(data[0].margin_8 < 0){
                            $('#margin_8').html(data[0].margin_8.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-success').addClass('text-danger');
                        }else{
                            $('#margin_8').html(data[0].margin_8.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-danger').addClass('text-success');
                        }

                        if(data[0].margin_9 < 0){
                            $('#margin_9').html(data[0].margin_9.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-success').addClass('text-danger');
                        }else{
                            $('#margin_9').html(data[0].margin_9.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-danger').addClass('text-success');
                        }

                        if(data[0].margin_10 < 0){
                            $('#margin_10').html(data[0].margin_10.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-success').addClass('text-danger');
                        }else{
                            $('#margin_10').html(data[0].margin_10.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-danger').addClass('text-success');
                        }

                        if(data[0].margin_11 < 0){
                            $('#margin_11').html(data[0].margin_11.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-success').addClass('text-danger');
                        }else{
                            $('#margin_11').html(data[0].margin_11.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-danger').addClass('text-success');
                        }

                        if(data[0].margin_12 < 0){
                            $('#margin_12').html(data[0].margin_12.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-success').addClass('text-danger');
                        }else{
                            $('#margin_12').html(data[0].margin_12.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")).removeClass('text-danger').addClass('text-success');
                        }
                        
                    },
                    error: function (data) {
                        console.log('Error:', data);
                    }
            })
        })

    });
</script>