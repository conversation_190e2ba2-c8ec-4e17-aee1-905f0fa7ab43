<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ url()->previous() }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">Riwayat Target</h3>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        @include('pages.target.data_jscript_details')
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">

                        <!--begin::Toolbar-->
                        <div class="d-flex flex-row-fluid justify-content-end gap-3">
                            @if(Auth::user()->roles == 'SALES SPV' || Auth::user()->roles == 'SUPERADMIN')
                            <div class="w-100 mw-200px ">
                                <button class="btn btn-my-primary text-white w-100" id="edit_target" hidden>Ubah Target</button>
                            </div>
                            <div class="w-100 mw-200px">
                                <div class="col input-group mb-3" id="time_target_form">
                                    <input type="text" class="form-control" name="tahun_target" value="{{ date('Y') }}" id="tahun_target" required />
                                    <span class="input-group-text" id="basic-addon2">
                                        <i class="fa fa-calendar"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="w-100 mw-200px">
                                <select class="form-select form-select-solid" id="pic" name="pic" >
                                    <option hidden>PIC</option>
                                    <option value="All">All</option>
                                    @foreach ($pic as $list)
                                    <option value="{{ $list->id }}">{{ $list->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            @endif
                        </div>
                        <!--end::Toolbar-->
                    </div>
                    <table id="datatable_target"
                        class="table align-middle table-row-bordered compact text-nowrap dataTable">
                        <thead>
                            <tr class="fw-bold">
                                <th>No</th>
                                <th>Bulan</th>
                                <th>Total Customer</th>
                                <th>Target Bulanan</th>
                                <th>Realisasi Bulanan</th>
                                <th>CR (Omzet)</th>
                                <th>Margin</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>January</td>
                                <td id="count_total_customer_1">{{ $count_total_customer_1 }}</td>
                                <td id="data_target_jan">Rp{{ number_format($data_target->jan,0,'',',') }}</td>
                                <td id="data_order_jan">Rp{{ number_format($data_order->jan,0,'',',') }}</td>
                                <td id="data_cr_jan">{{ $data_target->jan != 0 ? number_format(($data_order->jan/$data_target->jan) * 100, 0) : 0 }}%</td>
                                <td id="margin_1">
                                    <span class="@if($margin_1 < 0) text-danger @else text-success @endif">Rp{{ number_format($margin_1,0,'',',') }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>February</td>
                                <td id="count_total_customer_2">{{ $count_total_customer_2 }}</td>
                                <td id="data_target_feb">Rp{{ number_format($data_target->feb,0,'',',') }}</td>
                                <td id="data_order_feb">Rp{{ number_format($data_order->feb,0,'',',') }}</td>
                                <td id="data_cr_feb">{{ $data_target->feb != 0 ? number_format(($data_order->feb/$data_target->feb) * 100, 0) : 0 }}%</td>
                                <td id="margin_2">
                                    <span class="@if($margin_2 < 0) text-danger @else text-success @endif">Rp{{ number_format($margin_2,0,'',',') }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>March</td>
                                <td id="count_total_customer_3">{{ $count_total_customer_3 }}</td>
                                <td id="data_target_mar">Rp{{ number_format($data_target->mar,0,'',',') }}</td>
                                <td id="data_order_mar">Rp{{ number_format($data_order->mar,0,'',',') }}</td>
                                <td id="data_cr_mar">{{ $data_target->mar != 0 ? number_format(($data_order->mar/$data_target->mar) * 100, 0) : 0 }}%</td>
                                <td id="margin_3">
                                    <span class="@if($margin_3 < 0) text-danger @else text-success @endif">Rp{{ number_format($margin_3,0,'',',') }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>April</td>
                                <td id="count_total_customer_4">{{ $count_total_customer_4 }}</td>
                                <td id="data_target_apr">Rp{{ number_format($data_target->apr,0,'',',') }}</td>
                                <td id="data_order_apr">Rp{{ number_format($data_order->apr,0,'',',') }}</td>
                                <td id="data_cr_apr">{{ $data_target->apr != 0 ? number_format(($data_order->apr/$data_target->apr) * 100, 0) : 0 }}%</td>
                                <td id="margin_4">
                                    <span class="@if($margin_4 < 0) text-danger @else text-success @endif">Rp{{ number_format($margin_4,0,'',',') }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>May</td>
                                <td id="count_total_customer_5">{{ $count_total_customer_5 }}</td>
                                <td id="data_target_mei">Rp{{ number_format($data_target->mei,0,'',',') }}</td>
                                <td id="data_order_mei">Rp{{ number_format($data_order->mei,0,'',',') }}</td>
                                <td id="data_cr_mei">{{ $data_target->mei != 0 ? number_format(($data_order->mei/$data_target->mei) * 100, 0) : 0 }}%</td>
                                <td id="margin_5">
                                    <span class="@if($margin_5 < 0) text-danger @else text-success @endif">Rp{{ number_format($margin_5,0,'',',') }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>June</td>
                                <td id="count_total_customer_6">{{ $count_total_customer_6 }}</td>
                                <td id="data_target_jun">Rp{{ number_format($data_target->jun,0,'',',') }}</td>
                                <td id="data_order_jun">Rp{{ number_format($data_order->jun,0,'',',') }}</td>
                                <td id="data_cr_jun">{{ $data_target->jun != 0 ? number_format(($data_order->jun / $data_target->jun) * 100, 0) : 0 }}%</td>
                                <td id="margin_6">
                                    <span class="@if($margin_6 < 0) text-danger @else text-success @endif">Rp{{ number_format($margin_6,0,'',',') }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td>July</td>
                                <td id="count_total_customer_7">{{ $count_total_customer_7 }}</td>
                                <td id="data_target_jul">Rp{{ number_format($data_target->jul,0,'',',') }}</td>
                                <td id="data_order_jul">Rp{{ number_format($data_order->jul,0,'',',') }}</td>
                                <td id="data_cr_jul">{{ $data_target->jul != 0 ? number_format(($data_order->jul/$data_target->jul) * 100, 0) : 0 }}%</td>
                                <td id="margin_7">
                                    <span class="@if($margin_7 < 0) text-danger @else text-success @endif">Rp{{ number_format($margin_7,0,'',',') }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>August</td>
                                <td id="count_total_customer_8">{{ $count_total_customer_8 }}</td>
                                <td id="data_target_aug">Rp{{ number_format($data_target->aug,0,'',',') }}</td>
                                <td id="data_order_aug">Rp{{ number_format($data_order->aug,0,'',',') }}</td>
                                <td id="data_cr_aug">{{ $data_target->aug != 0 ? number_format(($data_order->aug/$data_target->aug) * 100, 0) : 0 }}%</td>
                                <td id="margin_8">
                                    <span class="@if($margin_8 < 0) text-danger @else text-success @endif">Rp{{ number_format($margin_8,0,'',',') }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td>September</td>
                                <td id="count_total_customer_9">{{ $count_total_customer_9 }}</td>
                                <td id="data_target_sep">Rp{{ number_format($data_target->sep,0,'',',') }}</td>
                                <td id="data_order_sep">Rp{{ number_format($data_order->sep,0,'',',') }}</td>
                                <td id="data_cr_sep">{{ $data_target->sep != 0 ? number_format(($data_order->sep/$data_target->sep) * 100, 0) : 0 }}%</td>
                                <td id="margin_9">
                                    <span class="@if($margin_9 < 0) text-danger @else text-success @endif">Rp{{ number_format($margin_9,0,'',',') }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>October</td>
                                <td id="count_total_customer_10">{{ $count_total_customer_10 }}</td>
                                <td id="data_target_okt">Rp{{ number_format($data_target->okt,0,'',',') }}</td>
                                <td id="data_order_okt">Rp{{ number_format($data_order->okt,0,'',',') }}</td>
                                <td id="data_cr_okt">{{ $data_target->okt != 0 ? number_format(($data_order->okt/$data_target->okt) * 100, 0) : 0 }}%</td>
                                <td id="margin_10">
                                    <span class="@if($margin_10 < 0) text-danger @else text-success @endif">Rp{{ number_format($margin_10,0,'',',') }}</span>

                                </td>
                            </tr>
                            <tr>
                                <td>11</td>
                                <td>November</td>
                                <td id="count_total_customer_11">{{ $count_total_customer_11 }}</td>
                                <td id="data_target_nov">Rp{{ number_format($data_target->nov,0,'',',') }}</td>
                                <td id="data_order_nov">Rp{{ number_format($data_order->nov,0,'',',') }}</td>
                                <td id="data_cr_nov">{{ $data_target->nov != 0 ? number_format(($data_order->nov/$data_target->nov) * 100, 0) : 0 }}%</td>
                                <td id="margin_11">
                                    <span class="@if($margin_11 < 0) text-danger @else text-success @endif">Rp{{ number_format($margin_11,0,'',',') }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td>12</td>
                                <td>December</td>
                                <td id="count_total_customer_12">{{ $count_total_customer_12 }}</td>
                                <td id="data_target_des">Rp{{ number_format($data_target->des,0,'',',') }}</td>
                                <td id="data_order_des">Rp{{ number_format($data_order->des,0,'',',') }}</td>
                                <td id="data_cr_des">{{ $data_target->des != 0 ? number_format(($data_order->des/$data_target->des) * 100, 0) : 0 }}%</td>
                                <td id="margin_12">
                                    <span class="@if($margin_12 < 0) text-danger @else text-success @endif">Rp{{ number_format($margin_12,0,'',',') }}</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>