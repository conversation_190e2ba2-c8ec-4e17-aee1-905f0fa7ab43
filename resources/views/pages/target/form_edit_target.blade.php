<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ route('target') }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">Edit Target Baru</h3>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        @include('pages.target.data_jscript_form_add_target')
        {{-- @include('pages.crm.data_jscript_form_faw') --}}
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-body p-10">
                <form method="POST" id="form-target" action="{{ route('target.update_target') }}"
                    enctype="multipart/form-data">
                    @csrf
                    @method('POST')
                    <input type="hidden" name="id_target" value="{{ $target->id_target }}">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-10">
                                <label for="id_pic" class="required form-label">PIC</label>
                                <input type="text" class="form-control form-control-solid" readonly name="id_pic" value="{{ $pic->name }}" id="id_pic" required />
                            </div>
                            <div class="mb-10">
                                <label for="year_target" class="required form-label">Tahun Target</label>
                                <div class="input-group mb-5" id="time_target_form">
                                    <input type="text" class="form-control" value="{{ $target->year_target ? $target->year_target : '' }}" name="year_target" id="year_target" required />
                                    <span class="input-group-text" id="basic-addon2">
                                        <i class="fa fa-calendar"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="mb-10">
                                <div class="py-5">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-row-bordered border">
                                            <tbody>
                                                <tr>
                                                    <th class="fw-bold text-center align-middle">January</th>
                                                    <td><input type="text" name="target_01" value="{{ $target->target_01 ? $target->target_01 : '' }}" id="target_01" class="form-control text-end"></td>
                                                </tr>
                                                <tr>
                                                    <th class="fw-bold text-center align-middle">February</th>
                                                    <td><input type="text" name="target_02" value="{{ $target->target_02 ? $target->target_02 : '' }}" id="target_02" class="form-control text-end"></td>
                                                </tr>
                                                <tr>
                                                    <th class="fw-bold text-center align-middle">March</th>
                                                    <td><input type="text" name="target_03" value="{{ $target->target_03 ? $target->target_03 : '' }}" id="target_03" class="form-control text-end"></td>
                                                </tr>
                                                <tr>
                                                    <th class="fw-bold text-center align-middle">April</th>
                                                    <td><input type="text" name="target_04" value="{{ $target->target_04 ? $target->target_04 : '' }}" id="target_04" class="form-control text-end"></td>
                                                </tr>
                                                <tr>
                                                    <th class="fw-bold text-center align-middle">May</th>
                                                    <td><input type="text" name="target_05" value="{{ $target->target_05 ? $target->target_05 : '' }}" id="target_05" class="form-control text-end"></td>
                                                </tr>
                                                <tr>
                                                    <th class="fw-bold text-center align-middle">June</th>
                                                    <td><input type="text" name="target_06" value="{{ $target->target_06 ? $target->target_06 : '' }}" id="target_06" class="form-control text-end"></td>
                                                </tr>
                                                <tr>
                                                    <th class="fw-bold text-center align-middle">July</th>
                                                    <td><input type="text" name="target_07" value="{{ $target->target_07 ? $target->target_07 : '' }}" id="target_07" class="form-control text-end"></td>
                                                </tr>
                                                <tr>
                                                    <th class="fw-bold text-center align-middle">August</th>
                                                    <td><input type="text" name="target_08" value="{{ $target->target_08 ? $target->target_08 : '' }}" id="target_08" class="form-control text-end"></td>
                                                </tr>
                                                <tr>
                                                    <th class="fw-bold text-center align-middle">September</th>
                                                    <td><input type="text" name="target_09" value="{{ $target->target_09 ? $target->target_09 : '' }}" id="target_09" class="form-control text-end"></td>
                                                </tr>
                                                <tr>
                                                    <th class="fw-bold text-center align-middle">October</th>
                                                    <td><input type="text" name="target_10" value="{{ $target->target_10 ? $target->target_10 : '' }}" id="target_10" class="form-control text-end"></td>
                                                </tr>
                                                <tr>
                                                    <th class="fw-bold text-center align-middle">November</th>
                                                    <td><input type="text" name="target_11" value="{{ $target->target_11 ? $target->target_11 : '' }}" id="target_11" class="form-control text-end"></td>
                                                </tr>
                                                <tr>
                                                    <th class="fw-bold text-center align-middle">December</th>
                                                    <td><input type="text" name="target_12" value="{{ $target->target_12 ? $target->target_12 : '' }}" id="target_12" class="form-control text-end"></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <input type="submit" form="form-target" value="Tambah" id="store-target"
                        class="btn my-primary text-white float-end">
                </form>
            </div>
        </div>
    </div>


</x-app-layout>
