<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ route('target') }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">Buat Target Baru</h3>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        @include('pages.target.data_jscript_form_add_target')
        {{-- @include('pages.crm.data_jscript_form_faw') --}}
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-body p-10">
                <form method="POST" id="form-target" action="{{ route('target.store_target') }}"
                    enctype="multipart/form-data">
                    @csrf
                    @method('POST')
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-10">
                                <label for="id_pic" class="required form-label">PIC</label>
                                <select name="id_pic" id="id_pic" class="form-select" required>
                                    <option value="" selected disabled>-- Silahkan pilih PIC</option>
                                    @foreach ($pic as $nama)
                                    <option value="{{ $nama->id }}">{{ $nama->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="mb-10">
                                <label for="year_target" class="required form-label">Tahun Target</label>
                                <div class="input-group mb-5" id="time_target_form">
                                    <input type="text" class="form-control" autocomplete="off" name="year_target" id="year_target" required />
                                    <span class="input-group-text" id="basic-addon2">
                                        <i class="fa fa-calendar"></i>
                                    </span>
                                </div>
                            </div>
                            <div class="mb-10 p-3 row">
                                @foreach(\Carbon\CarbonPeriod::create(now()->firstOfYear(), '1 month', now()) as $date)
                                    <button 
                                        class="btn btn-secondary mb-3 text-white text-dark" 
                                        type="button" 
                                        onclick="showCollapse('{{ $date->format('F') }}_section')">{{ $date->format('F') }}
                                    </button>
                                    <div class="collapse" id="{{ $date->format('F') }}_section">
                                        <div class="mb-10">
                                            <div class="table-responsive">
                                                <table class="table table-striped table-row-bordered border">
                                                    <tbody>
                                                        @foreach ($sumber as $list)
                                                            <tr>
                                                                <td class="fw-bold text-center align-middle">{{ $list->sumber }}</td>
                                                                <td class="d-flex justify-content-center">
                                                                    <div class="input-group w-75">
                                                                        <span class="input-group-text">Rp.</span>
                                                                        <input 
                                                                            type="text" 
                                                                            placeholder="Jumlah Target {{ $list->sumber }}" 
                                                                            name="{{ $list->id_sumber }}_{{ $date->format('F') }}" 
                                                                            id="target_{{ $list->id_sumber }}_{{ $date->format('F') }}" 
                                                                            oninput="total_target('target_{{ $list->id_sumber }}_{{ $date->format('F') }}', '{{ $date->format('F') }}')" 
                                                                            class="form-control"
                                                                        >
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                    <tr class="mt-3 bg-success">
                                                        <th class="fw-bold text-center align-middle">Total Target</th>
                                                        <td class="d-flex justify-content-center">
                                                            <div class="input-group w-75">
                                                                <span class="input-group-text">Rp.</span>
                                                                <input 
                                                                    type="text" 
                                                                    name="total_target_{{ $date->format('m') }}" 
                                                                    id="total_target_{{ $date->format('F') }}" 
                                                                    class="form-control"
                                                                >
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr class="mt-3 bg-success">
                                                        <th class="fw-bold text-center align-middle">Total Hari</th>
                                                        <td class="d-flex justify-content-center">
                                                            <div class="input-group w-75">
                                                            <input 
                                                                type="number" 
                                                                name="total_day_{{ $date->format('m') }}" 
                                                                id="total_day_{{ $date->format('F') }}" 
                                                                value="{{ \Carbon\Carbon::createFromFormat('Y-m', (old('year_target') ?? now()->year) . '-' . $date->format('m'))->daysInMonth }}"
                                                                oninput="total_target('target_{{ $list->id_sumber }}_{{ $date->format('F') }}', '{{ $date->format('F') }}')" 
                                                                class="form-control"
                                                            >
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    <tr class="mt-3 bg-success">
                                                        <th class="fw-bold text-center align-middle">Total Per Hari</th>
                                                        <td class="d-flex justify-content-center">
                                                            <div class="input-group w-75">
                                                                <span class="input-group-text">Rp.</span>
                                                                <input 
                                                                    type="text" 
                                                                    name="total_per_hari_{{ $date->format('m') }}" 
                                                                    id="total_per_hari_{{ $date->format('F') }}" 
                                                                    class="form-control"
                                                                >
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    <input type="submit" form="form-target" value="Submit" id="store-target"
                        class="btn my-primary text-white float-end">
                </form>
            </div>
            
            
            
            {{-- <div class="accordion" id="accordionExample">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingOne">
                        <button class="accordion-button text-white " type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                            Januari
                        </button>
                    </h2>
                    <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionExample">
                        <div class="accordion-body">
                            <strong>This is the first item's accordion body.</strong> It is shown by default, until the collapse plugin adds the appropriate classes that we use to style each element. These classes control the overall appearance, as well as the showing and hiding via CSS transitions. You can modify any of this with custom CSS or overriding our default variables. It's also worth noting that just about any HTML can go within the <code>.accordion-body</code>, though the transition does limit overflow.
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingTwo">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                        Accordion Item #2
                        </button>
                    </h2>
                    <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#accordionExample">
                        <div class="accordion-body">
                            <strong>This is the second item's accordion body.</strong> It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. These classes control the overall appearance, as well as the showing and hiding via CSS transitions. You can modify any of this with custom CSS or overriding our default variables. It's also worth noting that just about any HTML can go within the <code>.accordion-body</code>, though the transition does limit overflow.
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingThree">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                            Accordion Item #3
                        </button>
                    </h2>
                    <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#accordionExample">
                        <div class="accordion-body">
                            <strong>This is the third item's accordion body.</strong> It is hidden by default, until the collapse plugin adds the appropriate classes that we use to style each element. These classes control the overall appearance, as well as the showing and hiding via CSS transitions. You can modify any of this with custom CSS or overriding our default variables. It's also worth noting that just about any HTML can go within the <code>.accordion-body</code>, though the transition does limit overflow.
                        </div>
                    </div>
                </div>
            </div> --}}
        </div>
    </div>


</x-app-layout>
