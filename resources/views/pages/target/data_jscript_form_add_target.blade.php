<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/css/tempus-dominus.css" rel="stylesheet" type="text/css" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/6.2.4/js/tempus-dominus.js"></script>
<script src="{{ url('js/simple.money.format.js') }}"></script>
<script>
    $(function() {
        new tempusDominus.TempusDominus(document.getElementById('year_target'),
        {
            display: {
            viewMode: 'calendar',
            toolbarPlacement: 'top',
            components: {
                calendar: true,
                date: false,
                month: false,
                year: true,
                decades: true,
                clock: false,
                hours: false,
                minutes: false,
                seconds: false
            },
            theme: 'light'
            }
        })
    });

    $('#target_01').simpleMoneyFormat();
    $('#target_02').simpleMoneyFormat();
    $('#target_03').simpleMoneyFormat();
    $('#target_04').simpleMoneyFormat();
    $('#target_05').simpleMoneyFormat();
    $('#target_06').simpleMoneyFormat();
    $('#target_07').simpleMoneyFormat();
    $('#target_08').simpleMoneyFormat();
    $('#target_09').simpleMoneyFormat();
    $('#target_10').simpleMoneyFormat();
    $('#target_11').simpleMoneyFormat();
    $('#target_12').simpleMoneyFormat();

    function showCollapse(target) {
        var collapse = $(`#${target}`);
        var isCollapsed = !collapse.hasClass('show');

        if (isCollapsed) {
            collapse.collapse('show');
        } else {
            collapse.collapse('hide');
        }
        
    }

    function total_target(id, month) {
        $(`#${id}`).simpleMoneyFormat();
        let target_01 = +$(`#target_1_${month}`).val().replace(/,/g, '')
        let target_02 = +$(`#target_2_${month}`).val().replace(/,/g, '')
        let target_03 = +$(`#target_3_${month}`).val().replace(/,/g, '')
        let target_04 = +$(`#target_4_${month}`).val().replace(/,/g, '')
        let target_05 = +$(`#target_5_${month}`).val().replace(/,/g, '')
        let target_06 = +$(`#target_6_${month}`).val().replace(/,/g, '')
        let target_07 = +$(`#target_7_${month}`).val().replace(/,/g, '')
        let total = target_01 + target_02 + target_03 + target_04 + target_05 + target_06 + target_07;
        $(`#total_target_${month}`).val(total)
        $(`#total_target_${month}`).simpleMoneyFormat();
        let total_per_hari = Math.round(total / $(`#total_day_${month}`).val());
        $(`#total_per_hari_${month}`).val(total_per_hari)
        $(`#total_per_hari_${month}`).simpleMoneyFormat();
    }

    function searh_target_sumber(data, id, month) {
        const result = data.find(item => item.id_sumber === id && item.month === month);
        
        if (result) {
            return result.target_sumber
        }
    }

    function searh_target_sales(data, id, month, year) {
        // console.log(data, id, month, year)
        // Check if data exists and is an array
        if (!data || !Array.isArray(data)) {
            console.log('Data is undefined or not an array');
            return;
        }

        // Convert parameters to match data types
        id = parseInt(id);
        month = month.toString();
        year = parseInt(year);

        const result = data.find(item => 
            parseInt(item.user_id) === id && 
            item.month === month && 
            parseInt(item.year) === year
        );

        if (result) {
            return result.target_day;
        }
        return 0; // Return default value if no match found
        console.log(result)
        // if (result) {
            // console.log(result.target_day)
            // return result
        // }
    }
    
    $('#id_pic, #year_target').on("change", function(event){
        var id_pic = $('#id_pic').val();
        var year = $('#year_target').val();
        if ($('#id_pic').val() && $('#year_target').val()) {
            $.ajax({
                data: $('#id_pic, #year_target').serialize(),
                url: "{{ route('target.get_current_target') }}",
                success: function (data) {
                    // January
                    $(`#target_1_January`).val(searh_target_sumber(data[0].sumber_target, 1, 1)).simpleMoneyFormat();
                    $(`#target_2_January`).val(searh_target_sumber(data[0].sumber_target, 2, 1)).simpleMoneyFormat();
                    $(`#target_3_January`).val(searh_target_sumber(data[0].sumber_target, 3, 1)).simpleMoneyFormat();
                    $(`#target_4_January`).val(searh_target_sumber(data[0].sumber_target, 4, 1)).simpleMoneyFormat();
                    $(`#target_5_January`).val(searh_target_sumber(data[0].sumber_target, 5, 1)).simpleMoneyFormat();
                    $(`#target_6_January`).val(searh_target_sumber(data[0].sumber_target, 6, 1)).simpleMoneyFormat();
                    $(`#target_7_January`).val(searh_target_sumber(data[0].sumber_target, 7, 1)).simpleMoneyFormat();
                    $(`#total_target_January`).val(data[0].total_target.jan).simpleMoneyFormat();
                    $(`#total_per_hari_January`).val(searh_target_sales(data[0].sales_target, id_pic, '01', year)).simpleMoneyFormat();
                    // February
                    $(`#target_1_February`).val(searh_target_sumber(data[0].sumber_target, 1, 2)).simpleMoneyFormat();
                    $(`#target_2_February`).val(searh_target_sumber(data[0].sumber_target, 2, 2)).simpleMoneyFormat();
                    $(`#target_3_February`).val(searh_target_sumber(data[0].sumber_target, 3, 2)).simpleMoneyFormat();
                    $(`#target_4_February`).val(searh_target_sumber(data[0].sumber_target, 4, 2)).simpleMoneyFormat();
                    $(`#target_5_February`).val(searh_target_sumber(data[0].sumber_target, 5, 2)).simpleMoneyFormat();
                    $(`#target_6_February`).val(searh_target_sumber(data[0].sumber_target, 6, 2)).simpleMoneyFormat();
                    $(`#target_7_February`).val(searh_target_sumber(data[0].sumber_target, 7, 2)).simpleMoneyFormat();
                    $(`#total_target_February`).val(data[0].total_target.feb).simpleMoneyFormat();
                    $(`#total_per_hari_February`).val(searh_target_sales(data[0].sales_target, id_pic, '02', year)).simpleMoneyFormat();
                    // March
                    $(`#target_1_March`).val(searh_target_sumber(data[0].sumber_target, 1, 3)).simpleMoneyFormat();
                    $(`#target_2_March`).val(searh_target_sumber(data[0].sumber_target, 2, 3)).simpleMoneyFormat();
                    $(`#target_3_March`).val(searh_target_sumber(data[0].sumber_target, 3, 3)).simpleMoneyFormat();
                    $(`#target_4_March`).val(searh_target_sumber(data[0].sumber_target, 4, 3)).simpleMoneyFormat();
                    $(`#target_5_March`).val(searh_target_sumber(data[0].sumber_target, 5, 3)).simpleMoneyFormat();
                    $(`#target_6_March`).val(searh_target_sumber(data[0].sumber_target, 6, 3)).simpleMoneyFormat();
                    $(`#target_7_March`).val(searh_target_sumber(data[0].sumber_target, 7, 3)).simpleMoneyFormat();
                    $(`#total_target_March`).val(data[0].total_target.mar).simpleMoneyFormat();
                    $(`#total_per_hari_March`).val(searh_target_sales(data[0].sales_target, id_pic, '03', year)).simpleMoneyFormat();
                    // April
                    $(`#target_1_April`).val(searh_target_sumber(data[0].sumber_target, 1, 4)).simpleMoneyFormat();
                    $(`#target_2_April`).val(searh_target_sumber(data[0].sumber_target, 2, 4)).simpleMoneyFormat();
                    $(`#target_3_April`).val(searh_target_sumber(data[0].sumber_target, 3, 4)).simpleMoneyFormat();
                    $(`#target_4_April`).val(searh_target_sumber(data[0].sumber_target, 4, 4)).simpleMoneyFormat();
                    $(`#target_5_April`).val(searh_target_sumber(data[0].sumber_target, 5, 4)).simpleMoneyFormat();
                    $(`#target_6_April`).val(searh_target_sumber(data[0].sumber_target, 6, 4)).simpleMoneyFormat();
                    $(`#target_7_April`).val(searh_target_sumber(data[0].sumber_target, 7, 4)).simpleMoneyFormat();
                    $(`#total_target_April`).val(data[0].total_target.apr).simpleMoneyFormat();
                    $(`#total_per_hari_April`).val(searh_target_sales(data[0].sales_target, id_pic, '04', year)).simpleMoneyFormat();
                    // May
                    $(`#target_1_May`).val(searh_target_sumber(data[0].sumber_target, 1, 5)).simpleMoneyFormat();
                    $(`#target_2_May`).val(searh_target_sumber(data[0].sumber_target, 2, 5)).simpleMoneyFormat();
                    $(`#target_3_May`).val(searh_target_sumber(data[0].sumber_target, 3, 5)).simpleMoneyFormat();
                    $(`#target_4_May`).val(searh_target_sumber(data[0].sumber_target, 4, 5)).simpleMoneyFormat();
                    $(`#target_5_May`).val(searh_target_sumber(data[0].sumber_target, 5, 5)).simpleMoneyFormat();
                    $(`#target_6_May`).val(searh_target_sumber(data[0].sumber_target, 6, 5)).simpleMoneyFormat();
                    $(`#target_7_May`).val(searh_target_sumber(data[0].sumber_target, 7, 5)).simpleMoneyFormat();
                    $(`#total_target_May`).val(data[0].total_target.mei).simpleMoneyFormat();
                    $(`#total_per_hari_May`).val(searh_target_sales(data[0].sales_target, id_pic, '05', year)).simpleMoneyFormat();
                    // June
                    $(`#target_1_June`).val(searh_target_sumber(data[0].sumber_target, 1, 6)).simpleMoneyFormat();
                    $(`#target_2_June`).val(searh_target_sumber(data[0].sumber_target, 2, 6)).simpleMoneyFormat();
                    $(`#target_3_June`).val(searh_target_sumber(data[0].sumber_target, 3, 6)).simpleMoneyFormat();
                    $(`#target_4_June`).val(searh_target_sumber(data[0].sumber_target, 4, 6)).simpleMoneyFormat();
                    $(`#target_5_June`).val(searh_target_sumber(data[0].sumber_target, 5, 6)).simpleMoneyFormat();
                    $(`#target_6_June`).val(searh_target_sumber(data[0].sumber_target, 6, 6)).simpleMoneyFormat();
                    $(`#target_7_June`).val(searh_target_sumber(data[0].sumber_target, 7, 6)).simpleMoneyFormat();
                    $(`#total_target_June`).val(data[0].total_target.jun).simpleMoneyFormat();
                    $(`#total_per_hari_June`).val(searh_target_sales(data[0].sales_target, id_pic, '06', year)).simpleMoneyFormat();
                    // July
                    $(`#target_1_July`).val(searh_target_sumber(data[0].sumber_target, 1, 7)).simpleMoneyFormat();
                    $(`#target_2_July`).val(searh_target_sumber(data[0].sumber_target, 2, 7)).simpleMoneyFormat();
                    $(`#target_3_July`).val(searh_target_sumber(data[0].sumber_target, 3, 7)).simpleMoneyFormat();
                    $(`#target_4_July`).val(searh_target_sumber(data[0].sumber_target, 4, 7)).simpleMoneyFormat();
                    $(`#target_5_July`).val(searh_target_sumber(data[0].sumber_target, 5, 7)).simpleMoneyFormat();
                    $(`#target_6_July`).val(searh_target_sumber(data[0].sumber_target, 6, 7)).simpleMoneyFormat();
                    $(`#target_7_July`).val(searh_target_sumber(data[0].sumber_target, 7, 7)).simpleMoneyFormat();
                    $(`#total_target_July`).val(data[0].total_target.jul).simpleMoneyFormat();
                    $(`#total_per_hari_July`).val(searh_target_sales(data[0].sales_target, id_pic, '07', year)).simpleMoneyFormat();
                    // August
                    $(`#target_1_August`).val(searh_target_sumber(data[0].sumber_target, 1, 8)).simpleMoneyFormat();
                    $(`#target_2_August`).val(searh_target_sumber(data[0].sumber_target, 2, 8)).simpleMoneyFormat();
                    $(`#target_3_August`).val(searh_target_sumber(data[0].sumber_target, 3, 8)).simpleMoneyFormat();
                    $(`#target_4_August`).val(searh_target_sumber(data[0].sumber_target, 4, 8)).simpleMoneyFormat();
                    $(`#target_5_August`).val(searh_target_sumber(data[0].sumber_target, 5, 8)).simpleMoneyFormat();
                    $(`#target_6_August`).val(searh_target_sumber(data[0].sumber_target, 6, 8)).simpleMoneyFormat();
                    $(`#target_7_August`).val(searh_target_sumber(data[0].sumber_target, 7, 8)).simpleMoneyFormat();
                    $(`#total_target_August`).val(data[0].total_target.aug).simpleMoneyFormat();
                    $(`#total_per_hari_August`).val(searh_target_sales(data[0].sales_target, id_pic, '08', year)).simpleMoneyFormat();
                    // September
                    $(`#target_1_September`).val(searh_target_sumber(data[0].sumber_target, 1, 9)).simpleMoneyFormat();
                    $(`#target_2_September`).val(searh_target_sumber(data[0].sumber_target, 2, 9)).simpleMoneyFormat();
                    $(`#target_3_September`).val(searh_target_sumber(data[0].sumber_target, 3, 9)).simpleMoneyFormat();
                    $(`#target_4_September`).val(searh_target_sumber(data[0].sumber_target, 4, 9)).simpleMoneyFormat();
                    $(`#target_5_September`).val(searh_target_sumber(data[0].sumber_target, 5, 9)).simpleMoneyFormat();
                    $(`#target_6_September`).val(searh_target_sumber(data[0].sumber_target, 6, 9)).simpleMoneyFormat();
                    $(`#target_7_September`).val(searh_target_sumber(data[0].sumber_target, 7, 9)).simpleMoneyFormat();
                    $(`#total_target_September`).val(data[0].total_target.sep).simpleMoneyFormat();
                    $(`#total_per_hari_September`).val(searh_target_sales(data[0].sales_target, id_pic, '09', year)).simpleMoneyFormat();
                    // October
                    $(`#target_1_October`).val(searh_target_sumber(data[0].sumber_target, 1, 10)).simpleMoneyFormat();
                    $(`#target_2_October`).val(searh_target_sumber(data[0].sumber_target, 2, 10)).simpleMoneyFormat();
                    $(`#target_3_October`).val(searh_target_sumber(data[0].sumber_target, 3, 10)).simpleMoneyFormat();
                    $(`#target_4_October`).val(searh_target_sumber(data[0].sumber_target, 4, 10)).simpleMoneyFormat();
                    $(`#target_5_October`).val(searh_target_sumber(data[0].sumber_target, 5, 10)).simpleMoneyFormat();
                    $(`#target_6_October`).val(searh_target_sumber(data[0].sumber_target, 6, 10)).simpleMoneyFormat();
                    $(`#target_7_October`).val(searh_target_sumber(data[0].sumber_target, 7, 10)).simpleMoneyFormat();
                    $(`#total_target_October`).val(data[0].total_target.okt).simpleMoneyFormat();
                    $(`#total_per_hari_October`).val(searh_target_sales(data[0].sales_target, id_pic, '10', year)).simpleMoneyFormat();
                    // November
                    $(`#target_1_November`).val(searh_target_sumber(data[0].sumber_target, 1, 11)).simpleMoneyFormat();
                    $(`#target_2_November`).val(searh_target_sumber(data[0].sumber_target, 2, 11)).simpleMoneyFormat();
                    $(`#target_3_November`).val(searh_target_sumber(data[0].sumber_target, 3, 11)).simpleMoneyFormat();
                    $(`#target_4_November`).val(searh_target_sumber(data[0].sumber_target, 4, 11)).simpleMoneyFormat();
                    $(`#target_5_November`).val(searh_target_sumber(data[0].sumber_target, 5, 11)).simpleMoneyFormat();
                    $(`#target_6_November`).val(searh_target_sumber(data[0].sumber_target, 6, 11)).simpleMoneyFormat();
                    $(`#target_7_November`).val(searh_target_sumber(data[0].sumber_target, 7, 11)).simpleMoneyFormat();
                    $(`#total_target_November`).val(data[0].total_target.nov).simpleMoneyFormat();
                    $(`#total_per_hari_November`).val(searh_target_sales(data[0].sales_target, id_pic, '11', year)).simpleMoneyFormat();
                    // December
                    $(`#target_1_December`).val(searh_target_sumber(data[0].sumber_target, 1, 12)).simpleMoneyFormat();
                    $(`#target_2_December`).val(searh_target_sumber(data[0].sumber_target, 2, 12)).simpleMoneyFormat();
                    $(`#target_3_December`).val(searh_target_sumber(data[0].sumber_target, 3, 12)).simpleMoneyFormat();
                    $(`#target_4_December`).val(searh_target_sumber(data[0].sumber_target, 4, 12)).simpleMoneyFormat();
                    $(`#target_5_December`).val(searh_target_sumber(data[0].sumber_target, 5, 12)).simpleMoneyFormat();
                    $(`#target_6_December`).val(searh_target_sumber(data[0].sumber_target, 6, 12)).simpleMoneyFormat();
                    $(`#target_7_December`).val(searh_target_sumber(data[0].sumber_target, 7, 12)).simpleMoneyFormat();
                    $(`#total_target_December`).val(data[0].total_target.des).simpleMoneyFormat();
                    $(`#total_per_hari_December`).val(searh_target_sales(data[0].sales_target, id_pic, '12', year)).simpleMoneyFormat();
                },
                error: function (data) {
                    console.log('Error:', data);
                }
            });
        }
    })
</script>