<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ route('crm') }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">Target Detail</h3>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        @include('pages.target.data_jscript_index')
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-5">
            <div class="card-body pt-0 mt-5">
                <div class="d-flex justify-content-between">
                    @if(Auth::user()->roles == 'SUPERADMIN' || Auth::user()->roles == 'SALES SPV')
                    <div>
                        <div class="input-group mb-3">
                            <label class="input-group-text" for="pic_target">PIC</label>
                            <select class="form-select" id="pic_target" name="pic_target">
                            <option value="All" selected>All</option>
                            @foreach ($pic as $p)
                                <option value="{{ $p->id }}">{{ $p->name }}</option>
                            @endforeach
                            </select>
                        </div>
                    </div>
                    @endif
                    <div class="row">
                        <div class="col input-group mb-3" id="time_target_form">
                            <input type="text" class="form-control" name="tahun_target" value="{{ date('Y') }}" id="tahun_target" required />
                            <span class="input-group-text" id="basic-addon2">
                                <i class="fa fa-calendar"></i>
                            </span>
                        </div>
                        <div class="col input-group mb-3">
                            <label class="input-group-text" for="sumber">Sumber</label>
                            <select class="form-select" id="sumber" name="sumber">
                            <option value="" selected>All</option>
                            @foreach ($sumber as $sum)
                                <option value="{{ $sum->sumber }}">{{ $sum->sumber }}</option>
                            @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                <div class="py-5">
                    <div class="row">
                        <div class="col-md-9">
                            <div class="card card-flush">
                                <div class="card-header py-5">
                                    <h3 class="card-title fw-bold text-gray-800">Track Target dan Capaian Penjualan Tahunan</h3>
                                </div>
                                <div class="card-body d-flex justify-content-between flex-column pb-0 px-0 pt-1">
                                    <div class="d-flex flex-wrap d-grid gap-5 px-9 mb-5">
                                        <div class="me-md-2">
                                            <div class="d-flex mb-2">
                                                <span class="fs-2hx fw-bold text-gray-800 me-2 lh-1 ls-n2 total_customer">{{ $count_total_customer }}</span>
                                            </div>
                                            <span class="fs-6 fw-semibold text-gray-400">Total Customer</span>
                                        </div>
                                        <div class="border-start-dashed border-end-dashed border-1 border-gray-300 px-5 ps-md-10 pe-md-7 me-md-5">
                                            <div class="d-flex mb-2">
                                                <span class="fs-4 fw-semibold text-gray-400 me-1">Rp</span>
                                                <span class="fs-2hx fw-bold text-gray-800 me-2 lh-1 ls-n2 total_target">{{ number_format($total_target,0,'',',') }}</span>
                                            </div>
                                            <span class="fs-6 fw-semibold text-my-primary">Total Target</span>
                                        </div>
                                        <div class="m-0">
                                            <div class="d-flex align-items-center mb-2">
                                                <span class="fs-4 fw-semibold text-gray-400 align-self-start me-1">Rp</span>
                                                <span class="fs-2hx fw-bold text-gray-800 me-2 lh-1 ls-n2 total_realisasi">{{ number_format($total_realisasi,0,'',',') }}</span>
                                            </div>
                                            <span class="fs-6 fw-semibold text-success">Total Realisasi</span>
                                        </div>
                                    </div>
                                    <canvas id="kt_chartjs_1" class="mh-400px p-5"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card shadow-sm card-flush d-flex mb-5">
                                <div class="card-header bg-warning p-2 justify-content-center align-items-center">
                                    <h5 class="fw-semibold text-center lh-lg text-white">Target Bulanan</h5>
                                </div>
                                <div class="card-body">
                                    <h3 class="fw-semibold text-gray-800 lh-lg mb-5">{{ $this_month_year }}</h3>
                                    <div class="mb-20">
                                        <div class="mb-3">
                                            Target (bulan)
                                            <br>
                                            <span id="month_target" class="text-my-primary fw-semibold fs-2">Rp. {{ number_format($this_month_year_target,0,'',',') }}</span>
                                        </div>
                                        <div class="mb-3">
                                            Capaian (bulan)
                                            <br>
                                            <span id="month_real" class="text-success fw-semibold fs-2">Rp. {{ number_format($this_month_year_realisasi,0,'',',') }}</span>
                                        </div>
                                    </div>
                                    Progress target
                                    <div class="progress mt-3 mb-10">
                                        <div 
                                            class="progress-bar my-primary" 
                                            id="myProgressBar" 
                                            role="progressbar" 
                                            aria-label="Example 1px high" 
                                            style="width: {{ $this_month_year_progress }}%;" 
                                            aria-valuenow="{{ $this_month_year_realisasi }}" 
                                            aria-valuemin="0" 
                                            aria-valuemax="{{ $this_month_year_target }}">
                                        </div>
                                    </div>
                                    <a href="{{ route('target.add_target') }}" class="btn btn-sm btn-my-primary text-white fw-semibold w-100">
                                        Buat Target Baru
                                    </a>
                                    <a href="{{ route('target.view_detail') }}" class="mt-5 btn btn-sm btn-success text-white fw-semibold w-100">
                                        Lihat Seluruh Data Target
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                </div>
                <div class="mt-3 shadow-sm card card-flush">
                    <div class="card-header bg-primary p-2 justify-content-center align-items-center">
                        <h5 class="fw-semibold text-center lh-lg text-white">Detail Customer Deal</h5>
                    </div>
                    <div class="card-body p-9 pt-5 hover-scroll-overlay-y" style="height: 360px">
                        <table class="table table-row-bordered dataTable" id="list_customer_repeat_onreq">
                            <thead>
                                <tr>
                                    <td class="fw-bolder">Nama Konsumen</td>
                                    <td class="fw-bolder">Jumlah Order</td>
                                    <td class="fw-bolder">Sumber</td>
                                    <td class="fw-bolder">Grading</td>
                                    <td class="fw-bolder">Total harga</td>
                                    <td class="fw-bolder">Modal Sales</td>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div class="mt-3 px-9 bg-white rounded-bottom">
                        <table style="width:100%;" class="table table-row-bordered">
                            <tr>
                                <td style="width:62%" class="fw-bolder text-nowrap text-center">Jumlah Total</td>
                                <td style="width:20%" class="fw-bolder" id="sum_total_price">Rp{{ number_format($list_customer_repeat,0,'',',') }}</td>
                                <td style="width:18%" class="fw-bolder" id="sum_modal_price">Rp{{ number_format($total_modal_deal,0,'',',') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
</x-app-layout>