<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">
            <a href="{{ route('sales.index') }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
                </a> Task List All Order
        </span>
        <small class="text-white fs-6 fw-normal mt-3">Daftar All Order</small>
    </x-slot>
    <x-slot name="script">
        <script>
            $.ajaxSetup({
                headers: {
                    "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                },
            });

            let filters = {};
            const table = $("#datatable_tipe_produk").DataTable({
                lengthMenu: [
                    [10, 25, 50, 100, 500, -1],
                    [10, 25, 50, 100, 500, "All"],
                ],
                searching: false,
                responsive: false,
                lengthChange: true,
                autoWidth: false,
                order: [],
                pagingType: "full_numbers",
                dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Cari...",
                    paginate: {
                        Search: '<i class="icon-search"></i>',
                        first: "<i class='fas fa-angle-double-left'></i>",
                        previous: "<i class='fas fa-angle-left'></i>",
                        next: "<i class='fas fa-angle-right'></i>",
                        last: "<i class='fas fa-angle-double-right'></i>",
                    },
                },
                oLanguage: {
                    sSearch: "",
                },
                processing: true,
                serverSide: true,
                ajax: {
                    url: `/sales/task/dataTables`,
                    method: "POST",
                    data: function(d) {
                        return {
                            ...d,
                            ...filters,
                        };
                    },
                },
                columns: [
                    {
                        name: "created_at",
                        data: "DT_RowIndex",
                    },
                    {
                        name: "waktu_kontak",
                        data: "waktu_kontak"
                    },
                    {
                        name: "created_by",
                        data: "created_by",
                        orderable: false,
                    },
                    {
                        name: "tb_customer.nama",
                        data: "tb_customer.nama",
                        orderable: false,
                    },
                    {
                        name: "order",
                        data: "order",
                        orderable: false,
                    },
                    // {
                    //     name: "tgl_order",
                    //     data: "tgl_order",
                    //     orderable: false,
                    // },
                    {
                        name: "total_harga",
                        data: "total_harga",
                    },
                    {
                        name: "status_deal",
                        data: "status_deal",
                        orderable: false,
                    },
                    {
                        name: "status_order",
                        data: "status_order",
                        orderable: false,
                    },
                    // {
                    //     name: "kategori_produksi",
                    //     data: "kategori_produksi",
                    //     orderable: false,
                    // },
                    {
                        name: "progress",
                        data: "progress",
                        orderable: false,
                    },
                    {
                        name: "action",
                        data: "action",
                        orderable: false,
                    },

                ],
            });

            let debounceTimer;

            function handleSearch(e) {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    filters.keyword = e.target.value;
                    table.draw();
                }, 500);
            }

            $(document).on("change", ".filter-order-flag-dummy", function() {
                filters.flag_dummy_order = $(this).val();
                table.draw();
            }).on("change", ".filter-order-year", function() {
                filters.year = $(this).val();
                table.draw();
            }).on("change", ".filter-order-pic", function() {
                filters.pic = $(this).val();
                table.draw();
            }).on("change", ".filter-order-progress", function() {
                filters.progress = $(this).val();
                table.draw();
            }).on("change", ".filter-order-status-order", function() {
                filters.status_order = $(this).val();
                table.draw();
            }).on("change", ".filter-sumber", function() {
                filters.sumber = $(this).val();
                table.draw();
            }).on("change", ".filter-order-due-date", function() {
                filters.start_due_date = $('#start_due_date').val();
                filters.end_due_date = $(this).val();
                table.draw();
            });

            var isCollapsed = true;
            $('#collapseButton').on('click', function() {
                var collapse = $('#collapseExample');

                if (isCollapsed) {
                    collapse.collapse('show');
                    $("#table_div").removeClass("mt-2")
                } else {
                    collapse.collapse('hide');
                    $("#table_div").addClass("mt-2")
                }

                isCollapsed = !isCollapsed; // Toggle the collapse state
            });

            $('#clearFilter').on('click', function() {
                $('.filter-order-flag-dummy').val('');
                $('.filter-order-year').val('');
                $('.filter-order-pic').val('');
                $('.filter-order-progress').val('');
                $('.filter-order-status-order').val('');
                $('.filter-order-due-date').val('');
                $('.filter-sumber').val('');
                filters = {
                    keyword: $('#tipe_produk-search-box').val(),
                };
                table.draw();
            });
        </script>
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2"
                                        rx="1" transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                    <path
                                        d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                        fill="black"></path>
                                </svg>
                            </span>
                            <input type="text" oninput="handleSearch(event)" id="tipe_produk-search-box"
                                data-kt-docs-table-filter="search" class="form-control form-control-solid w-450px ps-15"
                                placeholder="Search ...">
                        </div>
                        <div class="d-flex justify-content-end gap-4" data-kt-docs-table-toolbar="base">
                            <button type="button" class="btn btn-light-primary" id="collapseButton">
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <path
                                            d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                                            fill="black"></path>
                                    </svg>
                                </span>
                                Filter
                            </button>
                        </div>
                    </div>

                    <div class="collapse" id="collapseExample">
                        <hr>
                        <div class=" bg-secondary rounded p-2 d-flex flex-row-fluid justify-content-between gap-3">
                            @php
                                $pic = \App\Models\User::pluck('name', 'id')->toArray();
                                $follow_up = \App\Models\FollowUp::pluck('follow_up')->toArray();
                                $lost = \App\Models\Lost::pluck('lost')->toArray();
                            @endphp
                            {{-- <div>
                                <select class="form-select form-select-solid filter-order-flag-dummy"
                                    name="flag_dummy_order">
                                    <option hidden>Tipe Order</option>
                                    <option value="">All</option>
                                    <option value="Dummy">Dummy</option>
                                    <option value="Produksi Massal">Produksi Massal</option>
                                    <option value="Produksi Ulang">Produksi Ulang</option>
                                    <option value="FU/Lost">FU/Lost</option>
                                    <option value="Jasa Lainnya">Jasa Lainnya</option>
                                </select>
                            </div> --}}

                            <div class="w-100 mw-100px">
                                <select class="form-select form-select-solid filter-order-year" name="year">
                                    <option hidden>Tahun</option>
                                    @php
                                        $currentYear = date('Y');
                                    @endphp
                                    <option value="" selected>All</option>
                                    @for ($year = $currentYear - 3; $year <= $currentYear; $year++)
                                        <option value="{{ $year }}">{{ $year }}</option>
                                    @endfor
                                </select>
                            </div>
                            @if (Auth::user()->roles == 'SALES SPV' || Auth::user()->roles == 'SUPERADMIN')
                                <div class="w-100 mw-100px">
                                    <select class="form-select form-select-solid filter-order-pic" name="pic">
                                        <option hidden>PIC</option>
                                        <option value="">All</option>
                                        @foreach ($pic as $id => $name)
                                            <option value="{{ $id }}">{{ $name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            @endif
                            <div class="w-100 mw-100px">
                                <select class="form-select form-select-solid filter-order-status-order" name="status_order">
                                    <option hidden>Status Order</option>
                                    <option value="">All</option>
                                    <option value="" disabled>-- FU</option>
                                    @foreach ($follow_up as $fu)
                                        <option value="{{ $fu }}">{{ $fu }}</option>
                                    @endforeach
                                    <option value="" disabled>-- Deal</option>
                                    <option value="Design">Design </option>
                                    <option value="FAW">FAW</option>
                                    <option value="Selesai Produksi">Selesai Produksi</option>
                                    <option value="" disabled>-- Lost</option>
                                    @foreach ($lost as $lostItem)
                                        <option value="{{ $lostItem }}">{{ $lostItem }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="w-100 mw-150px">
                                <select class="form-select form-select-solid filter-order-progress" name="progress">
                                    <option hidden>Progress</option>
                                    <option value="all">All</option>
                                    <option value="0">0%</option>
                                    {{-- <option value="25">25%</option>
                                    <option value="50">50%</option>
                                    <option value="75">75%</option> --}}
                                    <option value="100">100%</option>
                                </select>
                            </div>
                            <div class="w-100 mw-200px">
                                <table>
                                    <tr>
                                        <td><label>From</label></td>
                                        <td><input type="date" name="start_due_date" id="start_due_date" class="form-control"/></td>
                                    </tr>
                                    <tr>
                                        <td><label>To</label></td>
                                        <td><input type="date" name="end_due_date" id="end_due_date" class="form-control filter-order-due-date"/></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <hr>
                    </div>
                    <div class="table-responsive">
                        <table id="datatable_tipe_produk"
                            class="table-row-bordered compact text-nowrap dataTable no-footer table-striped table align-middle"
                            style="width:100%">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Waktu Kontak</th>
                                    <th>PIC</th>
                                    <th>Nama Customer</th>
                                    <th>Nama Produk</th>
                                    <th>Total Harga</th>
                                    <th>Status Deal</th>
                                    <th>Status Order</th>
                                    <th>Progress</th>
                                    <th class="text-center">Action</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
