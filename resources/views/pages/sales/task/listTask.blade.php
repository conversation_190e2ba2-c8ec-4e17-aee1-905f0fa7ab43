<x-app-layout>
    <x-slot name="header">
        <div class="d-flex gap-4">
            <div>
                <a href="{{ route('sales.task.index') }}" class="btn btn-white px-3 py-1">
                    <i class="fa-solid fa-angle-left"></i>
                </a>
            </div>
            <div>
                <div class="text-white fs-1">
                    @php
                        $kategori_produksi = $order->tb_produksi->kategori_produksi ?? '-';
                        $jumlah_produk = $order->tb_produksi->jumlah_produk ?? '-';
                        $item_sko = $order->sko ?? $order->sko_key ?? '-';
                        $order_name = "$kategori_produksi - $jumlah_produk [$item_sko]";
                    @endphp
                    Task Order {{ $order_name }}
                </div>
                <div class="text-white fs-6 fw-normal mt-3">Daftar All Task Order </div>
            </div>
        </div>
    </x-slot>
    <x-slot name="script">
        <script>
            const order_id = "{{ $order->order_key }}";
            $.ajaxSetup({
                headers: {
                    "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                },
            });

            let filters = {};
            const table = $("#datatable_tipe_produk").DataTable({
                lengthMenu: [
                    [10, 25, 50, 100, 500, -1],
                    [10, 25, 50, 100, 500, "All"],
                ],
                searching: false,
                responsive: false,
                lengthChange: true,
                autoWidth: false,
                order: [],
                pagingType: "full_numbers",
                dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Cari...",
                    paginate: {
                        Search: '<i class="icon-search"></i>',
                        first: "<i class='fas fa-angle-double-left'></i>",
                        previous: "<i class='fas fa-angle-left'></i>",
                        next: "<i class='fas fa-angle-right'></i>",
                        last: "<i class='fas fa-angle-double-right'></i>",
                    },
                },
                oLanguage: {
                    sSearch: "",
                },
                processing: true,
                serverSide: true,
                ajax: {
                    url: `/sales/task/list/${order_id}/dataTables`,
                    method: "POST",
                    data: function(d) {
                        return {
                            ...d,
                            ...filters,
                        };
                    },
                },
                columns: [{
                        name: "created_at",
                        data: "DT_RowIndex",
                    },
                    {
                        name: "status",
                        data: "status",
                        orderable: false,
                    },
                    {
                        name: "title",
                        data: "title",
                        orderable: false,
                    },
                    {
                        name: "detail",
                        data: "detail",
                        orderable: false,
                    },
                    {
                        name: "due_date",
                        data: "due_date",
                        orderable: false,
                    },
                    {
                        name: "completed_date",
                        data: "completed_date",
                        orderable: false,
                    },
                    {
                        name: "created_by",
                        data: "created_by",
                        orderable: false,
                    },
                    {
                        name: "action",
                        data: "action",
                        orderable: false,
                    },

                ],
            });

            let debounceTimer;

            function handleSearch(e) {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    filters.keyword = e.target.value;
                    table.draw();
                }, 500);
            }

            var isCollapsed = true;
            $('#collapseButton').on('click', function() {
                var collapse = $('#collapseExample');

                if (isCollapsed) {
                    collapse.collapse('show');
                    $("#table_div").removeClass("mt-2")
                } else {
                    collapse.collapse('hide');
                    $("#table_div").addClass("mt-2")
                }

                isCollapsed = !isCollapsed; // Toggle the collapse state
            });

            //modal editor
            $('body').on('click', '.edit-task', function (event) {
                event.preventDefault();
                var id = $(this).data('id');
                $.get(`/sales/task/show/${id}`, function (data) {
                    $('#submit-edit-task').val("edit-task");
                    $('#modal-edit-task').modal('show');
                    $('#id-task').val(data.id_task);
                    $('#order-key').val(data.order_key);
                    $('#title-edit').val(data.title);
                    $('#detail-edit').val(data.detail);
                    $('#due-date-edit').val(data.due_date);
                })
            });

            $('#submit-edit-task').click(function (e) {
                e.preventDefault();
                $(this).html('Sending..');
                var id = $('#id-task').val();
                var orderKey = $('#order-key').val();
                $.ajax({
                    data: $('#task-edit-form').serialize(),
                    url: `/sales/board/order-company/detail/${orderKey}/activity-task`,
                    type: "POST",
                    dataType: 'json',
                    success: function (data) {
                        $("#datatable_tipe_produk").DataTable().ajax.reload()
                        setTimeout(function () {
                            self.$("#edit-task-close").trigger("click");
                        }, 1200);
                        $('#modal-edit-task').modal('hide');
                        $('#submit-edit-task').html('Save Changes');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "Task berhasil diedit",
                            icon: 'success',
                        })
                    },
                    error: function (data) {
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });
        </script>
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2"
                                        rx="1" transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                    <path
                                        d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                        fill="black"></path>
                                </svg>
                            </span>
                            <input type="text" oninput="handleSearch(event)" id="tipe_produk-search-box"
                                data-kt-docs-table-filter="search" class="form-control form-control-solid w-450px ps-15"
                                placeholder="Search ...">
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table id="datatable_tipe_produk"
                            class="table-row-bordered compact text-nowrap dataTable no-footer table-striped table align-middle"
                            style="width:100%">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Status</th>
                                    <th>Title</th>
                                    <th>Detail</th>
                                    <th>Due Date</th>
                                    <th>Finished Date</th>
                                    <th>PIC</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modal-edit-task">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                <form id="task-edit-form" name="task-form" class="form-horizontal">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Task</h5>
                        <!--begin::Close-->
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-order-close"
                            data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                        </div>
                        <!--end::Close-->
                    </div>

                    <div class="modal-body mt-n5">
                        <div class="row">
                            <div class="col-lg-12">
                                <input type="hidden" name="id" id="id-task">
                                <input type="hidden" name="order_key" id="order-key">
                                <div class="mb-3">
                                    <label for="task" class="required form-label">Title</label>
                                    <input type="text" id="title-edit" name="title" required autofocus class="form-control" placeholder="Title">
                                    <small class="text-danger"><strong id="title_error"></strong></small>
                                </div>
                                <div class="mb-3">
                                    <label for="detail" class="required form-label">Detail</label>
                                    <textarea class="form-control" id="detail-edit" name="detail" required></textarea>
                                    <small class="text-danger"><strong id="detail_error"></strong></small>
                                </div>
                                <div class="mb-3">
                                    <label for="due_date" class="required form-label">Due Date</label>
                                    <input type="date" id="due-date-edit" name="due_date" required autofocus class="form-control">
                                    <small class="text-danger"><strong id="due_date_error"></strong></small>
                                </div>
                                <div class="mb-3">
                                    <label for="mark_done" class="form-label">Mark as Done</label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="mark-done-edit" name="mark_done" value="1">
                                    </div>
                                    <small class="text-danger"><strong id="mark_done_error"></strong></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="edit-bahan-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                        <button type="button" id="submit-edit-task" class="btn btn-primary my-primary">Save
                            changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
