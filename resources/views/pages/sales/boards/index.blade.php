<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">Board Company</span>
        <small class="text-white fs-6 fw-normal pt-2">
            Overview of all board company
        </small>
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        {{-- <div class="card card-flush me-5">
                    <div class="card-body"> --}}
        <div class="row">
            @foreach ($companies as $item)
                <div class="col-md-4">
                    <a href="{{ route('sales.board.board', $item->code) }}" class="card border border-2">
                        <div class="d-flex align-items-center">
                            <div>
                                <img src="https://ui-avatars.com/api/?name={{ urlencode($item->name) }}&size=100&background=random"
                                    alt="{{ $item->name }}"
                                    style="border-radius: 10px 0 0 10px; width: 100px; height: 100px;">
                            </div>
                            <div class="p-4">
                                <h4 class="">
                                    {{ $item->name }}
                                </h4>
                                <div class="mt-2 d-flex align-items-center gap-4">
                                    <div class="text-dark" style="font-size: 13px;">
                                        <i class="fas fa-user me-1"></i> <b>{{ $item->customers_count }}</b> PIC
                                        Customer
                                    </div>
                                    <div class="text-dark" style="font-size: 13px;">
                                        <i class="fas fa-shopping-cart me-1"></i> <b>{{ $item->orders_count }}</b>
                                        Orders
                                    </div>
                                </div>
                            </div>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
        {{-- </div>
                </div> --}}
    </div>
</x-app-layout>
