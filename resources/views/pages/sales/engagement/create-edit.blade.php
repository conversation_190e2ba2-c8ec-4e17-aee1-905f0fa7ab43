<form id="engagement-edit-form" name="engagement-form" class="form-horizontal">
    <div class="modal-header">
        <h5 class="modal-title">Create/Edit Engagement</h5>
        <!--begin::Close-->
        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-order-close"
            data-bs-dismiss="modal" aria-label="Close">
            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
        </div>
        <!--end::Close-->
    </div>

    <div class="modal-body mt-n5">
        <div class="row">
            <div class="col-lg-12">
                <input type="hidden" name="id" id="id-engagement" value="{{ isset($data) ? $data->id : '' }}">
                <div class="mb-3">
                    <label for="task" class="required form-label">PIC Assign</label>
                    <select class="form-select @error('assign_to') is-invalid @enderror"
                        name="assign_to" id="assign_to" data-control="select2"
                        data-placeholder="Nama PIC">
                        <option></option>
                        @if(isset($data) && $data->assign_to)
                            <option value="{{ $data->assign_to }}" selected>
                                {{ $data->userAssigned->name ?? 'Selected PIC' }}
                            </option>
                        @endif
                    </select>
                    @error('assign_to')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
                <div class="mb-3">
                    <label for="task" class="required form-label">Company</label>
                    <select class="form-select @error('company_id') is-invalid @enderror"
                        name="company_id" id="company_id" data-control="select2"
                        data-placeholder="Nama Perusahaan">
                        <option></option>
                        @if(isset($data) && $data->company_id)
                            <option value="{{ $data->company_id }}" selected>
                                {{ $data->company->name ?? 'Selected Company' }}
                            </option>
                        @endif
                    </select>
                    @error('company_id')
                        <span class="invalid-feedback" role="alert">
                            <strong>{{ $message }}</strong>
                        </span>
                    @enderror
                </div>
                <div class="mb-3">
                    <label for="type" class="required form-label">Tipe</label>
                    <select class="form-select" name="type" id="type">
                        @if (count(typeEngagement()) > 0)
                            @foreach (typeEngagement() as $key => $type)
                                <option value="{{$key}}" {{ (isset($data) && $data->type == $key) ? 'selected' : '' }}>{{$type}}</option>
                            @endforeach
                        @endif
                    </select>
                </div>
                <div class="mb-3">
                    <label for="task" class="required form-label">Title</label>
                    <input type="text" id="title" name="title" required autofocus class="form-control" placeholder="Title" value="{{ isset($data) ? $data->title : '' }}">
                    <small class="text-danger"><strong id="title_error"></strong></small>
                </div>
                <div class="mb-3">
                    <label for="detail" class="required form-label">Detail</label>
                    <textarea class="form-control" id="detail" name="detail" required>{{ isset($data) ? $data->detail : '' }}</textarea>
                    <small class="text-danger"><strong id="detail_error"></strong></small>
                </div>
                <div class="mb-3">
                    <label for="due_date" class="required form-label">Due Date</label>
                    <input type="date" id="due-date" name="due_date" required autofocus class="form-control" value="{{ isset($data) ? $data->due_date : '' }}">
                    <small class="text-danger"><strong id="due_date_error"></strong></small>
                </div>
                <div class="mb-3">
                    <label for="mark_done" class="form-label">Mark as Done</label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="mark-done" name="mark_done" value="1" {{ isset($data) && $data->mark_done ? 'checked' : '' }}>
                    </div>
                    <small class="text-danger"><strong id="mark_done_error"></strong></small>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-footer">
        <button type="button" id="edit-engagement-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
        <button type="button" id="submit-engagement" class="btn btn-primary my-primary">Save</button>
    </div>
</form>
<script>
    $(document).ready(function() {
        // Initialize Select2 for assign_to
        $('#assign_to').select2({
            dropdownParent: $('#engagement-edit-form'),
            placeholder: "Select an option",
            allowClear: true,
            ajax: {
                url: '/engagement/get-pic',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        q: params.term, // search term
                        type: $(this).attr('id') // determine which select2 is being used
                    };
                },
                processResults: function(data) {
                    return {
                        results: data.map(item => ({
                            id: item.id,
                            text: item.name || item.company_name // adjust based on your API response
                        }))
                    };
                }
            }
        });

        $('#company_id').select2({
            dropdownParent: $('#engagement-edit-form'),
            placeholder: "Select an option",
            allowClear: true,
            ajax: {
                url: '/engagement/get-company',
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        q: params.term, // search term
                        type: $(this).attr('id') // determine which select2 is being used
                    };
                },
                processResults: function(data) {
                    return {
                        results: data.map(item => ({
                            id: item.id,
                            text: item.name || item.company_name // adjust based on your API response
                        }))
                    };
                }
            }
        });
    });
    // Handle form submission
    $('#submit-engagement').on('click', function() {
        var formData = {
            id: $('#id-engagement').val(),
            assign_to: $('#assign_to').val(),
            company_id: $('#company_id').val(),
            type: $('#type').val(),
            title: $('#title').val(),
            detail: $('#detail').val(),
            due_date: $('#due-date').val(),
            mark_done: $('#mark-done').is(':checked') ? 1 : 0
        };

        $.ajax({
            url: '/engagement/store', // Adjust the URL as needed
            type: 'POST',
            data: formData,
            success: function(response) {
                $('#engagement-edit-form').trigger('reset'); // Reset the form
                $('#edit-engagement-close').click(); // Close the modal
                window.dt_forecast.ajax.reload(); // Reload the DataTable
                toastr.success('Engagement saved successfully!'); // Show success message
            },
            error: function(xhr) {
                // Handle error
                console.log(xhr.responseText);
            }
        });
    }); 
</script>