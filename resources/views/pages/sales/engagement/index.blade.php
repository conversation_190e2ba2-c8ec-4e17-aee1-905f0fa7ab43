<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">
        <a href="{{ route('sales.index') }}" class="pe-3">
                <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
            </a> <span class="text-white fs-1">Engagement</span>
        </span>
        <small class="text-white fs-6 fw-normal mt-3">Manage and view all engagement.</small>
    </x-slot>
    <x-slot name="script">
        <script>
            $(document).ready(function(){
                // Start Due Date filter
                const filterStartDueDate = document.querySelector('.filter-start-due-date')
                filterStartDueDate.addEventListener('change', function(e) {
                    // Update DataTable ajax data for start_due_date
                    window.dt_forecast.ajax.params().start_due_date = filterStartDueDate.value;
                    window.dt_forecast.ajax.reload();
                })

                // End Due Date filter
                const filterEndDueDate = document.querySelector('.filter-end-due-date')
                filterEndDueDate.addEventListener('change', function(e) {
                    // Update DataTable ajax data for end_due_date
                    window.dt_forecast.ajax.params().end_due_date = filterEndDueDate.value;
                    window.dt_forecast.ajax.reload();
                })

                window.throttleDTSearch = DataTable.util.throttle(function (dt, val) {
                    dt.search(val).draw();
                }, 450);

                $.ajaxSetup({
                    headers: {
                        "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                    },
                });

                let filters = {};
                window.dt_forecast = $('#datatable_engagement').DataTable({
                    processing: true,
                    serverSide: true,
                    paging: true,
                    pageLength: 10,
                    ajax: {
                        url: "{{ route('engagement.getData') }}",
                        data: function(d) {
                            d.start_due_date = document.querySelector('.filter-start-due-date').value;
                            d.end_due_date = document.querySelector('.filter-end-due-date').value;
                        }
                    },
                    deferRender: true,
                    columns: [
                        {
                            data: null,
                            className: "text-center",
                            orderable: false,
                            render: function (data, type, row, meta) {
                                return meta.row + meta.settings._iDisplayStart + 1;
                            }
                        },
                        {
                            name: "pic",
                            data: "pic",
                        },
                        {
                            name: "company.name",
                            data: "company.name",
                        },
                        {
                            name: "title",
                            data: "title",
                        },
                        {
                            name: "detail",
                            data: "detail",
                        },
                        {
                            name: "type",
                            data: "type",
                        },
                        {
                            name: "created_at",
                            data: "created_at",
                            className: "text-center",
                        },
                        {
                            name: "due_date",
                            data: "due_date",
                            className: "text-center",
                        },
                        {
                            name: "is_completed",
                            data: "is_completed",
                            className: "text-center",
                            orderable: false,
                        },
                        {
                            name: "action",
                            data: "action",
                            className: "text-center",
                            orderable: false,
                        }
                    ],
                });

                var isCollapsed = true;
                $('#collapseButton').on('click', function () {
                    var collapse = $('#collapseExample');
                    if (isCollapsed) {
                        collapse.collapse('show');
                        $("#table_div").removeClass("mt-2")
                    } else {
                        collapse.collapse('hide');
                        $("#table_div").addClass("mt-2")
                    }
                    
                    isCollapsed = !isCollapsed; // Toggle the collapse state
                });

                //filter page order
                const filterSearch = document.querySelector('[data-kt-docs-table-filter="search-order"]')
                filterSearch.addEventListener('keyup', function(e) {
                    window.throttleDTSearch(window.dt_forecast, e.target.value)
                })

                const filterStatus = document.querySelector('.filter-status')
                filterStatus.addEventListener('change', function(e) {
                    window.dt_forecast.columns(8).search(e.target.value).draw();
                })
            })

            $('#btn-add-engagement').on('click', function() {
                $('#modal-engagement').modal('show');
                $('#modal-engagement .modal-content').load("{{ route('engagement.create') }}", function() {
                    // Initialize form validation or any other JS needed after loading the modal content
                });
            });

            $('#modal-engagement').on('hidden.bs.modal', function () {
                $(this).find('.modal-content').empty();
            });

            function editEngagement(id) {
                $('#modal-engagement').modal('show');
                $('#modal-engagement .modal-content').load("{{ route('engagement.edit', '') }}/" + id, function() {
                    // Initialize form validation or any other JS needed after loading the modal content
                });
            }

            function deleteEngagement(id) {
                Swal.fire({
                    title: 'Apakah anda yakin?',
                    text: "Ingin hapus engagement ini?",
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Ya, Hapus!'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: "{{ route('engagement.destroy', '') }}/" + id,
                            type: "POST",
                            data: {
                                _method: 'DELETE'
                            },
                            success: function(response) {
                                // Perform the delete action here
                                Swal.fire(
                                    'Deleted!',
                                    'Engagement ini telah dihapus.',
                                    'success'
                                )
                                window.dt_forecast.ajax.reload();
                            },
                            error: function(xhr) {
                                Swal.fire('Error', 'Failed to delete engagement.', 'error');
                            }
                        });
                    }
                })
            };
        </script>
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="row">
            <div class="col-md-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <h5 class="card-title d-flex align-items-center">
                            Total Engagements
                        </h5>
                        <hr class="my-4" style="border-top: 1px dashed;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted">This Week</h6>
                                <p class="fs-4 fw-bold">{{ $engagements_week ?? 0 }}</p>
                            </div>
                            <div class="text-end">
                                <h6 class="text-muted">This Month</h6>
                                <p class="fs-4 fw-bold">{{ $engagements_month ?? 0 }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Engagement Status</h5>
                        <hr class="my-4" style="border-top: 1px dashed;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted">Open</h6>
                                <p class="fs-4 fw-bold">{{ $engagements_open ?? 0 }}</p>
                            </div>
                            <div class="text-end">
                                <h6 class="text-muted">Closed</h6>
                                <p class="fs-4 fw-bold">{{ $engagements_closed ?? 0 }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Upcoming Due Engagements</h5>
                        <hr class="my-4" style="border-top: 1px dashed;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted">Due Today</h6>
                                <p class="fs-4 fw-bold">{{ $engagements_due_today ?? 0 }}</p>
                            </div>
                            <div class="text-end">
                                <h6 class="text-muted">Due This Week</h6>
                                <p class="fs-4 fw-bold">{{ $engagements_due_week ?? 0 }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card shadow-sm mt-3">
            <div class="card-body">
                <div class="d-flex flex-row-fluid justify-content-between gap-3">
                    <div class="d-flex align-items-center position-relative">
                        <!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
                        <span class="svg-icon svg-icon-1 position-absolute ms-6">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none">
                                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2"
                                    rx="1" transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                <path
                                    d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                    fill="black"></path>
                            </svg>
                        </span>
                        <!--end::Svg Icon-->
                        <input type="text" data-kt-docs-table-filter="search-order"
                            class="form-control form-control-solid w-250px ps-15" placeholder="Search ...">
                    </div>
                    <div class="d-flex gap-3">
                        <button type="button" class="btn btn-light-primary" id="collapseButton">
                            <span class="svg-icon svg-icon-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none">
                                    <path
                                        d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                                        fill="black"></path>
                                </svg>
                            </span>
                            Filter
                        </button>
                        <button type="button" class="btn btn-success text-nowrap" title="Export Excel" id="btn-add-engagement">
                            <i class="fa fa-plus"></i> Add Engagement
                        </button>
                        <a href="/engagement" class="btn btn-sm btn-light ms-2" id="refresh-engagements" title="Refresh">
                                <i class="fa fa-refresh"></i>
                        </a>
                    </div>
                </div>
                <div class="collapse" id="collapseExample">
                    <hr>
                    <div class=" bg-secondary rounded p-2 d-flex flex-row-fluid justify-content-between gap-3">
                        <div class="w-25">
                            <label>Select Status</label>
                            <select class="form-select form-select-solid filter-status" name="is_completed">
                                <option value="">All</option>
                                <option value="1">Completed</option>
                                <option value="0">Not Completed</option>
                            </select>
                        </div>
                        
                        <div class="w-25">
                            <label>Select Start Due Date</label>
                            <input type="date" class="form-control form-control-solid filter-start-due-date" name="date" placeholder="Select Start Due Date"/>
                        </div>
                        <div class="w-25">
                            <label>Select End Due Date</label>
                            <input type="date" class="form-control form-control-solid filter-end-due-date" name="date" placeholder="Select End Due Date"/>
                        </div>
                    </div>
                    <hr>
                </div>
                <div class="table-responsive">
                    <table id="datatable_engagement" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Assign to</th>
                                <th>Company</th>
                                <th>Summary</th>
                                <th>Detail</th>
                                <th>Type</th>
                                <th>Create Date</th>
                                <th>Due Date</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" tabindex="-1" id="modal-engagement">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
            </div>
        </div>
    </div>
</x-app-layout>