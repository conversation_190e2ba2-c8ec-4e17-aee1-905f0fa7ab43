<x-app-layout>
    <x-slot name="header">
        <span><a href="{{ route('surat-jalan.index') }}" class="pe-3">
                <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
            </a><span class="text-white fs-1">
            {{($data ?? null) ? "Edit" : "Create"}} Surat Jalan
        </span></span>
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush me-5" style="min-height: calc(100vh - 300px)">
            <div class="border-bottom py-5 px-9">
                <h3>
                    Form Surat Jalan
                </h3>
                <small class="text-muted fs-6 fw-normal pt-2">
                    Silahkan isi form berikut untuk {{($data ?? null) ? "mengedit" : "membuat"}} Surat Jalan
                </small>
            </div>
            <div class="card-body">
                <form id="form_surat_jalan" method="POST" action="{{ route('surat-jalan.store') }}" enctype="multipart/form-data">
                    <input type="hidden" name="id" id="id" value="{{ $data->surat_jalan_key ?? '' }}" />
                    @csrf
                    <div class="row mb-8">
                        <div class="col-md-4">
                            @php
                                $no_surat_jalan = \App\Models\SuratJalan::generateSuratJalanNumber();
                            @endphp
                            <div class="form-group">
                                <label class="form-label">No Surat Jalan</label>
                                <input type="text" class="form-control @error('no_surat_jalan') is-invalid @enderror"
                                    name="no_surat_jalan" id="no_surat_jalan" placeholder="No Surat Jalan"
                                    value="{{ $data->no_surat_jalan ?? (null ?? $no_surat_jalan['no_surat_jalan']) }}"
                                    disabled />
                                @error('no_surat-jalan')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label required">Customer</label>
                                <select class="form-select @error('customer_id') is-invalid @enderror"
                                    name="customer_id" id="customer_id" data-control="select2"
                                    data-placeholder="Nama PIC">
                                    <option></option>
                                </select>
                                @error('customer_id')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Document Sign</label>
                                <div class="input-group">
                                        <span class="input-group-text"><i class="fa fa-link"></i></span>
                                <input type="text" class="form-control @error('lampiran') is-invalid @enderror"
                                            name="lampiran-sign" id="lampiran-sign" placeholder="Lampiran"
                                            value="{{ old('sign_lampiran', $data->sign_lampiran ?? '') }}" />
                                </div>
                                @if (isset($data))
                                    <a href="{{ asset($data->sign_lampiran) }}" target="_blank" class="btn btn-link">
                                        Lihat Lampiran
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="row mb-8">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Tanggal Kirim</label>
                                <input type="date"
                                    class="form-control @error('delivery_date') is-invalid @enderror"
                                    name="delivery_date" id="delivery_date"
                                    placeholder="Tanggal Jatuh Tempo"
                                    value="{{ old('delivery_date', $data->delivery_date ?? '') }}" />
                                @error('delivery_date')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">No PO</label>
                                <input type="text" class="form-control @error('no_po') is-invalid @enderror"
                                    name="no_po" id="no_po" placeholder="No Surat Jalan"
                                    value="{{ $data->no_po ?? '' }}" />
                                @error('no_po')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            @php
                                $status = [
                                    'draft' => 'Draft',
                                    'submitted' => 'Submitted',
                                ];
                            @endphp
                            <div class="form-group">
                                <label class="form-label">Status</label>
                                <select class="form-select @error('status') is-invalid @enderror" name="status"
                                    id="status" data-control="select2" data-placeholder="Status">
                                    <option value="" disabled>
                                        Pilih Status
                                    </option>
                                    @foreach ($status as $key => $value)
                                        <option value="{{ $key }}"
                                            {{ old('status', $data->status ?? '') == $key ? 'selected' : '' }}>
                                            {{ $value }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('status')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <h4>
                        Produk
                    </h4>
                    <div class="listProduct">
                        @php
                            $product_name = [''];
                            $dataDetailProduct = old('product_name', $data->details ?? ($product_name ?? []));
                        @endphp
                        @foreach ($dataDetailProduct as $key => $item)
                            <div class="row itemProduct mt-4">
                                <input type="hidden" name="id_product[]" id="id_product"
                                    value="{{ $item->id ?? '' }}" />
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label required">Kode Order</label>
                                        @if(isset($data) && $data->status == 'submitted' && isset($item->kode_order) || isset($data_order->sko))
                                            <input type="text" name="kode_order[]" class="form-control bg-secondary" readonly value="{{ $item->kode_order ?? $data_order->sko ?? '' }}" />
                                        @elseif (isset($item->kode_order) || isset($data_order->sko))
                                            <select name="kode_order[]" class="form-select kode-order" data-control="select2">
                                                @php
                                                    $kodeOrder = $item->kode_order ?? $data_order->sko ?? '';
                                                @endphp
                                                @if($kodeOrder)
                                                    <option value="{{ $kodeOrder }}" selected>{{ $kodeOrder }}</option>
                                                @endif
                                            </select>
                                        @else
                                            <select name="kode_order[]" class="form-select kode-order" data-control="select2"></select>
                                        @endif
                                        @error('kode_order.' . $key)
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label required">Nama Produk</label>
                                        <input type="text"
                                            class="form-control @error('product_name.' . $key) is-invalid @enderror"
                                            name="product_name[]" id="product_name" placeholder="Masukkan Nama Produk"
                                            value="{{ old('product_name.' . $key, $item->product_name ?? '') }}" />
                                        @error('product_name.' . $key)
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <div class="form-group">
                                        <label class="form-label required">Jumlah Koli</label>
                                        <input type="number"
                                            class="form-control @error('jumlah_koli.' . $key) is-invalid @enderror"
                                            name="jumlah_koli[]" id="jumlah_koli" placeholder="Jumlah Koli"
                                            value="{{ old('jumlah_koli.' . $key, $item->jumlah_koli ?? '') }}" />
                                        @error('jumlah_koli.' . $key)
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <div class="form-group">
                                        <label class="form-label required">Qty (pcs)</label>
                                        <input type="number"
                                            class="form-control @error('qty.' . $key) is-invalid @enderror"
                                            onkeyup="calculateTotalPrice(this)" onchange="calculateTotalPrice(this)"
                                            name="qty[]" id="qty" placeholder="Qty"
                                            value="{{ old('qty.' . $key, $item->quantity ?? '') }}" />
                                        @error('qty.' . $key)
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label required">Keterangan</label>
                                        <textarea class="form-control @error('description.' . $key) is-invalid @enderror" name="description[]"
                                            id="description" placeholder="Masukkan Keterangan">{{ old('description.' . $key, $item->description ?? '') }}</textarea>
                                        @error('description.' . $key)
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-1" id="elementTrash">
                                    <button type="button" class="btn btn-danger btn-sm mt-8 removeProduct">
                                        <i class="fa fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        @endforeach

                    </div>
                    <div class="row align-items-center">
                        <div class="col-md-9">
                            <button type="button" class="btn btn-info btn-sm mt-4" id="addProduct">
                                <i class="fas fa-plus"></i>
                                Tambah Produk
                            </button>
                        </div>
                        <div class="col-md-3 d-flex align-items-center gap-4">
                            <h5>Total :</h5>
                            <h3 class="totalAllPrice text-primary">
                                @php
                                    $total = 0;
                                    foreach ($dataDetailProduct as $item) {
                                        $qty = old('qty.' . $key, $item->quantity ?? 0);
                                        $total += $qty;
                                    }
                                @endphp
                                {{ number_format($total, 0, ',', '.') }}
                            </h3>
                        </div>
                    </div>

                    <div class="d-flex align-items-center gap-2 mt-8">
                        <a href="{{ route('surat-jalan.index') }}" class="btn btn-danger mt-4">
                            <i class="fas fa-times"></i>
                            Back
                        </a>
                        <button type="submit" class="btn btn-primary mt-4">
                            <i class="fas fa-save"></i>
                            Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <x-slot name="script">
        <script>
            $(function() {

                $('.kode-order').each(function() {
                    initKodeOrderSelect2($(this), $('#customer_id').val());
                });

                function initKodeOrderSelect2($select, customerId = null) {
                    $select.select2({
                        allowClear: true,
                        placeholder: "Pilih Kode order",
                        debug: true,
                        dropdownParent: $select.closest('.listProduct'),
                        ajax: {
                            url: "{{ route('crm.getOrder') }}",
                            data: function(params) {
                                return {
                                    customer_id: customerId || $('#customer_id').val(),
                                    q: params.term || ''
                                };
                            },
                            processResults: function(data) {
                                var resultsData = [];
                                $.each(data, function(index, item) {
                                    resultsData.push({
                                        id: item.id,
                                        text: item.name
                                    });
                                });
                                return { results: resultsData };
                            }
                        }
                    });
                }

                // Use event delegation for dynamically added elements
                $(document).on('change', '.kode-order', function() {
                    const $row = $(this).closest('.row');
                    const kodeOrderId = $(this).val();
                    console.log('Kode Order ID:', kodeOrderId);
                    if (kodeOrderId) {
                        $.ajax({
                            url: "/surat-jalan/getOrderDetail/",
                            type: 'GET',
                            data:{sko:kodeOrderId},
                            success: function(data) {
                                $row.find('input[name^="product_name"]').val(data?.tb_produksi?.nama_produk);
                                $('#no_po').val(data?.no_po);
                            },
                            error: function(xhr) {
                                console.error(xhr);
                            }
                        });
                    } else {
                        // $row.find('#product_name').val('');
                        // $row.find('#qty').val('');
                        // calculateTotalPrice($row.find('#qty'));
                    }
                });

                $.ajaxSetup({
                    headers: {
                        "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                    },
                });

                const addProduct = $('#addProduct');
                const listProduct = $('.listProduct');

                addProduct.on('click', function() {
                    console.log('addProduct');
                    const clone = listProduct.children().last().clone();

                    clone.find('input').val('');
                    clone.find('textarea').val('');
                    clone.find('.removeProduct').remove();

                    clone.find('input').attr('name', function() {
                        return $(this).attr('name').replace(/\[\d+\]/,
                            `[${listProduct.children().length}]`);
                    });
                    clone.find('input').attr('id', function() {
                        return $(this).attr('id').replace(/\[\d+\]/,
                            `[${listProduct.children().length}]`);
                    });
                    clone.find('textarea').attr('name', function() {
                        return $(this).attr('name').replace(/\[\d+\]/,
                            `[${listProduct.children().length}]`);
                    });
                    clone.find('textarea').attr('id', function() {
                        return $(this).attr('id').replace(/\[\d+\]/,
                            `[${listProduct.children().length}]`);
                    });

                    clone.find('.totalPrice').text('0');
                    clone.find('.totalPrice').attr('data-id', listProduct.children().length);

                    clone.find('#elementTrash').html(`
                        <button type="button" class="btn btn-danger btn-sm mt-8 removeProduct">
                            <i class="fa fa-trash-alt"></i>
                        </button>
                    `);

                    listProduct.append(clone);

                    listProduct.find('.removeProduct').off('click').on('click', function() {
                        if (listProduct.children().length > 1) {
                            $(this).closest('.row').remove();
                        }
                    });

                    clone.find('.select2-container').remove();
                    clone.find('.kode-order option').remove();
                    initKodeOrderSelect2(clone.find('.kode-order'), $('#customer_id').val());
                });

                $('.removeProduct').on('click', function() {
                    console.log('removeProduct');
                    if (listProduct.children().length > 0) {
                        $(this).closest('.row').remove();
                    }
                });

                const format = (item) => {
                    if (!item.id) {
                        return item.text;
                    }
                    var img =
                        "<span class='menu-icon me-3'><img src='https://ui-avatars.com/api/?name=" +
                        encodeURIComponent(item.text) +
                        "&background=random&size=32' alt='Avatar' class='rounded-circle me-2'/>"
                    var span = $("<span>", {
                        text: " " + item.text
                    });
                    span.prepend(img)
                    return span
                }

                $('#customer_id').select2({
                        allowClear: true,
                        debug: true,
                        ajax: {
                            url: "{{ route('surat-jalan.customers') }}",
                            dataType: 'json',
                            delay: 250,
                            data: function(params) {
                                return {
                                    q: params.term || '',
                                    page: params.page || 1
                                };
                            },
                            processResults: function(data, params) {
                                params.page = params.page || 1;

                                var resultsData = $.map(data.data, function(item) {
                                    return {
                                        id: item.id,
                                        text: item.nama + (item.nama_instansi ? ' - ' + item.nama_instansi : '') + (item.no_hp ? ' - ' + item.no_hp : '')
                                    };
                                });

                                return {
                                    results: resultsData,
                                    pagination: {
                                        more: data.current_page < data.last_page
                                    }
                                };
                            },
                            cache: true
                        },
                        templateResult: function(item) {
                            return format(item);
                        }
                    })
                    .on('select2:opening', function(e) {
                        $(this).data('select2').$dropdown.find(':input.select2-search__field').attr('placeholder',
                            'Cari Nama Customer');
                    });

                var selectedCustomerId = "{{ old('customer_id', $data->customer_id ?? '') }}";
                if (selectedCustomerId) {
                    @php
                        $customer = \App\Models\Customer::find(old('customer_id', $data->customer_id ?? ''));
                    @endphp
                    var selectedCustomerName = "{{ $customer->nama ?? '' }}";
                    var option = new Option(selectedCustomerName, selectedCustomerId, true, true);
                    $('#customer_id').append(option).trigger('change');
                }

                function formatNumberInput(element) {
                    var value = $(element).val();
                    value = value.replace(/[^0-9]/g, '');
                    value = new Intl.NumberFormat('id-ID', {
                        minimumFractionDigits: 0
                    }).format(value);
                    $(element).val(value);
                }

                $(document).on('keyup', '.number-format', function() {
                    formatNumberInput(this);
                }).find('.number-format').each(function() {
                    formatNumberInput(this);
                });
                
                $('#status').select2({
                    allowClear: true,
                    debug: true,
                })

            });
            

            function calculateTotalPrice(element) {
                const $row = $(element).closest('.row');
                const qty = parseFloat($row.find('#qty').val()) || 0;

                if (isNaN(qty)) {
                    return;
                }

                const totalPrice = qty;
                const formattedTotalPrice = new Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(totalPrice);

                $row.find('.totalPrice').text(formattedTotalPrice);
                calculateTotalAllPrice();
            }


            function calculateTotalAllPrice() {
                let totalAllPrice = 0;
                $('.listProduct .row').each(function() {
                    const qty = parseFloat($(this).find('#qty').val()) || 0;
                    totalAllPrice += qty;
                });

                $('.totalAllPrice').text(totalAllPrice);
            }
        </script>
    </x-slot>
</x-app-layout>
