<script>
    function prod_req_table() {
        dt_produksi = $('#produksi_berjalan_onreq').DataTable({
            // processing: true,
            // serverSide: true,
            scrollX: false,
            // lengthChange: false,
            bPaginate: false,
            info: false,
            ordering: false,
            ajax: {
                url: "{{ route('dashboard.table_get_produksi_berjalan') }}",
                data: function(d){
                    d.month = $('#month').val();
                    d.year = $('#year').val();
                }
            },
            deferRender: true,
            columnDefs: [ 
                {  
                    type: 'date', 
                    targets: [2, 3],
                    render: function (data, type, row) {
                        // Check if the date is not empty
                        if (data && data.trim() !== '' && data.trim() !== '30 Nov -0001') {
                            return data;
                        } else {
                            // Return an empty string for empty dates
                            return '';
                        }
                    }
                },
            ],
            order: [[ 3, 'desc' ]],
            columns: [
                {
                    data: 'nama',
                    name: 'nama',
                    width: '12%'
                },
                {
                    data: 'pic',
                    name: 'pic',
                    width: '5%'
                },
                {
                    data: 'tgl_faw',
                    name: 'tgl_faw',
                    width: '12%'
                },
                {
                    data: 'tgl_deadline',
                    name: 'tb_faws.tgl_deadline',
                    width: '12%'
                },
                {
                    data: 'progress_produksi',
                    name: 'progress_produksi',
                    width: '12%'
                },
                {
                    data: 'keterangan_tambahan',
                    name: 'keterangan_tambahan',
                    width: '47%'
                }
            ],
            
        });
    }
    $(document).ready(function () {

        $('#year').each(function () {

            var year = (new Date()).getFullYear();
            var current = year;
            year -= 3;
            for (var i = 0; i < 6; i++) {
                if ((year + i) == current)
                    $(this).append('<option selected value="' + (year + i) + '">' + (year + i) + '</option>');
                else
                    $(this).append('<option value="' + (year + i) + '">' + (year + i) + '</option>');
            }

        });

        prod_req_table()

        $('#jenis_kertas_onreq').hide();

        $('#month, #year').on("change", function (event) {
            console.log("???");
            event.preventDefault();

            $('#jenis_kertas').hide();
            $('#jenis_kertas_onreq').show();

            $.ajax({
                data: $('#month, #year').serialize(),
                        url: "{{ route('dashboard.get_jenis_kertas') }}",
                        dataType: 'JSON',
                        success: function (data) {
                            $.each(data[0], function(index, value){
                                if(value < 1){
                                    jenis_kertas = 0;
                                }else{
                                    jenis_kertas = value;
                                }
                                $('#jenis_kertas'+index).html(jenis_kertas);
                            });
                            
                        }
            });

            $.ajax({
                data: $('#month, #year').serialize(),
                url: "{{ route('dashboard.get_production_report') }}",
                dataType: 'JSON',
                success: function (data) {
                    $('#total_produksi').html(data[0].total_produksi.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                    $('#count_spk_total').html(data[0].count_spk_total.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                    $('#count_spk_tuntas').html(data[0].count_spk_tuntas.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                    $('#count_spk_reject').html(data[0].count_spk_reject.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                    $('#count_spk_berjalan').html(data[0].count_spk_berjalan.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                    $('#count_spk_total_dummy').html(data[0].count_spk_total_dummy.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                    $('#count_spk_tuntas_dummy').html(data[0].count_spk_tuntas_dummy.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                    // $('#count_spk_reject_dummy').html(data[0].count_spk_reject_dummy.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                    $('#count_spk_berjalan_dummy').html(data[0].count_spk_berjalan_dummy.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));

                    
                },
                error: function (data) {
                    console.log('Error:', data);
                }
            });

            $("#produksi_berjalan_onreq").dataTable().fnDestroy();

            prod_req_table()
        });
    }); </script>
