<div class="row g-5 g-xl-8" style="margin-top: 100px">
    <div class="row justify-content-between">
        <div class="col-md-4">
            <div class="input-group mb-3">
                <label class="input-group-text" for="pic_sales">PIC</label>
                <select class="form-select" id="pic_sales" name="pic_sales">
                  <option value="All" selected>All</option>
                  @foreach($pic_sales as $pp)
                  <option value="{{ $pp['id'] }}">{{ $pp['name'] }}</option>
                  @endforeach
                </select>
            </div>
        </div>
        <div class="col-md-4">
            <div class="row">
                <div class="col-md-6">
                    <div class="input-group mb-3">
                        <select class="form-select" id="month" name="month">
                                <!-- <option value="">All</option> -->
                            @foreach(\Carbon\CarbonPeriod::create(now()->firstOfMonth(), '1 month', now()->addMonths(11)) as $date)
                                <option value="{{ $date->format('m') }}">
                                    {{ $date->format('F') }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group mb-3">
                        <div class="input-group mb-5" id="time_target_form">
                            <select name="year" class="form-select" id="year">
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row g-3">
        <!--begin::Col-->
        <div class="col-lg-6">
            <div class="card card-flush hover-elevate-up mb-5">
                <a href="{{ route('target') }}">
                    <div class="card-header mt-6">
                        <div class="flex-column justify-content-start">
                            <h3 class="fw-bolder mb-1">Total Target</h3>
                            <h1 class="fw-bolder mb-1 text-danger" id="total_target"><b>{{ number_format($total_target,0,'',',') }}</b></h1>
                        </div>
                        <div class="flex-column justify-content-end">
                            <h3 class="text-end fw-bolder mb-1" id="bulan_target">Capaian ({{ date('F') }})</h3>
                            <h1 class="text-end mb-1 text-success" id="total_realisasi">{{ number_format($total_realisasi,0,'',',') }}</h1>
                        </div>
                    </div>
                    <div class="card-body p-9 pt-5">
                        <div class="d-flex flex-column">
                            <div class="d-flex justify-content-between w-100 fs-6 mb-3">
                                <span>Progress To Target</span>
                                <span id="target_progress">{{ round($target_progress) }}%</span>
                            </div>
                            <div class="h-8px bg-light rounded mb-3">
                                <div class="bg-danger rounded h-8px" id="target_progress_percentage" role="progressbar" style="width: {{ $target_progress }}%;" aria-valuenow="0"
                                    aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
            <div class="card card-flush">
                <div class="card-header">
                    <h3 class="card-title fw-bold mb-3">Detail Customer Deal</h3>
                </div>
                <div class="card-body p-9 pt-5 hover-scroll-overlay-y" style="height: 300px">
                    <table class="table table-row-bordered" id="list_customer_repeat">
                        <tbody>
                            <tr>
                                <td>Nama Konsumen</td>
                                <td>Jumlah Order</td>
                                <td>Sumber</td>
                                <td>Total harga</td>
                                <td>Modal Sales</td>
                            </tr>
                            @foreach ($list_customer_repeat as $row)
                            <tr>
                                <td>{{ $row->nama }}</td>
                                <td>{{ $row->cro }}</td>
                                <td>{{ $row->sumber }}</td>
                                <td>Rp{{ number_format($row->total_harga) }}</td>
                                <td>Rp{{ number_format($row->total_modal_sales) }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <table class="table table-row-bordered dataTable" id="list_customer_repeat_onreq">
                        <thead>
                            <tr>
                                <td>Nama Konsumen</td>
                                <td>Jumlah Order</td>
                                <td>Sumber</td>
                                <td>Total harga</td>
                                <td>Modal Sales</td>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
                <div class="mt-3 px-9 bg-white rounded-bottom">
                    <table style="width:100%;" class="table table-row-bordered">
                        <tr>
                            <td style="width:62%" class="fw-bolder text-nowrap text-center">Jumlah Total</td>
                            <td id="load_total_sum">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </td>
                            <td style="width:20%" class="fw-bolder text-center" id="sum_total_price">Rp{{ number_format($total_detail_deal,0,'',',') }}</td>
                            <td style="width:18%" class="fw-bolder text-center" id="sum_modal_price">Rp{{ number_format($total_modal_deal,0,'',',') }}</td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="row">
                <div class="col-md-4">
                    <div class="card card-flush hover-elevate-up mb-9 special-card">
                        <a class="m-6" href="{{route('crm.price_null')}}">
                            <div class="menu-icon mb-2">
                                <i class="fa-solid fa-user-group fs-3x text-my-primary" style="color: #fff"></i>
                            </div>
                            <div class="card-body d-flex flex-column mb-3 p-0">
                                <h1 class="fw-bolder mb-1 text-my-primary" id="count_price_null">{{ $count_price_null }}</h1>
                                <span class="text-my-primary pt-1 fw-semibold fs-6">Customer yang belum diberi harga</span>
                            </div>
                        </a>
                    </div>
                    <div class="card my-primary card-flush hover-elevate-up special-card">
                        <a class="m-6" href="{{route('crm.prioritas')}}">
                            <div class="menu-icon mb-2">
                                <i class="fa-solid fa-star fs-3x" style="color: #fff"></i>
                            </div>
                            <div class="card-body d-flex flex-column mb-3 p-0">
                                <h1 class="fw-bolder mb-1 text-white" id="count_prioritas">{{ $count_prioritas }}</h1>
                                <span class="text-white pt-1 fw-semibold fs-6">Customer Prioritas</span>
                            </div>
                            {{-- <div class="card my-primary mb-5">
                                
                            </div> --}}
                        </a>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-indigo w-100 mb-5">
                                <div class="card-body">
                                    <span class="text-white mb-3">Potensi Emas All</span>
                                    <h1 class="fw-bolder text-white" id="potensi_all"></h1>
                                </div>
                            </div>
                            <div class="card w-100 mb-5" style="background-color: #03CE66">
                                <div class="card-body">
                                    <span class="text-white mb-3">Omzet Repeat Order</span>
                                    <h1 class="fw-bolder text-white" id="omzet_repeat"></h1>
                                </div>
                            </div>
                            <div class="card bg-warning w-100 mb-5">
                                <div class="card-body">
                                    <span class="text-white mb-3">Potensi Follow Up</span>
                                    <h1 class="fw-bolder text-white" id="potensi_followup"></h1>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card w-100 mb-5" style="background-color: #03CE66">
                                <div class="card-body">
                                    <span class="text-white mb-3">Omzet New</span>
                                    <h1 class="fw-bolder text-white" id="omzet_deal"></h1>
                                </div>
                            </div>
                            <div class="card w-100 mb-5" style="background-color: #03CE66">
                                <div class="card-body">
                                    <span class="text-white mb-3">Omzet Lainnya</span>
                                    <h1 class="fw-bolder text-white" id="omzet_other"></h1>
                                </div>
                            </div>
                            <div class="card bg-danger w-100 mb-5">
                                <div class="card-body">
                                    <span class="text-white mb-3">Potensi Lost</span>
                                    <h1 class="fw-bolder text-white" id="potensi_lost"></h1>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-4">
                    <div class="card bg-indigo w-100 mb-5">
                        <div class="card-body" style="min-height: 115px">
                            <span class="text-white mb-3">Modal Sales</span>
                            <h1 class="fw-bolder text-white" id="modal_sales"></h1>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="card w-100 mb-5" style="background-color: #03CE66">
                        <div class="card-body" style="min-height: 115px">
                            <span class="text-white mb-3">CR New (Omzet)</span>
                            <h1 class="fw-bolder text-white mt-3" id="avg_closing_rate_per_omzet">{{ round($avg_closing_rate_per_omzet,2) }}%</h1>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="card w-100 mb-5" style="background-color: #03CE66">
                        <div class="card-body" style="min-height: 115px">
                            <span class="text-white mb-3">CR per-Customer</span>
                            <h1 class="fw-bolder text-white mt-3" id="avg_closing_rate_per_customer">{{ round($avg_closing_rate_per_customer,2) }}%</h1>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="card w-100 mb-5" style="background-color: #03CE66">
                        <div class="card-body">
                            <span class="text-white mb-3">Total Kontak New</span>
                            <h1 class="fw-bolder text-white" id="count_kontak_masuk">{{ $count_kontak_masuk }}</h1>
                            <h3 class="fw-bolder text-white">Kontak Masuk</h3>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="card w-100 mb-5" style="background-color: #03CE66">
                        <div class="card-body">
                            <span class="text-white mb-3">Gross Margin</span>
                            <h1 class="fw-bolder text-white" id="count_kontak_masuk">{{number_format(100 - $gross_margin ?? 0, 2)}}%</h1>
                        </div>
                    </div>
                </div>
                <div class="col-4">
                    <div class="card w-100 mb-5" style="background-color: #03CE66">
                        <div class="card-body">
                            <span class="text-white mb-3">Sales Lifecycle</span>
                            <h1 class="fw-bolder text-white" id="count_kontak_masuk">{{$count_cycle ?? 0}} days</h1>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <h1 class="fw-bold mb-5">Customer Journey</h1>
    <div class="row d-flex mb-5">
        <div class="col">
            <div class="card w-100" style="min-height: 150px;">
                <div class="card-body p-4 d-flex align-items-center justify-content-center">
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <p class="text-muted text-center">Konsumen Follow Up</p>
                        <p class="badge badge-success badge-lg">Follow Up</p>
                        <h3 class="fw-semibold text-center" id="count_total_fu">{{ $count_total_fu }}</h3>
                    </div>
                </div>
            </div>
            <div class="card w-100 mt-3" style="min-height: 150px;">
                <div class="card-body scroll-y bg-secondary rounded-top" style="height: 225px; padding: 0 20px 0 20px">
                    <table class="table table-row-bordered" id="total_data_fu">
                        <tbody style="font-size: x-small">
                            @foreach ($total_data_fu as $index => $row)
                            <tr class="row">
                                <td class="col-3">{{ $loop->index + 1 }}</td>
                                <td class="col-5">{{ $row->nama }}</td>
                                <td class="col-4">{{ $row->total_harga == 0 ? 0 : number_format($row->total_harga / 1000000,1,'.','') }}M</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <table class="table table-row-bordered dataTable" id="total_data_fu_onreq_table">
                        <tbody style="font-size: x-small" id="total_data_fu_onreq">

                        </tbody>
                    </table>
                </div>
                <div class="bg-secondary rounded-bottom" style="height: 30px; padding: 0 20px 0 20px">
                    <table class="table table-row-bordered">
                        <tbody style="font-size: x-small; font-weight: bolder">
                            <tr class="row">
                                <td class="col-7">Grand Total</td>
                                <td class="col-5" id="gt_fu">{{ number_format($sum_gt_fu / 1000000,1,'.',',') }}M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card w-100" style="min-height: 150px;">
                <div class="card-body p-4 d-flex align-items-center justify-content-center">
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <p class="text-muted text-center">Belum Hitung Harga</p>
                        <p class="badge badge-lg" style="background-color: #DBA078">FU Emas</p>
                        <h3 class="fw-semibold text-center" id="count_total_fu_emas">{{ $count_total_fu_emas }}</h3>
                    </div>
                </div>
            </div>
            <div class="card w-100 mt-3" style="min-height: 150px;">
                <div class="card-body scroll-y bg-secondary rounded-top" style="height: 225px; padding: 0 20px 0 20px">
                    <table class="table table-row-bordered" id="total_data_fu_emas">
                        <tbody style="font-size: x-small">
                            @foreach ($total_data_fu_emas as $index => $row)
                            <tr class="row">
                                <td class="col-3">{{ $loop->index + 1 }}</td>
                                <td class="col-5">{{ $row->nama }}</td>
                                <td class="col-4">{{ $row->total_harga == 0 ? 0 : number_format($row->total_harga / 1000000,1,'.','') }}M</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <table class="table table-row-bordered dataTable" id="total_data_fu_emas_onreq_table">
                        <tbody style="font-size: x-small" id="total_data_fu_emas_onreq">

                        </tbody>
                    </table>
                </div>
                <div class="bg-secondary rounded-bottom" style="height: 30px; padding: 0 20px 0 20px">
                    <table class="table table-row-bordered">
                        <tbody style="font-size: x-small; font-weight: bolder">
                            <tr class="row">
                                <td class="col-7">Grand Total</td>
                                <td class="col-5" id="gt_emas">{{ number_format($sum_gt_emas / 1000000,1,'.','') }}M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card w-100" style="min-height: 150px;">
                <div class="card-body p-4 d-flex align-items-center justify-content-center">
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <p class="text-muted text-center">Sudah Diberikan Harga</p>
                        <p class="badge badge-lg" style="background-color: #2477cf">Bronze</p>
                        <h3 class="fw-semibold text-center" id="count_total_fu_bronze">{{ $count_total_fu_bronze }}</h3>
                    </div>
                </div>
            </div>
            <div class="card w-100 mt-3" style="min-height: 150px;">
                <div class="card-body scroll-y bg-secondary rounded-top" style="height: 225px; padding: 0 20px 0 20px">
                    <table class="table table-row-bordered" id="total_data_fu_bronze">
                        <tbody style="font-size: x-small">
                            @foreach ($total_data_fu_bronze as $index => $row)
                            <tr class="row">
                                <td class="col-3">{{ $loop->index + 1 }}</td>
                                <td class="col-5">{{ $row->nama }}</td>
                                <td class="col-4">{{ $row->total_harga == 0 ? 0 : number_format($row->total_harga / 1000000,1,'.','') }}M</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <table class="table table-row-bordered dataTable" id="total_data_fu_bronze_onreq_table">
                        <tbody style="font-size: x-small" id="total_data_fu_bronze_onreq">

                        </tbody>
                    </table>
                </div>
                <div class="bg-secondary rounded-bottom" style="height: 30px; padding: 0 20px 0 20px">
                    <table class="table table-row-bordered">
                        <tbody style="font-size: x-small; font-weight: bolder">
                            <tr class="row">
                                <td class="col-7">Grand Total</td>
                                <td class="col-5" id="gt_bronze">{{ number_format($sum_gt_bronze / 1000000,1,'.','') }}M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="col">
            <div class="card w-100" style="min-height: 150px;">
                <div class="card-body p-4 d-flex align-items-center justify-content-center">
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <p class="text-muted text-center">Tertarik Membuat Dummy</p>
                        <p class="badge badge-lg" style="background-color: #D9214E">Platinum</p>
                        <h3 class="fw-semibold text-center" id="count_total_fu_platinum">{{ $count_total_fu_platinum }}</h3>
                    </div>
                </div>
            </div>
            <div class="card w-100 mt-3" style="min-height: 150px;">
                <div class="card-body scroll-y bg-secondary rounded-top" style="height: 225px; padding: 0 20px 0 20px">
                    <table class="table table-row-bordered" id="total_data_fu_platinum">
                        <tbody style="font-size: x-small">
                            @foreach ($total_data_fu_platinum as $index => $row)
                            <tr class="row">
                                <td class="col-3">{{ $loop->index + 1 }}</td>
                                <td class="col-5">{{ $row->nama }}</td>
                                <td class="col-4">{{ $row->total_harga == 0 ? 0 : number_format($row->total_harga / 1000000,1,'.','') }}M</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <table class="table table-row-bordered dataTable" id="total_data_fu_platinum_onreq_table">
                        <tbody style="font-size: x-small" id="total_data_fu_platinum_onreq">

                        </tbody>
                    </table>
                </div>
                <div class="bg-secondary rounded-bottom" style="height: 30px; padding: 0 20px 0 20px">
                    <table class="table table-row-bordered">
                        <tbody style="font-size: x-small; font-weight: bolder">
                            <tr class="row">
                                <td class="col-8">Grand Total</td>
                                <td class="col-4" id="gt_platinum">{{ number_format($sum_gt_platinum / 1000000,1,'.','') }}M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card w-100" style="min-height: 150px;">
                <div class="card-body p-4 d-flex align-items-center justify-content-center">
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <p class="text-muted text-center">Sudah Transfer Dummy</p>
                        <p class="badge badge-lg text-dark" style="background-color: #EAF2EE">Ruby</p>
                        <h3 class="fw-semibold text-center" id="count_total_fu_ruby">{{ $count_total_fu_ruby }}</h3>
                    </div>
                </div>
            </div>
            <div class="card w-100 mt-3" style="min-height: 150px;">
                <div class="card-body scroll-y bg-secondary rounded-top" style="height: 225px; padding: 0 20px 0 20px">
                    <table class="table table-row-bordered" id="total_data_fu_ruby">
                        <tbody style="font-size: x-small">
                            @foreach ($total_data_fu_ruby as $index => $row)
                            <tr class="row">
                                <td class="col-3">{{ $loop->index + 1 }}</td>
                                <td class="col-5">{{ $row->nama }}</td>
                                <td class="col-4">{{ $row->total_harga == 0 ? 0 : number_format($row->total_harga / 1000000,1,'.','') }}M</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <table class="table table-row-bordered dataTable" id="total_data_fu_ruby_onreq_table">
                        <tbody style="font-size: x-small" id="total_data_fu_ruby_onreq">

                        </tbody>
                    </table>
                </div>
                <div class="bg-secondary rounded-bottom" style="height: 30px; padding: 0 20px 0 20px">
                    <table class="table table-row-bordered">
                        <tbody style="font-size: x-small; font-weight: bolder">
                            <tr class="row">
                                <td class="col-8">Grand Total</td>
                                <td class="col-4" id="gt_ruby">{{ number_format($sum_gt_ruby / 1000000,1,'.','') }}M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card w-100" style="min-height: 150px;">
                <div class="card-body p-4 d-flex align-items-center justify-content-center">
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <p class="text-muted text-center">Sudah Confirm, Menunggu DP</p>
                        <p class="badge badge-primary badge-lg">Diamond</p>
                        <h3 class="fw-semibold text-center" id="count_total_fu_diamond">{{ $count_total_fu_diamond }}</h3>
                    </div>
                </div>
            </div>
            <div class="card w-100 mt-3" style="min-height: 150px;">
                <div class="card-body scroll-y bg-secondary rounded-top" style="height: 225px; padding: 0 20px 0 20px">
                    <table class="table table-row-bordered" id="total_data_fu_diamond">
                        <tbody style="font-size: x-small">
                            @foreach ($total_data_fu_diamond as $index => $row)
                            <tr class="row">
                                <td class="col-3">{{ $loop->index + 1 }}</td>
                                <td class="col-5">{{ $row->nama }}</td>
                                <td class="col-4">{{ $row->total_harga == 0 ? 0 : number_format($row->total_harga / 1000000,1,'.','') }}M</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <table class="table table-row-bordered dataTable" id="total_data_fu_diamond_onreq_table">
                        <tbody style="font-size: x-small" id="total_data_fu_diamond_onreq">

                        </tbody>
                    </table>
                </div>
                <div class="bg-secondary rounded-bottom" style="height: 30px; padding: 0 20px 0 20px">
                    <table class="table table-row-bordered">
                        <tbody style="font-size: x-small; font-weight: bolder">
                            <tr class="row">
                                <td class="col-8">Grand Total</td>
                                <td class="col-4" id="gt_diamond">{{ number_format($sum_gt_diamond / 1000000,1,'.','') }}M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card w-100" style="min-height: 150px;">
                <div class="card-body p-4 d-flex align-items-center justify-content-center">
                    <div class="d-flex flex-column justify-content-center align-items-center">
                        <p class="text-muted text-center">Konsumen Sudah Deal</p>
                        <p class="badge badge-lg" style="background-color: #03CE66">Deal</p>
                        <h3 class="fw-semibold text-center" id="count_total_fu_deal">{{ $count_total_fu_deal }}</h3>
                    </div>
                </div>
            </div>
            <div class="card w-100 mt-3" style="min-height: 150px;">
                <div class="card-body scroll-y bg-secondary rounded-top" style="height: 225px; padding: 0 20px 0 20px">
                    <table class="table table-row-bordered" id="total_data_fu_deal">
                        <tbody style="font-size: x-small">
                            @foreach ($total_data_fu_deal as $index => $row)
                            <tr class="row">
                                <td class="col-3">{{ $loop->index + 1 }}</td>
                                <td class="col-5">{{ $row->nama }}</td>
                                <td class="col-4">{{ $row->total_harga == 0 ? 0 : number_format($row->total_harga / 1000000,1,'.','') }}M</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <table class="table table-row-bordered dataTable" id="total_data_fu_deal_onreq_table">
                        <tbody style="font-size: x-small" id="total_data_fu_deal_onreq">

                        </tbody>
                    </table>
                </div>
                <div class="bg-secondary rounded-bottom" style="height: 30px; padding: 0 20px 0 20px">
                    <table class="table table-row-bordered">
                        <tbody style="font-size: x-small; font-weight: bolder">
                            <tr class="row">
                                <td class="col-7">Grand Total</td>
                                <td class="col-5" id="gt_deal">{{ number_format($sum_gt_deal / 1000000,1,'.','') }}M</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="row mb-5">
        <div class="col-md-4">
            <div class="card card-flush mb-5 mb-xl-10 h-lg-100">
                <div class="card-header pt-5">
                    <h3 class="card-title align-items-start flex-column">
                        <span class="card-label fw-bold text-dark">Ketercapaian Target Sales</span>
                    </h3>
                </div>
                <div class="card-body d-flex align-items-end px-0 pb-0 mt-0">
                    <canvas id="chart_sales_1" class="p-5 mh-200px" ></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card card-flush h-lg-100">
                <div class="card-header pt-5">
                    <h3 class="card-title align-items-start flex-column">
                        <span class="card-label fw-bold text-dark">Pie Chart Lost</span>
                    </h3>
                </div>
                <div class="card-body d-flex align-items-end pt-6">
                    <div class="row align-items-center mx-0 w-100">
                        <div class="col d-flex justify-content-end px-0">
                            <canvas id="chart_sales_2" class="mh-200px"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row mb-5">
        <div class="col-md-4">
            <div class="card card-flush w-100 mb-5">
                <div class="card-header mt-6">
                        <div class="flex-column justify-content-start">
                            <h3 class="fw-bolder mb-1">Sumber Deal</h3>
                        </div>
                        <!-- <div class="flex-column justify-content-end">
                            <select name="omzet_by" id="omzet_by" class="form-select">
                                <option value="sumber">By Sumber</option>
                                <option value="instansi">By Instansi</option>
                                <option value="produk">By Produk</option>
                            </select>
                        </div> -->
                </div>
                <div class="card-body pt-0 pb-0 hover-scroll-overlay-y" style="height: 232px">
                    <div>
                        <table id="datatable_omzet_online"
                            class="table-row-bordered compact text-nowrap dataTable no-footer table-striped table align-middle">
                            <thead>
                                <tr class="fw-bold">
                                    <th class="text-center align-middle">Sumber</th>
                                    <th class="text-center align-middle">Total Omzet</th>
                                </tr>
                            </thead>
                            <tbody> </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="card w-100 mb-5">
                <div class="card-header mt-6">
                    <div class="flex-column justify-content-start">
                        <h3 class="fw-bolder mb-1">Data Penyebab Lost</h3>
                    </div>
                </div>
                <div class="card-body p-9 pt-5 hover-scroll-overlay-y" style="height: 232px">
                    <table class="table table-row-bordered" id="list_lost">
                        <tbody>
                            <tr>
                                <td>Nama Customer</td>
                                <td>Keterangan Lost</td>
                                <td>Total Harga</td>
                            </tr>
                            @foreach ($list_lost as $row)
                            <tr>
                                <td>{{ $row->nama }}</td>
                                <td>{{ $row->status_order }}</td>
                                <td>Rp{{ number_format($row->total_harga) }}</td>
                            </tr>
                            @endforeach
                            @if ($list_lost_total > 0)
                            <tr>
                                <td></td>
                                <td>Total Lost</td>
                                <td>Rp{{ number_format($list_lost_total) }}</td>
                            </tr>
                            @endif
                        </tbody>
                    </table>
                    <table class="table table-row-bordered dataTable" id="list_customer_lost">
                        <thead>
                            <tr>
                                <td>Nama Konsumen</td>
                                <td>Keterangan Lost</td>
                                <td>Total Harga</td>
                            </tr>
                        </thead>
                        <tbody>
                            
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
