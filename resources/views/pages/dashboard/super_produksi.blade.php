<div class="row g-5 g-xl-8" style="margin-top: 100px">
    <div class="row justify-content-end">
        <div class="col-md-4">
            <div class="row">
                <div class="col-md-6 float-end">
                    <div class="input-group mb-3">
                        <select class="form-select" id="month_production" name="month">
                           @foreach(\Carbon\CarbonPeriod::create(now(), '1 month', now()->addMonths(11)) as $date)
                                <option value="{{ $date->format('m') }}">
                                    {{ $date->format('F') }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="input-group mb-3">
                        <div class="input-group mb-5" id="time_target_form">
                            <select name="year" class="form-select" id="year_production" style="width: 120px;">
                            </select>
                            <span class="input-group-text" id="basic-addon2">
                                <i class="fa fa-calendar"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row d-flex mb-3 mt-4">
            <div class="col">
                <div class="card mb-5 bg-primary text-white">
                    <h3 class="menu-icon m-6 mb-0 fw-semibold text-center text-white">
                        Jumlah Produksi <small>(pcs)</small>
                    </h3>
                    <div class="m-3 text-center">
                        <h1 class="fw-bolder mb-1 text-white" id="total_produksi">{{ number_format($total_produksi,0,'',',') }}</h1>
                        <span class="pt-1 fw-semibold fs-6">Total</span>
                    </div>
                    <div class="card-body mb-3 p-5">
                        <div class="row">
                            <div class="col border-end border-2 text-center">
                                <h2 class="fw-bolder mb-1 text-white" id="softbox_total">{{ number_format($softbox_total,0,'',',') }}</h2>
                                <span class="pt-2 fw-semibold fs-6">Softbox</span>
                            </div>
                            <div class="col border-end border-2 text-center">
                                <h2 class="fw-bolder mb-1 text-white" id="hardbox_total">{{ number_format($hardbox_total,0,'',',') }}</h2>
                                <span class="pt-2 fw-semibold fs-6">Hardbox</span>
                            </div>
                            <div class="col border-end border-2 text-center">
                                <h2 class="fw-bolder mb-1 text-white" id="corrugated_total">{{ number_format($corrugated_total,0,'',',') }}</h2>
                                <span class="pt-2 fw-semibold fs-6 text-nowrap">Corrugated Box</span>
                            </div>
                            <div class="col text-center">
                                <h2 class="fw-bolder mb-1 text-white" id="produksi_lainnya">{{ number_format($produksi_lainnya,0,'',',') }}</h2>
                                <span class="pt-2 fw-semibold fs-6 text-nowrap">Lainnya</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card mb-5 bg-success text-white">
                    <h3 class="menu-icon m-6 mb-0 fw-semibold text-center text-white">
                        SPK
                    </h3>
                    <div class="m-3 text-center">
                        <h1 class="fw-bolder mb-1 text-white" id="count_spk_total">{{ number_format($count_spk_total,0,'',',') }}</h1>
                        <span class="pt-1 fw-semibold fs-6">Total</span>
                    </div>
                    <div class="card-body d-flex flex-column mb-3 p-5">
                        <div class="row d-flex">
                            <div class="col border-end border-2 text-center">
                                <h2 class="fw-bolder mb-1 text-white" id="count_spk_tuntas">{{ number_format($count_spk_tuntas,0,'',',') }}</h2>
                                <span class="pt-2 fw-semibold fs-6">Tuntas</span>
                            </div>
                            <div class="col border-end border-2 text-center">
                                <h2 class="fw-bolder mb-1 text-white" id="count_spk_berjalan">{{ number_format($count_spk_berjalan,0,'',',') }}</h2>
                                <span class="pt-2 fw-semibold fs-6">Berjalan</span>
                            </div>
                            <div class="col text-center">
                                <h2 class="fw-bolder mb-1 text-white" id="count_spk_reject">{{ number_format($count_spk_reject,0,'',',') }}</h2>
                                <span class="pt-2 fw-semibold fs-6">Reject</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card mb-5 text-light bg-warning">
                    <h3 class="menu-icon m-6 mb-0 text-light fw-semibold text-center">
                        Dummy
                    </h3>
                    <div class="m-3 text-center">
                        <h1 class="fw-bolder mb-1 text-light" id="count_spk_total_dummy">{{ number_format($count_spk_total_dummy,0,'',',') }}</h1>
                        <span class="pt-1 fw-semibold fs-6">Total</span>
                    </div>
                    <div class="card-body d-flex flex-column mb-3 p-5">
                        <div class="row d-flex">
                            
                            <div class="col border-end border-2 text-center">
                                <h2 class="fw-bolder mb-1 text-light" id="count_spk_tuntas_dummy">{{ number_format($count_spk_tuntas_dummy,0,'',',') }}</h2>
                                <span class="pt-2 fw-semibold fs-6">Tuntas</span>
                            </div>
                            {{-- <div class="col border-end border-2 text-center">
                                <h1 class="fw-bolder mb-1" style="color: rgba(80, 205, 137);" id="count_spk_reject_dummy">{{ number_format($count_spk_reject_dummy,0,'',',') }}</h1>
                                <span class="pt-2 fw-semibold fs-6">Reject</span>
                            </div> --}}
                            <div class="col text-center">
                                <h2 class="fw-bolder mb-1 text-light" id="count_spk_berjalan_dummy">{{ number_format($count_spk_berjalan_dummy,0,'',',') }}</h2>
                                <span class="pt-2 fw-semibold fs-6">Berjalan</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
    <div class="row d-flex mb-5">
        <div class="col-lg-12">
            <div class="card card-flush">
                <div class="card-header">
                    <h3 class="card-title fw-bold mb-3">Produksi Berjalan</h3>
                </div>
                <div class="card-body p-9 pt-5 hover-scroll-overlay-y" style="height: 350px">
                    <table class="table table-row-bordered compact align-middle" id="produksi_berjalan_onreq">
                        <thead>
                            <tr class="fw-bold">
                                <td>Nama Konsumen</td>
                                <td>PIC</td>
                                <td>Tanggal FAW</td>
                                <td>Deadline</td>
                                <td>Progress Produksi</td>
                                <td class="text-center text-wrap">Catatan</td>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
 </div>