<script>

$(document).ready(function () {

    $('#year').each(function() {

        var year = (new Date()).getFullYear();
        var current = year;
        year -= 3;
        for (var i = 0; i < 6; i++) {
        if ((year+i) == current)
            $(this).append('<option selected value="' + (year + i) + '">' + (year + i) + '</option>');
        else
            $(this).append('<option value="' + (year + i) + '">' + (year + i) + '</option>');
        }

    })
    
    $('#omzet_instansi_section').hide();
    $('#omzet_tipe_produk_section').hide();

    $('#omzet_by').not('.form-select-solid').change(function(){
        var omz_by = $(this).val();

        if(omz_by == 'sumber'){
            $('#omzet_sumber_section').show();
            $('#omzet_instansi_section').hide();
            $('#omzet_tipe_produk_section').hide();
        }
        else if(omz_by == 'instansi'){
            $('#omzet_sumber_section').hide();
            $('#omzet_pic_section').hide();
            $('#omzet_instansi_section').show();
            $('#omzet_tipe_produk_section').hide();
        }
        else if(omz_by == 'produk'){
            $('#omzet_sumber_section').hide();
            $('#omzet_pic_section').hide();
            $('#omzet_instansi_section').hide();
            $('#omzet_tipe_produk_section').show();
        }
    });
    // Define chart element
    var cs1 = document.getElementById('chart_sales_1');

    const getLastNMonths = n => {           
        const d = new Date();
        const currentMonth = d.getMonth();
        const locale = 'en-GB';

        let result = [];
        for (let i = n; i > -1; i--) {
            d.setMonth(currentMonth - i);
            result.push(d.toLocaleDateString(locale, { month: 'long' }));
        }
        return result;
    };
   
    const data_target = [{{
                $annual_target->last_2month.
                ','.$annual_target->last_month.
                ','.$annual_target->this_month
    }}];
   
    const data_realisasi = [{{
                $annual_realisasi->last_2month.
                ','.$annual_realisasi->last_month.
                ','.$annual_realisasi->this_month
    }}];

    // Chart data
    const data = {
            labels: getLastNMonths(2),
            datasets: [{
                    label: 'Target',
                    data: data_target,
                    maxBarThickness: 30,
                    backgroundColor: '#E4620C',
                    stack: 'Stack 0',
                },
                {
                    label: 'Realisasi',
                    data: data_realisasi,
                    maxBarThickness: 30,
                    backgroundColor: '#50cd89',
                    stack: 'Stack 1',
                }
            ]
        };

    // Chart config
    const config = {
            type: 'bar',
            data: data,
            options: {
                plugins: {
                    title: {
                        display: false,
                    }
                },
                indexAxis: 'x',
                categoryPercentage: 0.6,
                barPercentage: 1.0,
                responsive: true,
                interaction: {
                    intersect: true,
                },
                scales: {
                    x: {
                        stacked: true,
                        grid: {
                            display: false,
                            offset: true
                        },
                    },
                    y: {
                        stacked: false,
                        grid: {
                            display: false,
                            offset: true
                        },
                    }
                }
            }
        };

        var myChart1 = new Chart(cs1, config);


        //chart sales 2
        var cs2 = document.getElementById('chart_sales_2');
        var coloR = [];
        var dynamicColors = function() {
            var r = Math.floor(Math.random() * 255);
            var g = Math.floor(Math.random() * 255);
            var b = Math.floor(Math.random() * 255);
            return "rgb(" + r + "," + g + "," + b + ")";
         };

        @foreach($lost as $lost2)
            coloR.push(dynamicColors());
        @endforeach

        const labels2 = [
            @foreach($lost as $lost)
            '{{ $lost->lost}}',
            @endforeach
        ];

        const data_lost = [
            @foreach($count_data_lost as $clost)
            '{{ $clost }}',
            @endforeach
        ];

        const data2 = {
            labels: labels2,
            datasets: [{
                data: data_lost,
                backgroundColor: coloR
            }],
        };

        // Chart config
        const config2 = {
            type: 'pie',
            data: data2,
            options: {
                plugins: {
                    title: {
                        display: false,
                    },
                    tooltip: {
                        intersect: false
                    },
                legend: {
                    display: true,
                    position: 'left'
                }
                },
                responsive: true,
                interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
                }
            }
        };
        var myChart2 = new Chart(cs2, config2);

        function nFormatter(num) {
            if (num >= 1000000000) {
                return (num / 1000000000).toFixed(2).replace(/\.0$/, '') + 'B';
            }
            if (num >= 1000000) {
                return (num / 1000000).toFixed(2).replace(/\.0$/, '') + 'M';
            }
            if (num >= 1000) {
                return (num / 1000).toFixed(2).replace(/\.0$/, '') + 'K';
            }
            return num;
        }
        
        $('#potensi_all').text(nFormatter({{ $potensi_all }}))
        $('#potensi_followup').text(nFormatter({{ $potensi_followup }}))
            
        $('#total_data_fu_onreq_table').hide();
        $('#total_data_fu_emas_onreq_table').hide();
        $('#total_data_fu_bronze_onreq_table').hide();
        $('#total_data_fu_platinum_onreq_table').hide();
        $('#total_data_fu_ruby_onreq_table').hide();
        $('#total_data_fu_diamond_onreq_table').hide();
        $('#total_data_fu_deal_onreq_table').hide();
        
        $('#list_customer_repeat').hide();
        // $('#list_customer_repeat_onreq').hide();
        $('#list_customer_lost').hide();
        // $('#list_customer_repeat_onreq').empty();
        
        dt_onrepeat = $('#list_customer_repeat_onreq').DataTable({
            processing: true,
            serverSide: true,
            lengthChange: false,
            bPaginate: false,
            info: false,
            ordering: false,
            ajax: {
                url: "{{ route('dashboard.table_get_sales_repeat') }}",
                data: function(d){
                    d.pic_sales = $('#pic_sales').val();
                    d.month = $('#month').val();
                    d.year = $('#year').val();
                }
            },
            columns: [
                {
                    data: 'nama',
                    name: 'nama'
                },
                {
                    data: 'total_harga',
                    name: 'total_harga'
                },
                {
                    data: 'cro',
                    name: 'cro'
                }
            ],
            
        });

        dt_lost = $('#list_customer_lost').DataTable({
            processing: true,
            serverSide: true,
            lengthChange: false,
            bPaginate: false,
            info: false,
            ordering: false,
            ajax: {
                url: "{{ route('dashboard.table_get_sales_lost') }}",
                data: function(d){
                    d.pic_sales = $('#pic_sales').val();
                    d.month = $('#month').val();
                    d.year = $('#year').val();
                }
            },
            columns: [
                {
                    data: 'nama',
                    name: 'nama'
                },
                {
                    data: 'status_order',
                    name: 'status_order'
                },
                {
                    data: 'tipe_produk',
                    name: 'tipe_produk'
                }
            ],
            
        });

        dt_omzet = $('#datatable_omzet_online').DataTable({
            // processing: true,
            // serverSide: true,
            lengthChange: false,
            bPaginate: false,
            info: false,
            ordering: false,
            ajax: {
                url: "{{ route('dashboard.table_omzet_online') }}",
                data: function(d){
                    d.pic_sales = $('#pic_sales').val();
                    d.month = $('#month').val();
                    d.year = $('#year').val();
                },
                // success: function(response) {
                //     // This function is called after a successful AJAX request.
                //     console.log(response); // Logs the response data
                // }
            },
            deferRender: true,
            columns: [
                {
                    data: 'sumber',
                    name: 'sumber'
                },
                {
                    data: 'total_harga',
                    name: 'total_harga'
                }
            ],
            
        });
        
        $('#omzet_sumber_onreq').hide();
        $('#omzet_instansi_onreq').hide();
        $('#omzet_tipe_produk_onreq').hide();

        $('#pic_sales, #month, #year').on("change", function(event){

            event.preventDefault();
            
            myChart1.destroy();
            myChart2.destroy();

            $('#omzet_sumber_onreq').show();
            $('#omzet_sumber').hide();
            $('#omzet_instansi').hide();
            $('#omzet_instansi_onreq').show();
            $('#omzet_tipe_produk_onreq').show();
            $('#omzet_tipe_produk').hide();

            $.ajax({
                data: $('#pic_sales, #month, #year').serialize(),
                        url: "{{ route('dashboard.get_sumber') }}",
                        dataType: 'JSON',
                        success: function (data) {
                            $.each(data[0], function(index, value){
                                $('#sumber'+index).html(Number(value).toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                            });
                            
                        }
            });

            $.ajax({
                data: $('#pic_sales, #month, #year').serialize(),
                        url: "{{ route('dashboard.get_tipe_instansi') }}",
                        dataType: 'JSON',
                        success: function (data) {
                            $.each(data[0], function(index, value){
                                $('#tipe_instansi'+index).html(Number(value).toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                            });
                            
                        }
            });

            $.ajax({
                data: $('#pic_sales, #month, #year').serialize(),
                        url: "{{ route('dashboard.get_tipe_produk') }}",
                        dataType: 'JSON',
                        success: function (data) {
                            $.each(data[0], function(index, value){
                                $('#tipe_produk'+index).html(Number(value).toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                            });
                            
                        }
            });

            $.ajax({
                        data: $('#pic_sales, #month, #year').serialize(),
                        url: "{{ route('dashboard.get_sales_report') }}",
                        dataType: 'JSON',
                        success: function (data) {

                            if (data[0].total_data_fu) {
                                $('#total_data_fu').hide();
                                $('#total_data_fu_onreq_table').show();
                                $('#total_data_fu_onreq').empty();

                                const data_fu = data[0].total_data_fu.map(function (data, index) {
                                    return `
                                    <tr class="row">
                                        <td class="col-3">${index+1}</td>
                                        <td class="col-5">${data.nama}</td>
                                        <td class="col-4">${nFormatter(+data.total_harga)}</td>
                                    </tr>
                                    `
                                })
                                
                                if (data_fu.length > 0) {
                                    $('#total_data_fu_onreq').html(data_fu)
                                } else {
                                    $('#total_data_fu_onreq').html(`<tr>
                                            <td class="text-center">Tidak ada data</td>
                                        </tr>`)
                                }
                            }

                            if (data[0].total_data_fu_emas) {
                                $('#total_data_fu_emas').hide();
                                $('#total_data_fu_emas_onreq_table').show();
                                $('#total_data_fu_emas_onreq').empty();

                                const data_fu = data[0].total_data_fu_emas.map(function (data, index) {
                                    return `
                                    <tr class="row">
                                        <td class="col-3">${index+1}</td>
                                        <td class="col-5">${data.nama}</td>
                                        <td class="col-4">${nFormatter(+data.total_harga)}</td>
                                    </tr>
                                    `
                                })

                                if (data_fu.length > 0) {
                                    $('#total_data_fu_emas_onreq').html(data_fu)
                                } else {
                                    $('#total_data_fu_emas_onreq').html(`<tr>
                                            <td class="text-center">Tidak ada data</td>
                                        </tr>`)
                                }
                            }

                            if (data[0].total_data_fu_bronze) {
                                $('#total_data_fu_bronze').hide();
                                $('#total_data_fu_bronze_onreq_table').show();
                                $('#total_data_fu_bronze_onreq').empty();

                                const data_fu = data[0].total_data_fu_bronze.map(function (data, index) {
                                    return `
                                    <tr class="row">
                                        <td class="col-3">${index+1}</td>
                                        <td class="col-5">${data.nama}</td>
                                        <td class="col-4">${nFormatter(+data.total_harga)}</td>
                                    </tr>
                                    `
                                })

                                if (data_fu) {
                                    $('#total_data_fu_bronze_onreq').html(data_fu)
                                } else {
                                    $('#total_data_fu_bronze_onreq').html(`<tr class="row">
                                        <td class="col-8">Tidak ada data</td>
                                        </tr>`)
                                }
                            }

                            if (data[0].total_data_fu_platinum) {
                                $('#total_data_fu_platinum').hide();
                                $('#total_data_fu_platinum_onreq_table').show();
                                $('#total_data_fu_platinum_onreq').empty();

                                const data_fu = data[0].total_data_fu_platinum.map(function (data, index) {
                                    return `
                                    <tr class="row">
                                        <td class="col-3">${index+1}</td>
                                        <td class="col-5">${data.nama}</td>
                                        <td class="col-4">${nFormatter(+data.total_harga)}</td>
                                    </tr>
                                    `
                                })
                                
                                if (data_fu.length > 0) {
                                    $('#total_data_fu_platinum_onreq').html(data_fu)
                                } else {
                                    $('#total_data_fu_platinum_onreq').html(`<tr>
                                            <td class="text-center">Tidak ada data</td>
                                        </tr>`)
                                }
                            }

                            if (data[0].total_data_fu_ruby) {
                                $('#total_data_fu_ruby').hide();
                                $('#total_data_fu_ruby_onreq_table').show();
                                $('#total_data_fu_ruby_onreq').empty();

                                const data_fu = data[0].total_data_fu_ruby.map(function (data, index) {
                                    return `
                                    <tr class="row">
                                        <td class="col-3">${index+1}</td>
                                        <td class="col-5">${data.nama}</td>
                                        <td class="col-4">${nFormatter(+data.total_harga)}</td>
                                    </tr>
                                    `
                                })
                                
                                if (data_fu.length > 0) {
                                    $('#total_data_fu_ruby_onreq').html(data_fu)
                                } else {
                                    $('#total_data_fu_ruby_onreq').html(`<tr>
                                            <td class="text-center">Tidak ada data</td>
                                        </tr>`)
                                }
                            }
                            if (data[0].total_data_fu_diamond) {
                                $('#total_data_fu_diamond').hide();
                                $('#total_data_fu_diamond_onreq_table').show();
                                $('#total_data_fu_diamond_onreq').empty();

                                const data_fu = data[0].total_data_fu_diamond.map(function (data, index) {
                                    return `
                                    <tr class="row">
                                        <td class="col-3">${index+1}</td>
                                        <td class="col-5">${data.nama}</td>
                                        <td class="col-4">${nFormatter(+data.total_harga)}</td>
                                    </tr>
                                    `
                                })
                                
                                if (data_fu.length > 0) {
                                    $('#total_data_fu_diamond_onreq').html(data_fu)
                                } else {
                                    $('#total_data_fu_diamond_onreq').html(`<tr>
                                            <td class="text-center">Tidak ada data</td>
                                        </tr>`)
                                }
                            }

                            if (data[0].total_data_fu_deal) {
                                $('#total_data_fu_deal').hide();
                                $('#total_data_fu_deal_onreq_table').show();
                                $('#total_data_fu_deal_onreq').empty();

                                const data_fu = data[0].total_data_fu_deal.map(function (data, index) {
                                    return `
                                    <tr class="row">
                                        <td class="col-3">${index+1}</td>
                                        <td class="col-5">${data.nama}</td>
                                        <td class="col-4">${nFormatter(+data.total_harga)}</td>
                                    </tr>
                                    `
                                })
                                
                                if (data_fu.length > 0) {
                                    $('#total_data_fu_deal_onreq').html(data_fu)
                                } else {
                                    $('#total_data_fu_deal_onreq').html(`<tr>
                                            <td class="text-center">Tidak ada data</td>
                                        </tr>`)
                                }
                            }

                            $('#gt_fu').html(nFormatter(data[0].sum_gt_fu));
                            $('#gt_emas').html(nFormatter(data[0].sum_gt_emas));
                            $('#gt_bronze').html(nFormatter(data[0].sum_gt_bronze));
                            $('#gt_platinum').html(nFormatter(data[0].sum_gt_platinum));
                            $('#gt_ruby').html(nFormatter(data[0].sum_gt_ruby));
                            $('#gt_diamond').html(nFormatter(data[0].sum_gt_diamond));
                            $('#gt_deal').html(nFormatter(data[0].sum_gt_deal));
                            $('#bulan_target').html('Capaian ('+data[0].bulan_target+')');
                            $('#total_target').html(data[0].total_target.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                            $('#total_realisasi').html(data[0].total_realisasi.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,"));
                            $('#omzet_deal').html(nFormatter(data[0].omzet_deal));
                            $('#modal_sales').html(nFormatter(data[0].modal_sales));
                            $('#omzet_repeat').html(nFormatter(data[0].omzet_repeat));
                            $('#potensi_all').html(nFormatter(data[0].potensi_all));
                            $('#potensi_followup').html(nFormatter(data[0].potensi_followup));
                            $('#potensi_lost').html(nFormatter(data[0].potensi_lost));
                            $('#target_progress').html(Math.round(data[0].target_progress)+'%');
                            $('#target_progress_percentage').width(data[0].target_progress+'%');
                            $('#count_price_null').html(data[0].count_price_null);
                            $('#count_prioritas').html(data[0].count_prioritas);
                            $('#count_omzet_deal').html(data[0].count_omzet_deal);
                            $('#count_potensi_all').html(data[0].count_potensi_all);
                            $('#avg_closing_rate_per_omzet').html(Number(data[0].avg_closing_rate_per_omzet).toFixed(2) + '%');
                            $('#avg_closing_rate_per_customer').html(Number(data[0].avg_closing_rate_per_customer).toFixed(2) + '%');
                            $('#count_konsumen_deal').html(data[0].count_konsumen_deal);
                            $('#count_kontak_masuk').html(data[0].count_kontak_masuk);
                            $('#count_kontak_masuk_emas').html(data[0].count_kontak_masuk_emas);
                            $('#count_total_fu').html(data[0].count_total_fu);
                            $('#count_total_fu_emas').html(data[0].count_total_fu_emas);
                            $('#count_total_fu_bronze').html(data[0].count_total_fu_bronze);
                            $('#count_total_fu_ruby').html(data[0].count_total_fu_ruby);
                            $('#count_total_fu_platinum').html(data[0].count_total_fu_platinum);
                            $('#count_total_fu_diamond').html(data[0].count_total_fu_diamond);
                            $('#count_total_fu_deal').html(data[0].count_total_fu_deal);
                            $('#count_data_lost').html(data[0].count_data_lost);
                            $('#omzet_other').html(nFormatter(data[0].omzet_other));

                            // Define chart element
                            var cs1 = document.getElementById('chart_sales_1');

                           
                            if(data[0].selected_month <= '3'){
                                var getMonths = n => {           
                                    const d = new Date();
                                    
                                    const currentMonth = data[0].selected_month-3;
                                    const locale = 'en-GB';
                                    // alert(data[0].selected_month);
                                    let result = [];
                                    for (let i = n; i < 5; i++) {
                                        d.setMonth(currentMonth + i);
                                        result.push(d.toLocaleDateString(locale, { month: 'long' }));
                                    }
                                    return result;
                                };

                                var data_target = [
                                        data[0].annual_target.this_month,
                                        data[0].annual_target.last_month,
                                        data[0].annual_target.last_2month
                                ];

                                var data_realisasi = [
                                        data[0].annual_realisasi.this_month,
                                        data[0].annual_realisasi.last_month,
                                        data[0].annual_realisasi.last_2month
                            ];
                            
                            } else {
                                var getMonths = n => {           
                                const d = new Date();
                                
                                const currentMonth = data[0].selected_month-1;
                                const locale = 'en-GB';
                                // alert(data[0].selected_month);
                                let result = [];
                                for (let i = n; i > -1; i--) {
                                    d.setMonth(currentMonth - i);
                                    result.push(d.toLocaleDateString(locale, { month: 'long' }));
                                }
                                return result;
                                };

                                var data_target = [
                                            data[0].annual_target.last_2month,
                                            data[0].annual_target.last_month,
                                            data[0].annual_target.this_month
                                ];
                            
                                var data_realisasi = [
                                            data[0].annual_realisasi.last_2month,
                                            data[0].annual_realisasi.last_month,
                                            data[0].annual_realisasi.this_month
                                ];
                            }

                            // Chart data
                            const datay = {
                                    labels: getMonths(2),
                                    datasets: [{
                                            label: 'Target',
                                            data: data_target,
                                            maxBarThickness: 30,
                                            backgroundColor: '#E4620C',
                                            stack: 'Stack 0',
                                        },
                                        {
                                            label: 'Realisasi',
                                            data: data_realisasi,
                                            maxBarThickness: 30,
                                            backgroundColor: '#50cd89',
                                            stack: 'Stack 1',
                                        }
                                    ]
                                };

                            // Chart config
                            const config = {
                                type: 'bar',
                                data: datay,
                                options: {
                                    plugins: {
                                        title: {
                                            display: false,
                                        }
                                    },
                                    indexAxis: 'x',
                                    categoryPercentage: 0.6,
                                    barPercentage: 1.0,
                                    responsive: true,
                                    interaction: {
                                        intersect: true,
                                    },
                                    scales: {
                                        x: {
                                            stacked: true,
                                            grid: {
                                                display: false,
                                                offset: true
                                            },
                                        },
                                        y: {
                                            stacked: false,
                                            grid: {
                                                display: false,
                                                offset: true
                                            },
                                        }
                                    }
                                }
                            };
                            myChart1.destroy();
                            myChart1 = new Chart(cs1, config);

                            //chart sales 2
                            var cs2 = document.getElementById('chart_sales_2');
                            var coloR = [];
                            var dynamicColors = function() {
                                var r = Math.floor(Math.random() * 255);
                                var g = Math.floor(Math.random() * 255);
                                var b = Math.floor(Math.random() * 255);
                                return "rgb(" + r + "," + g + "," + b + ")";
                            };

                            for(let i in data[0].lost){
                                coloR.push(dynamicColors());
                            }

                                a = [];
                                for(i in data){
                                    const labels2 = "["+a.push(data[i].lost)+"]";
                                }
                            
                            var data_lost = [];
                            $.each(data[0].data_lost[0], function(index, value){
                                data_lost.push(value);
                            });

                            const data2 = {
                                labels: labels2,
                                datasets: [{
                                    data: data_lost,
                                    backgroundColor: coloR
                                }],
                            };

                            // Chart config
                            const config2 = {
                                type: 'pie',
                                data: data2,
                                options: {
                                    plugins: {
                                        title: {
                                            display: false,
                                        },
                                        tooltip: {
                                            intersect: false
                                        },
                                    legend: {
                                        display: true,
                                        position: 'left'
                                    }
                                    },
                                    responsive: true,
                                    interaction: {
                                    mode: 'nearest',
                                    axis: 'x',
                                    intersect: false
                                    }
                                }
                            };
                            myChart2.destroy();
                            myChart2 = new Chart(cs2, config2);

                            $("#list_customer_lost").empty()
                            $("#list_customer_lost").dataTable().fnDestroy();
                            dt_onrepeat = $('#list_customer_lost').DataTable({
                                processing: true,
                                serverSide: true,
                                // lengthChange: false,
                                // bPaginate: false,
                                // info: false,
                                ajax: {
                                    url: "{{ route('dashboard.table_get_sales_lost') }}",
                                    data: function(d){
                                        d.pic_sales = $('#pic_sales').val();
                                        d.month = $('#month').val();
                                        d.year = $('#year').val();
                                    }
                                },
                                columns: [
                                    {
                                        data: 'nama',
                                        name: 'nama'
                                    },
                                    {
                                        data: 'status_order',
                                        name: 'status_order'
                                    },
                                    {
                                        data: 'total_harga',
                                        name: 'total_harga'
                                    }
                                ],
                                
                            });

                            if (data[0].list_lost_total) {
                                $('#list_customer_lost').append(`
                                    <tbody>
                                        <tr>
                                            <td></td>
                                            <td>Total Lost</td>
                                            <td>Rp${nFormatter(+data[0].list_lost_total)}</td>
                                        </tr>
                                    </tbody>
                                `)
                            }

                        },
                        error: function (data) {
                            console.log('Error:', data);
                        }
                    })

                    $('#list_customer_repeat_onreq').show();
                    $('#list_customer_repeat').hide();
                    $('#list_lost').hide();
                    $('#list_customer_lost').show();

                    $("#datatable_omzet_online").dataTable().fnDestroy();
                    dt_omzet = $('#datatable_omzet_online').DataTable({
                        processing: true,
                        serverSide: true,
                        // lengthChange: false,
                        // bPaginate: false,
                        // info: false,
                        ordering: false,
                        ajax: {
                            url: "{{ route('dashboard.table_omzet_online') }}",
                            data: function(d){
                                d.pic_sales = $('#pic_sales').val();
                                d.month = $('#month').val();
                                d.year = $('#year').val();
                            }
                        },
                        columns: [
                            {
                                data: 'sumber',
                                name: 'sumber'
                            },
                            {
                                data: 'total_harga',
                                name: 'total_harga'
                            }
                        ],
                        
                    });

                    $("#list_customer_repeat_onreq").dataTable().fnDestroy();
                    dt_onrepeat = $('#list_customer_repeat_onreq').DataTable({
                        processing: true,
                        serverSide: true,
                        lengthChange: false,
                        bPaginate: false,
                        info: false,
                        ajax: {
                            url: "{{ route('dashboard.table_get_sales_repeat') }}",
                            data: function(d){
                                d.pic_sales = $('#pic_sales').val();
                                d.month = $('#month').val();
                                d.year = $('#year').val();
                            }
                        },
                        columns: [
                            {
                                data: 'nama',
                                name: 'nama'
                            },
                            {
                                data: 'total_harga',
                                name: 'total_harga'
                            },
                            {
                                data: 'cro',
                                name: 'cro'
                            }
                        ],
                        
                    });
        });
});

</script>