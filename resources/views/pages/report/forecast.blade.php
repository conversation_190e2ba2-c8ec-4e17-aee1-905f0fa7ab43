<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">
        <a href="{{ route('report.index') }}" class="pe-3">
                <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
            </a> <span class="text-white fs-1">Forecast</span>
        </span>
        <small class="text-white fs-6 fw-normal mt-3">Analyze and predict future trends effectively.</small>
    </x-slot>
    <x-slot name="script">
        <script>
            $(document).ready(function(){
                window.throttleDTSearch = DataTable.util.throttle(function (dt, val) {
                    dt.search(val).draw();
                }, 450);

                $.ajaxSetup({
                    headers: {
                        "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                    },
                });

                let filters = {};
                window.dt_forecast = $('#datatable_forecast').DataTable({
                    processing: true,
                    serverSide: true,
                    paging: true,
                    pageLength: 10,
                    ajax: {
                        url: "{{ route('report.forecast.getData') }}",
                        data: function(d) {
                            // d.tgl_created = $('.filter-order-year').val()
                            // d.tgl_order_from_date = $('#filter-tgl-order-from').val();
                            // d.tgl_order_to_date = $('#filter-tgl-order-to').val();
                        }
                    },
                    deferRender: true,
                    columns: [
                        {
                            name: "tb_customer.nama",
                            data: "tb_customer.nama",
                        },
                        {
                            name: "tb_customer.nama_instansi",
                            data: "tb_customer.nama_instansi",
                        },
                        {
                            name: "status_deal",
                            data: "status_deal",
                            orderable: false,
                        },
                        {
                            name: "status_order",
                            data: "status_order",
                            orderable: false,
                        },
                        {
                            name: "user.name",
                            data: "user.name",
                            orderable: false,
                        },
                        {
                            name: "forecast_week",
                            data: "forecast_week",
                            orderable: false,
                        },
                        {
                            name: "forecast_month",
                            data: "forecast_month",
                            orderable: false,
                        },
                        {
                            name: "forecast_year",
                            data: "forecast_year",
                            orderable: false,
                        },
                        {
                            name: "total_order",
                            data: "total_order",
                            className: "text-center",
                            orderable: false,
                        },
                        {
                            name: "total_expected_revenue",
                            data: "total_expected_revenue",
                            orderable: false,
                        }

                    ],
                });

                var isCollapsed = true;
                $('#collapseButton').on('click', function () {
                    var collapse = $('#collapseExample');
                    if (isCollapsed) {
                        collapse.collapse('show');
                        $("#table_div").removeClass("mt-2")
                    } else {
                        collapse.collapse('hide');
                        $("#table_div").addClass("mt-2")
                    }
                    
                    isCollapsed = !isCollapsed; // Toggle the collapse state
                });

                //filter page order
                const filterSearch = document.querySelector('[data-kt-docs-table-filter="search-order"]')
                filterSearch.addEventListener('keyup', function(e) {
                    window.throttleDTSearch(window.dt_forecast, e.target.value)
                })

                const filterWeek = document.querySelector('.filter-week')
                filterWeek.addEventListener('change', function(e) {
                    window.dt_forecast.columns(5).search(e.target.value).draw();
                })

                const filterMonth = document.querySelector('.filter-month')
                filterMonth.addEventListener('change', function(e) {
                    window.dt_forecast.columns(6).search(e.target.value).draw();
                })

                const filterYear = document.querySelector('.filter-year')
                filterYear.addEventListener('change', function(e) {
                    window.dt_forecast.columns(7).search(e.target.value).draw();
                })
            })

            $('#btn-download-report').on('click', function() {
                // Collect filter values
                const week = $('.filter-week').val();
                const month = $('.filter-month').val();
                const year = $('.filter-year').val();

                // Construct the query parameters
                const queryParams = new URLSearchParams({
                    week: week || '',
                    month: month || '',
                    year: year || ''
                }).toString();

                // Redirect to the download route with query parameters
                window.location.href = "{{ route('report.forecast.downloadExcel') }}?" + queryParams;
            });
        </script>
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="row">
            <div class="col-md-4">
                <div class="card shadow-sm mb-4">
                    @php
                        $forecast_total_order = isset($forecast['total_orders']) ? (int)$forecast['total_orders'] : 0;
                        $projection_total_order = isset($projection['total_orders']) ? (int)$projection['total_orders'] : 0;

                        $forecast_total_expected_revenue = isset($forecast['total_expected_revenue']) ? $forecast['total_expected_revenue'] : 0;
                        $projection_total_expected_revenue = isset($projection['total_expected_revenue']) ? $projection['total_expected_revenue'] : 0;
                    @endphp
                    <div class="card-body">
                        <h5 class="card-title">Forecast for This Week</h5>
                        <hr class="my-4" style="border-top: 1px dashed;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted">Total Orders:</h6>
                                <p class="fs-4 fw-bold">{{ $forecast_total_order }}</p>
                            </div>
                            <div class="text-end">
                                <h6 class="text-muted">Expected Revenue:</h6>
                                <p class="fs-4 fw-bold">Rp {{ number_format($forecast_total_expected_revenue,0) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Projection for This Week</h5>
                        <hr class="my-4" style="border-top: 1px dashed;">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="text-muted">Total Orders:</h6>
                                <p class="fs-4 fw-bold">{{ $projection_total_order }}</p>
                            </div>
                            <div class="text-end">
                                <h6 class="text-muted">Expected Revenue:</h6>
                                <p class="fs-4 fw-bold">Rp {{ number_format($projection_total_expected_revenue,0) }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <h5 class="card-title">Differences</h5>
                        <hr class="my-4" style="border-top: 1px dashed;">
                        <div class="d-flex justify-content-between align-items-center">
                            @php
                                $total_order = $projection_total_order - $forecast_total_order;
                                $expected_revenue = $projection_total_expected_revenue - $forecast_total_expected_revenue;
                            @endphp
                            <div>
                                <h6 class="text-muted">Total Orders:</h6>
                                <p class="fs-4 fw-bold">{{ $total_order ?? 0 }}</p>
                            </div>
                            <div class="text-end">
                                <h6 class="text-muted">Revenue:</h6>
                                <p class="fs-4 fw-bold">Rp {{ number_format($expected_revenue,0) ?? 0 }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card shadow-sm mt-3">
            <div class="card-body">
                <h5 class="card-title">Forecast Report</h5>
                <div class="d-flex flex-row-fluid justify-content-between gap-3">
                    <div class="d-flex align-items-center position-relative">
                        <!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
                        <span class="svg-icon svg-icon-1 position-absolute ms-6">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none">
                                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2"
                                    rx="1" transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                <path
                                    d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                    fill="black"></path>
                            </svg>
                        </span>
                        <!--end::Svg Icon-->
                        <input type="text" data-kt-docs-table-filter="search-order"
                            class="form-control form-control-solid w-250px ps-15" placeholder="Search ...">
                    </div>
                    <div class="d-flex gap-3">
                        <button type="button" class="btn btn-light-primary" id="collapseButton">
                            <span class="svg-icon svg-icon-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none">
                                    <path
                                        d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                                        fill="black"></path>
                                </svg>
                            </span>
                            Filter
                        </button>
                        <button type="button" class="btn btn-success text-nowrap" title="Export Excel" id="btn-download-report">
                            <!--begin::Svg Icon | path: icons/duotune/arrows/arr075.svg-->
                            <i class="fa fa-file-excel"></i>
                            <!--end::Svg Icon-->Export Excel
                        </button>
                    </div>
                </div>
                <div class="collapse" id="collapseExample">
                    <hr>
                    <div class=" bg-secondary rounded p-2 d-flex flex-row-fluid justify-content-between gap-3">
                        <div class="w-25">
                            <select class="form-select form-select-solid filter-week" name="week">
                                <option value="">Select Week</option>
                                <option value="1">Week 1</option>
                                <option value="2">Week 2</option>
                                <option value="3">Week 3</option>
                                <option value="4">Week 4</option>
                                <option value="5">Week 5</option>
                            </select>
                        </div>
                        
                        <div class="w-25">
                            <select class="form-select filter-month" name="month">
                                <option value="">Select Month</option>
                                @foreach (month() as $key => $mt)
                                    <option value="{{$key}}" {{$key < date('m') ? 'disabled':''}}>{{$mt}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="w-25">
                            <select class="form-select filter-year" name="year">
                                <option value="">Select Year</option>
                                @php
                                    $y = date('Y');
                                @endphp
                                @for ($i = $y; $i <= $y+5 ; $i++)
                                    <option value="{{$i}}">{{$i}}</option>
                                @endfor
                            </select>
                        </div>
                    </div>
                    <hr>
                </div>
                <div class="table-responsive">
                    <table id="datatable_forecast" class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Company</th>
                                <th>Status Deal</th>
                                <th>Status Order</th>
                                <th>PIC</th>
                                <th>Week</th>
                                <th>Month</th>
                                <th>Year</th>
                                <th>Total order</th>
                                <th>Forecasted Revenue</th>
                            </tr>
                        </thead>
                    </table>
                </div>
                {{-- <div class="mt-4">
                    <h6>Key Insights:</h6>
                    <ul>
                        <li>Sales are expected to increase in the upcoming months.</li>
                        <li>Revenue growth is projected to be steady.</li>
                        <li>Consider adjusting marketing strategies based on forecasted trends.</li>
                    </ul>
                </div> --}}
            </div>
        </div>
    </div>
        
</x-app-layout>