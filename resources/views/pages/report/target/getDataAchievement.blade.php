
<div class="row g-4">
    <div class="col-md-8">
        <div class="card shadow-sm" style="min-height: 450px;">
            <div class="card-body">
                <h3>Rekap Capaian Penjualan Sales</h3>
                <table class="mt-3">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th nowrap>Nama Sales</th>
                            <th nowrap>Target / Month</th>
                            <th nowrap>Target / Days</th>
                            <th nowrap>Target / Today</th>
                            <th nowrap>Total Deal</th>
                            <th>Persentase</th>
                        </tr>
                    </thead>
                    <tbody>
                        @php
                            $grandTotal = [
                                'target_revenue' => 0,
                                'avg_revenue_per_day' => 0,
                                'total_per_today' => 0,
                                'total_invoice' => 0,
                                'percentage_achievement' => 0,
                            ];
                        @endphp
                        @forelse($targets as $i => $target)
                            @php
                                $rowClass = '';
                                if ($i === 0) {
                                    $rowClass = 'bg-success';
                                } elseif ($i === count($targets) - 1) {
                                    $rowClass = 'bg-danger';
                                }
                                $grandTotal['target_revenue'] += $target['target_revenue'];
                                $grandTotal['avg_revenue_per_day'] += $target['avg_revenue_per_day'];
                                $grandTotal['total_per_today'] += $target['total_per_today'];
                                $grandTotal['total_invoice'] += $target['total_invoice'];
                                $grandTotal['percentage_achievement'] += $target['percentage_achievement'];
                            @endphp
                            <tr class="{{ $rowClass }}">
                                <td class="text-center">{{ $i + 1 }}</td>
                                <td>{{ $target['name'] }}</td>
                                <td class="text-center" nowrap>{{ $target['target_revenue_formatted'] }}</td>
                                <td class="text-center">{{ $target['avg_revenue_per_day_formatted'] }}</td>
                                <td class="text-center">{{ $target['total_per_today_formatted'] }}</td>
                                <td class="text-center" nowrap>{{ $target['total_invoice_formatted'] }}</td>
                                <td class="text-center">{{ $target['percentage_achievement_formatted'] }}</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center">Tidak ada data</td>
                            </tr>
                        @endforelse
                        <tr>
                            <td colspan="2" class="text-center"><b>Grand Total</b></td>
                            <td class="text-center">Rp {{ number_format($grandTotal['target_revenue'], 0, ',', '.') }}</td>
                            <td class="text-center">Rp {{ number_format($grandTotal['avg_revenue_per_day'], 0, ',', '.') }}</td>
                            <td class="text-center">Rp {{ number_format($grandTotal['total_per_today'], 0, ',', '.') }}</td>
                            <td class="text-center">Rp {{ number_format($grandTotal['total_invoice'], 0, ',', '.') }}</td>
                            <td class="text-center">{{ $grandTotal['percentage_achievement']/count($targets) }}%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card shadow-sm" style="min-height: 450px;">
            <div class="card-body text-center">
                <h3>Top Penjualan Sales Risepack</h3>
                @if(count($targets) > 0)
                    @if ($targets[0]['original_total_invoice'] > 0)
                        <div class="text-center">
                            <img src="{{ asset($targets[0]['photo_url']) ?? asset('images/ava-boy.svg') }}" alt="Foto {{ $targets[0]['name'] }}" class="rounded-circle my-5" style="width: 120px; height: 120px; object-fit: cover;">
                            <h4 class="mb-1">{{ $targets[0]['name'] }}</h4>
                            <p class="mb-1">Total Penjualan: <strong>Rp {{ number_format($targets[0]['total_invoice'], 0, ',', '.') }}</strong></p>
                            <p class="mb-0">Persentase Capaian: <strong>{{ $targets[0]['percentage_achievement'] }}%</strong></p>
                        </div>
                    @else
                        <p class="text-danger">Belum ada penjualan yang tercatat.</p>
                    @endif
                @else
                    <p class="text-center">Belum ada data penjualan.</p>
                @endif
            </div>
        </div>
    </div>
</div>