<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">Creare/Edit Sales</span>
    </x-slot>
    <x-slot name="script">
        <script>
            document.getElementById('target_revenue').addEventListener('keyup', function(e) {
                let value = this.value.replace(/[^0-9]/g, '');
                if (value.length > 15) {
                    value = value.slice(0, 15); // Limit to 999 triliun
                }
                $('#days').val('');
                $('#target_day').val('');
                if (value !== '') {
                    this.value = Number(value).toLocaleString('id-ID', {
                        style: 'decimal',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    });
                } else {
                    this.value = '';
                }
            });

            $('#days').on('keyup keypress', function(e) {
                let targetRevenueInput = document.getElementById('target_revenue');
                let targetRevenue = parseInt(targetRevenueInput.value.replace(/[^0-9]/g, ''), 10);
                let days = parseInt(this.value.replace(/[^0-9]/g, ''), 10);
                console.log(targetRevenue, days);
                if (!isNaN(days)) {
                    let targetDay = Math.floor(targetRevenue / days);
                    const formattedTargetDay = targetDay.toLocaleString('id-ID', {
                        style: 'decimal',
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0
                    })
                    document.getElementById('target_day').value = formattedTargetDay;
                } else {
                    document.getElementById('target_day').value = '';
                }
            });
        </script>
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <h2 class="mb-4">Target Sales - {{$sales->name ?? ''}}</h2>
                    <hr class="my-4">
                    <form action="{{ route('report.target.storeTarget') }}" method="POST">
                        @csrf
                        <input type="hidden" name="user_id" value="{{$sales->id ?? ''}}">
                        <input type="hidden" name="id" value="{{$target->id ?? ''}}">
                        <div class="mb-3">
                            <label for="month" class="form-label">Month<span class="text-danger">*</span></label>
                            <select class="form-select" id="month" name="month">
                                @foreach (month() as $key => $mt)
                                    <option value="{{$key}}" {{isset($target->month) && $target->month == $key? 'selected' : ''}}>{{$mt}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="year" class="form-label">Year<span class="text-danger">*</span></label>
                            <select class="form-select" id="year" name="year">
                                @php
                                    $y = date('Y');
                                @endphp
                                @for ($i = $y; $i <= $y+5 ; $i++)
                                    <option value="{{$i}}" {{isset($target->year) && $target->year == $i? 'selected' : ''}}>{{$i}}</option>
                                @endfor
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="target_revenue" class="form-label">Target Revenue<span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text">Rp. </span>
                                <input type="text" name="target_revenue" id="target_revenue" value="{{isset($target->target_revenue)? number_format($target->target_revenue,0,',','.') : ''}}" class="form-control"/>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="days" class="form-label">Days<span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="days" name="days" value="{{ old('days', $target->days ?? '') }}" required>
                        </div>
                        <div class="mb-3">
                            <label for="target_day" class="form-label">Target Day</label>
                            <div class="input-group">
                                <span class="input-group-text">Rp. </span>
                                <input type="text" name="target_day" id="target_day" value="{{isset($target->target_day)? number_format($target->target_day,0,',','.') : ''}}" class="form-control"/>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
