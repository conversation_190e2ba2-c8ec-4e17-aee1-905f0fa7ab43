

<div class="row g-4">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <h2>Data Proyeksi</h2>
                <table id="proyeksiTable" class="">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th><PERSON>a Customer</th>
                            <th>Potensial</th>
                            <th>Journey</th>
                            <th>PIC</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($proyeksi as $index => $item)
                            <tr>
                                <td>{{ $index + 1 }}</td>
                                <td>{{ $item->tb_customer->nama ?? '-'}}</td>
                                <td>{{ $item->total_expected_revenue ? number_format($item->total_expected_revenue, 0 , ',','.') : '-'}}</td>
                                <td>{{ $item->status_order ?? '-'}}</td>
                                <td>{{ $item->user->name ?? '-'}}</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="5" class="text-center">Tidak ada data proyeksi</td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@push('scripts')
<!-- DataTables JS and CSS (CDN) -->
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.min.css">
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script>
$(document).ready(function() {
    $('#proyeksiTable').DataTable();
});
</script>
@endpush