<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">
        <a href="{{ route('report.target.index') }}" class="pe-3">
                <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
            </a> <span class="text-white fs-1">Laporan Target Capaian</span>
        </span>
    </x-slot>
    <x-slot name="script">
        <script>
            $(document).ready(function() {
                // Trigger initial fetch on page load
                // fetchData();
                // fetchDataProyeksi();
                // fetchDataLost();
            });

            function fetchData() {
                const month = document.getElementById('filter_month').value;
                const year = document.getElementById('filter_year').value;

                $.ajax({
                    url: `{{ route('report.target.getDataAchievement') }}`,
                    type: 'GET',
                    data: { month: month, year: year },
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    success: function(html) {
                        $('#content').html(html);
                    },
                    error: function() {
                        $('#content').html('<div class="alert alert-danger">Gagal memuat data.</div>');
                    }
                });
            }

            function fetchDataProyeksi() {
                const month = document.getElementById('filter_month').value;
                const year = document.getElementById('filter_year').value;

                $.ajax({
                    url: `{{ route('report.target.getDataProyeksi') }}`,
                    type: 'GET',
                    data: { month: month, year: year },
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    success: function(html) {
                        $('#dataProyeksi').html(html);
                    },
                    error: function() {
                        $('#dataProyeksi').html('<div class="alert alert-danger">Gagal memuat data.</div>');
                    }
                });
            }

            function fetchDataLost() {
                const month = document.getElementById('filter_month').value;
                const year = document.getElementById('filter_year').value;

                $.ajax({
                    url: `{{ route('report.target.getDataLost') }}`,
                    type: 'GET',
                    data: { month: month, year: year },
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    success: function(html) {
                        $('#dataLost').html(html);
                    },
                    error: function() {
                        $('#dataLost').html('<div class="alert alert-danger">Gagal memuat data.</div>');
                    }
                });
            }

            $('#filter_month').on('change', function(){
                fetchData();
                fetchDataProyeksi();
                fetchDataLost();
            });
            $('#filter_year').on('change', function(){
                fetchData();
                fetchDataProyeksi();
                fetchDataLost();
            });
            function call() {
                fetchData();
                fetchDataProyeksi();
                fetchDataLost();
            }
        </script>
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="row g-4">
            <div class="col-md-7">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="row g-3 align-items-end">
                            <div class="col">
                                <label for="filter_month" class="form-label mb-0">Bulan</label>
                                <select class="form-select" id="filter_month" name="month">
                                    @foreach(month() as $key => $m)
                                        <option value="{{ $key }}">
                                            {{ $m }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col">
                                <label for="filter_year" class="form-label mb-0">Tahun</label>
                                <select class="form-select" id="filter_year" name="year">
                                    @php
                                        $currentYear = now()->year;
                                        $startYear = $currentYear - 5;
                                        $endYear = $currentYear + 1;
                                    @endphp
                                    @for($y = $startYear; $y <= $endYear; $y++)
                                        <option value="{{ $y }}" {{ request('year', $currentYear) == $y ? 'selected' : '' }}>
                                            {{ $y }}
                                        </option>
                                    @endfor
                                </select>
                            </div>
                            <button class="btn btn-primary mt-3" onclick="call()"><i class="fa fa-refresh"></i> Refresh</button>
                        </div>
                    </div>
                </div>
            </div>
            <div id="content">
                <div class="alert alert-info col-7" id="filterAlert">
                    Silakan pilih filter bulan dan tahun terlebih dahulu untuk menampilkan data.
                </div>
            </div>
            <div id="dataProyeksi"></div>
            <div id="dataLost"></div>
    </div>

<style>
    table {
        width: 100%;
        border-collapse: collapse;
    }

    table th, table td {
        border: 1px solid #dee2e6;
        padding: 8px 12px;
        text-align: left;
    }

    table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
</style>
</x-app-layout>