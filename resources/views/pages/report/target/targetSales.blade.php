<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">Target Sales - {{$sales->name}}</span>
    </x-slot>
    <x-slot name="script">
        <script>
            //CSRF
            $(document).ready(function () {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
            });

            var datatable;

            datatable = $('#datatable_sales').DataTable({
                processing: true,
                serverSide: true,
                pageLength: 12,
                lengthMenu: [12, 24, 36, 48],
                ajax: {
                    url: '{!! url()->current() !!}'
                },
                columns: [{
                        data: 'id',
                        name: 'id',
                        width: '5%'
                    },
                    {
                        data: 'month',
                        name: 'month',
                        width: '10%',
                        orderable: true
                    },
                    {
                        data: 'year',
                        name: 'year',
                        width: '10%',
                        orderable: true
                    },
                    {
                        data: 'target_revenue',
                        name: 'target_revenue',
                        width: '10%'
                    },
                    {
                        data: 'days',
                        name: 'days',
                        width: '10%',
                        className: 'text-center'
                    },
                    {
                        data: 'target_day',
                        name: 'target_day',
                        width: '10%'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false,
                        width: '10%'
                    }
                ],
                // Add data-filter attribute
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(4)').attr('data-filter', data.roles)
                }
            });

            $('#filter-month, #filter-year').on('change', function () {
                let month = $('#filter-month').val();
                let year = $('#filter-year').val();
                datatable.ajax.url('{!! url()->current() !!}?month=' + month + '&year=' + year).load();
            });

            function deleteConfirmation(id) {
                swal.fire({
                    title: "Apakah anda yakin ingin hapus data ini ?",
                    icon: "warning",
                    showCancelButton: !0,
                    confirmButtonText: "Ya, Hapus!",
                    cancelButtonText: "Tidak, batal!",
                    reverseButtons: !0
                }).then(function (e) {
                    if (e.value === true) {
                        // var token = $("meta[name='csrf-token']").attr("content");
                        $.ajax({
                            url: "{{ route('report.target.deleteTarget','') }}"+'/'+id,
                            type: 'DELETE',
                            data: {
                                "id": id
                                // "_token": token,
                            },
                            success: function (data) {
                                if (data) {
                                    datatable.ajax.reload()
                                    swal("Done!", results.message, "success");
                                } else {
                                    swal("Error!", results.message, "error");
                                }
                            }
                        });

                    } else {
                        e.dismiss;
                    }
                }, function (dismiss) {
                    return false;
                })
            }
        </script>
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    @if (@session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <strong>Success!</strong> {{ @session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif
                    @if (@session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <strong>Error!</strong> {{ @session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif
                        
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <div class="d-flex align-items-center">
                                <select id="filter-month" class="form-select me-2" style="width: 120px;">
                                    <option value="">All Months</option>
                                    @foreach(month() as $key => $m)
                                        <option value="{{ $key }}">{{ $m }}</option>
                                    @endforeach
                                </select>
                                <select id="filter-year" class="form-select" style="width: 120px;">
                                    <option value="">All Years</option>
                                    @foreach(range(date('Y')-5, date('Y')+1) as $y)
                                        <option value="{{ $y }}">{{ $y }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end align-items-center">
                            <a href="{{route('report.target.createTarget', ["sales_id" => $sales->id])}}" class="btn btn-success"><i class="fa fa-plus"></i> Add Target</a>
                        </div>
                    </div>

                    <table id="datatable_sales"
                        class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Bulan</th>
                                <th>Tahun</th>
                                <th>Total Target</th>
                                <th>Jumlah Hari</th>
                                <th>Target per Hari</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody> </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
