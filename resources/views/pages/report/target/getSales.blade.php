<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">
        <a href="{{ route('report.target.index') }}" class="pe-3">
                <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
            </a> <span class="text-white fs-1">Pengaturan Target Sales</span>
        </span>
    </x-slot>
    <x-slot name="script">
        <script>
            //CSRF
            $(document).ready(function () {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
            });

            // var datatable;

            datatable = $('#datatable_sales').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{!! url()->current() !!}'
                },
                columns: [{
                        data: 'id',
                        name: 'id',
                        width: '5%'
                    },
                    {
                        data: 'name',
                        name: 'name'
                    },
                    {
                        data: 'email',
                        name: 'email'
                    },
                    {
                        data: 'status',
                        name: 'status'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false,
                        width: '25%'
                    }
                ],
                // Add data-filter attribute
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(4)').attr('data-filter', data.roles)
                }
            });

            const filterSearch = document.querySelector('[data-kt-docs-table-filter="search"]')
            filterSearch.addEventListener('keyup', function (e) {
                datatable.search(e.target.value).draw()
            })

            // Select filter options
            filterPayment = document.querySelectorAll('[data-kt-docs-table-filter="status"] [name="status"]')
            const filterButton = document.querySelector('[data-kt-docs-table-filter="filter"]')

            // Filter datatable on submit
            filterButton.addEventListener('click', function () {
                // Get filter values
                let status = ''

                // Get payment value
                filterPayment.forEach(r => {
                    if (r.checked) {
                        status = r.value
                    }

                    // Reset payment value if "All" is selected
                    if (status === 'all') {
                        status = ''
                    }
                })

                // Filter datatable --- official docs reference: https://datatables.net/reference/api/search()
                datatable.search(status).draw()
            })

            //modal editor
            $('body').on('click', '.edit-user', function (event) {
                event.preventDefault();
                var id = $(this).data('id');
                $.get("{{ url('user/show/') }}/" + id, function (data) {
                    $('#submit-edit-user').val("edit-karyawan");
                    $('#modal-edit-user').modal('show');
                    $('#id-user').val(data.id);
                    // Clear file input and preview when opening edit modal
                    $('#photo-edit').val('');
                    $('#photo-preview').attr('src', '').hide();
                    if (data.profile_photo_path) {
                        $('#photo-edit').data('profile-photo', data.profile_photo_path);
                        $('#photo-preview').attr('src', "{{ asset('') }}" + data.profile_photo_path).show();
                    } else {
                        $('#photo-edit').removeData('profile-photo');
                        $('#photo-preview').attr('src', '').hide();
                    }
                    $('#status-edit').find('option[value="' + data.status + '"]').prop('selected',
                        true);
                })
            });

            $('#submit-edit-user').click(function (e) {
                e.preventDefault();
                $(this).html('Sending..');
                var id = $('#id-user').val();
                $.ajax({
                    data: new FormData($('#user-edit-form')[0]),
                    processData: false,
                    contentType: false,
                    url: "{{ url('report/target/update-sales') }}/" + id,
                    type: "POST",
                    dataType: 'json',
                    success: function (data) {
                        $('#datatable_sales').DataTable().ajax.reload()
                        setTimeout(function () {
                                    self.$("#edit-user-close").trigger("click");
                                }, 1200);
                        $('#modal-edit-user').modal('hide');
                        $('#submit-edit-user').html('Save Changes');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "User berhasil diedit",
                            icon: 'success',
                        })
                    },
                    error: function (data) {
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });
            
        </script>
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                        transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                    <path
                                        d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                        fill="black"></path>
                                </svg>
                            </span>
                            <input type="text" id="user-search-box" data-kt-docs-table-filter="search"
                                class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                        </div>
                        <div class="d-flex justify-content-end" data-kt-docs-table-toolbar="base">
                            <button type="button" class="btn btn-light-primary me-3" data-kt-menu-trigger="click"
                                data-kt-menu-placement="bottom-end">
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none">
                                        <path
                                            d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                                            fill="black"></path>
                                    </svg>
                                </span>
                                <!--end::Svg Icon-->Filter Status
                            </button>
                            <div class="menu menu-sub menu-sub-dropdown w-300px w-md-325px" data-kt-menu="true"
                                id="kt-toolbar-filter">
                                <div class="px-7 py-5">
                                    <div class="mb-10">
                                        <label class="form-label fs-5 fw-bold mb-3">Status:</label>
                                        <div class="d-flex flex-column flex-wrap fw-bold"
                                            data-kt-docs-table-filter="status">
                                            <label
                                                class="form-check form-check-sm form-check-custom form-check-solid mb-3 me-5">
                                                <input class="form-check-input" type="radio" name="status"
                                                    value="all" checked="checked">
                                                <span class="form-check-label text-gray-600">All</span>
                                            </label>
                                        </div>
                                        <div class="d-flex flex-column flex-wrap fw-bold"
                                            data-kt-docs-table-filter="status">
                                            <label
                                                class="form-check form-check-sm form-check-custom form-check-solid mb-3 me-5">
                                                <input class="form-check-input" type="radio" name="status"
                                                    value="1">
                                                <span class="form-check-label text-gray-600">Aktif</span>
                                            </label>
                                        </div>
                                        <div class="d-flex flex-column flex-wrap fw-bold"
                                            data-kt-docs-table-filter="status">
                                            <label
                                                class="form-check form-check-sm form-check-custom form-check-solid mb-3 me-5">
                                                <input class="form-check-input" type="radio" name="status"
                                                    value="0">
                                                <span class="form-check-label text-gray-600">Tidak Aktif</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-end">
                                        <button type="reset" class="btn btn-light btn-active-light-primary me-2"
                                            data-kt-menu-dismiss="true" data-kt-docs-table-filter="reset">Reset</button>
                                        <button type="submit" class="btn btn-primary" data-kt-menu-dismiss="true"
                                            data-kt-docs-table-filter="filter">Apply</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end align-items-center d-none"
                            data-kt-docs-table-toolbar="selected">
                            <div class="fw-bolder me-5">
                                <span class="me-2" data-kt-docs-table-select="selected_count"></span>Selected</div>
                            <button type="button" class="btn btn-danger"
                                data-kt-docs-table-select="delete_selected">Selection Action</button>
                        </div>
                    </div>

                    <table id="datatable_sales"
                        class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nama</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody> </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modal-edit-user">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                <form id="user-edit-form" name="user-form" class="form-horizontal" enctype="multipart/form-data">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Data Sales</h5>
                        <!--begin::Close-->
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-order-close"
                            data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                        </div>
                        <!--end::Close-->
                    </div>

                    <div class="modal-body mt-n5">
                        <div class="row">
                            <div class="col-lg-12">
                                <input type="hidden" name="id" id="id-user">
                                <div class="mb-3">
                                    <label for="photo" class="form-label">Foto Profil</label>
                                    <input type="file" class="form-control" id="photo-edit" name="photo_edit" accept="image/*" onchange="previewPhoto(event)">
                                    <div class="mt-2">
                                        <img id="photo-preview" src="" alt="Preview Foto" style="max-width: 150px; max-height: 150px; display: none;">
                                    </div>
                                </div>
                                <script>
                                    function previewPhoto(event) {
                                        const input = event.target;
                                        const preview = document.getElementById('photo-preview');
                                        if (input.files && input.files[0]) {
                                            const reader = new FileReader();
                                            reader.onload = function(e) {
                                                preview.src = e.target.result;
                                                preview.style.display = 'block';
                                            }
                                            reader.readAsDataURL(input.files[0]);
                                        } else {
                                            preview.src = '';
                                            preview.style.display = 'none';
                                        }
                                    }

                                    // Show existing photo if available when modal is opened
                                    $('#modal-edit-user').on('show.bs.modal', function () {
                                        const photoPath = $('#photo-edit').data('profile-photo');
                                        const preview = document.getElementById('photo-preview');
                                        if (photoPath) {
                                            preview.src = photoPath;
                                            preview.style.display = 'block';
                                        } else {
                                            preview.src = '';
                                            preview.style.display = 'none';
                                        }
                                    });
                                </script>
                                <div class="mb-3">
                                    <label for="status" class="required form-label">Status</label>
                                    <select id="status-edit" name="status_edit" class="form-select form-control form-control-solid" required>
                                        <option value="" disabled selected>- Pilih Status</option>
                                        <option value="1">Aktif</option>
                                        <option value="0">Tidak Aktif</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="edit-user-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                        <button type="button" id="submit-edit-user" class="btn btn-primary my-primary">Save
                            changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
