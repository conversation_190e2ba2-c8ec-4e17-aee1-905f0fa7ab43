<div class="table-responsive mt-4">
    <table class="table table-bordered freeze-columns">
        <thead class="thead-dark">
            @php
                $getData = $orders ? $orders->map(function ($order) {
                    $order->surat_jalan_count = $order->tb_surat_jalan_detail->count();
                    return $order;
                })->sortByDesc('surat_jalan_count') : collect();

                $maxSuratJalanCount = $getData->max('surat_jalan_count') ?? 0;
            @endphp
            <tr>
                <th class="text-center" >#</th>
                <th class="" >Nama Customer</th>
                <th class="" >Kode Order</th>
                <th class="" >Item Name</th>
                <th class="text-center" >Item Order FAW(pcs)</th>
                <th class="text-center" >Deadline Produksi</th>
                <th class="text-center ">Tanggal Order</th>
                @if ($maxSuratJalanCount > 0)
                    @for ($i = 1; $i <= $maxSuratJalanCount; $i++)
                        <th class="text-center ">Delivery Date ({{$i}})</th>
                    @endfor
                    <th class="text-center ">Month Of Delivery</th>
                    @for ($i = 1; $i <= $maxSuratJalanCount; $i++)
                        <th class="text-center ">Number of Delivery ({{$i}})</th>
                    @endfor
                @endif
                <th class="text-center">Total Item Delivery (pcs)</th>
                <th class="text-center">Remaining Item Delivery (pcs)</th>
                <th class="text-center">Total Produksi (pcs)</th>
                <th class="text-center">Delivery Completeness</th>
                <th class="text-center">Product Issue</th>
                <th class="text-center">Tanggal Pengiriman complete</th>
                <th class="text-center">DP</th>
                <th class="text-center">Pelunasan</th>
                <th class="text-center">Ongkir</th>
                <th class="text-center">Tanggal DP</th>
                <th class="text-center">Jatuh Tempo Pelunasan</th>
                <th class="text-center">Tanggal Pelunasan All</th>
                <th class="text-center">No Surat Jalan</th>
                <th class="text-center">Link Document</th>
                <th class="text-center">Tanggal Invoice</th>
                <th class="text-center">Hari tempo</th>
                <th class="text-center">Jatuh Tempo Awal</th>
                <th class="text-center">Hari Terlambat</th>
                <th class="text-center">Harga Jual</th>
                <th class="text-center">Harga Modal</th>
                <th class="text-center">Tanggal Selesai Produksi</th>
                <th class="text-center">Tanggal FAW</th>
                <th class="text-center">Production Days</th>
                <th class="text-center">Margin</th>
                <th class="text-center">Days Reminding to Deadline</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($orders as $order)
            @php
                $qty_produk = (int)$order->tb_produksi->jumlah_produk ?? 0;
                $reject = (int)$order->tb_produksi->reject ?? 0;
                $total_deliver_item = 0;
            @endphp
            <tr>
                {{-- No --}}
                <td class="text-center">{{ ($orders->currentPage() - 1) * $orders->perPage() + $loop->iteration }}</td> 
                {{-- Nama Customer --}}
                <td class="" >{{ $order->tb_customer->nama }}</td>
                {{-- Kode Order --}}
                <td class="" >{{ $order->sko }}</td>
                {{-- Item Nama --}}
                <td class="" >{{ $order->tb_produksi->nama_produk }}</td>
                <td class="text-center" >{{ $qty_produk}}</td>
                <td class="text-center text-nowrap">{{ isset($order->tb_faw) ? convertDate($order->tb_faw->tgl_deadline) : '-' }}</td>
                <td class="text-center text-nowrap">{{ isset($order->tb_faw) ? convertDate($order->tb_faw->tgl_deadline_konsumen) : '-' }}</td>
                @if ($maxSuratJalanCount > 0)
                    @for ($i = 0; $i < $maxSuratJalanCount; $i++)
                        @if (isset($order->tb_surat_jalan_detail[$i]))
                            <td class="text-center text-nowrap">{{isset($order->tb_surat_jalan_detail[$i]->tb_surat_jalan->delivery_date) ? date('d-m-Y', strtotime($order->tb_surat_jalan_detail[$i]->tb_surat_jalan->delivery_date)) : '-'}}</td>
                        @endif
                    @endfor
                    <td class="text-center">{{isset($order->tb_surat_jalan_detail[0]->tb_surat_jalan->delivery_date) ? getMonth(date('m',strtotime($order->tb_surat_jalan_detail[0]->tb_surat_jalan->delivery_date))) : '-'}}</td>
                    @for ($i = 0; $i < $maxSuratJalanCount; $i++)
                        @if (isset($order->tb_surat_jalan_detail[$i]))
                            @php
                                $total_deliver_item += isset($order->tb_surat_jalan_detail[$i]->quantity) ? $order->tb_surat_jalan_detail[$i]->quantity : 0;
                            @endphp
                            <td class="text-center ">{{isset($order->tb_surat_jalan_detail[$i]->quantity) ? $order->tb_surat_jalan_detail[$i]->quantity : '-'  }}</td>
                        @endif
                    @endfor
                @endif
                @php
                    $total_produksi = $order->tb_spks->jumlah_fix ?? 0;
                    $remaining_item = $total_produksi - $total_deliver_item;
                @endphp
                <td class="text-center">{{$total_deliver_item}}</td>
                <td class="text-center">{{$remaining_item}}</td>
                <td class="text-center">{{$total_produksi}}</td>
                <td class="text-center">
                    @php
                        $text = '';
                        $color = '';
                    @endphp
                    @if ($remaining_item > 0)
                        @php
                            $text = 'Completed';
                            $color = 'bg-success';
                        @endphp
                    @else 
                        @if ($remaining_item < 0 && ($total_produksi-$qty_produk) > 0)
                            @php
                                $text = 'Pending Shipping';
                                $color = 'bg-warning';
                            @endphp
                        @elseif ($remaining_item < 0 && ($total_produksi-$qty_produk) < 0)
                            @php
                                $text = 'Kurang';
                                $color = 'bg-danger';
                            @endphp
                            @if ($remaining_item < 0)
                                @php
                                    $text = 'Kurang';
                                    $color = 'bg-danger';
                                @endphp
                            @else
                                @php
                                    $text = 'Completed';
                                    $color = 'bg-success';
                                @endphp
                            @endif
                        @endif
                    @endif
                    <span class="badge bg-{{$color}}">{{$text}}</span>
                </td>
                <td class="text-center">
                    <a class="btn btn-sm btn-success w-100 fpm" data-toggle="tooltip" data-key="{{$order->sko_key }}" href="javascript:void(0)">FPM</a>
                </td>
                <td class="text-center">
                    @if (isset($order->tb_surat_jalan_detail) && $order->tb_surat_jalan_detail->isNotEmpty())
                        @php
                            $last_index = $order->tb_surat_jalan_detail->count() - 1;
                            $last_data = $order->tb_surat_jalan_detail[$last_index] ?? null;
                        @endphp
                        @if ($last_data && isset($last_data->tb_surat_jalan->delivery_date) && $text == 'Completed')
                            {{ date('d-m-Y', strtotime($last_data->tb_surat_jalan->delivery_date)) }}
                        @else
                            -
                        @endif
                    @else
                        -
                    @endif
                </td>
                @php
                    $data_invoice = null;
                    if (isset($order->invoice_details) && $order->invoice_details->isNotEmpty()){
                        $data_invoice= $order->invoice_details->filter(function ($detail) {
                            return $detail->invoices && $detail->invoices->type === 'DP' || $detail->invoices->type === 'Pelunasan';
                        })->first();
                    }
                @endphp
                <td class="text-center">
                    @if ($data_invoice && isset($data_invoice->invoices->dp_terbayar))
                        Rp. {{number_format($data_invoice->invoices->dp_terbayar, 0)}}
                    @endif
                </td>
                <td class="text-center">
                    @if ($data_invoice && isset($data_invoice->invoices->total_pembayaran))
                        Rp. {{number_format($data_invoice->invoices->total_pembayaran, 0)}}
                    @endif
                </td>
                <td class="text-center">
                    @if ($data_invoice && isset($data_invoice->invoices->biaya_pengiriman))
                        Rp. {{number_format($data_invoice->invoices->biaya_pengiriman, 0)}}
                    @endif
                </td>
                <td class="text-center">
                    @if ($data_invoice && isset($data_invoice->invoices->tanggal_dp))
                        {{ date('d-m-Y', strtotime($data_invoice->invoices->tanggal_dp)) }}     
                    @else
                        -  
                    @endif
                </td>
                <td class="text-center text-nowrap">
                    @if ($data_invoice && isset($data_invoice->invoices->tanggal_jatuh_tempo_tambahan))
                        {{ date('d-m-Y', strtotime($data_invoice->invoices->tanggal_jatuh_tempo_tambahan)) }}
                    @elseif ($data_invoice && isset($data_invoice->invoices->tanggal_jatuh_tempo))
                        {{ date('d-m-Y', strtotime($data_invoice->invoices->tanggal_jatuh_tempo)) }}
                    @else
                        -
                    @endif
                </td>
                <td class="text-center text-nowrap">
                    @if ($data_invoice && isset($data_invoice->invoices->tanggal_pelunasan))
                        {{ date('d-m-Y', strtotime($data_invoice->invoices->tanggal_pelunasan)) }}
                    @else
                        -
                    @endif
                </td>
                <td class="text-center text-nowrap">
                    @if (isset($order->tb_surat_jalan_detail) && $order->tb_surat_jalan_detail->isNotEmpty())
                        <ul>
                            @foreach ($order->tb_surat_jalan_detail as $detail)
                                @if (isset($detail->tb_surat_jalan))
                                    @if (isset($detail->tb_surat_jalan->sign_lampiran))
                                        <li>
                                            <a href="{{ route('surat-jalan.show', ['id' => $detail->tb_surat_jalan->surat_jalan_key]) }}" target="_blank">
                                                {{ $detail->tb_surat_jalan->no_surat_jalan }}
                                            </a>
                                        </li>
                                    @else
                                        <li>{{($detail->tb_surat_jalan->no_surat_jalan)}}</li>
                                    @endif
                                @endif
                            @endforeach
                        </ul>
                    @else
                        -
                    @endif  
                </td>
                <td class="text-center text-nowrap">
                    @if ($order->link_dokumen_order)
                        <button class="btn btn-primary btn-sm" onclick="window.open('{{ $order->link_dokumen_order }}', '_blank')">Open Document</button>
                    @else
                        -
                    @endif
                </td>
                @php
                    $tgl_jatuh_tempo = null;
                    if ($data_invoice){
                        if ($data_invoice->invoices->tanggal_jatuh_tempo){
                           $tgl_jatuh_tempo = date('d-m-Y', strtotime($data_invoice->invoices->tanggal_jatuh_tempo));
                        }
                    }

                    $tgl_invoice = null;
                    if ($data_invoice){
                        $tgl_invoice = date('d-m-Y', strtotime($data_invoice->invoices->tanggal_invoice));
                    }
                @endphp
                <td class="text-center text-nowrap">
                    {{$tgl_invoice}}
                </td>
                <td class="text-center text-nowrap">    
                    {{-- Hari Tempo--}}
                    @if ($tgl_invoice && $tgl_jatuh_tempo)
                        @php
                            $hari_tempo = \Carbon\Carbon::parse($tgl_jatuh_tempo)->diffInDays(\Carbon\Carbon::parse($tgl_invoice)) ?? 0;
                        @endphp
                        {{ $hari_tempo }}
                    @else
                        -
                    @endif
                </td>
                <td class="text-center text-nowrap">
                    {{-- Jatuh tempo awal--}}
                    {{$tgl_jatuh_tempo}}
                </td>
                <td class="text-center text-nowrap">
                    {{-- Hari terlambat --}}
                    @if ($data_invoice)
                        @php
                            $pelunasan = $data_invoice->invoices->tanggal_pelunasan ?? null;
                            $lateDays = null;

                            if ($pelunasan != null) {
                                $lateDays = \Carbon\Carbon::parse($tgl_jatuh_tempo)->diffInDays(\Carbon\Carbon::parse(date('Y-m-d'))) ?? 0;
                            } else {
                                $lateDays = \Carbon\Carbon::parse($tgl_jatuh_tempo)->diffInDays(\Carbon\Carbon::parse($pelunasan)) ?? 0;
                            }
                        @endphp
                        {{ $lateDays != null ? $lateDays : '-' }}
                    @else
                        -
                    @endif
                </td>
                @php
                    $harga_jual = $order->tb_produksi->total_harga ?? 0;
                    $modal_sales = $order->tb_produksi->modal_sales ?? 0;
                    $margin = $harga_jual != 0 ? ($harga_jual - $modal_sales) / $harga_jual : 0;
                @endphp
                <td class="text-center text-nowrap">
                    Rp. {{number_format($harga_jual)}}
                </td>
                <td class="text-center text-nowrap">
                    Rp. {{number_format($modal_sales)}}
                </td>
                <td class="text-center text-nowrap">
                    {{isset($order->tb_spks) ? convertDate($order->tb_spks->tgl_selesai_all) : '-'}}
                </td>
                <td class="text-center text-nowrap">
                    {{ isset($order->tb_faw->tanggal_faw) ? date('d-m-Y', strtotime($order->tb_faw->tanggal_faw)) : '-' }}
                </td>
                <td class="text-center text-nowrap">
                    @php
                        $productionDays = null;
                        if (isset($order->tb_faw->tanggal_faw)) {
                            if (empty($order->tb_spks->tgl_selesai_all)) {
                                $productionDays = \Carbon\Carbon::parse(date('Y-m-d'))->diffInDays(\Carbon\Carbon::parse($order->tb_faw->tanggal_faw));
                            } else {
                                $productionDays = \Carbon\Carbon::parse($order->tb_spks->tgl_selesai_all)->diffInDays(\Carbon\Carbon::parse($order->tb_faw->tanggal_faw));
                            }
                        }
                    @endphp
                </td>
                <td class="text-center text-nowrap">
                    Rp. {{number_format($margin)}}
                </td>
                <td class="text-center text-nowrap">
                    @if ($text == 'Completed')
                        <span class="badge bg-success">Done</span>
                    @else
                        @if (isset($order->tb_faw->tgl_deadline))
                            @php
                                $daysReminding = \Carbon\Carbon::parse($order->tb_faw->tgl_deadline)->diffInDays(\Carbon\Carbon::parse(date('Y-m-d')));
                            @endphp
                            <span class="badge bg-warning">{{ $daysReminding }} days</span>
                        @endif
                    @endif
                </td>
            </tr>
            @empty
            <tr>
                <td colspan="13" class="text-center">No orders found.</td>
            </tr>
            @endforelse
        </tbody>
    </table>
</div>
<div class="d-flex justify-content-center">
    <div class="d-flex justify-content-between align-items-center w-100">
        <div>
            <label for="entriesPerPage">Show 
                <select id="entriesPerPage" class="form-select form-select-sm" style="width: auto; display: inline-block;">
                    <option value="10" {{ $per_page == 10 ? 'selected' : '' }}>10</option>
                    <option value="25" {{ $per_page == 25 ? 'selected' : '' }}>25</option>
                    <option value="50" {{ $per_page == 50 ? 'selected' : '' }}>50</option>
                    <option value="100" {{ $per_page == 100 ? 'selected' : '' }}>100</option>
                </select> entries of {{ $orders->total() }} 
            </label>
        </div>
        <div>
            {{ $orders->links() }}
        </div>
    </div>
    <script>
        $('#entriesPerPage').on('change', function () {
            const perPage = this.value;
            $.ajax({
                url: "{{ route('report.tracker.getData') }}",
                type: 'GET',
                data: { per_page: perPage },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function (response) {
                    $('#content').html(response);
                },
                error: function (xhr, status, error) {
                    console.error('Error fetching data:', error);
                }
            });
        });
    </script>
</div>
<div class="modal fade" id="modal_fpm">
    <div class="modal-dialog modal-dialog-centered modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detail Masalah</h5>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-fpm-close"
                    data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                </div>
            </div>
            <div class="modal-body mt-n5">
                <form action="" id="add-fpm">
                    @csrf
                    <input type="hidden" name="id_order" id="id_order_fpm">
                    <input type="hidden" name="order_key" id="order_key_fpm">
                    <input type="hidden" name="sko_key" id="sko_key_fpm">
                    <div class="row mt-10">
                        <div class="col-md-6">
                            <div class="mb-10">
                                <label for="id_customer" class="form-label">Customer</label>
                                <input type="text" name="id_customer_order_fpm" id="id_customer_order_fpm" readonly
                                    class="form-control form-control-solid" />
                            </div>
                            <div class="mb-10">
                                <label for="kode_order" class="form-label">Main Kode Order</label>
                                <input type="text" name="kode_order" id="kode_order_fpm"
                                    class="form-control form-control-solid" />
                            </div>
                            <div class="mb-10">
                                <label for="sko" class="form-label">Sub Kode Order</label>
                                <input type="text" name="sko" id="sko_fpm" readonly
                                    class="form-control form-control-solid" />
                            </div>
                            <div class="mb-10">
                                <label for="sko" class="form-label">Spesifikasi</label>
                                <textarea type="text" rows="3" name="spesifikasi_fpm" id="spesifikasi_fpm" readonly
                                    class="form-control form-control-solid"></textarea>
                            </div>
                            <div class="mb-10">
                                <label for="name" class="form-label">PIC Sales</label>
                                <input type="text" name="name" id="name_fpm" readonly
                                    class="form-control form-control-solid" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-10">
                                <label for="tgl_masalah" class="required form-label">Tanggal Masalah Dilaporkan</label>
                                <input type="date" name="tgl_masalah" id="tgl_masalah_fpm" class="form-control" />
                            </div>
                            <!-- <div class="fv-row mb-10">
                                <label for="tgl_deadline" class="required form-label">Tanggal Deadline Penyelesaian</label>
                                <input type="date" name="tgl_deadline_perbaikan" id="tgl_deadline_perbaikan" class="form-control"/>
                            </div> -->
                            <div class="mb-10">
                                <label for="kategori_masalah" class="required form-label">Kategori Masalah</label>
                                <select name="kategori_masalah" id="kategori_masalah_fpm" class="form-select">
                                    <option value="" disabled selected hidden>-- Pilih kategori</option>
                                    @foreach ($kategori_masalah as $masalah)
                                    <option value="{{ $masalah->kategori_masalah }}">{{ $masalah->kategori_masalah }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="mb-10">
                                <label for="detail_masalah" class="form-label">Detail Masalah</label>
                                <textarea name="detail_masalah" id="detail_masalah_fpm" class="form-control"
                                    rows="3"></textarea>
                            </div>
                            
                                <div class="mb-10">
                                    <div class="solusi" id="solusi">
                                        <div class="row">
                                            <div class="alert alert-danger d-flex align-items-center p-5">
                                                <div class="d-flex flex-column">
                                                    <h4 class="mb-1 text-dark">Solusi</h4>
                                                    <span>Harap pilih salah satu solusi yang tersedia dibawah.</span>
                                                        <div class="btn-group mt-3 justify-content-md-center" role="group">
                                                            <a class="btn btn-shadow btn-my-primary text-white" id="solusi-pu">Produksi Ulang</a>
                                                            <a class="btn btn-shadow btn-my-primary text-white" id="solusi-nego">Negosiasi</a>
                                                            <a class="btn btn-shadow btn-my-primary text-white" id="solusi-perbaikan">Perbaikan</a>
                                                        </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="solusi-chosen" id="solusi-chosen">
                                        <label for="solusi" class="form-label">Solusi</label>
                                        <input type="text" name="solusi" readonly id="solusi_fpm" class="form-control form-control-solid" />
                                    </div>
                                </div>
                            
                        </div>
                    </div>
                    {{-- <div class="d-flex justify-content-end">
                            <input type="submit" name="fpm_submit" value="Submit" class="btn btn-my-primary btn-shadow btn-lg text-white" id="process-fpm">
                        </div> --}}

                </form>

            </div>
        </div>
    </div>
</div>
<script>
    $(document).ready(function () {

        const table = document.querySelector('.freeze-columns');
        const ths = table.querySelectorAll('th');
        let left = 0;

        ths.forEach((th, index) => {
            if (index < 7) { // Freeze only the first 7 columns
                th.style.position = 'sticky';
                th.style.left = `${left}px`;
                th.style.zIndex = index === 0 ? 3 : 2; // Higher z-index for the first column
                left += th.offsetWidth; // Increment left by the column width
            }
        });

        const tds = table.querySelectorAll('tbody tr');
        tds.forEach(row => {
            let left = 0;
            const cells = row.querySelectorAll('td');
            cells.forEach((td, index) => {
                if (index < 7) { // Freeze only the first 7 columns
                    td.style.position = 'sticky';
                    td.style.left = `${left}px`;
                    td.style.zIndex = 1; // Lower z-index for table cells
                    left += td.offsetWidth; // Increment left by the column width
                }
            });
        });

        $(document).on('click', '.pagination a', function (event) {
            event.preventDefault();
            const url = $(this).attr('href');
            const perPage = $('#entriesPerPage').val();
            console.log(perPage);
            $.ajax({
                url: url,
                type: 'GET',
                data: { per_page: perPage },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function (response) {
                    $('#content').html(response);
                },
                error: function (xhr, status, error) {
                    console.error('Error fetching pagination data:', error);
                }
            });
        });
    });
    $('body').on('click', '.fpm', function (event) {
        event.preventDefault();
        var sko_key = $(this).data('key');
        $.get("{{ url('crm/show_fpm/') }}/" + sko_key, function (data) {
            $('#modal_fpm').modal('show');
            $('#id_order_fpm').val(data[0].id_order);
            $('#order_key_fpm').val(data[0].order_key);
            $('#sko_key_fpm').val(data[0].sko_key);
            $('#id_customer_order_fpm').val(data[0].nama);
            $('#kode_order_fpm').val(data[0].kode_order);
            $('#sko_fpm').val(data[0].sko);
            var spesifikasi_fpm = data[0].lp_panjang + 'X' + data[0].lp_lebar + ', ' +
                data[0].jenis_kertas + ', ' +
                data[0].gramasi + ', ' +
                data[0].laminasi + ', ' +
                data[0].sisi_laminasi + ', ' +
                data[0].finishing + ', ' +
                data[0].jumlah_produk + ' pcs, ' +
                data[0].notes;
            $('#spesifikasi_fpm').val(spesifikasi_fpm);
            $('#name_fpm').val(data[0].name);
            $('#tgl_masalah_fpm').val(data[0].tgl_masalah);
            $('#kategori_masalah_fpm').find('option[value="' + data[0].kategori_masalah + '"]').prop('selected', true);
            $('#detail_masalah_fpm').val(data[0].detail_masalah);
            // $('#solusi_fpm').val(data[0].solusi);
            // if (data[0].id_fpms !== null) {
            //     $('#solusi').show();
            // } else {
            //     $('#solusi').hide();
            // }
            $('#solusi').hide();
            if(data[0].solusi !== null) {
                $('#solusi-chosen').show();
                $('#solusi').hide();
            }else{
                $('#solusi-chosen').hide();
            }

            $('#solusi-pu').click(function (e) {
                window.location.replace("{{ url('crm/form_solusi') }}?sko_key="+data[0].sko_key+"&solusi=produksi-ulang");
            });
            $('#solusi-nego').click(function (e) {
                window.location.replace("{{ url('crm/form_solusi') }}?sko_key="+data[0].sko_key+"&solusi=negosiasi");
            });
            $('#solusi-perbaikan').click(function (e) {
                window.location.replace("{{ url('crm/form_solusi') }}?sko_key="+data[0].sko_key+"&solusi=perbaikan");
            });
            
        })
    });
</script>
<style>
    .table {
        border: 1px solid #dee2e6;
    }

    .table th{
        border: 1px solid #dee2e6;
    }

    .table th{
        background-color: #3c3b3b; /* Ensure the background doesn't overlap */
        color: #fff;
    }

    .table td{
        background-color: #ffffff; /* Ensure the background doesn't overlap */
    }

    .table-responsive {
        position: relative;
        overflow-x: auto;
        white-space: ;
    }

    .freeze-columns th:nth-child(1),
    .freeze-columns td:nth-child(1) {
        left: 0;
        z-index: 3; /* Higher z-index for the first column */
    }

    .freeze-columns th:nth-child(2),
    .freeze-columns td:nth-child(2) {
        left: 50px; /* Adjust based on column width */
    }

    .freeze-columns th:nth-child(3),
    .freeze-columns td:nth-child(3) {
        left: 100px; /* Adjust based on column width */
    }

    .freeze-columns th:nth-child(4),
    .freeze-columns td:nth-child(4) {
        left: 150px; /* Adjust based on column width */
    }

    .freeze-columns th:nth-child(5),
    .freeze-columns td:nth-child(5) {
        left: 200px; /* Adjust based on column width */
    }

    .freeze-columns th:nth-child(6),
    .freeze-columns td:nth-child(6) {
        left: 250px; /* Adjust based on column width */
    }

    .freeze-columns th:nth-child(7),
    .freeze-columns td:nth-child(7) {
        left: 300px; /* Adjust based on column width */
    }
</style>