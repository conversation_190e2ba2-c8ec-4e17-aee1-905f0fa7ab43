<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">
        <a href="{{ route('report.index') }}" class="pe-3">
                <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
            </a> <span class="text-white fs-1">Survey Responses</span>
        </span>
        <small class="text-white fs-6 fw-normal mt-3">Explore detailed survey responses and insights.</small>
    </x-slot>
    <x-slot name="script">
        <script>
            $(document).ready(function(){
                window.throttleDTSearch = DataTable.util.throttle(function (dt, val) {
                    dt.search(val).draw();
                }, 450);

                $.ajaxSetup({
                    headers: {
                        "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                    },
                });

                let filters = {};

                window.dt_survey = $('#datatable_survey').DataTable({
                    processing: true,
                    serverSide: true,
                    paging: true,
                    pageLength: 10,
                    ajax: {
                        url: "{{ route('survey.getAssignSurvey') }}",
                        data: function (d) {
                            d.is_report = true;
                            d.status_survey = $('#filter_status_survey').val() || '';
                            d.from_date = $('#filter_start_date').val() || '';
                            d.to_date = $('#filter_end_date').val() || '';
                            d.from_submit_date = $('#filter_start_submit_date').val() || '';
                            d.to_submit_date = $('#filter_end_submit_date').val() || '';
                        }
                    },
                    deferRender: true,
                    columnDefs: [ { type: 'date', targets: [2] } ],
                    order: [[ 2, 'desc' ]],
                    columns: [
                        {
                            data: 'sko',
                            name: 'tb_orders.sko'
                        },
                        {
                            data: 'nama',
                            name: 'nama'
                        },
                        {
                            data: 'tgl_order',
                            name: 'tgl_order',
                            searchable: false,
                            className: 'text-center'
                        },
                        {
                            data: 'submited_date',
                            name: 'tb_survey_link.submited_date',
                            searchable: false,
                            className: 'text-center'
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false,
                            className: 'text-center'
                        }
                    ],
                });

                var isCollapsed = true;
                $('#collapseButton').on('click', function () {
                    var collapse = $('#collapseExample');
                    if (isCollapsed) {
                        collapse.collapse('show');
                        $("#table_div").removeClass("mt-2")
                    } else {
                        collapse.collapse('hide');
                        $("#table_div").addClass("mt-2")
                    }
                    
                    isCollapsed = !isCollapsed;
                });

                //filter page order
                const filterSearch = document.querySelector('[data-kt-docs-table-filter="search-order"]')
                filterSearch.addEventListener('keyup', function(e) {
                    window.throttleDTSearch(window.dt_survey, e.target.value)
                })

                // Select filter options
                const resetButton = document.querySelector('[data-kt-docs-table-filter="reset"]')
                const filterButton = document.querySelector('[data-kt-docs-table-filter="filter-tanggalKirim"]')
                
                resetButton.addEventListener('click', function() {
                    $('#filter_start_date').val('');
                    $('#filter_end_date').val('');
                    $('#filter_start_submit_date').val('');
                    $('#filter_end_submit_date').val('');
                    $('#filter_status_survey').val('');
                    dt_survey.draw();
                })

                // Add click event for Apply button
                $('[data-kt-docs-table-filter="filter-tanggalKirim"]').on('click', function() {
                    dt_survey.draw();
                });
            });
        </script>
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="row">
            <div class="col-md-4">
                <div class="card shadow-sm mb-4 bg-info">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-left">
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="none" stroke="white" stroke-width="1.5" class="bi bi-bag-check" viewBox="0 0 16 16">
                                    <path d="M8 1a2 2 0 0 1 2 2v1h3.5A1.5 1.5 0 0 1 15 5.5v9A1.5 1.5 0 0 1 13.5 16h-11A1.5 1.5 0 0 1 1 14.5v-9A1.5 1.5 0 0 1 2.5 4H6V3a2 2 0 0 1 2-2z" />
                                    <path d="M8 9.293l-1.5-1.5a.5.5 0 0 0-.707.707L7.646 10l3-3a.5.5 0 0 0-.707-.707L8 9.293z" />
                                </svg>
                                <h6 class="text-white mt-3">Total Completed Order</h6>
                            </div>
                            <div>
                                <div style="font-size: 50px;font-weight:bold;" class="text-white">{{$total_order}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card shadow-sm mb-4 bg-primary">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-left">
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="white" class="bi bi-link" viewBox="0 0 16 16">
                                    <path d="M6.354 5.5H4a2.5 2.5 0 0 0 0 5h2.354a.5.5 0 0 1 0 1H4a3.5 3.5 0 0 1 0-7h2.354a.5.5 0 0 1 0 1zM9.646 5.5H12a2.5 2.5 0 0 1 0 5H9.646a.5.5 0 0 0 0 1H12a3.5 3.5 0 0 0 0-7H9.646a.5.5 0 0 0 0 1z"/>
                                    <path d="M8.354 8.354a.5.5 0 0 1 0-.708l3-3a.5.5 0 0 1 .708.708l-3 3a.5.5 0 0 1-.708 0z"/>
                                    <path d="M7.646 7.646a.5.5 0 0 1 0 .708l-3 3a.5.5 0 0 1-.708-.708l3-3a.5.5 0 0 1 .708 0z"/>
                                </svg>
                                <h6 class="text-white mt-3">Total Generated Link</h6>
                            </div>
                            <div>
                                <div style="font-size: 50px;font-weight:bold;" class="text-white">{{$total_generated_link}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card shadow-sm mb-4 bg-danger">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-left">
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" fill="white" class="bi bi-card-checklist" viewBox="0 0 16 16">
                                    <path d="M10.854 6.146a.5.5 0 1 1 .707.708l-3 3a.5.5 0 0 1-.708 0l-1-1a.5.5 0 1 1 .708-.708l.646.647 2.646-2.647z"/>
                                    <path d="M14 4.5V14a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V4.5a1.5 1.5 0 0 1 1.5-1.5h9A1.5 1.5 0 0 1 14 4.5zM3 4.5a.5.5 0 0 0-.5.5V14a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V5a.5.5 0 0 0-.5-.5H3z"/>
                                    <path d="M5 7.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5zm0 3a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 0 1h-3a.5.5 0 0 1-.5-.5z"/>
                                </svg>
                                <h6 class="text-white mt-3">Total Responses Submitted</h6>
                            </div>
                            <div>
                                <div style="font-size: 50px;font-weight:bold;" class="text-white">{{$total_submited}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-8">
                <div class="card shadow-sm mt-3">
                    <div class="card-body">
                        <h5 class="card-title">Survey Responses Report</h5>
                        <div class="d-flex flex-row-fluid justify-content-between gap-3">
                            <div class="d-flex align-items-center position-relative">
                                <!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
                                <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none">
                                        <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2"
                                            rx="1" transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                        <path
                                            d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                            fill="black"></path>
                                    </svg>
                                </span>
                                <!--end::Svg Icon-->
                                <input type="text" data-kt-docs-table-filter="search-order"
                                    class="form-control form-control-solid w-250px ps-15" placeholder="Search ...">
                            </div>
                            <div class="d-flex gap-3">
                                {{-- <button type="button" class="btn btn-light-primary" id="collapseButton">
                                    <span class="svg-icon svg-icon-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                            fill="none">
                                            <path
                                                d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                                                fill="black"></path>
                                        </svg>
                                    </span>
                                    Filter
                                </button> --}}
                                <button type="button" class="btn btn-light-primary me-3" data-kt-menu-trigger="click"
                                    data-kt-menu-placement="bottom-end">
                                    <span class="svg-icon svg-icon-2">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                            fill="none">
                                            <path
                                                d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                                                fill="black"></path>
                                        </svg>
                                    </span>
                                    Filter
                                </button>
                                <div class="menu menu-sub menu-sub-dropdown" data-kt-menu="true" id="kt-toolbar-filter">
                                    <div class="px-7 py-5">
                                        <label class="form-label fs-5 fw-bold mb-3">Status Survey</label>
                                        <select class="form-select form-select-solid mb-2" name="filter_status_survey" id="filter_status_survey">
                                            <option hidden>Status Survey</option>
                                            <option value="">All</option>
                                            <option value="ungenerated">Belum Generate Link Survey</option>
                                            <option value="unsubmited">Belum Submit</option>
                                            <option value="submited">Submit</option>
                                        </select>
                                        <label class="form-label fs-5 fw-bold mb-3">Tanggal Order:</label>
                                        <div class="form-group">
                                            <div class="d-flex align-items-center gap-4">
                                                <div class="input-group">
                                                    <span class="input-group-text">Start Date</span>
                                                    <input type="date" class="form-control form-control-solid"
                                                        name="filter_start_date" id="filter_start_date" placeholder="Tanggal Mulai">
                                                </div>
                                                <div class="input-group">
                                                    <span class="input-group-text">End Date</span>
                                                    <input type="date" class="form-control form-control-solid"
                                                        name="filter_end_date" id="filter_end_date" placeholder="Tanggal Akhir">
                                                </div>
                                            </div>
                                        </div>
                                        <label class="form-label fs-5 fw-bold mb-3">Tanggal Submit:</label>
                                        <div class="form-group">
                                            <div class="d-flex align-items-center gap-4">
                                                <div class="input-group">
                                                    <span class="input-group-text">Start Date</span>
                                                    <input type="date" class="form-control form-control-solid"
                                                        name="filter_start_submit_date" id="filter_start_submit_date" placeholder="Tanggal Mulai">
                                                </div>
                                                <div class="input-group">
                                                    <span class="input-group-text">End Date</span>
                                                    <input type="date" class="form-control form-control-solid"
                                                        name="filter_end_submit_date" id="filter_end_submit_date" placeholder="Tanggal Akhir">
                                                </div>
                                            </div>
                                            <div class="d-flex justify-content-end mt-3">
                                                <button type="reset" class="btn btn-light btn-active-light-primary me-2"
                                                    data-kt-menu-dismiss="true" data-kt-docs-table-filter="reset">Reset</button>
                                                <button type="submit" class="btn btn-primary" data-kt-menu-dismiss="true"
                                                    data-kt-docs-table-filter="filter-tanggalKirim">Apply</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn btn-success text-nowrap d-none" title="Export Excel" id="btn-download-report2">
                                    <!--begin::Svg Icon | path: icons/duotune/arrows/arr075.svg-->
                                    <i class="fa fa-file-excel"></i>
                                    <!--end::Svg Icon-->Export Excel
                                </button>
                            </div>
                        </div>
                        {{-- <div class="collapse" id="collapseExample">
                            <hr>
                            <div class=" bg-secondary rounded p-2 d-flex flex-row-fluid justify-content-between gap-3">
                                <div class="w-25">
                                    <select class="form-select form-select-solid filter-status">
                                        <option hidden>Status Survey</option>
                                        <option value="">All</option>
                                        <option value="ungenerated">Belum Generate Link Survey</option>
                                        <option value="unsubmited">Belum Submit</option>
                                        <option value="submited">Submit</option>
                                    </select>
                                </div>
                            </div>
                            <hr>
                        </div> --}}
                        <div class="table-responsive">
                            <table id="datatable_survey" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>SKO</th>
                                        <th>Nama Customer</th>
                                        <th>Tgl Order</th>
                                        <th>Tgl Submit</th>
                                        <th class="text-center">Action</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card shadow-sm mt-3">
                    <div class="card-body">
                        <h5 class="card-title">Top Score Questions by Responses</h5>
                        <ul class="list-group">
                            @if (count($data_responses) > 0)
                                @foreach($data_responses as $data)
                                    <li class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>{{ $data->question_text }}</span>
                                            <span class="badge bg-primary rounded-pill">{{ $data->total_sum }}</span>
                                        </div>
                                        <div class="text-muted">
                                            {{$data->total_count}} Response Survey
                                        </div>
                                    </li>
                                @endforeach
                            @endif
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
        
</x-app-layout>