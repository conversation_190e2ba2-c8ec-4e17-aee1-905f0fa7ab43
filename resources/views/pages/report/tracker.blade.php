<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">
        <a href="{{ route('report.index') }}" class="pe-3">
                <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
            </a> <span class="text-white fs-1">Tracking Order</span>
        </span>
        <small class="text-white fs-6 fw-normal mt-3">Explore detailed tracking order responses and insights.</small>
    </x-slot>
    <x-slot name="script">
        <script>
            $(function(){
                getData();
            })

            function getData() {
                $('#content').html('<div class="text-center my-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
                
                $.ajax({
                    url: "{{ route('report.tracker.getData') }}",
                    method: "GET",
                    data: { per_page: 10 },
                    success: function(response) {
                        $('#content').html(response);
                    },
                    error: function(xhr) {
                        console.error("An error occurred:", xhr.responseText);
                        $('#content').html('<div class="text-danger text-center my-5">Failed to load data. Please try again later.</div>');
                    }
                });
            }

            $('#btn-search').on('click', function(){
                $('#content').html('<div class="text-center my-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');
                
                $.ajax({
                    url: "{{ route('report.tracker.getData') }}",
                    method: "GET",
                    data: { 
                        per_page: 10, 
                        kode_order: $('#kode_order').val(),
                        tgl_deadline: $('#tgl_deadline').val()
                     },
                    success: function(response) {
                        $('#content').html(response);
                    },
                    error: function(xhr) {
                        console.error("An error occurred:", xhr.responseText);
                        $('#content').html('<div class="text-danger text-center my-5">Failed to load data. Please try again later.</div>');
                    }
                });
            })
        </script>
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="border border-dashed p-3">
                            <div class="row">
                                <div class="col-lg-4">
                                    <div class="form-group">
                                        <label class="form-label">Cari berdasarkan Kode Order atau Nama Pelanggan</label>
                                        <input type="text" class="form-control" name="kode_order" id="kode_order" placeholder="Masukan kode order atau nama pelanggan"/>
                                    </div>
                                </div>
                                <div class="col-lg-3">
                                    <div class="form-group">
                                        <label class="form-label">Tanggal Deadline</label>
                                        <input type="date" class="form-control" name="tgl_deadline" id="tgl_deadline"/>
                                    </div>
                                </div>
                                <div class="col-lg-5">
                                    <label class="form-label">Tanggal Order</label>
                                    <div class="d-flex align-items-center">
                                        <input type="date" class="form-control" name="start_tgl_order" id="start_tgl_order"/> s/d
                                        <input type="date" class="form-control" name="end_tgl_order" id="end_tgl_order"/>
                                    </div>
                                </div>
                                <div class="col-lg-4 d-none">
                                    <div class="form-group">
                                        <label class="form-label">Status Order</label>
                                        <select class="form-select" name="status_order">
                                            <option value="">Semua</option>
                                            <option value="pending">Pending Shipping</option>
                                            <option value="kurang">Kurang</option>
                                            <option value="completed">Completed</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <button type="button" class="btn btn-primary mt-3 w-100" id="btn-search"><i class="fas fa-search"></i> Cari</button>
                                </div>
                            </div>
                        </div>
                        <div id="content"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>