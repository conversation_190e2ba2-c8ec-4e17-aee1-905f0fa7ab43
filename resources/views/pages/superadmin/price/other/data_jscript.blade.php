{{-- <link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">
<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script> --}}
<script>
    $(document).ready(function() {
        const format = (item) => {
            if (!item.id) {
                return item.text;
            }
            var img = ""
            var span = $("<span>", {
                text: " " + item.text
            });
            span.prepend(img)
            return span
        }
        $('#id_jenis_ongkos').select2({
                allowClear: true,
                debug: true,
                dropdownParent: $('#modal-add-paper-price .modal-content'),
                ajax: {
                    url: "{{ route('crm.get_jenis_ongkos') }}",
                    processResults: function(data) {
                        var resultsData = []

                        $.each(data, function(index, item) {
                            resultsData.push({
                                id: item.id_jenis_ongkos,
                                text: item.jenis_ongkos
                            })
                        })

                        return {
                            results: resultsData
                        };
                    },
                    // cache: true
                },
                templateResult: function(item) {
                    return format(item)
                }
            })
            .on('select2:opening', function(e) {
                $(this).data('select2').$dropdown.find(':input.select2-search__field').attr('placeholder',
                    'Cari Jenis Ongkos')
            })

        $('#id_tools').select2({
                allowClear: true,
                debug: true,
                dropdownParent: $('#modal-add-paper-price .modal-content'),
                ajax: {
                    url: "{{ route('crm.get_tools') }}",
                    processResults: function(data) {
                        var resultsData = []

                        $.each(data, function(index, item) {
                            resultsData.push({
                                id: item.id_tools,
                                text: item.tools
                            })
                        })

                        return {
                            results: resultsData
                        };
                    },
                    // cache: true
                },
                templateResult: function(item) {
                    return format(item)
                }
            })
            .on('select2:opening', function(e) {
                $(this).data('select2').$dropdown.find(':input.select2-search__field').attr('placeholder',
                    'Cari Tools')
            })

        $('#id_bahans').select2({
            allowClear: true,
            debug: true,
            dropdownParent: $('#modal-add-paper-price .modal-content'),
            ajax: {
                url: "{{ route('crm.get_bahan') }}",
                processResults: function(data) {
                    var resultsData = []

                    $.each(data, function(index, item) {
                        resultsData.push({
                            id: item.id_bahans,
                            text: item.bahan
                        })
                    })

                    return {
                        results: resultsData
                    };
                },
                // cache: true
            },
            templateResult: function(item) {
                return format(item)
            }
        })
        .on('select2:opening', function(e) {
            $(this).data('select2').$dropdown.find(':input.select2-search__field').attr('placeholder',
                'Cari Bahans')
        })

        $('#id_jenis_ongkos_edit').select2({
                allowClear: true,
                debug: true,
                dropdownParent: $('#modal-edit-paper-price .modal-content'),
                ajax: {
                    url: "{{ route('crm.get_jenis_ongkos') }}",
                    processResults: function(data) {
                        var resultsData = []

                        $.each(data, function(index, item) {
                            resultsData.push({
                                id: item.id_jenis_ongkos,
                                text: item.jenis_ongkos
                            })
                        })

                        return {
                            results: resultsData
                        };
                    },
                    // cache: true
                },
                templateResult: function(item) {
                    return format(item)
                }
            })
            .on('select2:opening', function(e) {
                $(this).data('select2').$dropdown.find(':input.select2-search__field').attr('placeholder',
                    'Cari Jenis Ongkos')
            })

        $('#id_tools_edit').select2({
                allowClear: true,
                debug: true,
                dropdownParent: $('#modal-edit-paper-price .modal-content'),
                ajax: {
                    url: "{{ route('crm.get_tools') }}",
                    processResults: function(data) {
                        var resultsData = []

                        $.each(data, function(index, item) {
                            resultsData.push({
                                id: item.id_tools,
                                text: item.tools
                            })
                        })

                        return {
                            results: resultsData
                        };
                    },
                    // cache: true
                },
                templateResult: function(item) {
                    return format(item)
                }
            })
            .on('select2:opening', function(e) {
                $(this).data('select2').$dropdown.find(':input.select2-search__field').attr('placeholder',
                    'Cari Tools')
            })

        $('#id_bahans_edit').select2({
            allowClear: true,
            debug: true,
            dropdownParent: $('#modal-edit-paper-price .modal-content'),
            ajax: {
                url: "{{ route('crm.get_bahan') }}",
                processResults: function(data) {
                    var resultsData = []

                    $.each(data, function(index, item) {
                        resultsData.push({
                            id: item.id_bahans,
                            text: item.bahan
                        })
                    })

                    return {
                        results: resultsData
                    };
                },
                // cache: true
            },
            templateResult: function(item) {
                return format(item)
            }
        })
        .on('select2:opening', function(e) {
            $(this).data('select2').$dropdown.find(':input.select2-search__field').attr('placeholder',
                'Cari Bahan')
        })



    })
</script>