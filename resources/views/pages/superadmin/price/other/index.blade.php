<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">Other Paper Management</span>
    </x-slot>
    <x-slot name="script">
        <script>
            //CSRF
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
            });

            // var datatable;

            datatable = $('#datatable_paper_price').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{!! url()->current() !!}'
                },
                columns: [{
                        data: 'id_other_price',
                        name: 'id_other_price'
                    },
                    {
                        data: 'jenis_ongkos',
                        name: 'jenis_ong<PERSON>'
                    },
                    {
                        data: 'tools',
                        name: 'tools'
                    },
                    {
                        data: 'price_per_item',
                        name: 'price_per_item'
                    },
                    {
                        data: 'price_minimum',
                        name: 'price_minimum'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false,
                        width: '25%'
                    }
                ],
                // Add data-filter attribute
                createdRow: function(row, data, dataIndex) {
                    $(row).find('td:eq(4)').attr('data-filter', data.roles)
                }
            });

            const filterSearch = document.querySelector('[data-kt-docs-table-filter="search"]')
            filterSearch.addEventListener('keyup', function(e) {
                datatable.search(e.target.value).draw()
            })


            //modal editor
            $('#submit-form-paper-price').click(function(e) {
                e.preventDefault();
                $(this).html('Sending..');
                $.ajax({
                    data: $('#price-form').serialize(),
                    url: "{{ route('other_price.store') }}",
                    type: "POST",
                    dataType: 'json',
                    success: function(data) {
                        datatable.draw();
                        $('#datatable_paper_price').DataTable().ajax.reload()
                        setTimeout(function() {
                            self.$("#add-paper-price-close").trigger("click");
                        }, 1200);
                        $('#modal-add-paper-price').modal('hide');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "Other price baru berhasil dibuat",
                            icon: 'success',
                        })
                        location.reload();
                    },
                    error: function(data) {
                        $('#submit-form-paper-price').html('Save Changes');
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });

            //modal editor
            $('body').on('click', '.edit-paper-price', function(event) {
                event.preventDefault();
                var id = $(this).data('id');
                $.get("{{ url('other_price/show/') }}/" + id, function(data) {
                    $('#submit-edit-paper-price').val("edit-karyawan");
                    $('#modal-edit-paper-price').modal('show');
                    $('#id-other-price').val(data.id_other_price);
                    $('#id_jenis_ongkos_edit option[value=' + data.id_jenis_ongkos + ']').attr('selected', 'selected');
                    $('[aria-controls="select2-id_jenis_ongkos_edit-container"] .select2-selection__placeholder').html(data.tb_jenis_ongkos.jenis_ongkos);
                    $('#id_tools_edit option[value=' + data.id_tools + ']').attr('selected', 'selected');
                    $('[aria-controls="select2-id_tools_edit-container"] .select2-selection__placeholder').html(data.tb_tools.tools);
                    if (data.id_bahans != null) {
                        $('#id_bahans_edit option[value=' + data.id_bahans + ']').attr('selected', 'selected');
                        $('[aria-controls="select2-id_bahans_edit-container"] .select2-selection__placeholder').html(data.tb_bahans.bahan);
                    }
                    $('#price_per_item_edit').val(data.price_per_item);
                    $('#price_minimum_edit').val(data.price_minimum);
                    $('#quantity_edit').val(data.quantity);
                    if (data.is_optional) {
                        $("#is_optional_edit").prop("checked", true);
                    } else {
                        $("#is_optional_edit").prop("checked", false);
                    }
                    $('input[name="tipe_rumus_edit"]').each(function() {
                        var value = $(this).val();

                        if (value == data.tipe_rumus) {
                            $(this).prop("checked", true);
                        } else {
                            $(this).prop("checked", false);
                        }
                            
                    });
                })
            });

            $('#submit-edit-paper-price').click(function(e) {
                e.preventDefault();
                $(this).html('Sending..');
                var id = $('#id-price').val();
                $.ajax({
                    data: $('#paper-price-edit-form').serialize(),
                    url: "{{ url('other_price/update/') }}",
                    type: "POST",
                    dataType: 'json',
                    success: function(data) {
                        $('#datatable_paper_price').DataTable().ajax.reload()
                        setTimeout(function() {
                            self.$("#edit-paper-price-close").trigger("click");
                        }, 1200);
                        $('#modal-edit-paper-price').modal('hide');
                        $('#submit-edit-paper-price').html('Save Changes');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "Other price berhasil diedit",
                            icon: 'success',
                        })
                        location.reload();
                    },
                    error: function(data) {
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });

            function deleteConfirmation(id) {
                swal.fire({
                    title: "Hapus ?",
                    text: "Harap pastikan",
                    icon: "warning",
                    showCancelButton: !0,
                    confirmButtonText: "Ya, Hapus!",
                    cancelButtonText: "Tidak, batal!",
                    reverseButtons: !0
                }).then(function(e) {
                    if (e.value === true) {
                        // var token = $("meta[name='csrf-token']").attr("content");
                        $.ajax({
                            url: "{{ route('other_price.destroy','') }}" + '/' + id,
                            type: 'DELETE',
                            data: {
                                "id": id
                                // "_token": token,
                            },
                            success: function(data) {
                                if (data) {
                                    $('#datatable_paper_price').DataTable().ajax.reload()
                                    Swal.fire({
                                        title: 'SUCCESS!',
                                        text: "Other price berhasil dihapus",
                                        icon: 'success',
                                    })
                                } else {
                                    Swal.fire({
                                        title: 'FAILED!',
                                        text: "Other price gagal dihapus",
                                        icon: 'error',
                                    })
                                }
                            }
                        });

                    } else {
                        e.dismiss;
                    }
                }, function(dismiss) {
                    return false;
                })
            }
        </script>
        @include('pages.superadmin.price.other.data_jscript')
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1" transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                    <path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z" fill="black"></path>
                                </svg>
                            </span>
                            <input type="text" id="paper-price-search-box" data-kt-docs-table-filter="search" class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                        </div>
                        <div class="d-flex justify-content-end" data-kt-docs-table-toolbar="base">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#modal-add-paper-price" title="Add price">
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1" transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                                        <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="black"></rect>
                                    </svg>
                                </span>
                                Add Other Price
                            </button>
                        </div>
                        <div class="d-flex justify-content-end align-items-center d-none" data-kt-docs-table-toolbar="selected">
                            <div class="fw-bolder me-5">
                                <span class="me-2" data-kt-docs-table-select="selected_count"></span>Selected
                            </div>
                            <button type="button" class="btn btn-danger" data-kt-docs-table-select="delete_selected">Selection Action</button>
                        </div>
                    </div>

                    <table id="datatable_paper_price" class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Jenis Ongkos</th>
                                <th>Tools</th>
                                <th>Harga Satuan</th>
                                <th>Harga Minimum</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody> </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="modal" tabindex="-1" id="modal-add-paper-price">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">

                <div class="modal-header">
                    <h5 class="modal-title">Tambah Other Price</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-paper-price-close" data-bs-dismiss="modal" aria-label="Close">
                        <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                    </div>
                </div>

                <div class="modal-body mt-n5">
                    <div class="row mt-2">
                        <form id="price-form" name="price-form" class="form-horizontal row">
                            <div class="col-md-6">
                                <div class="mb-10">
                                    <label for="id_jenis_ongkos" class="required form-label">Jenis Ongkos</label>
                                    <select class="form-select" name="id_jenis_ongkos" id="id_jenis_ongkos" data-control="select2" data-placeholder="Jenis Ongkos">
                                        <option></option>
                                    </select>
                                </div>
                                <div class="mb-10">
                                    <label for="id_tools" class="required form-label">Tools</label>
                                    <select class="form-select" name="id_tools" id="id_tools" data-control="select2" data-placeholder="Tools">
                                        <option></option>
                                    </select>
                                </div>
                                <div class="mb-10">
                                    <label for="price_per_item" class="required form-label">Harga Satuan</label>
                                    <input type="number" step="any" name="price_per_item" id="price_per_item" class="form-control" placeholder="Harga Satuan" />
                                </div>
                                <div class="mb-10">
                                    <label for="price_minimum" class="required form-label">Harga Minimum</label>
                                    <input type="number" step="any" name="price_minimum" id="price_minimum" class="form-control" placeholder="Harga Minimum" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-10">
                                    <label for="id_bahans" class="required form-label">Bahan</label>
                                    <select class="form-select" name="id_bahans" id="id_bahans" data-control="select2" data-placeholder="Bahan">
                                        <option></option>
                                    </select>
                                </div>
                                <div class="mb-10">
                                    <label for="quantity" class="required form-label">Quantity</label>
                                    <input type="number" step="any" name="quantity" id="quantity" class="form-control" placeholder="Qty" />
                                </div>
                                <div class="mb-10">
                                    <div class="form-check form-check-custom mb-2">
                                        <label class="form-check-label me-2" for="is_optional">
                                            Optional
                                        </label>
                                        <input class="form-check-input" type="checkbox" name="is_optional" id="is_optional" />
                                    </div>
                                </div>
                                <div class="mb-10">
                                    <label for="jenis_box" class="form-label">Tipe Rumus</label>
                                    <div class="row mb-5" id="jenis_box">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="tipe_rumus" value="1" id="tipe_rumus_1">
                                                <label class="form-check-label" for="tipe_rumus_1">
                                                  Tipe 1
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="tipe_rumus" value="2" id="tipe_rumus_2">
                                                <label class="form-check-label" for="tipe_rumus_2">
                                                  Tipe 2
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="tipe_rumus" value="3" id="tipe_rumus_3">
                                                <label class="form-check-label" for="tipe_rumus_3">
                                                 Tipe 3
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <p>
                                        <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
                                            Panduan Tipe Rumus
                                        </button>
                                    </p>
                                    <div class="collapse" id="collapseExample">
                                        <table class="table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Tipe</th>
                                                    <th>Keterangan Rumus</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td nowrap>Tipe 1</td>
                                                    <td>Panjang Ukurang Kertas x Lebar Ukuran Kertas x Harga Satuan x Jumlah Kertas</td>
                                                </tr>
                                                <tr>
                                                    <td nowrap>Tipe 2</td>
                                                    <td>Harga Satuan x Jumlah Tercetak</td>
                                                </tr>
                                                <tr>
                                                    <td nowrap>Tipe 3</td>
                                                    <td>Harga Minimum Dasar + ((Qty - Qty Minimum) x Harga Satuan)</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" id="add-paper-price-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                    <button type="submit" id="submit-form-paper-price" class="btn btn-primary my-primary">Save</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" tabindex="-1" id="modal-edit-paper-price">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                <form id="paper-price-edit-form" name="price-form" class="form-horizontal">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Other Price</h5>
                        <!--begin::Close-->
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-order-close" data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                        </div>
                        <!--end::Close-->
                    </div>

                    <div class="modal-body mt-n5">
                        <div class="row">
                            <div class="col-md-6">
                                <input type="hidden" name="id_other_price" id="id-other-price">
                                <div class="mb-10">
                                    <label for="id_jenis_ongkos" class="required form-label">Jenis Ongkos</label>
                                    <select class="form-select" name="id_jenis_ongkos_edit" id="id_jenis_ongkos_edit" data-control="select2" data-placeholder="Jenis Ongkos">
                                        <option></option>
                                        @foreach ($jenis_ongkos as $jk)
                                        <option value="{{$jk->id_jenis_ongkos}}">{{$jk->jenis_ongkos}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="mb-10">
                                    <label for="id_tools" class="required form-label">Tools</label>
                                    <select class="form-select" name="id_tools_edit" id="id_tools_edit" data-control="select2" data-placeholder="Tools">
                                        <option></option>
                                        @foreach ($tools as $kp)
                                        <option value="{{$kp->id_tools}}">{{$kp->tools}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="mb-10">
                                    <label for="price_per_item_edit" class="required form-label">Harga Satuan</label>
                                    <input type="number" step="any" name="price_per_item_edit" id="price_per_item_edit" :value="old('price_per_item')" class="form-control" placeholder="Harga Satuan" />
                                </div>
                                <div class="mb-10">
                                    <label for="price_minimum_edit" class="required form-label">Harga Minimum</label>
                                    <input type="number" step="any" name="price_minimum_edit" id="price_minimum_edit" :value="old('price_minimum')" class="form-control" placeholder="Harga Minimum" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-10">
                                    <label for="id_bahans_edit" class="required form-label">Bahan</label>
                                    <select class="form-select" name="id_bahans_edit" id="id_bahans_edit" data-control="select2" data-placeholder="Bahans">
                                        <option></option>
                                        @foreach ($bahans as $kp)
                                            <option value="{{$kp->id_bahans}}">{{$kp->bahan}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="mb-10">
                                    <label for="quantity_edit" class="required form-label">Quantity</label>
                                    <input type="number" step="any" name="quantity_edit" id="quantity_edit" :value="old('quantity')" class="form-control" placeholder="Qty" />
                                </div>
                                <div class="mb-10">
                                    <div class="form-check form-check-custom mb-2">
                                        <label class="form-check-label me-2" for="is_optional_edit">
                                            Optional
                                        </label>
                                        <input class="form-check-input" type="checkbox" name="is_optional_edit" id="is_optional_edit" />
                                    </div>
                                </div>
                                <div class="mb-10">
                                    <label for="jenis_box" class="form-label">Tipe Rumus</label>
                                    <div class="row mb-5" id="jenis_box">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="tipe_rumus_edit" value="1" id="tipe_rumus_1">
                                                <label class="form-check-label" for="tipe_rumus_1">
                                                  Tipe 1
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="tipe_rumus_edit" value="2" id="tipe_rumus_2">
                                                <label class="form-check-label" for="tipe_rumus_2">
                                                  Tipe 2
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="tipe_rumus_edit" value="3" id="tipe_rumus_3">
                                                <label class="form-check-label" for="tipe_rumus_3">
                                                 Tipe 3
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <p>
                                        <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
                                            Panduan Tipe Rumus
                                        </button>
                                    </p>
                                    <div class="collapse" id="collapseExample">
                                        <table class="table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>Tipe</th>
                                                    <th>Keterangan Rumus</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td nowrap>Tipe 1</td>
                                                    <td>Panjang Ukurang Kertas x Lebar Ukuran Kertas x Harga Satuan x Jumlah Kertas</td>
                                                </tr>
                                                <tr>
                                                    <td nowrap>Tipe 2</td>
                                                    <td>Harga Satuan x Jumlah Tercetak</td>
                                                </tr>
                                                <tr>
                                                    <td nowrap>Tipe 3</td>
                                                    <td>Harga Minimum Dasar + ((Qty - Qty Minimum) x Harga Satuan)</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="edit-paper-price-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                        <button type="button" id="submit-edit-paper-price" class="btn btn-primary my-primary">Save
                            changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>