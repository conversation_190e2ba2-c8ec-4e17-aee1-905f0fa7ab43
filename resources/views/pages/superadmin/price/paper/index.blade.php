<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">Price Paper Management</span>
    </x-slot>
    <x-slot name="script">
        <script>
            //CSRF
            $(document).ready(function () {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
            });

            // var datatable;

            datatable = $('#datatable_paper_price').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{!! url()->current() !!}'
                },
                columns: [
                    {
                        data: 'id_paper_price',
                        name: 'id_paper_price'
                    },
                    {
                        data: 'jenis_kertas',
                        name: 'jenis_kertas'
                    },
                    {
                        data: 'kertas_plano',
                        name: 'kertas_plano'
                    },
                    {
                        data: 'gramasi',
                        name: 'gramasi'
                    },
                    {
                        data: 'price',
                        name: 'price'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false,
                        width: '25%'
                    }
                ],
                // Add data-filter attribute
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(4)').attr('data-filter', data.roles)
                }
            });

            const filterSearch = document.querySelector('[data-kt-docs-table-filter="search"]')
            filterSearch.addEventListener('keyup', function (e) {
                datatable.search(e.target.value).draw()
            })


            //modal editor
            $('#submit-form-paper-price').click(function (e) {
                e.preventDefault();
                $(this).html('Sending..');
                $.ajax({
                    data: $('#price-form').serialize(),
                    url: "{{ route('paper_price.store') }}",
                    type: "POST",
                    dataType: 'json',
                    success: function (data) {
                        datatable.draw();
                        $('#datatable_paper_price').DataTable().ajax.reload()
                        setTimeout(function () {
                                    self.$("#add-paper-price-close").trigger("click");
                                }, 1200);
                        $('#modal-add-paper-price').modal('hide');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "paper price baru berhasil dibuat",
                            icon: 'success',
                        })
                    },
                    error: function (data) {
                        $('#submit-form-paper-price').html('Save Changes');
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });

            //modal editor
            $('body').on('click', '.edit-paper-price', function (event) {
                event.preventDefault();
                var id = $(this).data('id');
                $.get("{{ url('paper_price/show/') }}/" + id, function (data) {
                    $('#submit-edit-paper-price').val("edit-karyawan");
                    $('#modal-edit-paper-price').modal('show');
                    $('#id-paper-price').val(data.id_paper_price);
                    $('#id_jenis_kertas_edit option[value='+data.id_jenis_kertas+']').attr('selected','selected');
                    $('[aria-controls="select2-id_jenis_kertas_edit-container"] .select2-selection__placeholder').html(data.tb_jenis_kertas.jenis_kertas);
                    $('#id_kertas_plano_edit option[value='+data.id_kertas_plano+']').attr('selected','selected');
                    $('[aria-controls="select2-id_kertas_plano_edit-container"] .select2-selection__placeholder').html(data.tb_kertas_planos.kertas_plano);
                    $('#id_gramasi_edit option[value='+data.id_gramasi+']').attr('selected','selected');
                    $('[aria-controls="select2-id_gramasi_edit-container"] .select2-selection__placeholder').html(data.tb_gramasi.gramasi);
                    $('#amount_edit').val(data.price);
                })
            });

            $('#submit-edit-paper-price').click(function (e) {
                e.preventDefault();
                $(this).html('Sending..');
                var id = $('#id-price').val();
                $.ajax({
                    data: $('#paper-price-edit-form').serialize(),
                    url: "{{ url('paper_price/update/') }}",
                    type: "POST",
                    dataType: 'json',
                    success: function (data) {
                        $('#datatable_paper_price').DataTable().ajax.reload()
                        setTimeout(function () {
                                    self.$("#edit-paper-price-close").trigger("click");
                                }, 1200);
                        $('#modal-edit-paper-price').modal('hide');
                        $('#submit-edit-paper-price').html('Save Changes');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "paper price berhasil diedit",
                            icon: 'success',
                        })
                    },
                    error: function (data) {
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });

            function deleteConfirmation(id) {
                swal.fire({
                    title: "Hapus ?",
                    text: "Harap pastikan",
                    icon: "warning",
                    showCancelButton: !0,
                    confirmButtonText: "Ya, Hapus!",
                    cancelButtonText: "Tidak, batal!",
                    reverseButtons: !0
                }).then(function (e) {
                    if (e.value === true) {
                        // var token = $("meta[name='csrf-token']").attr("content");
                        $.ajax({
                            url: "{{ route('paper_price.destroy','') }}"+'/'+id,
                            type: 'DELETE',
                            data: {
                                "id": id
                                // "_token": token,
                            },
                            success: function (data) {
                                if (data) {
                                    $('#datatable_paper_price').DataTable().ajax.reload()
                                    Swal.fire({
                                        title: 'SUCCESS!',
                                        text: "paper price berhasil dihapus",
                                        icon: 'success',
                                    })
                                } else {
                                    Swal.fire({
                                        title: 'FAILED!',
                                        text: "paper price gagal dihapus",
                                        icon: 'error',
                                    })
                                }
                            }
                        });

                    } else {
                        e.dismiss;
                    }
                }, function (dismiss) {
                    return false;
                })
            }

        </script>
        @include('pages.superadmin.price.paper.data_jscript')
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                        transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                    <path
                                        d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                        fill="black"></path>
                                </svg>
                            </span>
                            <input type="text" id="paper-price-search-box" data-kt-docs-table-filter="search"
                                class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                        </div>
                        <div class="d-flex justify-content-end" data-kt-docs-table-toolbar="base">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal"
                                data-bs-target="#modal-add-paper-price" title="Add price">
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none">
                                        <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1"
                                            transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                                        <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="black"></rect>
                                    </svg>
                                </span>
                                Add Price
                            </button>
                        </div>
                        <div class="d-flex justify-content-end align-items-center d-none"
                            data-kt-docs-table-toolbar="selected">
                            <div class="fw-bolder me-5">
                                <span class="me-2" data-kt-docs-table-select="selected_count"></span>Selected</div>
                            <button type="button" class="btn btn-danger"
                                data-kt-docs-table-select="delete_selected">Selection Action</button>
                        </div>
                    </div>

                    <table id="datatable_paper_price"
                        class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Jenis Kertas</th>
                                <th>Kertas Plano</th>
                                <th>Gramasi</th>
                                <th>Harga</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody> </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="modal" tabindex="-1" id="modal-add-paper-price">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                
                    <div class="modal-header">
                        <h5 class="modal-title">Tambah Paper Price</h5>
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-paper-price-close"
                            data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                        </div>
                    </div>

                    <div class="modal-body mt-n5">
                        <div class="row mt-2">
                            <form id="price-form" name="price-form" class="form-horizontal">
                                <div class="col-lg-12">
                                    <div class="fv-row mb-10">
                                        <label for="id_jenis_kertas" class="required form-label">Jenis Kertas</label>
                                        <select class="form-select" name="id_jenis_kertas" id="id_jenis_kertas"
                                            data-control="select2" data-placeholder="Jenis Kertas">
                                            <option></option>
                                        </select>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="id_kertas_plano" class="required form-label">Kertas Plano</label>
                                        <select class="form-select" name="id_kertas_plano" id="id_kertas_plano"
                                            data-control="select2" data-placeholder="Kertas Plano">
                                            <option></option>
                                        </select>
                                    </div>
                                    <div class="fv-row mb-10">
                                        <label for="id_gramasi" class="required form-label">Gramasi</label>
                                        <select class="form-select" name="id_gramasi" id="id_gramasi"
                                            data-control="select2" data-placeholder="Gramasi">
                                            <option></option>
                                        </select>
                                    </div>
                                    <div class="mb-10">
                                        <label for="price" class="required form-label">Harga</label>
                                        <input type="number" step="any" name="amount" id="amount" class="form-control" placeholder="Harga" />
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="add-paper-price-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                        <button type="submit" id="submit-form-paper-price" class="btn btn-primary my-primary">Save</button>
                    </div>
            </div>
        </div>
    </div>
    <div class="modal fade" tabindex="-1" id="modal-edit-paper-price">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                <form id="paper-price-edit-form" name="price-form" class="form-horizontal">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Paper Price</h5>
                        <!--begin::Close-->
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-order-close"
                            data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                        </div>
                        <!--end::Close-->
                    </div>

                    <div class="modal-body mt-n5">
                        <div class="row">
                            <div class="col-lg-12">
                                <input type="hidden" name="id_paper_price" id="id-paper-price">
                                <div class="fv-row mb-10">
                                    <label for="id_jenis_kertas" class="required form-label">Jenis Kertas</label>
                                    <select class="form-select" name="id_jenis_kertas_edit" id="id_jenis_kertas_edit"
                                        data-control="select2" data-placeholder="Jenis Kertas">
                                        <option></option>
                                        @foreach ($jenis_kertas as $jk)
                                            <option value="{{$jk->id_jenis_kertas}}">{{$jk->jenis_kertas}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="id_kertas_plano" class="required form-label">Kertas Plano</label>
                                    <select class="form-select" name="id_kertas_plano_edit" id="id_kertas_plano_edit"
                                        data-control="select2" data-placeholder="Kertas Plano">
                                        <option></option>
                                        @foreach ($kertas_plano as $kp)
                                            <option value="{{$kp->id_kertas_plano}}">{{$kp->kertas_plano}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="fv-row mb-10">
                                    <label for="id_gramasi" class="required form-label">Gramasi</label>
                                    <select class="form-select" name="id_gramasi_edit" id="id_gramasi_edit"
                                        data-control="select2" data-placeholder="Gramasi">
                                        <option></option>
                                        @foreach ($gramasi as $gr)
                                            <option value="{{$gr->id_gramasi}}">{{$gr->gramasi}}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="mb-10">
                                    <label for="price" class="required form-label">Harga</label>
                                    <input type="number" step="any" name="amount_edit" id="amount_edit" :value="old('price')" class="form-control" placeholder="Harga" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="edit-paper-price-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                        <button type="button" id="submit-edit-paper-price" class="btn btn-primary my-primary">Save
                            changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
