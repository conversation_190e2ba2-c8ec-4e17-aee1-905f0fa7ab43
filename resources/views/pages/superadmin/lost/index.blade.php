<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">lost Order Management</span>
    </x-slot>
    <x-slot name="script">
        <script>
            //CSRF
            $(document).ready(function () {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
            });

            // var datatable;

            datatable = $('#datatable_lost').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{!! url()->current() !!}'
                },
                columns: [{
                        data: 'id_lost',
                        name: 'id_lost'
                    },
                    {
                        data: 'lost',
                        name: 'lost'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false,
                        width: '25%'
                    }
                ],
                // Add data-filter attribute
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(4)').attr('data-filter', data.roles)
                }
            });

            const filterSearch = document.querySelector('[data-kt-docs-table-filter="search"]')
            filterSearch.addEventListener('keyup', function (e) {
                datatable.search(e.target.value).draw()
            })


            //modal editor
            $('#submit-form-lost').click(function (e) {
                e.preventDefault();
                $(this).html('Sending..');
                $.ajax({
                    data: $('#lost-form').serialize(),
                    url: "{{ route('lost.store') }}",
                    type: "POST",
                    dataType: 'json',
                    success: function (data) {
                        datatable.draw();
                        $('#datatable_lost').DataTable().ajax.reload()
                        setTimeout(function () {
                                    self.$("#add-lost-close").trigger("click");
                                }, 1200);
                        $('#modal-add-lost').modal('hide');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "lost baru berhasil dibuat",
                            icon: 'success',
                        })
                    },
                    error: function (data) {
                        $('#submit-form-lost').html('Save Changes');
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });

            //modal editor
            $('body').on('click', '.edit-lost', function (event) {
                event.preventDefault();
                var id = $(this).data('id');
                $.get("{{ url('lost/show/') }}/" + id, function (data) {
                    $('#submit-edit-lost').val("edit-karyawan");
                    $('#modal-edit-lost').modal('show');
                    $('#id-lost').val(data.id_lost);
                    $('#lost-edit').val(data.lost);
                })
            });

            $('#submit-edit-lost').click(function (e) {
                e.preventDefault();
                $(this).html('Sending..');
                var id = $('#id-lost').val();
                $.ajax({
                    data: $('#lost-edit-form').serialize(),
                    url: "{{ url('lost/update/') }}",
                    type: "POST",
                    dataType: 'json',
                    success: function (data) {
                        $('#datatable_lost').DataTable().ajax.reload()
                        setTimeout(function () {
                                    self.$("#edit-lost-close").trigger("click");
                                }, 1200);
                        $('#modal-edit-lost').modal('hide');
                        $('#submit-edit-lost').html('Save Changes');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "lost berhasil diedit",
                            icon: 'success',
                        })
                    },
                    error: function (data) {
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });

            function deleteConfirmation(id) {
                swal.fire({
                    title: "Hapus ?",
                    text: "Harap pastikan",
                    icon: "warning",
                    showCancelButton: !0,
                    confirmButtonText: "Ya, Hapus!",
                    cancelButtonText: "Tidak, batal!",
                    reverseButtons: !0
                }).then(function (e) {
                    if (e.value === true) {
                        // var token = $("meta[name='csrf-token']").attr("content");
                        $.ajax({
                            url: "{{ route('lost.destroy','') }}"+'/'+id,
                            type: 'DELETE',
                            data: {
                                "id": id
                                // "_token": token,
                            },
                            success: function (data) {
                                if (data) {
                                    $('#datatable_lost').DataTable().ajax.reload()
                                    Swal.fire({
                                        title: 'SUCCESS!',
                                        text: "lost berhasil dihapus",
                                        icon: 'success',
                                    })
                                } else {
                                    Swal.fire({
                                        title: 'FAILED!',
                                        text: "lost gagal dihapus",
                                        icon: 'error',
                                    })
                                }
                            }
                        });

                    } else {
                        e.dismiss;
                    }
                }, function (dismiss) {
                    return false;
                })
            }

        </script>
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                        transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                    <path
                                        d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                        fill="black"></path>
                                </svg>
                            </span>
                            <input type="text" id="lost-search-box" data-kt-docs-table-filter="search"
                                class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                        </div>
                        <div class="d-flex justify-content-end" data-kt-docs-table-toolbar="base">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal"
                                data-bs-target="#modal-add-lost" title="Add lost">
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none">
                                        <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1"
                                            transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                                        <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="black"></rect>
                                    </svg>
                                </span>
                                Add lost
                            </button>
                        </div>
                        <div class="d-flex justify-content-end align-items-center d-none"
                            data-kt-docs-table-toolbar="selected">
                            <div class="fw-bolder me-5">
                                <span class="me-2" data-kt-docs-table-select="selected_count"></span>Selected</div>
                            <button type="button" class="btn btn-danger"
                                data-kt-docs-table-select="delete_selected">Selection Action</button>
                        </div>
                    </div>

                    <table id="datatable_lost"
                        class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>lost</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody> </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="modal" tabindex="-1" id="modal-add-lost">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                
                    <div class="modal-header">
                        <h5 class="modal-title">Tambah lost Baru</h5>
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-lost-close"
                            data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                        </div>
                    </div>

                    <div class="modal-body mt-n5">
                        <div class="row">
                            <div class="col-lg-12">
                                <form id="lost-form" name="lost-form" class="form-horizontal">
                                    <div class="mb-3">
                                        <label for="lost" class="required form-label">lost</label>
                                        <input type="text" id="lost" name="lost" :value="old('lost')" required autofocus
                                            class="form-control" placeholder="Nama lost">
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="add-lost-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                        <button type="submit" id="submit-form-lost" class="btn btn-primary my-primary">Save</button>
                    </div>
            </div>
        </div>
    </div>
    <div class="modal fade" tabindex="-1" id="modal-edit-lost">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                <form id="lost-edit-form" name="lost-form" class="form-horizontal">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit lost</h5>
                        <!--begin::Close-->
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-order-close"
                            data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                        </div>
                        <!--end::Close-->
                    </div>

                    <div class="modal-body mt-n5">
                        <div class="row">
                            <div class="col-lg-12">
                                <input type="hidden" name="id_lost" id="id-lost">
                                <div class="mb-3">
                                    <label for="lost" class="required form-label">lost</label>
                                    <input type="text" id="lost-edit" name="lost_edit" :value="old('lost')" required
                                        autofocus class="form-control" placeholder="Nama lost">
                                    <small class="text-danger"><strong id="lost_error"></strong></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="edit-lost-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                        <button type="button" id="submit-edit-lost" class="btn btn-primary my-primary">Save
                            changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
