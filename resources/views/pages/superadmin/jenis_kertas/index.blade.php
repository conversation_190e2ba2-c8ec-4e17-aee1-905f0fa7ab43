<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1"><PERSON><PERSON></span>
    </x-slot>
    <x-slot name="script">
        <script>
            //CSRF
            $(document).ready(function () {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
            });

            // var datatable;

            datatable = $('#datatable_jenis_kertas').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{!! url()->current() !!}'
                },
                columns: [{
                        data: 'id_jenis_kertas',
                        name: 'id_jenis_kertas'
                    },
                    {
                        data: 'jenis_kertas',
                        name: 'jenis_kertas'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false,
                        width: '25%'
                    }
                ],
                // Add data-filter attribute
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(4)').attr('data-filter', data.roles)
                }
            });

            const filterSearch = document.querySelector('[data-kt-docs-table-filter="search"]')
            filterSearch.addEventListener('keyup', function (e) {
                datatable.search(e.target.value).draw()
            })


            //modal editor
            $('#submit-form-jenis_kertas').click(function (e) {
                e.preventDefault();
                $(this).html('Sending..');
                $.ajax({
                    data: $('#jenis_kertas-form').serialize(),
                    url: "{{ route('jenis_kertas.store') }}",
                    type: "POST",
                    dataType: 'json',
                    success: function (data) {
                        datatable.draw();
                        $('#datatable_jenis_kertas').DataTable().ajax.reload()
                        setTimeout(function () {
                                    self.$("#add-jenis_kertas-close").trigger("click");
                                }, 1200);
                        $('#modal-add-jenis_kertas').modal('hide');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "jenis_kertas baru berhasil dibuat",
                            icon: 'success',
                        })
                    },
                    error: function (data) {
                        $('#submit-form-jenis_kertas').html('Save Changes');
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });

            //modal editor
            $('body').on('click', '.edit-jenis_kertas', function (event) {
                event.preventDefault();
                var id = $(this).data('id');
                $.get("{{ url('jenis_kertas/show/') }}/" + id, function (data) {
                    $('#submit-edit-jenis_kertas').val("edit-karyawan");
                    $('#modal-edit-jenis_kertas').modal('show');
                    $('#id-jenis_kertas').val(data.id_jenis_kertas);
                    $('#jenis_kertas-edit').val(data.jenis_kertas);
                })
            });

            $('#submit-edit-jenis_kertas').click(function (e) {
                e.preventDefault();
                $(this).html('Sending..');
                var id = $('#id-jenis_kertas').val();
                $.ajax({
                    data: $('#jenis_kertas-edit-form').serialize(),
                    url: "{{ url('jenis_kertas/update/') }}",
                    type: "POST",
                    dataType: 'json',
                    success: function (data) {
                        $('#datatable_jenis_kertas').DataTable().ajax.reload()
                        setTimeout(function () {
                                    self.$("#edit-jenis_kertas-close").trigger("click");
                                }, 1200);
                        $('#modal-edit-jenis_kertas').modal('hide');
                        $('#submit-edit-jenis_kertas').html('Save Changes');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "jenis_kertas berhasil diedit",
                            icon: 'success',
                        })
                    },
                    error: function (data) {
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });

            function deleteConfirmation(id) {
                swal.fire({
                    title: "Hapus ?",
                    text: "Harap pastikan",
                    icon: "warning",
                    showCancelButton: !0,
                    confirmButtonText: "Ya, Hapus!",
                    cancelButtonText: "Tidak, batal!",
                    reverseButtons: !0
                }).then(function (e) {
                    if (e.value === true) {
                        // var token = $("meta[name='csrf-token']").attr("content");
                        $.ajax({
                            url: "{{ route('jenis_kertas.destroy','') }}"+'/'+id,
                            type: 'DELETE',
                            data: {
                                "id": id
                                // "_token": token,
                            },
                            success: function (data) {
                                if (data) {
                                    $('#datatable_jenis_kertas').DataTable().ajax.reload()
                                    Swal.fire({
                                        title: 'SUCCESS!',
                                        text: "jenis_kertas berhasil dihapus",
                                        icon: 'success',
                                    })
                                } else {
                                    Swal.fire({
                                        title: 'FAILED!',
                                        text: "jenis_kertas gagal dihapus",
                                        icon: 'error',
                                    })
                                }
                            }
                        });

                    } else {
                        e.dismiss;
                    }
                }, function (dismiss) {
                    return false;
                })
            }

        </script>
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                        transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                    <path
                                        d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                        fill="black"></path>
                                </svg>
                            </span>
                            <input type="text" id="jenis_kertas-search-box" data-kt-docs-table-filter="search"
                                class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                        </div>
                        <div class="d-flex justify-content-end" data-kt-docs-table-toolbar="base">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal"
                                data-bs-target="#modal-add-jenis_kertas" title="Add jenis_kertas">
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none">
                                        <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1"
                                            transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                                        <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="black"></rect>
                                    </svg>
                                </span>
                                Add jenis_kertas
                            </button>
                        </div>
                        <div class="d-flex justify-content-end align-items-center d-none"
                            data-kt-docs-table-toolbar="selected">
                            <div class="fw-bolder me-5">
                                <span class="me-2" data-kt-docs-table-select="selected_count"></span>Selected</div>
                            <button type="button" class="btn btn-danger"
                                data-kt-docs-table-select="delete_selected">Selection Action</button>
                        </div>
                    </div>

                    <table id="datatable_jenis_kertas"
                        class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>jenis_kertas</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody> </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="modal" tabindex="-1" id="modal-add-jenis_kertas">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                
                    <div class="modal-header">
                        <h5 class="modal-title">Tambah jenis kertas Baru</h5>
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-jenis_kertas-close"
                            data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                        </div>
                    </div>

                    <div class="modal-body mt-n5">
                        <div class="row">
                            <div class="col-lg-12">
                                <form id="jenis_kertas-form" name="jenis_kertas-form" class="form-horizontal">
                                    <div class="mb-3">
                                        <label for="jenis_kertas" class="required form-label">jenis_kertas</label>
                                        <input type="text" id="jenis_kertas" name="jenis_kertas" :value="old('jenis_kertas')" required autofocus
                                            class="form-control" placeholder="Nama jenis_kertas">
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="add-jenis_kertas-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                        <button type="submit" id="submit-form-jenis_kertas" class="btn btn-primary my-primary">Save</button>
                    </div>
            </div>
        </div>
    </div>
    <div class="modal fade" tabindex="-1" id="modal-edit-jenis_kertas">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                <form id="jenis_kertas-edit-form" name="jenis_kertas-form" class="form-horizontal">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit jenis kertas</h5>
                        <!--begin::Close-->
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-order-close"
                            data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                        </div>
                        <!--end::Close-->
                    </div>

                    <div class="modal-body mt-n5">
                        <div class="row">
                            <div class="col-lg-12">
                                <input type="hidden" name="id_jenis_kertas" id="id-jenis_kertas">
                                <div class="mb-3">
                                    <label for="jenis_kertas" class="required form-label">jenis_kertas</label>
                                    <input type="text" id="jenis_kertas-edit" name="jenis_kertas_edit" :value="old('jenis_kertas')" required
                                        autofocus class="form-control" placeholder="Nama jenis_kertas">
                                    <small class="text-danger"><strong id="jenis_kertas_error"></strong></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="edit-jenis_kertas-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                        <button type="button" id="submit-edit-jenis_kertas" class="btn btn-primary my-primary">Save
                            changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
