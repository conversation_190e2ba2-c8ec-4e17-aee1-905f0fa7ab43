<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">Mesin Management</span>
    </x-slot>
    <x-slot name="script">
        <script>
            //CSRF
            $(document).ready(function() {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
            });

            // var datatable;

            datatable = $('#datatable_mesin').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{!! url()->current() !!}'
                },
                columns: [{
                        data: 'id_mesin',
                        name: 'id_mesin'
                    },
                    {
                        data: 'mesin',
                        name: 'mesin'
                    },
                    {
                        data: 'max_area_print',
                        name: 'max_area_print'
                    },
                    {
                        data: 'min_area_print',
                        name: 'min_area_print'
                    },
                    {
                        data: 'max_size_paper',
                        name: 'max_size_paper'
                    },
                    {
                        data: 'min_size_paper',
                        name: 'min_size_paper'
                    },
                    {
                        data: 'insheet',
                        name: 'insheet'
                    },
                    {
                        data: 'koefisien',
                        name: 'koefisien'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false,
                        width: '25%'
                    }
                ],
                // Add data-filter attribute
                createdRow: function(row, data, dataIndex) {
                    $(row).find('td:eq(4)').attr('data-filter', data.roles)
                }
            });

            const filterSearch = document.querySelector('[data-kt-docs-table-filter="search"]')
            filterSearch.addEventListener('keyup', function(e) {
                datatable.search(e.target.value).draw()
            })


            //modal editor
            $('#submit-form-mesin').click(function(e) {
                e.preventDefault();
                $(this).html('Sending..');
                $.ajax({
                    data: $('#mesin-form').serialize(),
                    url: "{{ route('mesin.store') }}",
                    type: "POST",
                    dataType: 'json',
                    success: function(data) {
                        datatable.draw();
                        $('#datatable_mesin').DataTable().ajax.reload()
                        setTimeout(function() {
                            self.$("#add-mesin-close").trigger("click");
                        }, 1200);
                        $('#modal-add-mesin').modal('hide');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "mesin baru berhasil dibuat",
                            icon: 'success',
                        })
                    },
                    error: function(data) {
                        $('#submit-form-mesin').html('Save Changes');
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });

            //modal editor
            $('body').on('click', '.edit-mesin', function(event) {
                event.preventDefault();
                var id = $(this).data('id');
                $.get("{{ url('mesin/show/') }}/" + id, function(data) {
                    $('#submit-edit-mesin').val("edit-karyawan");
                    $('#modal-edit-mesin').modal('show');
                    $('#id-mesin').val(data.id_mesin);
                    $('#mesin-edit').val(data.mesin);
                    $('#max-area-print-edit').val(data.max_area_print);
                    $('#min-area-print-edit').val(data.min_area_print);
                    $('#max-size-paper-edit').val(data.max_size_paper);
                    $('#min-size-paper-edit').val(data.min_size_paper);
                    $('#insheet-edit').val(data.insheet);
                    $('#koefisien-edit').val(data.koefisien);
                })
            });

            $('#submit-edit-mesin').click(function(e) {
                e.preventDefault();
                $(this).html('Sending..');
                var id = $('#id-mesin').val();
                $.ajax({
                    data: $('#mesin-edit-form').serialize(),
                    url: "{{ url('mesin/update/') }}",
                    type: "POST",
                    dataType: 'json',
                    success: function(data) {
                        $('#datatable_mesin').DataTable().ajax.reload()
                        setTimeout(function() {
                            self.$("#edit-mesin-close").trigger("click");
                        }, 1200);
                        $('#modal-edit-mesin').modal('hide');
                        $('#submit-edit-mesin').html('Save Changes');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "mesin berhasil diedit",
                            icon: 'success',
                        })
                    },
                    error: function(data) {
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });

            function deleteConfirmation(id) {
                swal.fire({
                    title: "Hapus ?",
                    text: "Harap pastikan",
                    icon: "warning",
                    showCancelButton: !0,
                    confirmButtonText: "Ya, Hapus!",
                    cancelButtonText: "Tidak, batal!",
                    reverseButtons: !0
                }).then(function(e) {
                    if (e.value === true) {
                        // var token = $("meta[name='csrf-token']").attr("content");
                        $.ajax({
                            url: "{{ route('mesin.destroy','') }}" + '/' + id,
                            type: 'DELETE',
                            data: {
                                "id": id
                                // "_token": token,
                            },
                            success: function(data) {
                                if (data) {
                                    $('#datatable_mesin').DataTable().ajax.reload()
                                    Swal.fire({
                                        title: 'SUCCESS!',
                                        text: "mesin berhasil dihapus",
                                        icon: 'success',
                                    })
                                } else {
                                    Swal.fire({
                                        title: 'FAILED!',
                                        text: "mesin gagal dihapus",
                                        icon: 'error',
                                    })
                                }
                            }
                        });

                    } else {
                        e.dismiss;
                    }
                }, function(dismiss) {
                    return false;
                })
            }
        </script>
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1" transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                    <path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z" fill="black"></path>
                                </svg>
                            </span>
                            <input type="text" id="mesin-search-box" data-kt-docs-table-filter="search" class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                        </div>
                        <div class="d-flex justify-content-end" data-kt-docs-table-toolbar="base">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#modal-add-mesin" title="Add Mesin">
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1" transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                                        <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="black"></rect>
                                    </svg>
                                </span>
                                Add Mesin
                            </button>
                        </div>
                        <div class="d-flex justify-content-end align-items-center d-none" data-kt-docs-table-toolbar="selected">
                            <div class="fw-bolder me-5">
                                <span class="me-2" data-kt-docs-table-select="selected_count"></span>Selected
                            </div>
                            <button type="button" class="btn btn-danger" data-kt-docs-table-select="delete_selected">Selection Action</button>
                        </div>
                    </div>

                    <table id="datatable_mesin" class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Mesin</th>
                                <th>Max Area Print</th>
                                <th>Min Area Print</th>
                                <th>Max Size Paper</th>
                                <th>Min Size Paper</th>
                                <th>Insheet</th>
                                <th>Koefisien</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody> </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="modal" tabindex="-1" id="modal-add-mesin">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">

                <div class="modal-header">
                    <h5 class="modal-title">Tambah Mesin Baru</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-mesin-close" data-bs-dismiss="modal" aria-label="Close">
                        <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                    </div>
                </div>

                <div class="modal-body mt-n5">
                    <div class="row">
                        <div class="col-lg-12">
                            <form id="mesin-form" name="mesin-form" class="form-horizontal">
                                <div class="mb-3">
                                    <label for="mesin" class="required form-label">Mesin</label>
                                    <input type="text" id="mesin" name="mesin" :value="old('mesin')" required autofocus class="form-control" placeholder="Nama Mesin">
                                </div>
                                <div class="mb-3">
                                    <label for="max_area_print" class="required form-label">Max Area Print</label>
                                    <input type="number" id="max_area_print" name="max_area_print" :value="old('max_area_print')" required autofocus class="form-control" placeholder="Maksimal Area Print">
                                </div>
                                <div class="mb-3">
                                    <label for="min_area_print" class="required form-label">Min Area Print</label>
                                    <input type="number" id="min_area_print" name="min_area_print" :value="old('min_area_print')" required autofocus class="form-control" placeholder="Minimal Area Print">
                                </div>
                                <div class="mb-3">
                                    <label for="max_size_paper" class="required form-label">Max Size Paper</label>
                                    <input type="number" id="max_size_paper" name="max_size_paper" :value="old('max_size_paper')" required autofocus class="form-control" placeholder="Maksimal Size Paper">
                                </div>
                                <div class="mb-3">
                                    <label for="min_size_paper" class="required form-label">Min Size Paper</label>
                                    <input type="number" id="min_size_paper" name="min_size_paper" :value="old('min_size_paper')" required autofocus class="form-control" placeholder="Minimal Size Paper">
                                </div>
                                <div class="mb-3">
                                    <label for="insheet" class="required form-label">Insheet</label>
                                    <input type="number" id="insheet" name="insheet" :value="old('insheet')" required autofocus class="form-control" placeholder="Insheet">
                                </div>
                                <div class="mb-3">
                                    <label for="koefisien" class="required form-label">Koefisien</label>
                                    <input type="number" id="koefisien" name="koefisien" :value="old('koefisien')" required autofocus class="form-control" placeholder="Koefisien">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" id="add-mesin-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                    <button type="submit" id="submit-form-mesin" class="btn btn-primary my-primary">Save</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" tabindex="-1" id="modal-edit-mesin">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                <form id="mesin-edit-form" name="mesin-form" class="form-horizontal">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Mesin</h5>
                        <!--begin::Close-->
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-order-close" data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                        </div>
                        <!--end::Close-->
                    </div>

                    <div class="modal-body mt-n5">
                        <div class="row">
                            <div class="col-lg-12">
                                <input type="hidden" name="id_mesin" id="id-mesin">
                                <div class="mb-3">
                                    <label for="mesin" class="required form-label">Mesin</label>
                                    <input type="text" id="mesin-edit" name="mesin_edit" :value="old('mesin')" required autofocus class="form-control" placeholder="Nama Mesin">
                                    <small class="text-danger"><strong id="mesin_error"></strong></small>
                                </div>
                                <div class="mb-3">
                                    <label for="max-area-print" class="required form-label">Max Area Print</label>
                                    <input type="text" id="max-area-print-edit" name="max_area_print_edit" :value="old('max_area_print')" required autofocus class="form-control" placeholder="Maksimal Area Print">
                                    <small class="text-danger"><strong id="max_area_print_error"></strong></small>
                                </div>
                                <div class="mb-3">
                                    <label for="min-area-print" class="required form-label">Min Area Print</label>
                                    <input type="text" id="min-area-print-edit" name="min_area_print_edit" :value="old('min_area_print')" required autofocus class="form-control" placeholder="Minimal Area Print">
                                    <small class="text-danger"><strong id="min_area_print_error"></strong></small>
                                </div>
                                <div class="mb-3">
                                    <label for="max-size-paper" class="required form-label">Max Size Paper</label>
                                    <input type="text" id="max-size-paper-edit" name="max_size_paper_edit" :value="old('max_size_paper')" required autofocus class="form-control" placeholder="Maksimal Size Paper">
                                    <small class="text-danger"><strong id="max_size_paper_error"></strong></small>
                                </div>
                                <div class="mb-3">
                                    <label for="min-size-paper" class="required form-label">Min Size Paper</label>
                                    <input type="text" id="min-size-paper-edit" name="min_size_paper_edit" :value="old('min_size_paper')" required autofocus class="form-control" placeholder="Minimal Size Paper">
                                    <small class="text-danger"><strong id="min_size_paper_error"></strong></small>
                                </div>
                                <div class="mb-3">
                                    <label for="insheet" class="required form-label">Insheet</label>
                                    <input type="text" id="insheet-edit" name="insheet_edit" :value="old('insheet')" required autofocus class="form-control" placeholder="Insheet">
                                    <small class="text-danger"><strong id="insheet_error"></strong></small>
                                </div>
                                <div class="mb-3">
                                    <label for="koefisien" class="required form-label">Koefisien</label>
                                    <input type="text" id="koefisien-edit" name="koefisien_edit" :value="old('koefisien')" required autofocus class="form-control" placeholder="Koefisien">
                                    <small class="text-danger"><strong id="koefisien_error"></strong></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="edit-mesin-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                        <button type="button" id="submit-edit-mesin" class="btn btn-primary my-primary">Save
                            changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>