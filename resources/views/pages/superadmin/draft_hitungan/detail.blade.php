<div class="modal-header">
    <h5 class="modal-title">{{$data->description}} - {{$data->code}}</h5>
    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-order-close"
        data-bs-dismiss="modal" aria-label="Close">
        <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
    </div>
</div>

<div class="modal-body mt-n5">
    <div class="row">
        <div class="col-lg-6">
            <table class="table">
                <tr>
                    <td><PERSON><PERSON></td>
                    <td>:</td>
                    <td>{{$data->description}}</td>
                </tr>
                <tr>
                    
                    <td>Dimensi (cm)</td>
                    <td nowrap>:</td>
                    <td>{{$data->dimensi_p.' x '.$data->dimensi_l.' x '.$data->dimensi_t}}</td>
                </tr>
                <tr>
                    <td><PERSON><PERSON></td>
                    <td nowrap>:</td>
                    <td>{{$data->tb_jenis_kertas->jenis_kertas.' - '.str_replace('/','x',$data->tb_kertas_plano->kertas_plano)}}</td>
                </tr>
                <tr>
                    <td>Gramasi (gsm)</td>
                    <td nowrap>:</td>
                    <td>{{$data->tb_gramasi->gramasi}}</td>
                </tr>
                <tr>
                    
                    <td>LP (cm)</td>
                    <td nowrap>:</td>
                    <td>{{$data->luas_permukaan}}</td>
                </tr>
                <tr>
                    <td>Ukuran Kertas/Potong Board (cm)</td>
                    <td nowrap>:</td>
                    <td>{{$data->luas_kertas}}</td>
                </tr>
                <tr>
                    <td>Quantity (pcs)</td>
                    <td nowrap>:</td>
                    <td>{{number_format($data->quantity, 0, '', ',')}}</td>
                </tr>
                @if ($data->jumlah_potong)
                    <tr>
                        <td>Hasil Potong Kertas (lembar)</td>
                        <td nowrap>:</td>
                        <td>{{number_format($data->jumlah_potong, 0, '', ',')}}</td>
                    </tr>
                @endif
                @if ($data->waste)
                    <tr>
                        <td>Waste</td>
                        <td nowrap>:</td>
                        <td>{{$data->waste}}%</td>
                    </tr>
                @endif
            </table>
            <table class="table mt-3" style="border-collapse:collapse;">
                <tr class="bg-warning">
                    <td colspan="2" class="text-center border"><strong>Kebutuhan Kertas</strong></td>
                </tr>
                @if (isset($data->kebutuhan_kertas))
                    @php
                        $kebutuhan_kertas = json_decode($data->kebutuhan_kertas, true);
                    @endphp
                    @foreach ($kebutuhan_kertas as $key => $k_kertas)
                        <tr>
                            <td class="border"><span class="ms-4">{{converting($key)}}</span></td>
                            <td class="border text-end"><span class="me-4">{{$k_kertas}}</span></td>
                        </tr>
                    @endforeach
                @endif
            </table>
        </div>
        <div class="col-lg-6">
            <table class="table" style="border-collapse:collapse;">
                <tr class="bg-warning">
                    <td colspan="2" class="text-center border"><strong>HPP(Rp)</strong></td>
                </tr>
                @if (isset($data->additional_cost))
                    @php
                        $additional_cost = json_decode($data->additional_cost, true);
                    @endphp
                    @foreach ($additional_cost as $key => $cost)
                        <tr>
                            <td class="border"><span class="ms-4">{{$key}}</span></td>
                            <td class="border text-end"><span class="me-4">{{number_format($cost, 0, '', ',')}}</span></td>
                        </tr>
                    @endforeach
                @endif
                <tr>
                    <td class="border"><span class="ms-4"><strong>Subtotal</strong></span></td>
                    <td class="border text-end"><span class="me-4">{{number_format($data->harga_modal*$data->quantity, 0, '', ',')}}</span></td>
                </tr>
                <tr>
                    <td class="border"><span class="ms-4"><strong>Harga Modal</strong></span></td>
                    <td class="border text-end"><span class="me-4">{{number_format($data->harga_modal, 0, '', ',')}}</span></td>
                </tr>
            </table>
        </div>
    </div>
</div>

<div class="modal-footer">
    <button type="button" id="detail-draft-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
</div>