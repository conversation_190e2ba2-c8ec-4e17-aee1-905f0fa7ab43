<x-app-layout>
    <x-slot name="header">
        <ol class="breadcrumb breadcrumb-separatorless fs-6 fw-semibold">
            <li class="breadcrumb-item pe-3"><a href="{{ url()->previous() }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-2xl" style="color: white"></i>
                </a></li>
            <li class="breadcrumb-item pe-3"><a href="#" class="pe-3">
                    <h3 class="display-6 text-white">
                        Data Draft Perhitungan
                    </h3>
                </a></li>
        </ol>
    </x-slot>
    <x-slot name="script">
        <script>
            //CSRF
            $(document).ready(function () {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
            });

            // var datatable;

            datatable = $('#datatable_draft_hitungan').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{!! url()->current() !!}'
                },
                columns: [
                    {
                        data: 'code',
                        name: 'code'
                    },
                    {
                        data: 'description',
                        name: 'description'
                    },
                    {
                        data: 'bahan',
                        name: 'bahan'
                    },
                    {
                        data: 'dimensi',
                        name: 'dimensi'
                    },
                    {
                        data: 'luas_permukaan',
                        name: 'luas_permukaan'
                    },
                    {
                        data: 'quantity',
                        name: 'quantity'
                    },
                    {
                        data: 'harga_modal',
                        name: 'harga_modal'
                    },
                    {
                        data: 'customer',
                        name: 'customer'
                    },
                    {
                        data: 'pic',
                        name: 'pic'
                    },
                    {
                        data: 'status',
                        name: 'status'
                    },
                    {
                        name: 'created_at',
                        data: {
                            '_': 'created_at.display', 
                            'sort': 'created_at.timestamp' 
                        },
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false,
                        width: '25%'
                    }
                ],
                order: [[10, 'desc']],
                // Add data-filter attribute
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(4)').attr('data-filter', data.roles)
                }
            });

            const filterSearch = document.querySelector('[data-kt-docs-table-filter="search"]')
            filterSearch.addEventListener('keyup', function (e) {
                datatable.search(e.target.value).draw()
            })


            //modal editor
            $('#submit-form-claim-data').click(function (e) {
                e.preventDefault();
                var id_draft = '';
                $('.drafts').each(function(){
                    if ($(this).val() != '') {
                        id_draft += '&id_draft[]='+$(this).val();
                    }
                })
                $(this).html('Sending..');
                $.ajax({
                    data: $('#claim-data-form').serialize() + id_draft,
                    url: "{{ route('draft_hitungan.store') }}",
                    type: "POST",
                    dataType: 'json',
                    success: function (data) {
                        datatable.draw();
                        $('#datatable_draft_hitungan').DataTable().ajax.reload()
                        setTimeout(function () {
                                self.$("#add-claim-data-close").trigger("click");
                            }, 1200);
                        $('#modal-add-claim-data').modal('hide');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "Data berhasil di claim",
                            icon: 'success',
                        })
                    },
                    error: function (data) {
                        $('#submit-form-claim-data').html('Save Changes');
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Terjadi kesalahan",
                            icon: 'error',
                        })
                    }
                });
            });

            //modal editor
            $('body').on('click', '.detail_draft', function (event) {
                event.preventDefault();
                var id = $(this).data('id');
                $.get("{{ url('draft_hitungan/detail/') }}/" + id, function (data) {
                    $('#content-detail-draft').html(data);
                    $('#modal-detail-draft').modal('show');
                })
            });

            function deleteConfirmation(id) {
                swal.fire({
                    title: "Anda yakin ingin hapus data ini ?",
                    text: "Harap pastikan",
                    icon: "warning",
                    showCancelButton: !0,
                    confirmButtonText: "Ya, Hapus!",
                    cancelButtonText: "Tidak, batal!",
                    reverseButtons: !0
                }).then(function (e) {
                    if (e.value === true) {
                        $.ajax({
                            url: "{{ route('draft_hitungan.destroy','') }}"+'/'+id,
                            type: 'DELETE',
                            data: {
                                "id": id
                            },
                            success: function (data) {
                                if (data) {
                                    $('#datatable_draft_hitungan').DataTable().ajax.reload()
                                    Swal.fire({
                                        title: 'SUCCESS!',
                                        text: "Draft berhasil dihapus",
                                        icon: 'success',
                                    })
                                } else {
                                    Swal.fire({
                                        title: 'FAILED!',
                                        text: "Draft gagal dihapus",
                                        icon: 'error',
                                    })
                                }
                            }
                        });

                    } else {
                        e.dismiss;
                    }
                }, function (dismiss) {
                    return false;
                })
            }

            const format = (item) => {
                if (!item.id) {
                    return item.text;
                }
                var img =""
                if (item.jenis_bahan) {
                    var span = $("<span>", {
                        text: item.text 
                    });   
                } else {
                    var span = $("<span>", {
                        text: item.text
                    });   
                }
                span.prepend(img)
                return span
            }
            $(document).ready(function() {

                $('#id_produksi').select2({
                    allowClear: true,
                    debug: true,
                    dropdownParent: $('#customer_section'),
                    ajax: {
                        url: "{{ route('draft_hitungan.getOrderProduct') }}",
                        processResults: function(data) {
                            var resultsData = []

                            $.each(data, function(index, item) {
                                resultsData.push({
                                    id: item.id,
                                    text: item.text
                                })
                            })

                            return {
                                results: resultsData
                            };
                        },
                        // cache: true
                    },
                    templateResult: function(item) {
                        return format(item)
                    }
                })
                .on('select2:opening', function(e) {
                    $(this).data('select2').$dropdown.find(':input.select2-search__field').attr('placeholder',
                        'Cari Customer')
                })

                $('#id_draft_1').select2({
                    allowClear: true
                });
            })
            var no = 1;
            $('#btn-add-draft').on('click', function(){
                no = no+1;

                var draft = '<div class="mb-3">';
                draft += '<select class="form-select drafts" name="id_draft[]" id="id_draft_'+no+'" data-control="select2" data-placeholder="Pilih Referensi Perhitungan">';
                draft += $('#list-draft').html();
                draft += '</select>';
                draft += '</div>';

                $('#all-draft').append(draft);
                $('#id_draft_'+no).select2({
                    allowClear: true
                });
            })

            // $('#id_draft').on('change', function() {
            //     id = $(this).val();
            //     $.get("{{ url('draft_hitungan/getDraft') }}/" + id, function (data) {
            //         const harga_modal = parseInt(data.harga_modal).toLocaleString();
            //         const qty = parseInt(data.quantity).toLocaleString();
            //         $('#panjang_cetak').val(data.p);
            //         $('#lebar_cetak').val(data.l);
            //         $('#jumlah_potong').val(data.jumlah_potong);
            //         $('#waste').val(data.waste);
            //         $('#quantity').val(qty);
            //         $('#harga_modal').val('Rp '+harga_modal);
            //     })
            // })

            $('#btn-hitung').on('click', function(){
                var id_draft = '';
                $('.drafts').each(function(){
                    if ($(this).val() != '') {
                        id_draft += '&id_draft[]='+$(this).val();
                    }
                })
                
                $.ajax({
                    data: $('#claim-data-form').serialize()+id_draft,
                    url: "{{ url('draft_hitungan/getDraft') }}",
                    type: "GET",
                    dataType: 'json',
                    success: function (data) {
                        // setTimeout(function () {
                        //     self.$("#add-claim-data-close").trigger("click");
                        // }, 1200);
                        // $('#modal-add-claim-data').modal('hide');
                        // Swal.fire({
                        //     title: 'SUCCESS!',
                        //     text: "Data berhasil di claim",
                        //     icon: 'success',
                        // })
                        const harga_modal = parseInt(data.harga_modal).toLocaleString();
                        const qty = parseInt(data.quantity).toLocaleString();
                        $('#panjang_cetak').val(data.p);
                        $('#lebar_cetak').val(data.l);
                        $('#jumlah_potong').val(data.jumlah_potong);
                        $('#waste').val(data.waste);
                        $('#quantity').val(qty);
                        $('#harga_modal').val('Rp '+harga_modal);
                    },
                    error: function (data) {
                        $('#submit-form-claim-data').html('Save Changes');
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Terjadi kesalahan",
                            icon: 'error',
                        })
                    }
                });
            })
        </script>
        <style>
            th{
                font-weight: 600 !important;
            }
        </style>
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                        transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                    <path
                                        d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                        fill="black"></path>
                                </svg>
                            </span>
                            <input type="text" id="jenis_kertas-search-box" data-kt-docs-table-filter="search"
                                class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                        </div>
                        <div class="d-flex justify-content-end" data-kt-docs-table-toolbar="base">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal"
                            data-bs-target="#modal-add-claim-data" title="Add claim_data" >
                                <i class="fa fa-save me-1"></i>
                                Claim Data
                            </button>
                        </div>
                        <div class="d-flex justify-content-end align-items-center d-none"
                            data-kt-docs-table-toolbar="selected">
                            <div class="fw-bolder me-5">
                                <span class="me-2" data-kt-docs-table-select="selected_count"></span>Selected</div>
                            <button type="button" class="btn btn-danger"
                                data-kt-docs-table-select="delete_selected">Selection Action</button>
                        </div>
                    </div>

                    <table id="datatable_draft_hitungan"
                        class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
                        <thead>
                            <tr>
                                <th width="5%">Kode</th>
                                <th width="10%">Nama</th>
                                <th width="10%">Bahan</th>
                                <th width="10%">Dimensi (cm)</th>
                                <th width="10%">LP (cm)</th>
                                <th width="10%">Qty (pcs)</th>
                                <th width="10%">Harga Modal</th>
                                <th width="10%">Nama Customer</th>
                                <th width="5%" class="text-center">PIC</th>
                                <th width="10%">Status</th>
                                <th width="5%">Last Created</th>
                                <th class="text-center" width="5%">Action</th>
                            </tr>
                        </thead>
                        <tbody> </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="modal fade" tabindex="-1" id="modal-add-claim-data">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content" id='content-claim-data'>
                <div class="modal-header">
                    <h5 class="modal-title">Claim Data Perhitungan</h5>
                    <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-claim-data-close"
                        data-bs-dismiss="modal" aria-label="Close">
                        <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                    </div>
                </div>
                
                <div class="modal-body mt-n5">
                    <form id="claim-data-form" name="claim-data-form" class="form-horizontal">
                        <div class="row">
                            <div class="col-lg-12">
                                    <div class="mb-3" id="customer_section">
                                        <label for="id_produksi" class="required form-label">Nama Customer</label>
                                        <select class="form-select" name="id_produksi" id="id_produksi"
                                            data-control="select2" data-placeholder="Pilih Customer">
                                            <option></option>
                                        </select>
                                    </div>
                                </form>
                            </div>
                            <div class="col-lg-12">
                                <form id="claim-data-form" name="claim-data-form" class="form-horizontal">
                                    <div id="all-draft">
                                        <div class="mb-3">
                                            <label for="id_draft" class="required form-label">Kode Draft Perhitungan</label>
                                            <select class="form-select drafts" name="id_draft[]" id="id_draft_1" data-control="select2" data-placeholder="Pilih Referensi Perhitungan">
                                                <option value="" selected></option>
                                                @foreach ($drafts as $draft)
                                                    <option value="{{$draft->id}}">{{$draft->code.' - '.$draft->description.' ('.$draft->tb_bahans->bahan.')'}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </form>
                                <div class="d-flex align-items-center justify-content-between">
                                    <button class="btn btn-primary" type="button" id="btn-add-draft"><i class="fa fa-plus me-1"></i> Tambah Referensi</button>
                                    <button class="btn btn-danger" type="button" id="btn-hitung"><i class="fa fa-calculator me-1"></i> Hitung</button>
                                </div>
                            </div>
                            <div class="row m-5">
                                <div class="col-md-3">
                                    <label class="form-label">Ukuran Permukan Cetak (cm)</label>
                                </div>
                                <div class="col-md-9">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="panjang" class="form-label">Panjang</label>
                                            <input type="text" class="form-control text-center" id="panjang_cetak" name="panjang_cetak" />
                                        </div>
                                        <div class="col-md-6">
                                            <label for="lebar" class="form-label">Lebar</label>
                                            <input type="text" class="form-control text-center" id="lebar_cetak" name="lebar_cetak" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row m-5">
                                <div class="col-md-3">
                                    <label class="form-label">Hasil Potong Kertas (lembar)</label>
                                </div>
                                <div class="col-md-9">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="jumlah_kertas" class="form-label">Jumlah kertas yang didapat</label>
                                            <input type="text" class="form-control text-center" name="jumlah_potong" id="jumlah_potong" readonly>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="wasting" class="form-label">Waste (%)</label>
                                            <input type="text" class="form-control text-center" name="waste" id="waste" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row m-5">
                                <div class="col-md-3">
                                    <label class="form-label">Detail Akhir</label>
                                </div>
                                <div class="col-md-9">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="panjang" class="form-label">Quantity (pcs)</label>
                                            <input type="text" class="form-control text-center" id="quantity" name="quantity" readonly/>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="lebar" class="form-label">Total Harga Modal</label>
                                            <input type="text" class="form-control text-center" id="harga_modal" name="harga_modal" readonly/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                
                <div class="modal-footer">
                    <button type="button" id="add-claim-data-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                    <button type="submit" id="submit-form-claim-data" class="btn btn-primary my-primary">Save</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" tabindex="-1" id="modal-detail-draft">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content" id='content-detail-draft'>
            </div>
        </div>
    </div>

    <div id="list-draft" class="d-none">
        <option value="" selected></option>
        @foreach ($drafts as $draft)
            <option value="{{$draft->id}}">{{$draft->code.' - '.$draft->description.' ('.$draft->tb_bahans->bahan.')'}}</option>
        @endforeach
    </div>
</x-app-layout>
