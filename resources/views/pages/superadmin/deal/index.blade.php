<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">Status Follow Up Management</span>
    </x-slot>
    <x-slot name="script">
        <script>
            //CSRF
            $(document).ready(function () {
                $.ajaxSetup({
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                });
            });

            // var datatable;

            datatable = $('#datatable_deal').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: '{!! url()->current() !!}'
                },
                columns: [{
                        data: 'id_deal',
                        name: 'id_deal'
                    },
                    {
                        data: 'deal',
                        name: 'deal'
                    },
                    {
                        data: 'position',
                        name: 'position'
                    },
                    {
                        data: 'action',
                        name: 'action',
                        orderable: false,
                        searchable: false,
                        width: '25%'
                    }
                ],
                // Add data-filter attribute
                createdRow: function (row, data, dataIndex) {
                    $(row).find('td:eq(4)').attr('data-filter', data.roles)
                }
            });

            const filterSearch = document.querySelector('[data-kt-docs-table-filter="search"]')
            filterSearch.addEventListener('keyup', function (e) {
                datatable.search(e.target.value).draw()
            })


            //modal editor
            $('#submit-form-deal').click(function (e) {
                e.preventDefault();
                $(this).html('Sending..');
                $.ajax({
                    data: $('#deal-form').serialize(),
                    url: "{{ route('deal.store') }}",
                    type: "POST",
                    dataType: 'json',
                    success: function (data) {
                        datatable.draw();
                        $('#datatable_deal').DataTable().ajax.reload()
                        setTimeout(function () {
                                    self.$("#add-deal-close").trigger("click");
                                }, 1200);
                        $('#modal-add-deal').modal('hide');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "deal baru berhasil dibuat",
                            icon: 'success',
                        })
                    },
                    error: function (data) {
                        $('#submit-form-deal').html('Save Changes');
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });

            //modal editor
            $('body').on('click', '.edit-deal', function (event) {
                event.preventDefault();
                var id = $(this).data('id');
                $.get("{{ url('deal/show/') }}/" + id, function (data) {
                    $('#submit-edit-deal').val("edit-karyawan");
                    $('#modal-edit-deal').modal('show');
                    $('#id-deal').val(data.id_deal);
                    $('#deal-edit').val(data.deal);
                    $('#posisi-edit').val(data.position);
                })
            });

            $('#submit-edit-deal').click(function (e) {
                e.preventDefault();
                $(this).html('Sending..');
                var id = $('#id-deal').val();
                $.ajax({
                    data: $('#deal-edit-form').serialize(),
                    url: "{{ url('deal/update/') }}",
                    type: "POST",
                    dataType: 'json',
                    success: function (data) {
                        $('#datatable_deal').DataTable().ajax.reload()
                        setTimeout(function () {
                                    self.$("#edit-deal-close").trigger("click");
                                }, 1200);
                        $('#modal-edit-deal').modal('hide');
                        $('#submit-edit-deal').html('Save Changes');
                        Swal.fire({
                            title: 'SUCCESS!',
                            text: "deal berhasil diedit",
                            icon: 'success',
                        })
                    },
                    error: function (data) {
                        Swal.fire({
                            title: 'ERROR!',
                            text: "Harap Lengkapi form yang ada",
                            icon: 'error',
                        })
                    }
                });
            });

            function deleteConfirmation(id) {
                swal.fire({
                    title: "Hapus ?",
                    text: "Harap pastikan",
                    icon: "warning",
                    showCancelButton: !0,
                    confirmButtonText: "Ya, Hapus!",
                    cancelButtonText: "Tidak, batal!",
                    reverseButtons: !0
                }).then(function (e) {
                    if (e.value === true) {
                        // var token = $("meta[name='csrf-token']").attr("content");
                        $.ajax({
                            url: "{{ route('deal.destroy','') }}"+'/'+id,
                            type: 'DELETE',
                            data: {
                                "id": id
                                // "_token": token,
                            },
                            success: function (data) {
                                if (data) {
                                    $('#datatable_deal').DataTable().ajax.reload()
                                    Swal.fire({
                                        title: 'SUCCESS!',
                                        text: "deal berhasil dihapus",
                                        icon: 'success',
                                    })
                                } else {
                                    Swal.fire({
                                        title: 'FAILED!',
                                        text: "deal gagal dihapus",
                                        icon: 'error',
                                    })
                                }
                            }
                        });

                    } else {
                        e.dismiss;
                    }
                }, function (dismiss) {
                    return false;
                })
            }

        </script>
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1"
                                        transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                    <path
                                        d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                        fill="black"></path>
                                </svg>
                            </span>
                            <input type="text" id="deal-search-box" data-kt-docs-table-filter="search"
                                class="form-control form-control-solid w-450px ps-15" placeholder="Search ...">
                        </div>
                        <div class="d-flex justify-content-end" data-kt-docs-table-toolbar="base">
                            <button type="button" class="btn btn-success" data-bs-toggle="modal"
                                data-bs-target="#modal-add-deal" title="Add deal">
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none">
                                        <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1"
                                            transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                                        <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="black"></rect>
                                    </svg>
                                </span>
                                Add deal
                            </button>
                        </div>
                        <div class="d-flex justify-content-end align-items-center d-none"
                            data-kt-docs-table-toolbar="selected">
                            <div class="fw-bolder me-5">
                                <span class="me-2" data-kt-docs-table-select="selected_count"></span>Selected</div>
                            <button type="button" class="btn btn-danger"
                                data-kt-docs-table-select="delete_selected">Selection Action</button>
                        </div>
                    </div>

                    <table id="datatable_deal"
                        class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>deal</th>
                                <th>Posisi</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody> </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="modal" tabindex="-1" id="modal-add-deal">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                
                    <div class="modal-header">
                        <h5 class="modal-title">Tambah deal Baru</h5>
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-deal-close"
                            data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                        </div>
                    </div>

                    <div class="modal-body mt-n5">
                        <div class="row">
                            <div class="col-lg-12">
                                <form id="deal-form" name="deal-form" class="form-horizontal">
                                    <div class="mb-3">
                                        <label for="deal" class="required form-label">deal</label>
                                        <input type="text" id="deal" name="deal" :value="old('deal')" required autofocus
                                            class="form-control" placeholder="Nama deal">
                                    </div>
                                    <div class="mb-3">
                                        <label for="posisi" class="required form-label">posisi</label>
                                        <input type="number" id="posisi" name="posisi" :value="old('posisi')" required autofocus
                                            class="form-control" placeholder="Posisi ke">
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="add-deal-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                        <button type="submit" id="submit-form-deal" class="btn btn-primary my-primary">Save</button>
                    </div>
            </div>
        </div>
    </div>
    <div class="modal fade" tabindex="-1" id="modal-edit-deal">
        <div class="modal-dialog modal-dialog-centered modal-xl">
            <div class="modal-content">
                <form id="deal-edit-form" name="deal-form" class="form-horizontal">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit deal</h5>
                        <!--begin::Close-->
                        <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" id="modal-add-order-close"
                            data-bs-dismiss="modal" aria-label="Close">
                            <span class="svg-icon svg-icon-2x"><i class="fa fa-times"></i></span>
                        </div>
                        <!--end::Close-->
                    </div>

                    <div class="modal-body mt-n5">
                        <div class="row">
                            <div class="col-lg-12">
                                <input type="hidden" name="id_deal" id="id-deal">
                                <div class="mb-3">
                                    <label for="deal" class="required form-label">deal</label>
                                    <input type="text" id="deal-edit" name="deal_edit" :value="old('deal')" required
                                        autofocus class="form-control" placeholder="Nama deal">
                                    <small class="text-danger"><strong id="deal_error"></strong></small>
                                </div>
                                <div class="mb-3">
                                    <label for="posisi" class="required form-label">posisi</label>
                                    <input type="number" id="posisi-edit" name="posisi_edit" :value="old('posisi')" required
                                        autofocus class="form-control" placeholder="Posisi ke">
                                    <small class="text-danger"><strong id="posisi_error"></strong></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" id="edit-deal-close" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                        <button type="button" id="submit-edit-deal" class="btn btn-primary my-primary">Save
                            changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</x-app-layout>
