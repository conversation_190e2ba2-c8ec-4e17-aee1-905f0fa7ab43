<x-app-layout>
    <x-slot name="header">
        <span><a href="{{ route('quotation.index') }}" class="pe-3">
                <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
            </a><span class="text-white fs-1 fw-bold">
            Quotation Details
        </span></span>
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush me-5" style="min-height: calc(100vh - 300px)">
            <div class="card-body">
                <div class="row mb-8">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label fw-semibold">Quotation Number</label>
                            <p class="fs-12 fw-bold" style="font-size: 16px; font-weight: bold !important">{{ $data->no_quotation ?? 'N/A' }}</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label fw-semibold">Customer</label>
                            <p class="fs-12 fw-bold"  style="font-size: 16px; font-weight: bold !important">{{ $data->customer->nama ?? 'N/A' }}</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label fw-semibold">Status</label>
                            <p class="fs-12 fw-bold">
                                <span class="badge {{ strtoupper($data->status) === 'DRAFT' ? 'bg-info' : (strtoupper($data->status) === 'SUBMITTED' ? 'bg-primary' : '') }}">
                                    {{ strtoupper($data->status) ?? 'N/A' }}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
                <h4 class="fw-bold">
                    Products
                </h4>
                <table class="table align-middle table-row-dashed fs-6 gy-5">
                    <thead>
                        <tr class="bg-light">
                            <th class="fw-semibold p-5">Product Name</th>
                            <th class="fw-semibold p-5">Specifications</th>
                            <th class="fw-semibold p-5 text-center">Quantity (pcs)</th>
                            <th class="fw-semibold p-5 text-end">Unit Price</th>
                            <th class="fw-semibold p-5 text-end">Total Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($data->details ?? [] as $item)
                            <tr>
                                <td class="fw-bold p-5">{{ $item->product_name ?? 'N/A' }}</td>
                                <td class="fw-bold p-5">{{ $item->spesifikasi ?? 'N/A' }}</td>
                                <td class="fw-bold p-5 text-center">{{ $item->quantity ?? 'N/A' }}</td>
                                <td class="fw-bold p-5 text-end">Rp {{ number_format($item->price ?? 0, 0, ',', '.') }}</td>
                                <td class="fw-bold p-5 text-end">Rp {{ number_format(($item->price ?? 0) * ($item->quantity ?? 0), 0, ',', '.') }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                    <tfoot>
                        <tr class="bg-light">
                            <td colspan="5" class="fw-bold text-end px-4">
                                <strong>Total: &nbsp;</strong>
                                <strong>
                                    Rp
                                    @php
                                        $total = 0;
                                        foreach ($data->details ?? [] as $item) {
                                            $total += ($item->price ?? 0) * ($item->quantity ?? 0);
                                        }
                                    @endphp
                                    {{ number_format($total, 0, ',', '.') }}
                                </strong>
                            </td>
                        </tr>
                    </tfoot>
                </table>

                <div class="d-flex align-items-center gap-2 mt-8">
                    <a href="{{ route('quotation.index') }}" class="btn btn-danger mt-4 fw-bold">
                        <i class="fas fa-times"></i>
                        Back
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
