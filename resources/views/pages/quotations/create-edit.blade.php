<x-app-layout>
    <x-slot name="header">
        <span><a href="{{ route('quotation.index') }}" class="pe-3">
                <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
            </a><span class="text-white fs-1">
            {{($data ?? null) ? "Edit" : "Create"}} Quotation
        </span></span>
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush me-5" style="min-height: calc(100vh - 300px)">
            <div class="border-bottom py-5 px-9">
                <h3>
                    Form Quotation
                </h3>
                <small class="text-muted fs-6 fw-normal pt-2">
                    Silahkan isi form berikut untuk {{($data ?? null) ? "mengedit" : "membuat"}} Quotation
                </small>
            </div>
            <div class="card-body">
                <form id="form_quotation" method="POST" action="{{ route('quotation.store') }}">
                    <input type="hidden" name="id" id="id" value="{{ $data->quotation_key ?? '' }}" />
                    @csrf
                    <div class="row mb-8">
                        <div class="col-md-4">
                            @php
                                $no_quotation = \App\Models\Quotation::generateQuotationNumber();
                            @endphp
                            <div class="form-group">
                                <label class="form-label required">No Quotation</label>
                                <input type="text" class="form-control @error('no_quotation') is-invalid @enderror"
                                    name="no_quotation" id="no_quotation" placeholder="No Quotation"
                                    value="{{ $data->no_quotation ?? (null ?? $no_quotation['no_quotation']) }}"
                                    disabled />
                                @error('no_quotation')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label required">Customer</label>
                                <select class="form-select @error('customer_id') is-invalid @enderror"
                                    name="customer_id" id="customer_id" data-control="select2"
                                    data-placeholder="Nama PIC">
                                    <option></option>
                                </select>
                                @error('customer_id')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            @php
                                $status = [
                                    'draft' => 'Draft',
                                    'submitted' => 'Submitted',
                                ];
                            @endphp
                            <div class="form-group">
                                <label class="form-label required">Status</label>
                                <select class="form-select @error('status') is-invalid @enderror" name="status"
                                    id="status" data-control="select2" data-placeholder="Status">
                                    <option value="" disabled>
                                        Pilih Status
                                    </option>
                                    @foreach ($status as $key => $value)
                                        <option value="{{ $key }}"
                                            {{ old('status', $data->status ?? '') == $key ? 'selected' : '' }}>
                                            {{ $value }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('status')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                    </div>
                    <h4>
                        Produk
                    </h4>
                    <div class="listProduct">
                        @php
                            $product_name = [''];
                            $dataDetailProduct = old('product_name', $data->details ?? ($product_name ?? []));
                        @endphp
                        @foreach ($dataDetailProduct as $key => $item)
                            <div class="row itemProduct mt-4">
                                <input type="hidden" name="id_product[]" id="id_product"
                                    value="{{ $item->id ?? '' }}" />
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label required">Nama Produk</label>
                                        <input type="text"
                                            class="form-control @error('product_name.' . $key) is-invalid @enderror"
                                            name="product_name[]" id="product_name" placeholder="Masukkan Nama Produk"
                                            value="{{ old('product_name.' . $key, $item->product_name ?? '') }}" />
                                        @error('product_name.' . $key)
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label required">Spesifikasi</label>
                                        <textarea class="form-control @error('specification.' . $key) is-invalid @enderror" name="specification[]"
                                            id="specification" placeholder="Masukkan Spesifikasi">{{ old('specification.' . $key, $item->spesifikasi ?? '') }}</textarea>
                                        @error('specification.' . $key)
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <div class="form-group">
                                        <label class="form-label required">Qty (pcs)</label>
                                        <input type="number"
                                            class="form-control @error('qty.' . $key) is-invalid @enderror"
                                            onkeyup="calculateTotalPrice(this)" onchange="calculateTotalPrice(this)"
                                            name="qty[]" id="qty" placeholder="Qty"
                                            value="{{ old('qty.' . $key, $item->quantity ?? '') }}" />
                                        @error('qty.' . $key)
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label class="form-label required">Harga Satuan</label>
                                        <input type="text"
                                            class="form-control @error('price.' . $key) is-invalid @enderror number-format"
                                            name="price[]" id="price" placeholder="Harga Satuan"
                                            onkeyup="calculateTotalPrice(this)" onchange="calculateTotalPrice(this)"
                                            value="{{ old('price.' . $key, $item->price ?? '') }}" />
                                        @error('price.' . $key)
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label class="form-label required">Jumlah Harga</label>
                                        <h5 class="totalPrice fw-bold" data-id="{{ $key }}">
                                            Rp
                                            @php
                                                $price = str_replace('.', '', old('price.' . $key) ?? ($item->price ?? 0));
                                                $qty = old('qty.' . $key) ?? ($item->quantity ?? 0);
                                            @endphp
                                            {{ number_format($price * $qty, 0, ',', '.') }}
                                        </h5>
                                    </div>
                                </div>
                                <div class="col-md-1" id="elementTrash">
                                    <button type="button" class="btn btn-danger btn-sm mt-8 removeProduct">
                                        <i class="fa fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        @endforeach

                    </div>
                    <div class="row align-items-center">
                        <div class="col-md-9">
                            <button type="button" class="btn btn-info btn-sm mt-4" id="addProduct">
                                <i class="fas fa-plus"></i>
                                Tambah Produk
                            </button>
                        </div>
                        <div class="col-md-3 d-flex align-items-center gap-4">
                            <h5>Total :</h5>
                            <h3 class="totalAllPrice text-primary">
                                Rp
                                @php
                                    $total = 0;
                                    foreach ($dataDetailProduct as $item) {
                                        $price = str_replace('.', '', old('price.' . $key, $item->price ?? 0));
                                        $qty = old('qty.' . $key, $item->quantity ?? 0);
                                        $total += $price * $qty;
                                    }
                                @endphp
                                {{ number_format($total, 0, ',', '.') }}
                            </h3>
                        </div>
                    </div>

                    <div class="d-flex align-items-center justify-content-between gap-2 mt-12">
                        <a href="{{ route('quotation.index') }}" class="btn btn-danger ">
                            <i class="fas fa-times"></i>
                            Back
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i>
                            Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <x-slot name="script">
        <script>
            $(function() {
                $.ajaxSetup({
                    headers: {
                        "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                    },
                });

                const addProduct = $('#addProduct');
                const listProduct = $('.listProduct');

                addProduct.on('click', function() {
                    console.log('addProduct');
                    const clone = listProduct.children().last().clone();

                    clone.find('input').val('');
                    clone.find('textarea').val('');
                    clone.find('.removeProduct').remove(); // Remove existing remove button

                    clone.find('input').attr('name', function() {
                        return $(this).attr('name').replace(/\[\d+\]/,
                            `[${listProduct.children().length}]`);
                    });
                    clone.find('input').attr('id', function() {
                        return $(this).attr('id').replace(/\[\d+\]/,
                            `[${listProduct.children().length}]`);
                    });
                    clone.find('textarea').attr('name', function() {
                        return $(this).attr('name').replace(/\[\d+\]/,
                            `[${listProduct.children().length}]`);
                    });
                    clone.find('textarea').attr('id', function() {
                        return $(this).attr('id').replace(/\[\d+\]/,
                            `[${listProduct.children().length}]`);
                    });

                    clone.find('.totalPrice').text('0');
                    clone.find('.totalPrice').attr('data-id', listProduct.children().length);

                    clone.find('#elementTrash').html(`
                        <button type="button" class="btn btn-danger btn-sm mt-8 removeProduct">
                            <i class="fa fa-trash-alt"></i>
                        </button>
                    `);

                    listProduct.append(clone);

                    listProduct.find('.removeProduct').off('click').on('click', function() {
                        if (listProduct.children().length > 1) {
                            $(this).closest('.row').remove();
                        }
                    });
                });

                $('.removeProduct').on('click', function() {
                    console.log('removeProduct');
                    if (listProduct.children().length > 0) {
                        $(this).closest('.row').remove();
                    }
                });

                const format = (item) => {
                    if (!item.id) {
                        return item.text;
                    }
                    var img =
                        "<span class='menu-icon me-3'><img src='https://ui-avatars.com/api/?name=" +
                        encodeURIComponent(item.text) +
                        "&background=random&size=32' alt='Avatar' class='rounded-circle me-2'/>"
                    var span = $("<span>", {
                        text: " " + item.text
                    });
                    span.prepend(img)
                    return span
                }

                $('#status').select2({
                    allowClear: true,
                    debug: true,
                })

                $('#customer_id').select2({
                        allowClear: true,
                        debug: true,
                        ajax: {
                            url: "{{ route('quotation.customers') }}",
                            dataType: 'json',
                            delay: 250,
                            data: function(params) {
                                return {
                                    q: params.term || '',
                                    page: params.page || 1
                                };
                            },
                            processResults: function(data, params) {
                                params.page = params.page || 1;

                                var resultsData = $.map(data.data, function(item) {
                                    return {
                                        id: item.id,
                                        text: item.nama + (item.nama_instansi ? ' - ' + item.nama_instansi : '') + (item.no_hp ? ' - ' + item.no_hp : '')
                                    };
                                });

                                return {
                                    results: resultsData,
                                    pagination: {
                                        more: data.current_page < data.last_page
                                    }
                                };
                            },
                            cache: true
                        },
                        templateResult: function(item) {
                            return format(item);
                        }
                    })
                    .on('select2:opening', function(e) {
                        $(this).data('select2').$dropdown.find(':input.select2-search__field').attr('placeholder',
                            'Cari Nama Customer');
                    });

                var selectedCustomerId = "{{ old('customer_id', $data->customer_id ?? '') }}";
                if (selectedCustomerId) {
                    @php
                        $customer = \App\Models\Customer::find(old('customer_id', $data->customer_id ?? ''));
                    @endphp
                    var selectedCustomerName = "{{ $customer->nama ?? '' }}";
                    var option = new Option(selectedCustomerName, selectedCustomerId, true, true);
                    $('#customer_id').append(option).trigger('change');
                }

                function formatNumberInput(element) {
                    var value = $(element).val();
                    value = value.replace(/[^0-9]/g, '');
                    value = new Intl.NumberFormat('id-ID', {
                        minimumFractionDigits: 0
                    }).format(value);
                    $(element).val(value);
                }

                $(document).on('keyup', '.number-format', function() {
                    formatNumberInput(this);
                }).find('.number-format').each(function() {
                    formatNumberInput(this);
                });

            });

            function calculateTotalPrice(element) {
                const $row = $(element).closest('.row');
                const qty = parseFloat($row.find('#qty').val()) || 0;
                const price = parseFloat($row.find('#price').val().replace(/\./g, '').replace(/,/g, '.')) || 0;

                if (isNaN(qty) || isNaN(price)) {
                    return;
                }

                const totalPrice = qty * price;
                const formattedTotalPrice = new Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(totalPrice);

                $row.find('.totalPrice').text(formattedTotalPrice);
                calculateTotalAllPrice();
            }


            function calculateTotalAllPrice() {
                let totalAllPrice = 0;
                $('.listProduct .row').each(function() {
                    const qty = parseFloat($(this).find('#qty').val()) || 0;
                    const price = parseFloat($(this).find('#price').val().replace(/\./g, '').replace(/,/g, '.')) || 0;
                    totalAllPrice += qty * price;
                });

                const formattedTotalAllPrice = new Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(totalAllPrice);

                $('.totalAllPrice').text(formattedTotalAllPrice);
            }

            $('#form_quotation').on('submit', function(e) {
                let hasEmptyFields = false;
                
                // Check required inputs
                $(this).find('input[required], select[required], textarea[required]').each(function() {
                    if (!$(this).val()) {
                        hasEmptyFields = true;
                        return false; // Break the loop
                    }
                });

                // Check product details
                $('.itemProduct').each(function() {
                    const productName = $(this).find('#product_name').val();
                    const specification = $(this).find('#specification').val();
                    const qty = $(this).find('#qty').val();
                    const price = $(this).find('#price').val();

                    if (!productName || !specification || !qty || !price) {
                        hasEmptyFields = true;
                        return false; // Break the loop
                    }
                });

                if (hasEmptyFields) {
                    e.preventDefault();
                    Swal.fire({
                        title: 'Warning!',
                        text: 'Tolong lengkapi semua kolom yang wajib diisi',
                        icon: 'warning',
                        confirmButtonText: 'OK'
                    });
                }
            });
        </script>
    </x-slot>
</x-app-layout>
