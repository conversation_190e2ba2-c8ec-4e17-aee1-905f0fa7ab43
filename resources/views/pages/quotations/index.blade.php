<x-app-layout>
    <x-slot name="header">
        <span class="text-white fs-1">
            <a href="{{ route('sales.index') }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
                </a> Penawaran
        </span>
        <small class="text-white fs-6 fw-normal mt-3">Daftar Penawaran</small>
    </x-slot>
    <x-slot name="script">
        <script>
            $.ajaxSetup({
                headers: {
                    "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                },
            });

            let filters = {};
            const table = $("#datatable_tipe_produk").DataTable({
                lengthMenu: [
                    [10, 25, 50, 100, 500, -1],
                    [10, 25, 50, 100, 500, "All"],
                ],
                searching: false,
                responsive: false,
                lengthChange: true,
                autoWidth: false,
                order: [],
                pagingType: "full_numbers",
                dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Cari...",
                    paginate: {
                        Search: '<i class="icon-search"></i>',
                        first: "<i class='fas fa-angle-double-left'></i>",
                        previous: "<i class='fas fa-angle-left'></i>",
                        next: "<i class='fas fa-angle-right'></i>",
                        last: "<i class='fas fa-angle-double-right'></i>",
                    },
                },
                oLanguage: {
                    sSearch: "",
                },
                processing: true,
                serverSide: true,
                ajax: {
                    url: `/quotation/dataTables`,
                    method: "POST",
                    data: function(d) {
                        return {
                            ...d,
                            ...filters,
                        };
                    },
                },
                columns: [{
                        name: "created_at",
                        data: "DT_RowIndex",
                    },
                    {
                        name: "no_quotation",
                        data: "no_quotation",
                        orderable: false,
                    },
                    {
                        name: "customer.nama",
                        data: "customer.nama",
                        orderable: false,
                    },
                    {
                        name: "total_price",
                        data: "total_price",
                        orderable: false,
                    },
                    {
                        name: "status_badge",
                        data: "status_badge",
                        orderable: false,
                    },
                    {
                        name: "created_at",
                        data: "created_at",
                        orderable: false,
                    },
                    {
                        name: "created_by",
                        data: "created_by",
                        orderable: false,
                    },
                    {
                        name: "action",
                        data: "action",
                        orderable: false,
                    },

                ],
            });

            let debounceTimer;

            function handleSearch(e) {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    filters.keyword = e.target.value;
                    table.draw();
                }, 500);
            }

            $(document).on('click', '.deleteData', async function() {
                const id = $(this).data("id");
                const dataInput = $(this).data("input");
                const nama = dataInput.no_quotation;
                const urlTarget = `/quotation/${id}`
                Swal.fire({
                    title: "Apakah yakin?",
                    text: `Data ${nama} akan dihapus`,
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#6492b8da",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Yakin, hapus",
                    cancelButtonText: "Batal",
                }).then((result) => {
                    console.log(result, 'result');
                    if (result.isConfirmed) {
                        $.ajax({
                            url: urlTarget,
                            method: "post",
                            data: [{
                                name: "_method",
                                value: "DELETE"
                            }],
                            success: function(res) {
                                table.draw();
                                Swal.fire(`Berhasil dihapus`, res.message, "success");
                            },
                            error: function(res) {
                                console.log(res);
                                Swal.fire(`Gagal`, `${res.responseJSON.message}`, "error");
                            },
                        });
                    }
                });
            });

            function generateInvoice(sko) {
                const urlTarget = `/quotation/generate-invoice/${sko}`
                Swal.fire({
                    title: "Apakah yakin?",
                    text: `ingin generate invoice`,
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#6492b8da",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Yakin, Buat Invoice",
                    cancelButtonText: "Batal",
                }).then((result) => {
                    console.log(result, 'result');
                    if (result.isConfirmed) {
                        $.ajax({
                            url: urlTarget,
                            type: "POST",
                            success: function(res) {
                                table.draw();
                                const invoiceCode = res.data?.invoice_code ?? '';
                                Swal.fire(`Invoice Berhasil dibuat`, `Invoice Code: ${invoiceCode}`, "success");
                            },
                            error: function(res) {
                                console.log(res);
                                Swal.fire(`Gagal`, `${res.responseJSON.message}`, "error");
                            },
                        });
                    }
                });
            }
            // Select filter options
            const resetButton = document.querySelector('[data-kt-docs-table-filter="reset"]')
            const filterButton = document.querySelector('[data-kt-docs-table-filter="filter-tanggalKirim"]')
            resetButton.addEventListener('click', function() {
                $('#filter_start_date').val('');
                $('#filter_end_date').val('');
                filters = {
                    keyword: $('#tipe_produk-search-box').val(),
                };
                table.draw();
            })
            filterButton.addEventListener('click', function() {
                filters.start_date = $('#filter_start_date').val();
                filters.end_date = $('#filter_end_date').val();
                table.draw();
            })
        </script>
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2"
                                        rx="1" transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                    <path
                                        d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                        fill="black"></path>
                                </svg>
                            </span>
                            <input type="text" oninput="handleSearch(event)" id="tipe_produk-search-box"
                                data-kt-docs-table-filter="search" class="form-control form-control-solid w-450px ps-15"
                                placeholder="Search ...">
                        </div>
                        <div class="d-flex justify-content-end" data-kt-docs-table-toolbar="base">
                            <button type="button" class="btn btn-light-primary me-3" data-kt-menu-trigger="click"
                                data-kt-menu-placement="bottom-end">
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none">
                                        <path
                                            d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                                            fill="black"></path>
                                    </svg>
                                </span>
                                Filter
                            </button>
                            <div class="menu menu-sub menu-sub-dropdown" data-kt-menu="true"
                                id="kt-toolbar-filter">
                                <div class="px-7 py-5">
                                    <div class="">
                                        <label class="form-label fs-5 fw-bold mb-3">Tanggal Penawaran:</label>
                                        <div class="form-group">
                                            <div class="d-flex align-items-center gap-4">
                                                <div class="input-group">
                                                    <span class="input-group-text">Start Date</span>
                                                    <input type="date" class="form-control form-control-solid"
                                                        name="filter_start_date" id="filter_start_date" placeholder="Tanggal Mulai">
                                                </div>
                                                <div class="input-group">
                                                    <span class="input-group-text">End Date</span>
                                                    <input type="date" class="form-control form-control-solid"
                                                        name="filter_end_date" id="filter_end_date" placeholder="Tanggal Akhir">
                                                </div>
                                            </div>
                                            <div class="d-flex justify-content-end mt-3">
                                                <button type="reset" class="btn btn-light btn-active-light-primary me-2"
                                                    data-kt-menu-dismiss="true" data-kt-docs-table-filter="reset">Reset</button>
                                                <button type="submit" class="btn btn-primary" data-kt-menu-dismiss="true"
                                                    data-kt-docs-table-filter="filter-tanggalKirim">Apply</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <a href="{{ route('quotation.create') }}" class="btn btn-success"
                                data-bs-target="#modal-add-tipe_produk" title="Add tipe_produk">
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2"
                                            rx="1" transform="rotate(-90 11.364 20.364)" fill="black"></rect>
                                        <rect x="4.36396" y="11.364" width="16" height="2" rx="1"
                                            fill="black"></rect>
                                    </svg>
                                </span>
                                Add Quotation
                            </a>
                        </div>
                    </div>
                    <table id="datatable_tipe_produk"
                        class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>No Quotation</th>
                                <th>Customer</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Creation Date</th>
                                <th>Created By</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody> </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
