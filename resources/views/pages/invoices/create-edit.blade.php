<x-app-layout>
    <x-slot name="header">
        <span><a href="{{ route('invoice.index') }}" class="pe-3">
                <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
            </a> <span class="text-white fs-1">
            {{ $data ?? null ? 'Edit' : 'Create' }} Invoice
        </span></span>
    </x-slot>

    <div class="container-xxl" id="kt_content_container">
        <div class="card card-flush me-5" style="min-height: calc(100vh - 300px)">
            <div class="border-bottom py-5 px-9">
                <h3>
                    Form Invoice
                </h3>
                <small class="text-muted fs-6 fw-normal pt-2">
                    Silahkan isi form berikut untuk {{ $data ?? null ? 'mengedit' : 'membuat' }} Invoice
                </small>
            </div>
            <div class="card-body">
                <form id="form_invoice" method="POST" enctype="multipart/form-data"
                    action="{{ route('invoice.store', ['type' => request('type')]) }}">
                    <input type="hidden" name="id" id="id" value="{{ $data->invoice_key ?? '' }}" />
                    @csrf
                    <div class="row mb-8 g-5">
                        <div class="col-md-4">
                            @php
                                $no_invoice = \App\Models\Invoice::generateInvoiceNumber(request('type'));
                            @endphp
                            <div class="form-group">
                                <label class="form-label required">No Invoice</label>
                                <input type="text" class="form-control @error('no_invoice') is-invalid @enderror"
                                    name="no_invoice" id="no_invoice" placeholder="No Invoice"
                                    value="{{ $data->no_invoice ?? ($no_invoice['no_invoice'] ?? null) }}" disabled />
                                @error('no_invoice')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label required">Customer</label>
                                <select class="form-select @error('customer_id') is-invalid @enderror {{isset($data->customer_id) && $data->status != 'submitted' ? 'bg-secondary' : ''}}" {{isset($data->customer_id) ? 'readonly' : ''}}
                                    name="customer_id" id="customer_id" data-control="select2"
                                    data-placeholder="Nama PIC">
                                    <option></option>
                                </select>
                                @error('customer_id')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label required">No. PO</label>
                                <input type="text" class="form-control @error('no_po') is-invalid @enderror"
                                    name="no_po" id="no_po" placeholder="No. PO"
                                    value="{{ old('no_po', $data->no_po ?? $data_order->no_po ?? '') }}" />
                                @error('no_po')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        @if (request('type') === 'sales')
                            <div class="col-md-4">
                                <div class="form-group mb-2">
                                    <label class="form-label required">Lampiran</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fa fa-link"></i></span>
                                        <input type="text" class="form-control @error('lampiran') is-invalid @enderror"
                                            name="lampiran" id="lampiran" placeholder="Lampiran"
                                            value="{{ old('lampiran', $data->lampiran ?? $data_order->link_dokumen_order ?? '') }}" />
                                    </div>
                                    @error('lampiran')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                                {{-- @if (!empty($data->file_lampiran))
                                    <a href="{{ asset($data->file_lampiran) }}" target="_blank" class="text-primary fw-bold">
                                        <i class="fas fa-file-alt text-dark pe-1"></i> Lihat Lampiran
                                    </a>
                                @endif --}}
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label required">Tanggal Jatuh Tempo</label>
                                    <input type="date"
                                        class="form-control @error('tanggal_jatuh_tempo') is-invalid @enderror"
                                        name="tanggal_jatuh_tempo" id="tanggal_jatuh_tempo"
                                        placeholder="Tanggal Jatuh Tempo"
                                        value="{{ old('tanggal_jatuh_tempo', $data->tanggal_jatuh_tempo ?? '') }}" />
                                    @error('tanggal_jatuh_tempo')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label required">Tanggal Jatuh Tempo Tambahan</label>
                                    <input type="date"
                                        class="form-control @error('tanggal_jatuh_tempo_tambahan') is-invalid @enderror"
                                        name="tanggal_jatuh_tempo_tambahan" id="tanggal_jatuh_tempo_tambahan"
                                        placeholder="Tanggal Jatuh Tempo"
                                        value="{{ old('tanggal_jatuh_tempo_tambahan', $data->tanggal_jatuh_tempo_tambahan ?? '') }}" />
                                    @error('tanggal_jatuh_tempo_tambahan')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            </div>
                        @endif
                        <div class="col-md-4">
                            @php
                                $status = [
                                    'draft' => 'Draft',
                                    'submitted' => 'Submitted',
                                ];
                            @endphp
                            <div class="form-group">
                                <label class="form-label required">Status</label>
                                <select class="form-select @error('status') is-invalid @enderror" name="status"
                                    id="status" data-control="select2" data-placeholder="Status">
                                    <option value="" disabled>
                                        Pilih Status
                                    </option>
                                    @foreach ($status as $key => $value)
                                        <option value="{{ $key }}"
                                            {{ old('status', $data->status ?? '') == $key ? 'selected' : '' }}>
                                            {{ $value }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('status')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div> 
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label required">Catatan</label>
                                <textarea name="note" class="form-control">{{ old('note', $data->note ?? '') }}</textarea>
                            </div>
                        </div>
                        <div class="col-md-4">
                            @php
                                $status_paid = [
                                    'unpaid' => 'Unpaid',
                                    'paid' => 'Paid',
                                ];
                            @endphp
                            {{-- <div class="form-group">
                                <label class="form-label required">Status Paid</label>
                                <select class="form-select @error('status_paid') is-invalid @enderror"
                                    name="status_paid" id="status_paid" data-control="select2"
                                    data-placeholder="status_paid">
                                    <option value="" disabled>
                                        Pilih Status Paid
                                    </option>
                                    @foreach ($status_paid as $key => $value)
                                        <option value="{{ $key }}"
                                            {{ old('status_paid', $data->status_paid ?? '') == $key ? 'selected' : '' }}>
                                            {{ $value }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('status_paid')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div> --}}
                        </div>
                        {{-- @if (request('type') === 'sales') --}}
                            <div class="col-md-4 tanggalPelunasan">
                                <div class="form-group">
                                    <label class="form-label required">Tanggal Pelunasan</label>
                                    <input type="date"
                                        class="form-control @error('tanggal_pelunasan') is-invalid @enderror"
                                        name="tanggal_pelunasan" id="tanggal_pelunasan"
                                        placeholder="Tanggal Jatuh Tempo"
                                        value="{{ old('tanggal_pelunasan', $data->tanggal_pelunasan ?? '') }}" />
                                    @error('tanggal_pelunasan')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            </div>
                        {{-- @endif --}}
                    </div>
                    <h4>
                        Produk
                    </h4>
                    <div class="listProduct">
                        @php
                            $nama_produk = [''];
                            $dataDetailProduct = old('nama_produk', $data->details ?? ($nama_produk ?? []));
                        @endphp
                        @foreach ($dataDetailProduct as $key => $item)
                            <div class="row itemProduct mt-4">
                                <input type="hidden" name="id_product[]" id="id_product"
                                    value="{{ $item->id ?? '' }}" />
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label required">Kode Order</label>
                                        @if(isset($data) && $data->status == 'submitted' && isset($item->kode_order) || isset($data_order->sko))
                                            <input type="text" name="kode_order[]" class="form-control bg-secondary" readonly value="{{ $item->kode_order ?? $data_order->sko ?? '' }}" />
                                        @elseif (isset($item->kode_order) || isset($data_order->sko))
                                            <select name="kode_order[]" class="form-select kode-order" data-control="select2">
                                                @php
                                                    $kodeOrder = $item->kode_order ?? $data_order->sko ?? '';
                                                @endphp
                                                @if($kodeOrder)
                                                    <option value="{{ $kodeOrder }}" selected>{{ $kodeOrder }}</option>
                                                @endif
                                            </select>
                                        @else
                                            <select name="kode_order[]" class="form-select kode-order" data-control="select2"></select>
                                        @endif
                                        @error('kode_order.' . $key)
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label required">Nama Produk</label>
                                        <input type="text"
                                            class="form-control @error('nama_produk.' . $key) is-invalid @enderror"
                                            name="nama_produk[]" id="nama_produk" placeholder="Masukkan Nama Produk"
                                            value="{{ old('nama_produk.' . $key, $item->product_name ?? $data_order->tb_produksi->nama_produk ?? '') }}" />
                                        @error('nama_produk.' . $key)
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label class="form-label required">Spesifikasi</label>
                                        <textarea class="form-control @error('specification.' . $key) is-invalid @enderror" name="specification[]"
                                            id="specification" placeholder="Masukkan Spesifikasi">{{ old('specification.' . $key, $item->spesifikasi ?? $data_order->tb_produksi->notes ?? '') }}</textarea>
                                        @error('specification.' . $key)
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <div class="form-group">
                                        <label class="form-label required">Qty (pcs)</label>
                                        <input type="number"
                                            class="form-control @error('qty.' . $key) is-invalid @enderror"
                                            onkeyup="calculateTotalPrice(this)" onchange="calculateTotalPrice(this)"
                                            name="qty[]" id="qty" placeholder="Qty"
                                            value="{{ old('qty.' . $key, $item->quantity ?? $data_order->tb_produksi->jumlah_produk ??'') }}" />
                                        @error('qty.' . $key)
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label class="form-label required">Harga Satuan</label>
                                        <input type="text"
                                            class="form-control @error('price.' . $key) is-invalid @enderror number-format"
                                            name="price[]" id="price" placeholder="Harga Satuan"
                                            onkeyup="calculateTotalPrice(this)" onchange="calculateTotalPrice(this)"
                                            value="{{ old('price.' . $key, $item->harga_satuan ?? $data_order->tb_produksi->harga_produk ?? '') }}" />
                                        @error('price.' . $key)
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <div class="form-group">
                                        <label class="form-label required">Jumlah Harga</label>
                                        <h5 class="totalPrice fw-bold" data-id="{{ $key }}">
                                            Rp
                                            @php
                                                $price = str_replace(
                                                    '.',
                                                    '',
                                                    old('price.' . $key, $item->harga_satuan ?? 0),
                                                );
                                                $qty = old('qty.' . $key, $item->quantity ?? 0);
                                            @endphp
                                            {{ number_format($price * $qty, 0, ',', '.') }}
                                        </h5>
                                    </div>
                                </div>
                                <div class="col-md-1 d-none" id="elementTrash">
                                    <button type="button" class="btn btn-danger btn-sm mt-8 removeProduct">
                                        <i class="fa fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                        @endforeach

                    </div>
                    <button type="button" class="btn btn-info btn-sm mt-4 d-none" id="addProduct">
                        <i class="fas fa-plus"></i>
                        Tambah Produk
                    </button>

                    <div class="row my-8">
                        <div class="col-md-4 d-flex align-items-start">
                            <div class="form-group">
                                <label class="form-label">PPN</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_ppn" id="is_ppn"
                                        onkeyup="calculateTotalAllPrice(this)" onchange="calculateTotalAllPrice(this)"
                                        value="1" {{ old('is_ppn', $data->is_ppn ?? false) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_ppn" style="width: 200px">
                                        Apakah termasuk PPN?
                                    </label>
                                </div>
                            </div>
                            <div class="form-group ppnInput">
                                <label class="form-label">Persentase PPN</label>
                                <div class="input-group mb-3">
                                    <input type="number" class="form-control @error('ppn') is-invalid @enderror"
                                        onkeyup="calculateTotalAllPrice(this)" onchange="calculateTotalAllPrice(this)"
                                        placeholder="Persentase PPN"
                                        value="{{ old('ppn', $data->persentase_ppn ?? '') }}" name="ppn"
                                        id="persentase_ppn" />
                                    <span class="input-group-text" id="basic-addon2">%</span>
                                </div>
                                @error('ppn')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        @if (request('type') === 'sales')
                            <div class="col-md-4 biayaPengirimanInput">
                                <div class="form-group">
                                    <label class="form-label">Biaya Pengiriman</label>
                                    <input type="text"
                                        class="form-control number-format @error('biaya_pengiriman') is-invalid @enderror"
                                        name="biaya_pengiriman" id="biaya_pengiriman" placeholder="0"
                                        onkeyup="calculateTotalAllPrice(this)" onchange="calculateTotalAllPrice(this)"
                                        value="{{ old('biaya_pengiriman', $data->biaya_pengiriman ?? '') }}" />
                                    @error('biaya_pengiriman')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            </div>
                        @endif
                        @if (request('type') === 'sales')
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">Potongan/Deposit</label>
                                    <input type="text"
                                        class="form-control number-format @error('potongan') is-invalid @enderror"
                                        name="potongan" id="potongan" placeholder="0"
                                        onkeyup="calculateTotalAllPrice(this)" onchange="calculateTotalAllPrice(this)"
                                        value="{{ old('potongan', $data->diskon ?? '') }}" />
                                    @error('potongan')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            </div>
                        @endif
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Down Payment
                                    {{ request('type') === 'sales' ? 'Terbayar' : '' }} </label>
                                <input type="text"
                                    class="form-control number-format @error('down_payment') is-invalid @enderror"
                                    onkeyup="calculateTotalAllPrice(this)" onchange="calculateTotalAllPrice(this)"
                                    name="down_payment" id="down_payment" placeholder="0"
                                    value="{{ old('down_payment', $data->dp_terbayar ?? '') }}" />
                                @error('down_payment')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Total Pembayaran
                                    {{ request('type') === 'sales' ? 'Terbayar' : '' }} </label>
                                <input type="text"
                                    class="form-control number-format @error('total_pembayaran') is-invalid @enderror"
                                    onkeyup="calculateTotalAllPrice(this)" onchange="calculateTotalAllPrice(this)"
                                    name="total_pembayaran" id="total_pembayaran" placeholder="0"
                                    value="{{ old('total_pembayaran', $data->total_pembayaran ?? '') }}" />
                                @error('total_pembayaran')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                        </div>
                    </div>
                    <div class="row p-4 border-dashed border-warning rounded">
                         <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Tanggal Pembayaran DP</label>
                                <input type="date"
                                    class="form-control @error('tanggal_dp') is-invalid @enderror"
                                    name="tanggal_dp" id="tanggal_dp"
                                    placeholder="Tanggal Jatuh Tempo"
                                    value="{{ old('tanggal_dp', $data->tanggal_dp ?? '') }}" />
                                @error('tanggal_dp')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Tanggal Pelunasan</label>
                                <input type="date"
                                    class="form-control @error('tanggal_pelunasan') is-invalid @enderror"
                                    name="tanggal_pelunasan" id="tanggal_pelunasan"
                                    placeholder="Tanggal Jatuh Tempo"
                                    value="{{ old('tanggal_pelunasan', $data->tanggal_pelunasan ?? '') }}" />
                                @error('tanggal_pelunasan')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end">
                        <table class="table table-borderless" style="width: auto;">
                            <tbody>
                                <tr>
                                    <td>
                                        <h5>Sub Total :</h5>
                                    </td>
                                    <td>
                                        <h3 class="subTotal text-primary">
                                            Rp 0
                                        </h3>
                                    </td>
                                </tr>
                                <tr class="ppnResult">
                                    <td>
                                        <h5>PPN :</h5>
                                    </td>
                                    <td>
                                        <h3 class="totalPPN text-primary">
                                            Rp 0
                                        </h3>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h5>Total Tagihan:</h5>
                                    </td>
                                    <td>
                                        <h3 class="totalAllPrice text-primary">
                                            Rp
                                            @php
                                                $total = 0;
                                                foreach ($dataDetailProduct as $item) {
                                                    $price = str_replace(
                                                        '.',
                                                        '',
                                                        old('price.' . $key, $item->price ?? 0),
                                                    );
                                                    $qty = old('qty.' . $key, $item->quantity ?? 0);
                                                    $total += $price * $qty;
                                                }
                                            @endphp
                                            {{ number_format($total, 0, ',', '.') }}
                                        </h3>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h5>Total Pembayaran :</h5>
                                    </td>
                                    <td>
                                        <h3 class="totalPembayaran text-primary">
                                            Rp 
                                            @php
                                                $totalPembayaran = 0;
                                                if (isset($data->total_pembayaran)) {
                                                    $totalPembayaran = $data->total_pembayaran + $data->dp_terbayar;
                                                }
                                            @endphp
                                            {{ number_format($totalPembayaran, 0, ',', '.') }}
                                        </h3>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <h5>Sisa Pelunasan :</h5>
                                    </td>
                                    <td>
                                        <h3 class="sisaPelunasan text-primary">
                                            Rp 
                                            @php
                                                $sisaPelunasan = isset($data->sisa_pelunasan) ? $data->sisa_pelunasan : 0;
                                            @endphp
                                            {{ number_format($sisaPelunasan, 0, ',', '.') }}
                                        </h3>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-between align-items-center gap-2 mt-12">
                        <a href="{{ route('invoice.index') }}" class="btn btn-danger">
                            <i class="fas fa-times"></i>
                            Back
                        </a>
                        <button type="submit" class="btn btn-primary" id="btn-save" disabled>
                            <i class="fas fa-save"></i>
                            Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <x-slot name="script">
        <script>
            $(function() {
                $('.kode-order').each(function() {
                    initKodeOrderSelect2($(this), $('#customer_id').val());
                });

                function initKodeOrderSelect2($select, customerId = null) {
                    $select.select2({
                        allowClear: true,
                        placeholder: "Pilih Kode order",
                        debug: true,
                        dropdownParent: $select.closest('.listProduct'),
                        ajax: {
                            url: "{{ route('crm.getOrder') }}",
                            data: function(params) {
                                return {
                                    customer_id: customerId || $('#customer_id').val(),
                                    q: params.term || '',
                                    is_invoice: true
                                };
                            },
                            processResults: function(data) {
                                var resultsData = [];
                                $.each(data, function(index, item) {
                                    resultsData.push({
                                        id: item.id,
                                        text: item.name
                                    });
                                });
                                return { results: resultsData };
                            }
                        }
                    });
                }
                
                $.ajaxSetup({
                    headers: {
                        "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                    },
                });

                const addProduct = $('#addProduct');
                const listProduct = $('.listProduct');

                addProduct.on('click', function() {
                    console.log('addProduct');
                    const clone = listProduct.children().last().clone();

                    clone.find('input').val('');
                    clone.find('textarea').val('');
                    clone.find('.removeProduct').remove(); // Remove existing remove button

                    clone.find('input').attr('name', function() {
                        return $(this).attr('name').replace(/\[\d+\]/,
                            `[${listProduct.children().length}]`);
                    });
                    clone.find('input').attr('id', function() {
                        return $(this).attr('id').replace(/\[\d+\]/,
                            `[${listProduct.children().length}]`);
                    });
                    clone.find('textarea').attr('name', function() {
                        return $(this).attr('name').replace(/\[\d+\]/,
                            `[${listProduct.children().length}]`);
                    });
                    clone.find('textarea').attr('id', function() {
                        return $(this).attr('id').replace(/\[\d+\]/,
                            `[${listProduct.children().length}]`);
                    });

                    clone.find('.totalPrice').text('0');
                    clone.find('.totalPrice').attr('data-id', listProduct.children().length);

                    clone.find('#elementTrash').html(`
                        <button type="button" class="btn btn-danger btn-sm mt-8 removeProduct">
                            <i class="fa fa-trash-alt"></i>
                        </button>
                    `);

                    listProduct.append(clone);

                    listProduct.find('.removeProduct').off('click').on('click', function() {
                        if (listProduct.children().length > 1) {
                            $(this).closest('.row').remove();
                        }
                    });
                });

                $('.removeProduct').on('click', function() {
                    console.log('removeProduct');
                    if (listProduct.children().length > 0) {
                        $(this).closest('.row').remove();
                    }
                });

                const format = (item) => {
                    if (!item.id) {
                        return item.text;
                    }
                    var img =
                        "<span class='menu-icon me-3'><img src='https://ui-avatars.com/api/?name=" +
                        encodeURIComponent(item.text) +
                        "&background=random&size=32' alt='Avatar' class='rounded-circle me-2'/>"
                    var span = $("<span>", {
                        text: " " + item.text
                    });
                    span.prepend(img)
                    return span
                }

                $('#status').select2({
                    allowClear: true,
                    debug: true,
                })

                function showStatusPaid() {
                    const statusPaid = $('#status_paid').val();
                    $('.tanggalPelunasan').find('input').removeClass('required');
                    $('.tanggalPelunasan').find('input').val('');
                    if (statusPaid === 'paid') {
                        $('.tanggalPelunasan').find('input').addClass('required');
                    }
                    if (statusPaid === 'paid') {
                        $('.tanggalPelunasan').show();
                    } else {
                        $('.tanggalPelunasan').hide();
                    }
                }
                showStatusPaid();

                $('#status_paid').select2({
                    allowClear: true,
                    debug: true,
                }).on('select2:select', function(e) {
                    showStatusPaid();
                });

                $('#customer_id').select2({
                        allowClear: true,
                        debug: true,
                        ajax: {
                            url: "{{ route('invoice.customers') }}",
                            dataType: 'json',
                            delay: 250,
                            data: function(params) {
                                return {
                                    q: params.term || '',
                                    page: params.page || 1
                                };
                            },
                            processResults: function(data, params) {
                                params.page = params.page || 1;

                                var resultsData = $.map(data.data, function(item) {
                                    return {
                                        id: item.id,
                                        text: item.nama + (item.nama_instansi ? ' - ' + item.nama_instansi : '') + (item.no_hp ? ' - ' + item.no_hp : '')
                                    };
                                });

                                return {
                                    results: resultsData,
                                    pagination: {
                                        more: data.current_page < data.last_page
                                    }
                                };
                            },
                            cache: true
                        },
                        templateResult: function(item) {
                            return format(item);
                        }
                    })
                    .on('select2:opening', function(e) {
                        $(this).data('select2').$dropdown.find(':input.select2-search__field').attr('placeholder',
                            'Cari Nama Customer');
                    }).on('change', function(e){
                        e.preventDefault();
                        var customer_id = this.value;
                        $('.kode-order').each(function() {
                            initKodeOrderSelect2($(this), customer_id);
                        });
                    });
                    
                var selectedCustomerId = "{{ old('customer_id', $data->customer_id ?? $data_order->id_customer ?? '') }}";
                if (selectedCustomerId) {
                    @php
                        $customer = \App\Models\Customer::find(old('customer_id', $data->customer_id ?? $data_order->id_customer ?? ''));
                    @endphp
                    var selectedCustomerName = "{{ $customer->nama ?? '' }}";
                    var option = new Option(selectedCustomerName, selectedCustomerId, true, true);
                    $('#customer_id').append(option).trigger('change');
                }

                function formatNumberInput(element) {
                    var value = $(element).val();
                    value = value?.replace(/[^0-9]/g, '');
                    value = new Intl.NumberFormat('id-ID', {
                        minimumFractionDigits: 0
                    }).format(value);
                    $(element).val(value);
                }

                $(document).on('keyup', '.number-format', function() {
                    formatNumberInput(this);
                }).find('.number-format').each(function() {
                    formatNumberInput(this);
                });


                $("#is_ppn").on('change', function() {
                    showInputPPN()
                });

                function showInputPPN() {
                    const ppnInput = $('.ppnInput');
                    const biayaPengirimanInput = $('.biayaPengirimanInput');
                    const ppnResult = $('.ppnResult');

                    if ($('#is_ppn').is(':checked')) {
                        ppnInput.show();
                        ppnResult.show()
                    } else {
                        ppnInput.hide();
                        ppnResult.hide()
                    }
                }

                showInputPPN()

            });

            function calculateTotalPrice(element) {
                const $row = $(element).closest('.row');
                const qty = parseFloat($row.find('#qty').val()) || 0;
                const price = parseFloat($row.find('#price').val()?.replace(/\./g, '').replace(/,/g, '.')) || 0;

                if (isNaN(qty) || isNaN(price)) {
                    return;
                }

                const totalPrice = qty * price;
                const formattedTotalPrice = new Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(totalPrice);

                $row.find('.totalPrice').text(formattedTotalPrice);
                calculateTotalAllPrice();
            }


            calculateTotalAllPrice();

            function calculateTotalAllPrice() {
                let totalPriceProduct = 0;
                $('.listProduct .row').each(function() {
                    const qty = parseFloat($(this).find('#qty').val()) || 0;
                    const price = parseFloat($(this).find('#price').val()?.replace(/\./g, '')?.replace(/,/g, '.')) || 0;
                    totalPriceProduct += qty * price;
                });

                const persentasePPN = parseFloat($('#persentase_ppn')?.val()?.replace(/\./g, '')?.replace(/,/g, '.')) || 0;
                const biayaPengiriman = parseFloat($('#biaya_pengiriman').val()?.replace(/\./g, '')?.replace(/,/g, '.')) || 0;
                const potongan = parseFloat($('#potongan').val()?.replace(/\./g, '')?.replace(/,/g, '.')) || 0;
                const downPayment = parseFloat($('#down_payment').val()?.replace(/\./g, '')?.replace(/,/g, '.')) || 0;
                const totalPembayaran = parseFloat($('#total_pembayaran').val()?.replace(/\./g, '')?.replace(/,/g, '.')) || 0;
                const isPPN = $('#is_ppn').is(':checked');

                const subTotal = totalPriceProduct;
                const adjustedSubTotal = subTotal;
                const finalSubTotal = adjustedSubTotal < 0 ? 0 : adjustedSubTotal;
                const totalPpn = isPPN ? (finalSubTotal * (persentasePPN / 100)) : 0;
                const total = finalSubTotal + totalPpn + biayaPengiriman - potongan;
                const sisaPelunasan = total - downPayment - totalPembayaran;

                const totalAll = totalPembayaran + downPayment;

                if (totalAll <= total) {
                    $('#btn-save').prop('disabled', false);
                } else {
                    $('#btn-save').prop('disabled', true);
                }

                $('.totalPPN').text(new Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(totalPpn));

                $('.subTotal').text(new Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(totalPriceProduct));

                const formattedTotal = new Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(total);

                $('.totalAllPrice').text(formattedTotal);

                $('.totalPembayaran').text(new Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(totalPembayaran+downPayment));

                $('.sisaPelunasan').text(new Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                }).format(sisaPelunasan));
            }

            $(document).on('change', '.kode-order', function() {
                const $row = $(this).closest('.row');
                const kodeOrderId = $(this).val();
                console.log('Kode Order ID:', kodeOrderId);
                if (kodeOrderId) {
                    $.ajax({
                        url: "/surat-jalan/getOrderDetail/",
                        type: 'GET',
                        data:{sko:kodeOrderId},
                        success: function(data) {
                            $row.find('input[name^="product_name"]').val(data?.tb_produksi?.nama_produk);
                            $row.find('input[name^="price"]').val(data?.tb_produksi?.harga_produk);
                            $row.find('input[name^="nama_produk"]').val(data?.tb_produksi?.nama_produk);
                            $row.find('textarea[name^="specification"]').val(data?.tb_produksi?.notes);
                            $row.find('input[name^="qty"]').val(data?.tb_produksi?.jumlah_produk).trigger('keyup');
                            $('#no_po').val(data?.no_po);
                            $('#lampiran').val(data?.link_dokumen_order);
                        },
                        error: function(xhr) {
                            console.error(xhr);
                        }
                    });
                } else {
                    // $row.find('#product_name').val('');
                    // $row.find('#qty').val('');
                    // calculateTotalPrice($row.find('#qty'));
                }
            });
            $(document).ready(function() {
                @if(isset($data_order) && isset($data_order->tb_produksi))
                    $('#qty').val("{{$data_order->tb_produksi->jumlah_produk}}").trigger('keyup');
                @endif
            });
        </script>
    </x-slot>
</x-app-layout>
