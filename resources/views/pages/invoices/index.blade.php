<x-app-layout>
    <x-slot name="header">
            <span class="text-white fs-1">
                <a href="{{ route('sales.index') }}" class="pe-3">
                    <i class="fa-solid fa-angles-left fa-lg" style="color: white"></i>
                </a> Invoice
        </span>
    </x-slot>
    <x-slot name="script">
        <script>
            $.ajaxSetup({
                headers: {
                    "X-CSRF-TOKEN": $("meta[name='csrf-token']").attr("content"),
                },
            });

            let filters = {};
            const table = $("#datatable_tipe_produk").DataTable({
                lengthMenu: [
                    [10, 25, 50, 100, 500, -1],
                    [10, 25, 50, 100, 500, "All"],
                ],
                searching: false,
                responsive: false,
                lengthChange: true,
                autoWidth: false,
                order: [],
                pagingType: "full_numbers",
                dom: '<"top"f>rt<"mt-4 d-flex align-items-center justify-content-between"ilp><"clear">',
                language: {
                    search: "_INPUT_",
                    searchPlaceholder: "Cari...",
                    paginate: {
                        Search: '<i class="icon-search"></i>',
                        first: "<i class='fas fa-angle-double-left'></i>",
                        previous: "<i class='fas fa-angle-left'></i>",
                        next: "<i class='fas fa-angle-right'></i>",
                        last: "<i class='fas fa-angle-double-right'></i>",
                    },
                },
                oLanguage: {
                    sSearch: "",
                },
                processing: true,
                serverSide: true,
                ajax: {
                    url: `/invoice/dataTables`,
                    method: "POST",
                    data: function(d) {
                        return {
                            ...d,
                            ...filters,
                        };
                    },
                },
                columns: [{
                        name: "created_at",
                        data: "DT_RowIndex",
                    },

                    {
                        name: "created_by",
                        data: "created_by",
                        orderable: false,
                    },
                    {
                        name: "created_at",
                        data: "created_at",
                        orderable: false,
                    },
                    {
                        name: "no_invoice",
                        data: "no_invoice",
                        orderable: false,
                    },
                    {
                        name: "customer.nama",
                        data: "customer.nama",
                        orderable: false,
                    },
                    {
                        name: "kode_order",
                        data: "kode_order",
                        orderable: false,
                    },
                    {
                        name: "nama_produk",
                        data: "nama_produk",
                        orderable: false,
                    },
                    {
                        name: "total_price",
                        data: "total_price",
                        orderable: false,
                        className: "text-center",
                    },

                    {
                        name: "dp_terbayar",
                        data: "dp_terbayar",
                        orderable: false,
                        className: "text-center",
                    },
                    {
                        name: "sisa_pelunasan",
                        data: "sisa_pelunasan",
                        orderable: false,
                        className: "text-center",
                    },
                    {
                        name: "total_pembayaran",
                        data: "total_pembayaran",
                        orderable: false,
                        className: "text-center",
                    },
                    {
                        name: "tanggal_dp",
                        data: "tanggal_dp",
                        orderable: false,
                        className: "text-center",
                    },
                    {
                        name: "tanggal_pelunasan",
                        data: "tanggal_pelunasan",
                        orderable: false,
                        className: "text-center",
                    },
                    {
                        name: "tanggal_jatuh_tempo",
                        data: "tanggal_jatuh_tempo",
                        orderable: false,
                        className: "text-center",
                    },
                    {
                        name: "tanggal_jatuh_tempo_tambahan",
                        data: "tanggal_jatuh_tempo_tambahan",
                        orderable: false,
                        className: "text-center",
                    },
                    {
                        name: "status_badge",
                        data: "status_badge",
                        orderable: false,
                        className: "text-center",
                    },
                    {
                        name: "status_paid",
                        data: "status_paid",
                        orderable: false,
                        className: "text-center",
                    },
                    {
                        name: "action",
                        data: "action",
                        orderable: false,
                    },

                ],
            });

            let debounceTimer;

            function handleSearch(e) {
                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    filters.keyword = e.target.value;
                    table.draw();
                }, 500);
            }

            $(document).on('change', '#filter_start_date', function() {
                filters.start_date = $(this).val();
                table.draw();
            }).on('change', '#filter_end_date', function() {
                filters.end_date = $(this).val();
                table.draw();
            }).on('change', '#filter_status_jatuh_tempo', function() {
                filters.status_jatuh_tempo = $(this).val();
                table.draw();
            }).on('change', '#filter_pic', function() {
                filters.pic = $(this).val();
                table.draw();
            }).on('change', '#filter_type', function() {
                filters.type = $(this).val();
                table.draw();
            });

            $(document).on('click', '.deleteData', async function() {
                const id = $(this).data("id");
                const dataInput = $(this).data("input");
                const nama = dataInput.no_invoice;
                const urlTarget = `/invoice/${id}`
                Swal.fire({
                    title: "Apakah yakin?",
                    text: `Data ${nama} akan dihapus`,
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#6492b8da",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Yakin, hapus",
                    cancelButtonText: "Batal",
                }).then((result) => {
                    console.log(result, 'result');
                    if (result.isConfirmed) {
                        $.ajax({
                            url: urlTarget,
                            method: "post",
                            data: [{
                                name: "_method",
                                value: "DELETE"
                            }],
                            success: function(res) {
                                table.draw();
                                Swal.fire(`Berhasil dihapus`, res.message, "success");
                            },
                            error: function(res) {
                                console.log(res);
                                Swal.fire(`Gagal`, `${res.responseJSON.message}`, "error");
                            },
                        });
                    }
                });
            });

            var isCollapsed = true;
            $('#collapseButton').on('click', function() {
                var collapse = $('#collapseExample');

                if (isCollapsed) {
                    collapse.collapse('show');
                    $("#table_div").removeClass("mt-2")
                } else {
                    collapse.collapse('hide');
                    $("#table_div").addClass("mt-2")
                }

                isCollapsed = !isCollapsed; // Toggle the collapse state
            });

            $('#clearFilter').on('click', function() {
                $('#filter_start_date').val('');
                $('#filter_end_date').val('');
                $('#filter_status_jatuh_tempo').val('').trigger('change');
                $('#filter_pic').val('').trigger('change');
                $('#filter_type').val('').trigger('change');
                filters = {
                    keyword: $('#tipe_produk-search-box').val(),
                };
                table.draw();
            });

            $('#btnClearFilter').on('click', function() {
                $('#filter_start_date').val('');
                $('#filter_end_date').val('');
                $('#filter_status_jatuh_tempo').val('').trigger('change');
                $('#filter_pic').val('').trigger('change');
                $('#filter_type').val('').trigger('change');
                filters = {
                    keyword: $('#tipe_produk-search-box').val(),
                };
                table.draw();
            });

            function filterOverdue(value) {
                $('#filter_status_jatuh_tempo').val(value).trigger('change');
                filters.status_jatuh_tempo = value;
                table.draw();
            }
        </script>
    </x-slot>
    <div class="container-xxl" id="kt_content_container">
        <div class="row">
            <div class="col-md-3">
                <div class="card bg-danger hover-elevate-up cursor-pointer" id="count_price_null" onclick="filterOverdue(4)">
                    <div>
                        <div class="card-body d-flex flex-column">
                            <!--begin::Amount-->
                            <span class="fs-2hx fw-bolder me-2 lh-1 ls-n2 text-white">
                                Rp {{ number_format($total_overdue_invoices, 0, '', ',') }}
                            </span>
                            <!--end::Amount-->
                            <!--begin::Subtitle-->
                            <span class="fw-semibold fs-6 pt-4 text-white">
                                Total Tagihan Lewat Tempo
                            </span>
                            <!--end::Subtitle-->
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info hover-elevate-up cursor-pointer" id="count_price_null" onclick="filterOverdue(5)">
                    <div>
                        <div class="card-body d-flex flex-column">
                            <!--begin::Amount-->
                            <span class="fs-2hx fw-bolder me-2 lh-1 ls-n2 text-white">
                                Rp {{ number_format($total_upcoming_invoices, 0, '', ',') }}
                            </span>
                            <!--end::Amount-->
                            <!--begin::Subtitle-->
                            <span class="fw-semibold fs-6 pt-4 text-white">
                                Total Tagihan Akan Datang
                            </span>
                            <!--end::Subtitle-->
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-primary hover-elevate-up cursor-pointer" id="count_price_null" onclick="filterOverdue(7)">
                    <div>
                        <div class="card-body d-flex flex-column">
                            <!--begin::Amount-->
                            <span class="fs-2hx fw-bolder me-2 lh-1 ls-n2 text-white">
                                Rp {{ number_format($total_invoices_this_month, 0, '', ',') }}
                            </span>
                            <!--end::Amount-->
                            <!--begin::Subtitle-->
                            <span class="fw-semibold fs-6 pt-4 text-white">
                                Total Invoice Bulan Ini
                            </span>
                            <!--end::Subtitle-->
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success hover-elevate-up cursor-pointer" id="count_price_null" onclick="filterOverdue(6)">
                    <div>
                        <div class="card-body d-flex flex-column">
                            <!--begin::Amount-->
                            <span class="fs-2hx fw-bolder me-2 lh-1 ls-n2 text-white">
                                Rp {{ number_format($total_invoices_paid_this_month, 0, '', ',') }}
                            </span>
                            <!--end::Amount-->
                            <!--begin::Subtitle-->
                            <span class="fw-semibold fs-6 pt-4 text-white">
                                Total Invoice Terbayar Bulan Ini
                            </span>
                            <!--end::Subtitle-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card card-flush mt-10">
            <div class="card-body pt-0">
                <div class="py-5">
                    <div class="d-flex flex-stack flex-wrap mb-5">
                        <div class="d-flex align-items-center position-relative my-1 mb-2 mb-md-0">
                            <span class="svg-icon svg-icon-1 position-absolute ms-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                    viewBox="0 0 24 24" fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2"
                                        rx="1" transform="rotate(45 17.0365 15.1223)" fill="black"></rect>
                                    <path
                                        d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                        fill="black"></path>
                                </svg>
                            </span>
                            <input type="text" oninput="handleSearch(event)" id="tipe_produk-search-box"
                                data-kt-docs-table-filter="search" class="form-control form-control-solid w-450px ps-15"
                                placeholder="Search ...">
                        </div>
                        <div class="d-flex justify-content-end gap-4" data-kt-docs-table-toolbar="base">
                            <button type="button" class="btn btn-light-primary" id="collapseButton">
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <path
                                            d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z"
                                            fill="black"></path>
                                    </svg>
                                </span>
                                Filter
                            </button>
                            <button type="button" class="btn btn-info btn-sm" id="clearFilter">
                                <i class="fa fa-refresh"></i>
                                Refresh
                            </button>
                            <div class="dropdown">
                                <a href="{{ route('invoice.create', ['type' => 'sales']) }}">
                                    <button class="btn btn-success" type="button">
                                        <i class="fa fa-plus"></i>
                                        Add Invoice
                                    </button>
                                </a>
                                {{-- <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton" style="max-width: 170px">
                                    <li><a class="dropdown-item px-5 py-3"
                                            href="{{ route('invoice.create', ['type' => 'proforma']) }}">Proforma
                                            Invoice</a></li>
                                    <li><a class="dropdown-item px-5 py-3"
                                            href="{{ route('invoice.create', ['type' => 'sales']) }}">Sales Invoice</a>
                                    </li>
                                </ul> --}}
                            </div>
                        </div>
                    </div>

                    <div class="collapse" id="collapseExample">
                        <hr>
                        <div class=" bg-secondary rounded p-2 d-flex flex-row-fluid gap-4">
                            <div class="form-group">
                                <div class="d-flex align-items-center gap-4">
                                    <div class="input-group">
                                        <span class="input-group-text">Start Date</span>
                                        <input type="date" class="form-control form-control-solid"
                                            name="filter_start_date" id="filter_start_date" placeholder="Tanggal Mulai">
                                    </div>
                                    <div class="input-group">
                                        <span class="input-group-text">End Date</span>
                                        <input type="date" class="form-control form-control-solid"
                                            name="filter_end_date" id="filter_end_date" placeholder="Tanggal Akhir">
                                    </div>
                                </div>
                            </div>
                            @php
                            $type = [
                                'dp' => 'DP',
                                'pelunasan' => 'Pelunasan',
                            ];
                            $statusOptions = [
                                'Semua' => '',
                                'Lewat Tempo' => 2,
                                'Akan Datang' => 1,
                                'Sudah Lunas' => 3,
                            ];
                            $users = \App\Models\User::pluck('name', 'id')->toArray();
                        @endphp

                            <div class="form-group d-none">
                                <select class="form-select" name="filter_type" id="filter_type" data-control="select2"
                                    data-placeholder="Pilih Tipe">
                                    <option></option>
                                    @foreach ($type as $key => $value)
                                        <option value="{{ $key }}">{{ $value }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="form-group">
                                <select class="form-select" name="filter_status_jatuh_tempo"
                                    id="filter_status_jatuh_tempo" data-control="select2"
                                    data-placeholder="Pilih Status Jatuh Tempo">
                                    @foreach ($statusOptions as $label => $value)
                                        <option value="{{ $value }}">{{ $label }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="form-group">
                                <select class="form-select" name="filter_pic" id="filter_pic" data-control="select2"
                                    data-placeholder="Pilih PIC">
                                    <option></option>
                                    @foreach ($users as $id => $name)
                                        <option value="{{ $id }}">{{ $name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="ms-auto">
                                <button type="button" class="btn btn-danger btn-sm" id="btnClearFilter">
                                    <i class="fa fa-trash-alt"></i>
                                    Clear Filter
                                </button>
                            </div>
                        </div>
                        <hr>
                    </div>

                    {{-- <div class="form-group">
                        <label for="tipe_produk" class="form-label">Customer</label>
                        <select class="form-select" name="company_id_edit" id="company_id_edit"
                            data-control="select2" data-placeholder="Pilih Company">
                            <option></option>
                            @foreach ($customers as $customer)
                                <option value="{{ $customer->id }}">{{ $customer->nama }}</option>
                            @endforeach
                        </select>
                    </div> --}}
                    <div class="table-responsive">
                        <table id="datatable_tipe_produk"
                            class="table-row-bordered compact text-nowrap dataTable no-footer table-striped table align-middle"
                            style="width:100%">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>PIC</th>
                                    <th>Tanggal Invoice</th>
                                    <th>No Invoice</th>
                                    <th>Nama Konsumen</th>
                                    <th>Kode Order</th>
                                    <th>Nama Produk</th>
                                    <th>Total Tagihan</th>
                                    <th>DP</th>
                                    <th>Sisa Tagihan</th>
                                    <th>Total Pelunasan</th>
                                    <th>Tgl Pembayaran DP</th>
                                    <th>Tgl Pelunasan</th>
                                    <th>Tanggal Jatuh Tempo</th>
                                    <th>Tanggal Jatuh Tempo Tambahan</th>
                                    <th>Status</th>
                                    <th>Status Paid</th>
                                    <th class="text-center">Action</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
