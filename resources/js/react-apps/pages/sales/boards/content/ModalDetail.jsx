import axios from "axios";
import React, { useEffect, useState } from "react";

const ModalDetailBoard = (props) => {
    const { data, onHide } = props;
    const [showInput, setShowInput] = useState(false);
    const [showActivityTask, setShowActivityTask] = useState(true);
    const [loading, setLoading] = useState(false);
    const [dataActivityTask, setDataActivityTask] = useState([]);
    const [reasonLost, setReasonLost] = useState(null);
    const [form, setForm] = useState({
        id: "",
        title: "",
        detail: "",
        due_date: "",
    });

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setForm({
            ...form,
            [name]: value,
        });
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (loading) return;
        if (!form.title || !form.detail) {
            Swal.fire({
                icon: "error",
                title: "Gagal",
                text: "Semua field harus diisi",
                showConfirmButton: false,
                timer: 1500,
            });
            return;
        }

        setLoading(true);

        try {
            const { data: response } = await axios.post(
                `/sales/board/order-company/detail/${data.order_key}/activity-task`,
                form
            );

            if (response.status) {
                Swal.fire({
                    icon: "success",
                    title: "Berhasil",
                    text: response.message,
                    showConfirmButton: false,
                    timer: 1500,
                });
                setForm({
                    title: "",
                    detail: "",
                    due_date: "",
                });
                setShowInput(false);
                getActivityTask();

                // Fetch and render card again after submit
                const statusOrder = data.status_order;
                try {
                    await axios.get(`/sales/board/order-company?status=follow_up&status_order=${statusOrder}&page=1&keyword=`);
                    // Optionally, trigger a refresh event if needed
                    if (window.$) {
                        $(`#kt_refresh_board`).trigger("click");
                    }
                } catch (err) {
                    // handle error silently
                }
            } else {
                Swal.fire({
                    icon: "error",
                    title: "Gagal",
                    text: response.message,
                    showConfirmButton: false,
                    timer: 1500,
                });
            }
        } catch (error) {
            Swal.fire({
                icon: "error",
                title: "Gagal",
                text: `Terjadi kesalahan saat menyimpan data ${error.message}`,
                showConfirmButton: false,
                timer: 1500,
            });
            console.error("Error:", error);
        }
        setLoading(false);
    };

    const getActivityTask = async () => {
        setLoading(true);
        try {
            const { data: response } = await axios.get(
                `/sales/board/order-company/detail/${data.order_key}/activity-task`
            );
            if (response.status) {
                setDataActivityTask(response.data);
            }
        } catch (error) {
            console.error("Error:", error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (data?.order_key) {
            getActivityTask();
        }
    }, [data]);

    const handleDelete = async (id) => {
        console.log(id);
        Swal.fire({
            title: "Apakah Anda yakin?",
            text: "Anda tidak akan dapat mengembalikan ini!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, hapus!",
            scrollbarPadding: false,
        }).then(async (result) => {
            if (result.isConfirmed) {
                setLoading(true);
                try {
                    const { data: response } = await axios.delete(
                        `/sales/board/order-company/detail/${data.order_key}/activity-task/${id}`
                    );
                    if (response.status) {
                        Swal.fire({
                            icon: "success",
                            title: "Berhasil",
                            text: response.message,
                            showConfirmButton: false,
                            timer: 1500,
                        });
                        getActivityTask();
                    }
                } catch (error) {
                    console.error("Error:", error);
                } finally {
                    setLoading(false);
                }
            }
        });
    };

    const handleMarkDone = async (id) => {
        Swal.fire({
            title: "Apakah Anda yakin?",
            text: "Anda tidak akan dapat mengembalikan ini!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Ya, selesai!",
            scrollbarPadding: false,
        }).then(async (result) => {
            if (result.isConfirmed) {
                setLoading(true);
                try {
                    const { data: response } = await axios.post(
                        `/sales/board/order-company/detail/${data.order_key}/activity-task/${id}/mark-done`
                    );
                    if (response.status) {
                        Swal.fire({
                            icon: "success",
                            title: "Berhasil",
                            text: response.message,
                            showConfirmButton: false,
                            timer: 1500,
                        });
                        getActivityTask();
                    }
                } catch (error) {
                    console.error("Error:", error);
                } finally {
                    setLoading(false);
                }
            }
        });
    };

    const handleSubmitConfirmLost = async (e) => {
        e.preventDefault();
        setLoading(true);
        try {
            const { data: response } = await axios.post(
                `/sales/board/order-company/detail/${data.order_key}/change-lost`,
                {
                    reason: reasonLost,
                }
            );
            if (response.status) {
                Swal.fire({
                    icon: "success",
                    title: "Berhasil",
                    text: response.message,
                    showConfirmButton: false,
                    timer: 1500,
                });
                setShowActivityTask(true);
                $("#modalDetailBoard").modal("hide");
                $(`#kt_refresh_board`).trigger("click");
            }
        } catch (error) {
            console.error("Error:", error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="modal fade" id="modalDetailBoard">
            <div className="modal-dialog modal-md">
                <div
                    className="modal-content overflow-hidden"
                    style={{ height: "calc(100vh - 70px)" }}
                >
                    <div className="modal-header py-3 px-5">
                        <h5 className="modal-title">Detail Order</h5>
                        <div
                            className="btn btn-icon btn-sm btn-active-light-primary ms-2"
                            onClick={onHide}
                        >
                            <span className="svg-icon svg-icon-2x">
                                <i className="fa fa-times"></i>
                            </span>
                        </div>
                    </div>
                    <div className="modal-body px-4 py-0">
                        <div className="">
                            <div className="">
                                <div className="px-4 pt-6 pb-4">
                                    <h2>
                                        {data.tb_produksi?.jenis_bahan} -{" "}
                                        {data.tb_produksi?.nama_produk}
                                    </h2>
                                    <div className="d-flex flex-wrap align-items-center gap-4 text-muted mt-2">
                                        <div className="">
                                            <i className="fas fa-calendar-alt me-2"></i>
                                            {new Date(
                                                data.created_at
                                            ).toLocaleDateString("id-ID", {
                                                day: "2-digit",
                                                month: "long",
                                                year: "numeric",
                                            })}
                                        </div>
                                        <div className="text-center">
                                            <span>•</span>
                                        </div>
                                        <div className="">
                                            <i className="fas fa-user-circle me-2"></i>
                                            {data.tb_customer?.nama}
                                        </div>
                                        <div className="text-center">
                                            <span>•</span>
                                        </div>
                                        <div className="">
                                            <i className="fas fa-building me-2"></i>
                                            {data.tb_customer?.company?.name  || "-"}
                                        </div>
                                    </div>
                                    {data.status_deal === "Follow Up" && (
                                        <div className="mt-3">
                                            <button
                                                onClick={() => {
                                                    setShowActivityTask(false);
                                                }}
                                                className="btn btn-sm btn-danger px-3 py-2"
                                            >
                                                <i className="fas fa-times-circle me-2"></i>
                                                Jadikan Lost
                                            </button>
                                        </div>
                                    )}
                                </div>
                            </div>
                            {showActivityTask ? (
                                <div className="p-0 container-slide-wrapper">
                                    <div
                                        className={`slide-in-out border border-gray-300 w-100 ${
                                            showInput ? "show" : "hide"
                                        }`}
                                        style={{
                                            borderRadius: "10px",
                                        }}
                                    >
                                        <div
                                            className={`
                                        p-4 d-flex align-items-center justify-content-between border-bottom border-gray-300
                                    
                                        `}
                                            style={{
                                                boxShadow:
                                                    "0 0 6px rgba(0, 0, 0, 0.1)",
                                            }}
                                        >
                                            <h4>Input Activity Task</h4>
                                            <div
                                                onClick={() => {
                                                    setShowInput(false);
                                                    setForm({
                                                        title: "",
                                                        detail: "",
                                                        due_date: "",
                                                    });
                                                }}
                                                role="button"
                                                className="btn btn-sm btn-danger px-3 py-2"
                                            >
                                                <i className="fas fa-times"></i>
                                                Tutup
                                            </div>
                                        </div>
                                        <div
                                            className=" p-5"
                                            style={{
                                                backgroundColor: "#f8f9fa",
                                            }}
                                        >
                                            <form onSubmit={handleSubmit}>
                                                <div className="form-group">
                                                    <label className="form-label required">
                                                        Title
                                                    </label>
                                                    <input
                                                        type="text"
                                                        className="form-control"
                                                        placeholder="Masukkan title"
                                                        autoComplete="off"
                                                        name="title"
                                                        required
                                                        value={form.title}
                                                        onChange={
                                                            handleInputChange
                                                        }
                                                    />
                                                </div>
                                                <div className="form-group mt-5">
                                                    <label className="form-label required">
                                                        Detail
                                                    </label>
                                                    <textarea
                                                        className="form-control"
                                                        placeholder="Masukkan detail"
                                                        autoComplete="off"
                                                        name="detail"
                                                        required
                                                        value={form.detail}
                                                        onChange={
                                                            handleInputChange
                                                        }
                                                    ></textarea>
                                                </div>
                                                <div className="form-group mt-5">
                                                    <label className="form-label required">
                                                        Due Date
                                                    </label>
                                                    <input
                                                        type="date"
                                                        className="form-control"
                                                        placeholder="Masukkan due date"
                                                        autoComplete="off"
                                                        name="due_date"
                                                        value={form.due_date}
                                                        onChange={
                                                            handleInputChange
                                                        }
                                                    />
                                                </div>
                                                <div className="mt-5">
                                                    <button
                                                        className="btn btn-primary"
                                                        disabled={loading}
                                                    >
                                                        {loading ? (
                                                            <span
                                                                className="spinner-border spinner-border-sm"
                                                                role="status"
                                                                aria-hidden="true"
                                                            ></span>
                                                        ) : (
                                                            <i className="fas fa-save me-2"></i>
                                                        )}
                                                        Simpan
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <div
                                        className={`border border-gray-300 w-100
                                    ${showInput ? "mt-18" : ""}
                                    `}
                                        style={{
                                            borderRadius: "10px",
                                        }}
                                    >
                                        <div
                                            className={`p-4 d-flex align-items-center justify-content-between border-bottom border-gray-300`}
                                        >
                                            <h4>Activity Task</h4>
                                            <div
                                                onClick={() => {
                                                    setShowInput(true);
                                                }}
                                                role="button"
                                                className="btn btn-sm btn-primary px-3 py-2"
                                            >
                                                <i className="fas fa-plus-circle"></i>
                                                Tambah
                                            </div>
                                        </div>
                                        <div
                                            className="overflow-auto"
                                            style={{
                                                height: "calc(100vh - 330px)",
                                                backgroundColor: "#f8f9fa",
                                            }}
                                        >
                                            <ul className="list-group list-group-flush">
                                                {loading ? (
                                                    <div
                                                        className="d-flex justify-content-center align-items-center"
                                                        style={{
                                                            height: "100%",
                                                        }}
                                                    >
                                                        <div
                                                            className="spinner-border"
                                                            role="status"
                                                        >
                                                            <span className="visually-hidden">
                                                                Loading...
                                                            </span>
                                                        </div>
                                                    </div>
                                                ) : dataActivityTask.length >
                                                  0 ? (
                                                    dataActivityTask.map(
                                                        (task, index) => (
                                                            <li
                                                                key={index}
                                                                className="border-bottom border-gray-300 d-flex gap-1 align-items-start bg-transparent p-4"
                                                            >
                                                                <div>
                                                                    <img
                                                                        src={`https://ui-avatars.com/api/?name=Dua&background=random`}
                                                                        alt="Avatar"
                                                                        className="rounded-circle me-3"
                                                                        style={{
                                                                            width: "35px",
                                                                            height: "35px",
                                                                        }}
                                                                    />
                                                                    {task.is_completed && (
                                                                        <div className="mt-4 px-1">
                                                                            <i
                                                                                className="fas fa-check-circle text-success"
                                                                                style={{
                                                                                    fontSize:
                                                                                        "25px",
                                                                                }}
                                                                            ></i>
                                                                        </div>
                                                                    )}
                                                                </div>
                                                                <div className="">
                                                                    <div
                                                                        className=""
                                                                        style={{
                                                                            fontSize:
                                                                                "10px",
                                                                            fontStyle:
                                                                                "italic",
                                                                            color: "#6c757d",
                                                                        }}
                                                                    >
                                                                        {
                                                                            task
                                                                                ?.created_by
                                                                                ?.name
                                                                        }{" "}
                                                                        -{" "}
                                                                        {new Date(
                                                                            task.created_at
                                                                        ).toLocaleDateString(
                                                                            "id-ID",
                                                                            {
                                                                                day: "2-digit",
                                                                                month: "long",
                                                                                year: "numeric",
                                                                            }
                                                                        )}{" "}
                                                                        {new Date(
                                                                            task.created_at
                                                                        ).toLocaleTimeString(
                                                                            "id-ID",
                                                                            {
                                                                                hour: "2-digit",
                                                                                minute: "2-digit",
                                                                            }
                                                                        )}
                                                                    </div>
                                                                    <div
                                                                        style={{
                                                                            fontSize:
                                                                                "15px",
                                                                            fontWeight:
                                                                                "bold",
                                                                        }}
                                                                    >
                                                                        {
                                                                            task.title
                                                                        }
                                                                    </div>
                                                                    <div
                                                                        className=""
                                                                        style={{
                                                                            fontSize:
                                                                                "13px",
                                                                            color: "#5a6268",
                                                                            fontWeight:
                                                                                "regular",
                                                                        }}
                                                                    >
                                                                        {
                                                                            task.detail
                                                                        }
                                                                    </div>
                                                                    <div className="d-flex flex-wrap align-items-center gap-2 mt-2 text-primary">
                                                                        <div
                                                                            className=""
                                                                            style={{
                                                                                fontSize:
                                                                                    "12px",
                                                                                fontWeight:
                                                                                    "bold",
                                                                            }}
                                                                        >
                                                                            Due
                                                                            Date:{" "}
                                                                            {task.due_date
                                                                                ? new Date(
                                                                                      task.due_date
                                                                                  ).toLocaleDateString(
                                                                                      "id-ID",
                                                                                      {
                                                                                          day: "2-digit",
                                                                                          month: "long",
                                                                                          year: "numeric",
                                                                                      }
                                                                                  )
                                                                                : "-"}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div className="ms-auto">
                                                                    {parseInt(
                                                                        task.is_completed
                                                                    ) !== 1 && (
                                                                        <div
                                                                            className="text-nowrap"
                                                                            style={{
                                                                                textDecoration:
                                                                                    "underline",
                                                                            }}
                                                                            role="button"
                                                                            onClick={() => {
                                                                                handleMarkDone(
                                                                                    task.id_key
                                                                                );
                                                                            }}
                                                                        >
                                                                            <i className="fas fa-check-circle text-success pe-2"></i>
                                                                            Mark
                                                                            as
                                                                            Done
                                                                        </div>
                                                                    )}
                                                                    {parseInt(
                                                                        task.is_completed
                                                                    ) !== 1 && (<div className="d-flex align-items-center gap-3">
                                                                        <div
                                                                            className="mt-2"
                                                                            role="button"
                                                                            onClick={() => {
                                                                                setShowInput(
                                                                                    true
                                                                                );
                                                                                setForm(
                                                                                    {
                                                                                        id: task.id_key,
                                                                                        title: task.title,
                                                                                        detail: task.detail,
                                                                                        due_date:
                                                                                            task.due_date,
                                                                                    }
                                                                                );
                                                                            }}
                                                                        >
                                                                            <i className="fas fa-pencil text-primary pe-1"></i>
                                                                            Edit
                                                                        </div>
                                                                        <div
                                                                            className="mt-2"
                                                                            role="button"
                                                                            onClick={() => {
                                                                                handleDelete(
                                                                                    task.id_key
                                                                                );
                                                                            }}
                                                                        >
                                                                            <i className="fas fa-trash-alt pe-1 text-danger"></i>
                                                                            Hapus
                                                                        </div>
                                                                    </div>
                                                                    )}
                                                                </div>
                                                            </li>
                                                        )
                                                    )
                                                ) : (
                                                    <div
                                                        className="d-flex justify-content-center align-items-center"
                                                        style={{
                                                            height: "300px",
                                                        }}
                                                    >
                                                        <div
                                                            className="text-muted"
                                                            style={{
                                                                fontSize:
                                                                    "16px",
                                                            }}
                                                        >
                                                            <i className="fas fa-exclamation-circle me-2"></i>
                                                            Belum ada Activity
                                                            Task
                                                        </div>
                                                    </div>
                                                )}
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            ) : (
                                <div className="">
                                    <div
                                        className={`slide-in-out border border-gray-300 w-100 `}
                                        style={{
                                            borderRadius: "10px",
                                        }}
                                    >
                                        <div
                                            className={`
                                        p-4 d-flex align-items-center justify-content-between border-bottom border-gray-300
                                    
                                        `}
                                            style={{
                                                boxShadow:
                                                    "0 0 6px rgba(0, 0, 0, 0.1)",
                                            }}
                                        >
                                            <h4>Konfirmasi Lost</h4>
                                            <div
                                                onClick={() => {
                                                    setShowActivityTask(false);
                                                }}
                                                role="button"
                                                className="btn btn-sm btn-danger px-3 py-2"
                                            >
                                                <i className="fas fa-times"></i>
                                                Tutup
                                            </div>
                                        </div>
                                        <div
                                            className=" p-5"
                                            style={{
                                                backgroundColor: "#f8f9fa",
                                            }}
                                        >
                                            <form
                                                onSubmit={
                                                    handleSubmitConfirmLost
                                                }
                                            >
                                                <h6 className="text-center text-danger mb-4">
                                                    Apakah Anda yakin ingin
                                                    menjadikan order ini sebagai
                                                    lost? Jika ya, silahkan isi
                                                    pilih alasan Lost
                                                </h6>
                                                <div className="form-group">
                                                    <select
                                                        className="form-select"
                                                        name="reason"
                                                        required
                                                        onChange={(e) => {
                                                            setReasonLost(
                                                                e.target.value
                                                            );
                                                        }}
                                                    >
                                                        <option
                                                            value=""
                                                            selected
                                                            disabled
                                                        >
                                                            Pilih Alasan Lost
                                                        </option>
                                                        {lost_list?.map(
                                                            (reason, index) => (
                                                                <option
                                                                    key={index}
                                                                    value={
                                                                        reason.name
                                                                    }
                                                                >
                                                                    {
                                                                        reason.name
                                                                    }
                                                                </option>
                                                            )
                                                        )}
                                                    </select>
                                                </div>
                                                <div>
                                                    <button
                                                        className="btn btn-danger mt-5"
                                                        disabled={loading}
                                                    >
                                                        {loading ? (
                                                            <span
                                                                className="spinner-border spinner-border-sm"
                                                                role="status"
                                                                aria-hidden="true"
                                                            ></span>
                                                        ) : (
                                                            <i className="fas fa-save me-2"></i>
                                                        )}
                                                        Jadikan Lost
                                                    </button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ModalDetailBoard;
