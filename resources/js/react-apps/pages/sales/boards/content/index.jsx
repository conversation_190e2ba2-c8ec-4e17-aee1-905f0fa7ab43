import React, {
    useEffect,
    useState,
    useMemo,
    useCallback,
    useRef,
} from "react";
import CardBoard from "./CardBoard";
import { useDrop } from "react-dnd";
import axios from "axios";
import { useLocation, useNavigate } from "react-router-dom";

const SalesBoardContent = React.memo((props) => {
    const {
        activeTab,
        index,
        column,
        dataBoards,
        setDataBoards,
        hasLoadedBoard,
        setHasLoadedBoard,
        filters,
        getDetailBoard,
    } = props;

    const location = useLocation();
    const Navigate = useNavigate();

    const listCardRef = useRef(null);
    const [loadingState, setLoadingState] = useState({
        board: false,
        paginate: false,
    });

    const [paginate, setPaginate] = useState({
        meta: {},
        endPaginate: false,
        loadingPaginate: false,
    });
    const boardKey = `${column.name}_${activeTab}`;

    const fetchDataBoards = async (page = 1) => {
        try {
            delete filters.is_refresh
            const status_deal = activeTab.replace("tab_", "")
            const { data: response } = await axios.get(
                `/sales/board/order-company`,
                {
                    params: {
                        status: status_deal,
                        status_order: column.name,
                        page: page ?? 1,
                        ...filters,
                    },
                }
            );

            return response;
        } catch (error) {
            console.error("Error fetching data boards:", error);
        }
    };

    const getDataBoards = async (page = 1, isNextPage = false, forceReload = false) => {
        if (
            loadingState.board || 
            loadingState.paginate ||
            (paginate.endPaginate && !forceReload) ||
            (!isNextPage && !forceReload)
        ) return;

        setLoadingState(prev => ({
            ...prev,
            board: !isNextPage,
            paginate: isNextPage
        }));

        try {
            const response = await fetchDataBoards(page);
            if (!response?.status) return;

            const mappedOrders = response.data
                ?.map(item => ({
                    ...item,
                    status_order: item.status_order,
                    position_status_order: item.position_status_order ?? item.updated_at,
                }))
                .sort((a, b) => a.position_status_order - b.position_status_order);

            setDataBoards(prev => ({
                ...prev,
                [boardKey]: isNextPage 
                    ? [...(prev[boardKey] ?? []), ...mappedOrders]
                    : mappedOrders
            }));

            setPaginate({
                meta: response.meta,
                endPaginate: response.meta.last_page === page,
            });
        } finally {
            setLoadingState({
                board: false,
                paginate: false
            });
        }
    };

    useEffect(() => {
        getDataBoards(1, false, true);
    }, [activeTab, column.name]);

    useEffect(() => {
        getDataBoards(1, false, true);
    }, [filters, filters.keywords]);

    const [{ canDrop, isOver, itemDrop }, drop] = useDrop(() => ({
        accept: "card",
        drop: (item) => handleChangeBoard(item, column),
        collect: (monitor) => ({
            isOver: monitor.isOver(),
            canDrop: monitor.canDrop(),
            itemDrop: monitor.getItem(),
        }),
    }));

    const filterBoards = useMemo(
        () => dataBoards[boardKey] ?? [],
        [dataBoards, boardKey]
    );

    const handleChangeBoard = async (item, column) => {
        setDataBoards((prev) => {
            const updated = { ...prev };
            const sourceKey = `${item.status_order}_${activeTab}`;
            const targetKey = `${column.name}_${activeTab}`;

            const sourceList = updated[sourceKey] || [];
            const itemIndex = sourceList.findIndex(
                (data) => data.id_order === item.id_order
            );

            if (itemIndex !== -1) {
                const updatedItem = {
                    ...sourceList[itemIndex],
                    status_order: column.name,
                };
                updated[sourceKey] = sourceList.filter(
                    (_, idx) => idx !== itemIndex
                );
                updated[targetKey] = [
                    updatedItem,
                    ...(updated[targetKey] || []),
                ];
            }
            return updated;
        });

        try {
            const { data: response } = await axios.post(
                `/sales/board/order-company/detail/${item.order_key}`,
                {
                    order_key: item.order_key,
                    status_order: column.name,
                }
            );
            if (response?.status) {
                const responseFetch = await fetchDataBoards(1);
                console.log("responseFetch", responseFetch);
                if (responseFetch?.status) {
                    const mappedOrders = responseFetch.data
                        ?.map((item) => ({
                            ...item,
                            status_order: item.status_order,
                            position_status_order:
                                item.position_status_order ?? item.updated_at,
                        }))

                    setDataBoards((prev) => ({
                        ...prev,
                        [boardKey]: [
                            ...(prev[boardKey] ?? []),
                            ...mappedOrders,
                        ],
                    }));

                    setPaginate((prev) => ({
                        ...prev,
                        meta: responseFetch?.meta,
                        endPaginate: responseFetch.meta.last_page === 1,
                        loadingPaginate: false,
                    }));
                }
            }
        } catch (error) {
            console.error("Error updating order:", error);
        }
    };

    const moveCard = async (dragIndex, hoverIndex, item) => {
        setDataBoards((prev) => {
            const updated = { ...prev };
            const sourceKey = `${item.status_order}_${activeTab}`;
            const targetKey = `${column.name}_${activeTab}`;

            const sourceList = updated[sourceKey] || [];
            const targetList = updated[targetKey] || [];

            if (sourceList[dragIndex]) {
                const [movedItem] = sourceList.splice(dragIndex, 1);
                movedItem.status_order = column.name;
                targetList.splice(0, 0, movedItem); // Insert at the top
            }

            updated[sourceKey] = sourceList;
            updated[targetKey] = targetList;
            return updated;
        });
    };

    const renderCard = (item, index) => (
        <CardBoard
            key={item.id || index}
            item={item}
            index={index}
            moveCard={moveCard}
            onClick={async (item) => {
                await getDetailBoard(item.order_key);
                Navigate(`${location.pathname}?order_key=${item.order_key}`);
            }}
        />
    );

    const boardListStyles = {
        height: "calc(100vh - 240px)",
        scrollBehavior: "smooth",
    };

    useEffect(() => {
        let isFetching = false;

        const handleScroll = () => {
            if (listCardRef.current && !isFetching) {
                const { scrollTop, scrollHeight, clientHeight } =
                    listCardRef.current;
                if (scrollTop + clientHeight >= scrollHeight - 2) {
                    isFetching = true;
                    getDataBoards(
                        paginate.meta?.current_page + 1,
                        true
                    ).finally(() => {
                        isFetching = false;
                    });
                }
            }
        };

        const refCurrent = listCardRef.current;
        if (refCurrent) {
            refCurrent.addEventListener("scroll", handleScroll);
        }

        return () => {
            if (refCurrent) {
                refCurrent.removeEventListener("scroll", handleScroll);
            }
        };
    }, [paginate?.meta]);

    return (
        <div
            className={`col-3 ${
                canDrop && isOver ? "border border-gray-300 shadow-sm" : ""
            }`}
            ref={drop}
            style={{ marginLeft: index === 0 ? "40px" : "" }}
        >
            <div className="p-4" style={{ height: "100%" }}>
                <div className="d-flex align-items-center justify-content-between">
                    <h3>
                        <span
                            className="bg-primary me-2"
                            style={{
                                height: "10px",
                                width: "10px",
                                borderRadius: "50%",
                                display: "inline-block",
                            }}
                        ></span>
                        {column.name}
                        {/* <span className="badge bg-white text-dark ms-3 rounded-circle border border-gray-300">
                            {paginate?.meta?.total ?? 0}
                        </span> */}
                    </h3>
                    <div
                        role="button"
                        className="btn btn-sm btn-active-color-primary px-2"
                    >
                        <i
                            className="fas fa-ellipsis-v"
                            style={{ fontSize: "1.2rem" }}
                        ></i>
                    </div>
                </div>

                <div
                    ref={listCardRef}
                    className="listCard d-flex flex-column gap-4 py-4 overflow-auto"
                    style={boardListStyles}
                >
                    {filterBoards.map(renderCard)}
                    {filterBoards.length === 0 &&
                        !paginate.endPaginate &&
                        !loadingState?.board && (
                            <div className="text-center">
                                <button
                                    className="btn btn-link"
                                    onClick={() =>
                                        getDataBoards(1, false, true)
                                    }
                                >
                                    <i className="fas fa-sync"></i>
                                    &nbsp; Perbarui Data
                                </button>
                            </div>
                        )}

                    {(loadingState.board || loadingState.paginate) && (
                        <div className="text-center">
                            <div
                                className="spinner-border text-primary"
                                role="status"
                            >
                                <span className="visually-hidden">
                                    Loading...
                                </span>
                            </div>
                        </div>
                    )}
                </div>
            </div>
            {loadingState.board && !loadingState.paginate && (
                <div
                    className="loading-overlay d-flex justify-content-center align-items-center"
                    style={{
                        position: "fixed",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        width: "100%",
                        height: "100%",
                        backgroundColor: "rgba(0, 0, 0, 0.25)",
                        zIndex: 9999,
                    }}
                >
                    <div className="card p-4">
                        <div
                            className="spinner-border text-primary"
                            role="status"
                        >
                            <span className="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
});

export default SalesBoardContent;
