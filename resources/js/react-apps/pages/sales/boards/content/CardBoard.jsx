import React, { useRef } from "react";
import { useDrag, useDrop } from "react-dnd";

const CardBoard = (props) => {
    const { item, style, index, moveCard, onClick } = props;

    // const [{ isDragging }, drag] = useDrag(() => ({
    //     type: "card",
    //     item: item,
    //     hover: (draggedItem, monitor) => {
    //         console.log("Hovering over", draggedItem, item, monitor);
    //         if (!monitor.isOver({ shallow: true })) return;
    //         console.log("Hovering over", draggedItem, item, monitor);
    //     },
    //     collect: (monitor) => ({
    //         isDragging: monitor.isDragging(),
    //     }),
    //     end(item, monitor) {
    //         // console.log("Dropped!", item);
    //     },
    // }));

    const ref = useRef(null);
    const [{ handlerId }, drop] = useDrop({
        accept: "card",
        collect(monitor) {
            return {
                handlerId: monitor.getHandlerId(),
            };
        },
        hover(item, monitor) {
            // if (!ref.current) {
            //     return;
            // }
            // const dragIndex = item.index;
            // const hoverIndex = index;
            // // Don't replace items with themselves
            // if (dragIndex === hoverIndex) {
            //     return;
            // }
            // // Determine rectangle on screen
            // const hoverBoundingRect = ref.current?.getBoundingClientRect();
            // // Get vertical middle
            // const hoverMiddleY =
            //     (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
            // // Determine mouse position
            // const clientOffset = monitor.getClientOffset();
            // // Get pixels to the top
            // const hoverClientY = clientOffset.y - hoverBoundingRect.top;
            // // Only perform the move when the mouse has crossed half of the items height
            // // When dragging downwards, only move when the cursor is below 50%
            // // When dragging upwards, only move when the cursor is above 50%
            // // Dragging downwards
            // if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
            //     return;
            // }
            // // Dragging upwards
            // if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
            //     return;
            // }
            // // Time to actually perform the action
            // moveCard(dragIndex, hoverIndex, item);
            // // Note: we're mutating the monitor item here!
            // // Generally it's better to avoid mutations,
            // // but it's good here for the sake of performance
            // // to avoid expensive index searches.
            // item.index = hoverIndex;
        },
    });
    const [{ isDragging }, drag] = useDrag({
        type: "card",
        item: {
            ...item,
            index,
        },
        collect: (monitor) => ({
            isDragging: monitor.isDragging(),
        }),
    });
    const opacity = isDragging ? 0 : 1;
    drag(drop(ref));

    return (
        <div
            className="bg-white p-5 cardBoardItem"
            style={{
                borderRadius: "6px",
                boxShadow: "0 1px 3px rgba(0, 0, 0, 0.08)",
                border: isDragging ? "2pxrgb(85, 83, 83)" : "1px solid #e0e0e0",
                opacity: isDragging ? 0.3 : 1,
                cursor: isDragging ? "move" : "pointer",
                ...style,
            }}
        >
            <div
                ref={ref}
                onClick={() => onClick(item)}
            >

                <h4>
                    {item.tb_produksi?.jenis_bahan} -{" "}
                    {item.tb_produksi?.nama_produk}
                </h4>
                <div className="d-flex flex-wrap align-items-center gap-4 text-muted mt-6">
                    <div className="">
                        <i className="fas fa-user-circle me-2"></i>
                        {item.tb_customer?.nama}
                    </div>
                    <div className="text-center">
                        <span>•</span>
                    </div>
                    <div className="">
                        <i className="fas fa-building me-2"></i>
                        {item.tb_customer?.company?.name  || "-"}
                    </div>
                </div>
                <div className="text-muted mt-3">
                    <i className="fas fa-tag me-2"></i>
                    {item.tb_customer?.nama_brand || "-"}
                </div>
                <div className="text-muted mt-3">
                    <i className="fas fa-calendar-alt me-2"></i>
                    {new Date(item.waktu_kontak).toLocaleDateString("id-ID", {
                        day: "2-digit",
                        month: "long",
                        year: "numeric",
                    })}
                </div>
            </div>
            {item.status_deal === "Deal" && (
                <div className="text-muted mt-3">
                    <i className="fas fa-file-alt me-2"></i>
                    {item.sko || "-"}
                </div>
            )}
            {item.uncompleted_tasks && item.uncompleted_tasks.length > 0 && ( 
                <div className="mt-3">
                    <div className="accordion" id={`accordion-${item.sko_key}`}>
                        <div className="accordion-item border-0">
                            <h2 className="accordion-header">
                                <button 
                                    className="accordion-button p-2 rounded-2 bg-light shadow-none collapsed hover-bg-info-soft" 
                                    type="button" 
                                    data-bs-toggle="collapse" 
                                    data-bs-target={`#collapse-task-${item.sko_key}`}
                                    aria-expanded="false"
                                    data-bs-parent={`#accordion-${item.sko_key}`}
                                    style={{
                                        fontSize: '0.875rem',
                                        transition: 'all 0.2s ease'
                                    }}
                                >
                                    <div className="d-flex align-items-center gap-2">
                                        <i className="fas fa-tasks me-2 text-info"></i>
                                        Task Berjalan
                                        <span className="badge bg-info ms-2">{item.uncompleted_tasks.length}</span>
                                    </div>
                                </button>
                            </h2>
                            <div 
                                id={`collapse-task-${item.sko_key}`} 
                                className="accordion-collapse collapse"
                                data-bs-parent={`#accordion-${item.sko_key}`}
                            >
                                <div className="accordion-body p-2">
                                    {item.uncompleted_tasks.map((task, index) => (
                                        <div key={index} className="d-flex align-items-center justify-content-between p-2 mb-1 rounded hover-bg-light-subtle">
                                            <div className="d-flex align-items-center gap-3">
                                                <i className="fas fa-circle-notch text-primary fa-spin" style={{ fontSize: '0.6rem' }}></i>
                                                <span className="text-dark fw-medium" style={{ fontSize: '0.875rem' }}>{task.title}</span>
                                            </div>
                                            <div className="text-primary" style={{ fontSize: '0.75rem' }}>
                                                <i className="fas fa-calendar-alt me-1"></i>
                                                {new Date(task.due_date).toLocaleDateString("id-ID", { day: "2-digit", month: "long", year: "numeric" })}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

        </div>
    );
};

export default CardBoard;
