import React, { useEffect, useState } from "react";

const ColoumnHeaderBoard = (props) => {
    const { coloumn, index, dataBoards } = props;
    return (
        <div
            className="col-3"
            style={{
                marginLeft: `${index === 0 ? "40px" : ""}`,
                marginRight: `${
                    index === followups.length - 1 ? "50px" : "0px"
                }`,
            }}
        >
            <div className="p-4" style={{ height: "100%" }}>
                <div className="d-flex align-items-center justify-content-between">
                    <h3>
                        <span
                            className="bg-primary me-2"
                            style={{
                                height: "10px",
                                width: "10px",
                                borderRadius: "50%",
                                display: "inline-block",
                            }}
                        ></span>
                        {coloumn.follow_up}
                        <span className="badge bg-white text-dark ms-3 rounded-circle">
                            {3}
                        </span>
                    </h3>
                    <div
                        role="button"
                        className="btn btn-sm btn-active-color-primary px-2"
                    >
                        <i
                            className="fas fa-ellipsis-v"
                            style={{ fontSize: "1.2rem" }}
                        ></i>
                    </div>
                </div>
                <div className="listCard py-4">
                    {dataBoards
                        .filter((item) => item.status === coloumn.id)
                        .map((item, index) => (
                            <div
                                className="bg-white border border-gray-300 p-5"
                                style={{
                                    borderRadius: "6px",
                                    boxShadow: "0 1px 3px rgba(0, 0, 0, 0.08)",
                                }}
                            >
                                <h5>Pendidikan Agama Islam</h5>
                                <div className="d-flex align-items-center gap-4 text-muted mt-4">
                                    <div className="">
                                        <i className="fas fa-calendar-alt me-2"></i>
                                        12/12/2023
                                    </div>
                                    <div className="">
                                        <i className="fas fa-user me-2"></i>3
                                    </div>
                                </div>
                            </div>
                        ))}
                </div>
            </div>
        </div>
    );
};

export default SalesBoardFollowUpIndex;
