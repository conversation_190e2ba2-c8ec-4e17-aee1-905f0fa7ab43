import React, { useEffect, useState, useCallback, useMemo } from "react";
import SalesBoardContent from "./content";
import { HTML5Backend } from "react-dnd-html5-backend";
import { DndProvider } from "react-dnd";
import ModalDetailBoard from "./content/ModalDetail";
import axios from "axios";
import { useLocation, useNavigate, useSearchParams } from "react-router-dom";
import _ from "lodash";

function SalesBoardIndex() {
    const location = useLocation();
    const Navigate = useNavigate();
    const params = useSearchParams();

    const [activeTab, setActiveTab] = useState("tab_follow_up");
    const [columnHeaders, setColumnHeaders] = useState([]);
    const [dataBoards, setDataBoards] = useState({});
    const [hasLoadedBoard, setHasLoadedBoard] = useState({});
    const [loadingDetail, setLoadingDetail] = useState(false);
    const [filters, setFilters] = useState({
        keyword: "",
        // status: "",
    });

    const listTabs = useMemo(
        () => [
            { id: "tab_follow_up", name: "Follow Up" },
            { id: "tab_deal", name: "Deal" },
            { id: "tab_lost", name: "Lost" },
        ],
        []
    );

    const getColumnHeaders = useCallback(
        (tabId) => {
            switch (tabId) {
                case "tab_follow_up":
                    return follow_up_list;
                case "tab_deal":
                    return deal_list;
                case "tab_lost":
                    return lost_list;
                default:
                    return follow_up_list;
            }
        },
        [follow_up_list, deal_list, lost_list]
    );

    const handleTabClick = useCallback(
        (tabId) => {
            localStorage.setItem("activeTabSalesBoard", tabId);
            setActiveTab(tabId);
            setColumnHeaders(getColumnHeaders(tabId));
        },
        [getColumnHeaders]
    );

    useEffect(() => {
        const storedTab =
            localStorage.getItem("activeTabSalesBoard") || "tab_follow_up";
        setActiveTab(storedTab);
        setColumnHeaders(getColumnHeaders(storedTab));
    }, [getColumnHeaders]);

    const handleSearch = _.debounce((searchTerm) => {
        setFilters((prev) => ({
            ...prev,
            keyword: searchTerm,
        }));
    }, 300);

    const handleSearchInputChange = (event) => {
        const searchTerm = event.target.value;
        handleSearch(searchTerm);
    };

    const [modalDetailBoard, setModalDetailBoard] = useState({
        show: false,
        data: {},
    });

    const getDetailBoard = async (id) => {
        if (!id) return;
        if (loadingDetail || modalDetailBoard?.data?.order_key === id) return;
        setLoadingDetail(true);
        try {
            const { data: response } = await axios.get(
                `/sales/board/order-company/detail/${id}`
            );
            if (response?.status) {
                setModalDetailBoard({
                    show: true,
                    data: response.data,
                });
                const modal = new bootstrap.Modal(
                    document.getElementById("modalDetailBoard")
                );
                modal.show();
            }
        } catch (error) {
            console.error("Error fetching detail board:", error);
        }
        setLoadingDetail(false);
    };

    useEffect(() => {
        const searchParams = new URLSearchParams(location.search);
        const orderKeyFromUrl = searchParams.get("order_key");
        const currentOrderKey = modalDetailBoard.data?.order_key;

        if (orderKeyFromUrl && currentOrderKey !== orderKeyFromUrl) {
            getDetailBoard(orderKeyFromUrl);
        }
    }, [params]);

    useEffect(() => {
        $("#modalDetailBoard").on("hidden.bs.modal", function () {
            onHideModal()
        });
    }, []);

    const onHideModal = () => {
        Navigate(`${location.pathname}`);
        const modal = bootstrap.Modal.getInstance(
            document.getElementById("modalDetailBoard")
        );
        if (modal) {
            modal.hide();
        }
        setModalDetailBoard({ show: false, data: {} });
    }

    return (
        <div id="kt_content_container">
            <div
                className="d-flex align-items-end justify-content-between"
                style={{ paddingLeft: "48px" }}
            >
                <ul
                    className="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-5 fw-bolder mt-n10"
                    id="myTab"
                >
                    {listTabs.map((tab) => (
                        <li className="nav-item" key={tab.id}>
                            <a
                                onClick={() => handleTabClick(tab.id)}
                                className={`nav-link text-white ${
                                    activeTab === tab.id ? "active" : ""
                                }`}
                                role="button"
                            >
                                Board {tab.name}
                            </a>
                        </li>
                    ))}
                </ul>
                <div className="p-4 d-flex align-items-center gap-4">
                    <div className="d-flex align-items-center position-relative">
                        <span className="svg-icon svg-icon-1 position-absolute ms-6">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="none"
                            >
                                <rect
                                    opacity="0.5"
                                    x="17.0365"
                                    y="15.1223"
                                    width="8.15546"
                                    height="2"
                                    rx="1"
                                    transform="rotate(45 17.0365 15.1223)"
                                    fill="black"
                                ></rect>
                                <path
                                    d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                    fill="black"
                                ></path>
                            </svg>
                        </span>
                        <input
                            type="text"
                            data-kt-docs-table-filter="search-order"
                            className="form-control form-control-solid w-250px ps-15"
                            placeholder="Cari order ..."
                            // value={filters.search}
                            onChange={handleSearchInputChange}
                        />
                    </div>
                    <div class="dropdown">
                        <button
                            class="btn btn-secondary"
                            type="button"
                            data-bs-toggle="dropdown"
                            aria-expanded="false"
                        >
                            <i className="fas fa-calendar-alt"></i>
                            Filter Tgl Order
                        </button>
                        <ul class="dropdown-menu dropdown-menu-lg p-4">
                            <div class="row align-items-start">
                                <div class="col-md-6">
                                    <label class="form-label fs-5 fw-bold mb-3">
                                        From
                                    </label>
                                    <input
                                        type="date"
                                        id="filter-dummy-from"
                                        name="tgl_order_from_date"
                                        class="form-control form-control-solid fw-bold"
                                        onChange={(e) => {
                                            const date = e.target.value;
                                            setFilters((prev) => ({
                                                ...prev,
                                                tgl_order_from_date: date,
                                            }));
                                        }}
                                    />
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fs-5 fw-bold mb-3">
                                        To
                                    </label>
                                    <input
                                        type="date"
                                        id="filter-dummy-to"
                                        name="tgl_order_to_date"
                                        class="form-control form-control-solid fw-bold"
                                        onChange={(e) => {
                                            const date = e.target.value;
                                            setFilters((prev) => ({
                                                ...prev,
                                                tgl_order_to_date: date,
                                            }));
                                        }}
                                    />
                                </div>
                            </div>
                            <button
                                className="btn btn-info btn-sm mt-3"
                                onClick={() => {
                                    setFilters((prev) => ({
                                        ...prev,
                                        tgl_order_from_date: "",
                                        tgl_order_to_date: "",
                                    }));
                                }}
                            >
                                Reset
                            </button>
                        </ul>
                    </div>
                    <button
                        onClick={() => {
                            setDataBoards({});
                            setHasLoadedBoard({});
                            setFilters({});
                            $('[name="tgl_order_to_date"]').val("");
                        }}
                        id="kt_refresh_board"
                        className="btn btn-primary btn-sm"
                    >
                        <i className="fas fa-sync"></i>
                    </button>
                </div>
            </div>

            <DndProvider backend={HTML5Backend}>
                <div
                    className="py-8"
                    style={{
                        height: "calc(100vh - 100px)",
                        background: "#F7F7F7",
                        overflowX: "auto",
                    }}
                >
                    <div className="d-flex align-items-start">
                        {columnHeaders.map((column, index) => (
                            <SalesBoardContent
                                key={column.id}
                                column={column}
                                index={index}
                                activeTab={activeTab}
                                // handleChangeBoard={handleChangeBoard}
                                dataBoards={dataBoards}
                                setDataBoards={setDataBoards}
                                hasLoadedBoard={hasLoadedBoard}
                                setHasLoadedBoard={setHasLoadedBoard}
                                filters={filters}
                                getDetailBoard={getDetailBoard}
                            />
                        ))}
                    </div>
                </div>
            </DndProvider>
            <ModalDetailBoard
                show={modalDetailBoard.show}
                data={modalDetailBoard.data}
                onHide={() => {
                    onHideModal()
                }}
            />
            {loadingDetail && (
                <div
                    className="loading-overlay d-flex justify-content-center align-items-center"
                    style={{
                        position: "fixed",
                        top: "50%",
                        left: "50%",
                        transform: "translate(-50%, -50%)",
                        width: "100%",
                        height: "100%",
                        backgroundColor: "rgba(0, 0, 0, 0.25)",
                        zIndex: 9999,
                    }}
                >
                    <div className="card p-4">
                        <div
                            className="spinner-border text-primary"
                            role="status"
                        >
                            <span className="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default React.memo(SalesBoardIndex);
