import { RouterProvider, createBrowserRouter } from "react-router-dom";
import SalesBoardIndex from "../pages/sales/boards/Index";

const router = createBrowserRouter([
    {
        // Component: () => <AppLayout />,
        children: [
            {
                path: "/sales/board",
                element: <SalesBoardIndex />,
            },
        ],
    },
]);

export default function RouteApps() {
    return <RouterProvider router={router} />;
}
