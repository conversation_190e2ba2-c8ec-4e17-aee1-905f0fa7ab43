<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth; // Import the Auth facade
use App\Models\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\SoftDeletes;
class CompanyEngagement extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'tb_engagement_recaps';

    protected $primaryKey = 'id';

    protected $fillable = [
        'company_id',
        'type',
        'title',
        'detail',
        'due_date',
        'created_by',
        'is_completed',
        'completed_at',
        'assign_to',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    public function userAssigned()
    {
        return $this->belongsTo(User::class, 'assign_to', 'id');
    }

    protected static function booted()
    {
        static::creating(function ($company) {
            if (Auth::check()) {
                $company->created_by = Auth::id();  
            }
        });
    }
}
