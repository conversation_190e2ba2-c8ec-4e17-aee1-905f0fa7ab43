<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use \Illuminate\Database\Eloquent\SoftDeletes;

class SuratJalan extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ['id'];

    protected $table = 'tb_surat_jalan';

    public function details()
    {
        return $this->hasMany(SuratJalanDetail::class, 'surat_jalan_key', 'surat_jalan_key');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'id_customer');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by_id', 'id');
    }

    public function scopeFilter($query, $filter)
    {
        $query->when($filter->keyword, function ($query, $status) {
            $query->where(function ($query) use ($status) {
                $query->where('no_surat_jalan', 'like', "%{$status}%")
                    ->orWhere('status', 'like', "%{$status}%")
                    ->orWhere('created_at', 'like', "%{$status}%")
                    ->orWhereHas('customer', function ($query) use ($status) {
                        $query->where('nama', 'like', "%{$status}%")
                            ->orWhereHas('company', function ($query) use ($status) {
                                $query->where('name', 'like', "%{$status}%");
                            });
                    });
            });
        })
        ->when($filter->start_date, function ($query, $startDate) {
            $query->whereDate('delivery_date', '>=', $startDate);
        })
        ->when($filter->end_date, function ($query, $endDate) {
            $query->whereDate('delivery_date', '<=', $endDate);
        });
    }

    public static function generateSuratJalanNumber()
    {
        $year = date('Y');
        $month = date('n');
        $romanMonths = [
            1 => 'I', 2 => 'II', 3 => 'III', 4 => 'IV', 5 => 'V', 
            6 => 'VI', 7 => 'VII', 8 => 'VIII', 9 => 'IX', 10 => 'X', 
            11 => 'XI', 12 => 'XII'
        ];
        $romanMonth = $romanMonths[$month];

        $lastQuotation = self::orderBy('created_at', 'desc')->first();
        $lastNumber = $lastQuotation ? $lastQuotation->no_number : 0;
        $newNumber = sprintf("%04d", $lastNumber + 1);

        return [
            'no_year' => $year,
            'no_roman_month' => $romanMonth,
            'no_number' => $newNumber,
            'no_surat_jalan' => "SJ-{$year}-{$romanMonth}-{$newNumber}"
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->surat_jalan_key)) {
                $model->surat_jalan_key = (string) \Illuminate\Support\Str::uuid();
            }
        });
    }
}
