<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Profile extends Model
{
    use HasFactory;

    protected $table = 'tb_profiles';

    protected $primaryKey = 'id';

    protected $fillable = [
        'id_customer',
        'durasi_berdiri',
        'skala_bisnis',
        'tingkat_penjualan',
        'segmen_pasar',
        'channel_marketing',
        'nama_campaign',
        'anniv_usaha',
        'imlek',
        'idul_fitri',
        'natal',
        'keb_packaging',
        'potensi_freq',
        'profiling',
        'direct_meeting'
    ];
}
