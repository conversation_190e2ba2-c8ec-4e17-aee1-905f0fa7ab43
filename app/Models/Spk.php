<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Spk extends Model
{
    use HasFactory;

    protected $table = 'tb_spks';

    protected $primaryKey = 'id_spk';

    protected $fillable = [
        'order_key',
        'sko_key',
        'tgl_deadline',
        'catatan_khusus',
        'pisaupond',
        'klisepoly',
        'kliseemboss',
        'klisespotuv',
        'ctv',
        'kertas',
        'filmpisau',
        'bahanfinishing',
        'harga1',
        'harga2',
        'harga3',
        'harga4',
        'harga5',
        'harga6',
        'harga7',
        'harga8',
        'harga_pracetak',
        'harga_cetak',
        'harga_laminasi',
        'harga_poly',
        'harga_emboss',
        'harga_spotuv',
        'harga_lapis',
        'harga_jendelamika',
        'harga_pond',
        'harga_finishing',
        'harga_dummy',
        'harga_ve',
        'progress_produksi_1',
        'jumlah_plano',
        'vendor_1',
        'tgl_produksi_1',
        'tgl_selesai_1',
        'pic_validasi_1',
        'flag_status_1',
        'tgl_flag_status_1',
        'catatan_1',
        'check_1_1',
        'check_1_2',
        'check_1_3',
        'check_1_4',
        'progress_produksi_2',
        'jumlah_awal_pp_2',
        'jumlah_hasil_pp_2',
        'reject_pp_2',
        'vendor_2',
        'tgl_produksi_2',
        'tgl_selesai_2',
        'pic_validasi_2',
        'flag_status_2',
        'tgl_flag_status_2',
        'catatan_2',
        'check_2_1',
        'check_2_2',
        'check_2_3',
        'check_2_4',
        'progress_produksi_3',
        'jumlah_awal_pp_3',
        'jumlah_hasil_pp_3',
        'reject_pp_3',
        'vendor_3',
        'tgl_produksi_3',
        'tgl_selesai_3',
        'pic_validasi_3',
        'flag_status_3',
        'tgl_flag_status_3',
        'catatan_3',
        'check_3_1',
        'check_3_2',
        'check_3_3',
        'check_3_4',
        'progress_produksi_4',
        'jumlah_awal_pp_4',
        'jumlah_hasil_pp_4',
        'reject_pp_4',
        'vendor_4',
        'tgl_produksi_4',
        'tgl_selesai_4',
        'pic_validasi_4',
        'flag_status_4',
        'tgl_flag_status_4',
        'catatan_4',
        'check_4_1',
        'check_4_2',
        'check_4_3',
        'check_4_4',
        'check_4_5',
        'progress_produksi_5',
        'jumlah_awal_pp_5',
        'jumlah_hasil_pp_5',
        'reject_pp_5',
        'vendor_5',
        'tgl_produksi_5',
        'tgl_selesai_5',
        'pic_validasi_5',
        'flag_status_5',
        'tgl_flag_status_5',
        'catatan_5',
        'check_5_1',
        'check_5_2',
        'check_5_3',
        'check_5_4',
        'check_5_5',
        'progress_produksi_6',
        'jumlah_awal_pp_6',
        'jumlah_hasil_pp_6',
        'reject_pp_6',
        'vendor_6',
        'tgl_produksi_6',
        'tgl_selesai_6',
        'pic_validasi_6',
        'flag_status_6',
        'tgl_flag_status_6',
        'catatan_6',
        'check_6_1',
        'check_6_2',
        'check_6_3',
        'check_6_4',
        'progress_produksi_7',
        'jumlah_awal_pp_7',
        'jumlah_hasil_pp_7',
        'reject_pp_7',
        'vendor_7',
        'tgl_produksi_7',
        'tgl_selesai_7',
        'pic_validasi_7',
        'flag_status_7',
        'tgl_flag_status_7',
        'catatan_7',
        'check_7_1',
        'check_7_2',
        'progress_produksi_8',
        'jumlah_awal_pp_8',
        'jumlah_hasil_pp_8',
        'reject_pp_8',
        'vendor_8',
        'tgl_produksi_8',
        'tgl_selesai_8',
        'pic_validasi_8',
        'flag_status_8',
        'tgl_flag_status_8',
        'catatan_8',
        'check_8_1',
        'check_8_2',
        'check_8_3',
        'check_8_4',
        'check_8_5',
        'progress_produksi_9',
        'jumlah_awal_pp_9',
        'jumlah_hasil_pp_9',
        'reject_pp_9',
        'vendor_9',
        'tgl_produksi_9',
        'tgl_selesai_9',
        'pic_validasi_9',
        'flag_status_9',
        'tgl_flag_status_9',
        'catatan_9',
        'check_9_1',
        'check_9_2',
        'check_9_3',
        'check_9_4',
        'check_9_5',
        'progress_produksi_10',
        'jumlah_awal_pp_10',
        'jumlah_hasil_pp_10',
        'reject_pp_10',
        'vendor_10',
        'tgl_produksi_10',
        'tgl_selesai_10',
        'pic_validasi_10',
        'flag_status_10',
        'tgl_flag_status_10',
        'catatan_10',
        'check_10_1',
        'check_10_2',
        'check_10_3',
        'check_10_4',
        'check_10_5',
        'progress_produksi_dummy',
        'check_dummy_1',
        'check_dummy_2',
        'check_dummy_3',
        'check_dummy_4',
        'check_dummy_5',
        'check_dummy_6',
        'check_dummy_7',
        'flag_status_dummy',
        'tgl_flag_status_dummy',
        'progress_produksi_ve',
        'jumlah_awal_pp_ve',
        'jumlah_hasil_pp_ve',
        'reject_pp_ve',
        'vendor_ve',
        'tgl_produksi_ve',
        'tgl_selesai_ve',
        'pic_validasi_ve',
        'check_ve_1',
        'check_ve_2',
        'check_ve_3',
        'check_ve_4',
        'catatan_ve',
        'flag_status_ve',
        'tgl_flag_status_ve',
        'jumlah_fix',
        'tgl_kirim',
        'tgl_selesai_all',
        'total_keseluruhan_harga'
    ];
}
