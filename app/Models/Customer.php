<?php

namespace App\Models;

use App\Models\Company;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Customer extends Model
{
    use HasFactory;

    protected $table = 'tb_customers';

    protected $primaryKey = 'id_customer';

    protected $fillable = [
        'kode_kustomer',
        'nama',
        'grading',
        'no_hp',
        'email_bisnis',
        'posisi_pic_cust',
        'jenis_usaha',
        'tipe_instansi',
        'alamat',
        'tipe_instansi',
        'nama_instansi',
        'alamat_instansi',
        'npwp',
        'nama_brand',
        'company_id'
    ];

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function orders()
    {
        return $this->hasMany(Order::class, 'id_customer', 'customer_id');
    }
}
