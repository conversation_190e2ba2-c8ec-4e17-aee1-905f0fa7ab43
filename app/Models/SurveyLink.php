<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use \Illuminate\Database\Eloquent\SoftDeletes;

class SurveyLink extends Model
{
    use HasFactory,SoftDeletes;

    protected $table = 'tb_survey_link';

    protected $primaryKey = 'id';

    protected $fillable = [
        'order_id',
        'survey_id',
        'generate_link',
        'submited_date',
        'submited_by'
    ];

    public function survey()
    {
        return $this->belongsTo(Survey::class, 'survey_id', 'id');
    }

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id', 'id_order');
    }

    public function responses()
    {
        return $this->hasMany(SurveyResponses::class, 'survey_link_id', 'id');
    }
}
