<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class DraftHitungan extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'tb_draft_hitungan';

    protected $primaryKey = 'id';

    protected $fillable = [
        'code',
        'id_layout',
        'id_bahan',
        'id_jenis_kertas',
        'id_kertas_plano',
        'id_gramasi',
        'dimensi_p',
        'dimensi_l',
        'dimensi_t',
        'luas_permukaan',
        'luas_kertas',
        'detail',
        'jumlah_potong',
        'waste',
        'kebutuhan_kertas',
        'additional_cost',
        'description',
        'quantity',
        'harga_modal',
        'type',
        'status',
        'id_produksi',
        'order_key',
        'sko_key',
    ];

    public function tb_jenis_kertas()
    {
        return $this->belongsTo(JenisKertas::class, 'id_jenis_kertas');
    }

    public function tb_bahans()
    {
        return $this->belongsTo(Bahan::class, 'id_bahan', 'id_bahans');
    }

    public function tb_kertas_plano()
    {
        return $this->belongsTo(KertasPlano::class, 'id_kertas_plano');
    }

    public function tb_gramasi()
    {
        return $this->belongsTo(Gramasi::class, 'id_gramasi');
    }

    public function tb_layout()
    {
        return $this->belongsTo(Layout::class, 'id_layout');
    }

    public function tb_produksi()
    {
        return $this->belongsTo(Produksi::class, 'id_produksi');
    }

    public function tb_orders()
    {
        return $this->belongsTo(Order::class, 'sko_key', 'sko_key');
    }
}
