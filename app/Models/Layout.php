<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Layout extends Model
{
    use HasFactory;

    protected $table = 'tb_layouts';

    protected $primaryKey = 'id_layout';

    protected $fillable = [
        'kode_layout',
        'nama_layout',
        'id_kategori_layout',
        'path_gambar_produk',
        'path_gambar_layout',
        'jenis_box',
        'dimensi_tinggi',
        'dimensi_lebar',
        'dimensi_panjang',
        'tambahan_sisi_kertas',
        'rumus_lebar_cetak_1_angka_1',
        'rumus_lebar_cetak_1_parameter_1',
        'rumus_lebar_cetak_1_angka_2',
        'rumus_lebar_cetak_1_parameter_2',
        'rumus_lebar_cetak_1_angka_3',
        'rumus_lebar_cetak_1_parameter_3',
        'rumus_lebar_cetak_1_angka_4',
        'rumus_lebar_cetak_1_parameter_4',
        'rumus_panjang_cetak_1_angka_1',
        'rumus_panjang_cetak_1_parameter_1',
        'rumus_panjang_cetak_1_angka_2',
        'rumus_panjang_cetak_1_parameter_2',
        'rumus_panjang_cetak_1_angka_3',
        'rumus_panjang_cetak_1_parameter_3',
        'rumus_panjang_cetak_1_angka_4',
        'rumus_panjang_cetak_1_parameter_4',
        'rumus_lebar_cetak_2_angka_1',
        'rumus_lebar_cetak_2_parameter_1',
        'rumus_lebar_cetak_2_angka_2',
        'rumus_lebar_cetak_2_parameter_2',
        'rumus_lebar_cetak_2_angka_3',
        'rumus_lebar_cetak_2_parameter_3',
        'rumus_lebar_cetak_2_angka_4',
        'rumus_lebar_cetak_2_parameter_4',
        'rumus_panjang_cetak_2_angka_1',
        'rumus_panjang_cetak_2_parameter_1',
        'rumus_panjang_cetak_2_angka_2',
        'rumus_panjang_cetak_2_parameter_2',
        'rumus_panjang_cetak_2_angka_3',
        'rumus_panjang_cetak_2_parameter_3',
        'rumus_panjang_cetak_2_angka_4',
        'rumus_panjang_cetak_2_parameter_4',
        'rumus_lebar_cetak_3_angka_1',
        'rumus_lebar_cetak_3_parameter_1',
        'rumus_lebar_cetak_3_angka_2',
        'rumus_lebar_cetak_3_parameter_2',
        'rumus_lebar_cetak_3_angka_3',
        'rumus_lebar_cetak_3_parameter_3',
        'rumus_lebar_cetak_3_angka_4',
        'rumus_lebar_cetak_3_parameter_4',
        'rumus_panjang_cetak_3_angka_1',
        'rumus_panjang_cetak_3_parameter_1',
        'rumus_panjang_cetak_3_angka_2',
        'rumus_panjang_cetak_3_parameter_2',
        'rumus_panjang_cetak_3_angka_3',
        'rumus_panjang_cetak_3_parameter_3',
        'rumus_panjang_cetak_3_angka_4',
        'rumus_panjang_cetak_3_parameter_4',
        'tipe_rumus'
    ];
}
