<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class SuratJalanDetail extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    protected $table = 'tb_surat_jalan_detail';

    public function tb_surat_jalan()
    {
        return $this->belongsTo(SuratJalan::class, 'surat_jalan_key', 'surat_jalan_key');
    }
}
