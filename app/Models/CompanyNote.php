<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth; // Import the Auth facade
use App\Models\Company;
use App\Models\User;

class CompanyNote extends Model
{
    use HasFactory;

    protected $table = 'tb_company_notes';

    protected $primaryKey = 'id';

    protected $fillable = [
        'company_id',
        'message',
        'created_by',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'created_by', 'id');
    }

    protected static function booted()
    {
        static::creating(function ($company) {
            if (Auth::check()) {
                $company->created_by = Auth::id();  
            }
        });
    }
}
