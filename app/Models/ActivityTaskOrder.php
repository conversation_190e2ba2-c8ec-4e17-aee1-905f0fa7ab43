<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ActivityTaskOrder extends Model
{
    use HasFactory;
    protected $guarded = ['id'];

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by_id');
    }


    public function scopeFilter($query, $request)
    {
        $query->when($request->keyword, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                return $query->where('order_key', 'like', "%$search%")
                    ->orWHere('title', 'like', "%$search%")
                    ->orWhere('detail', 'like', "%$search%")
                    ->orWhere('due_date', 'like', "%$search%")
                    ->orWhere('completed_at', 'like', "%$search%");
            });
        });
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) \Illuminate\Support\Str::uuid();
            }
        });
    }
}
