<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use \Illuminate\Database\Eloquent\SoftDeletes;

class SurveyResponses extends Model
{
    use HasFactory,SoftDeletes;

    protected $table = 'tb_responses';

    protected $primaryKey = 'id';

    protected $fillable = [
        'survey_link_id',
        'survey_question_id',
        'question_text',
        'option_text',
        'bobot',
        'answer'
    ];

    public function survey_link()
    {
        return $this->belongsTo(SurveyLink::class, 'survey_link_id', 'id');
    }
}
