<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Layout;
class KategoriLayout extends Model
{
    use HasFactory;

    protected $table = 'tb_kategori_layouts';

    protected $primaryKey = 'id_kategori';

    protected $fillable = [
        'nama_kategori',
        'keterangan'
    ];

    public function tb_layouts()
    {
        return $this->hasMany(Layout::class, 'id_kategori_layout', 'id_kategori');
    }

    public function getCountLayout(){
        return  Layout::where(['id_kategori_layout' => $this->attributes['id_kategori']])->count();
    }
}
