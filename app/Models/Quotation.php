<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Quotation extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    public function details()
    {
        return $this->hasMany(QuotationDetail::class, 'quotation_key', 'quotation_key');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'id_customer');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by_id', 'id');
    }

    public function scopeFilter($query, $filter)
    {
        $query->when($filter->keyword, function ($query, $status) {
            $query->where(function ($query) use ($status) {
                $query->where('no_quotation', 'like', "%{$status}%")
                    ->orWhere('total_price', 'like', "%{$status}%")
                    ->orWhere('status', 'like', "%{$status}%")
                    ->orWhere('created_at', 'like', "%{$status}%")
                    ->orWhereHas('customer', function ($query) use ($status) {
                        $query->where('nama', 'like', "%{$status}%")
                            ->orWhereHas('company', function ($query) use ($status) {
                                $query->where('name', 'like', "%{$status}%");
                            });
                    });
            });
        })
        ->when($filter->start_date, function ($query, $startDate) {
            $query->whereDate('created_at', '>=', $startDate);
        })
        ->when($filter->end_date, function ($query, $endDate) {
            $query->whereDate('created_at', '<=', $endDate);
        });
    }

    public static function generateQuotationNumber()
    {
        $year = date('Y');
        $month = date('n');
        $romanMonths = [
            1 => 'I', 2 => 'II', 3 => 'III', 4 => 'IV', 5 => 'V', 
            6 => 'VI', 7 => 'VII', 8 => 'VIII', 9 => 'IX', 10 => 'X', 
            11 => 'XI', 12 => 'XII'
        ];
        $romanMonth = $romanMonths[$month];

        $lastQuotation = self::orderBy('created_at', 'desc')->first();
        $lastNumber = $lastQuotation ? $lastQuotation->no_number : 0;
        $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);

        return [
            'no_year' => $year,
            'no_roman_month' => $romanMonth,
            'no_number' => $newNumber,
            'no_quotation' => "SPH-RSP-{$year}-{$romanMonth}-{$newNumber}"
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->quotation_key)) {
                $model->quotation_key = (string) \Illuminate\Support\Str::uuid();
            }
        });
    }
}
