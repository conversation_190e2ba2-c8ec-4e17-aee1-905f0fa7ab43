<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OtherPrice extends Model
{
    use HasFactory;

    protected $table = 'tb_other_price';

    protected $primaryKey = 'id_other_price';

    protected $fillable = [
        'id_other_price',
        'id_jenis_ongkos',
        'id_tools',
        'id_bahans',
        'price_per_item',
        'price_minimum',
        'is_optional',
        'quantity',
        'tipe_rumus'
    ];

    public function tb_jenis_ongkos()
    {
        return $this->belongsTo(JenisOngkos::class, 'id_jenis_ongkos');
    }

    public function tb_tools()
    {
        return $this->belongsTo(Tools::class, 'id_tools');
    }

    public function tb_bahans()
    {
        return $this->belongsTo(Bahan::class, 'id_bahans');
    }
}
