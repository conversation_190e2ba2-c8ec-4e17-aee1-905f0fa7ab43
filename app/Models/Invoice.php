<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Invoice extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    public function details()
    {
        return $this->hasMany(InvoiceDetail::class, 'invoice_key', 'invoice_key');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class, 'customer_id', 'id_customer');
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by_id', 'id');
    }

    public function scopeFilter($query, $filter)
    {
        $query->when($filter->keyword, function ($query, $status) {
            $query->where(function ($query) use ($status) {
                $query->where('no_invoice', 'like', "%{$status}%")
                    ->orWhere('total', 'like', "%{$status}%")
                    ->orWhere('status', 'like', "%{$status}%")
                    ->orWhere('created_at', 'like', "%{$status}%")
                    ->orWhere('sisa_pelunasan', 'like', "%{$status}%")
                    ->orWhereHas('customer', function ($query) use ($status) {
                        $query->where('nama', 'like', "%{$status}%")
                            ->orWhereHas('company', function ($query) use ($status) {
                                $query->where('name', 'like', "%{$status}%");
                            });
                    })
                    ->orWhereHas('details', function ($query) use ($status) {
                        $query->where('kode_order', 'like', "%{$status}%")
                            ->orWhere('product_name', 'like', "%{$status}%");
                    });
            });
        })
            ->when($filter->start_date, function ($query, $startDate) {
                $query->whereDate('created_at', '>=', $startDate);
            })
            ->when($filter->end_date, function ($query, $endDate) {
                $query->whereDate('created_at', '<=', $endDate);
            })
            ->when($filter->status_jatuh_tempo, function ($query, $statusJatuhTempo) {
                switch (intval($statusJatuhTempo)) {
                    case 1:
                        $query->where(function($q) {
                            $q->where('tanggal_jatuh_tempo', '>=', now())
                              ->orWhere('tanggal_jatuh_tempo_tambahan', '>=', now());
                        })
                             ->where('sisa_pelunasan', '>', 0);
                        break;
                    case 2:
                        $query->where('tanggal_jatuh_tempo', '<=', now())
                             ->where('sisa_pelunasan', '>', 0);
                        break;
                    case 3:
                        $query->where('sisa_pelunasan', '=', 0);
                        break;
                }

                return $query;
            })
            ->when($filter->type, function ($query, $status) {
                $query->where('type', $status);
            })
            ->when($filter->pic, function ($query, $pic) {
                $query->where('created_by_id', $pic);
            });
    }

    public static function generateInvoiceNumber($type = 'sales')
    {
        $year = date('Y');
        $month = date('n');
        $romanMonths = [
            1 => 'I',
            2 => 'II',
            3 => 'III',
            4 => 'IV',
            5 => 'V',
            6 => 'VI',
            7 => 'VII',
            8 => 'VIII',
            9 => 'IX',
            10 => 'X',
            11 => 'XI',
            12 => 'XII'
        ];
        $romanMonth = $romanMonths[$month];

        $lastInvoice = self::orderBy('created_at', 'desc')->firstWhere('invoice_type', $type);
        $lastNumber = $lastInvoice ? $lastInvoice->no_number : 0;
        $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        $prefix = $type === 'sales' ? 'SI' : 'PI';

        return [
            'no_year' => $year,
            'no_roman_month' => $romanMonth,
            'no_number' => $newNumber,
            'no_invoice' => "{$prefix}-RSP-{$year}-{$romanMonth}-{$newNumber}"
        ];
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->invoice_key)) {
                $model->invoice_key = (string) \Illuminate\Support\Str::uuid();
            }
        });
    }
}
