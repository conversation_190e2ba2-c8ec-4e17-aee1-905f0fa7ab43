<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON><PERSON><PERSON>\LaravelIdGenerator\IdGenerator;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Order extends Model
{
    use HasFactory;

    protected $table = 'tb_orders';

    protected $primaryKey = 'id_order';

    protected $fillable = [
        'id_customer',
        'order_key',
        'sko_key',
        'sko',
        'produk_ke',
        'id_pic',
        'sumber',
        'tgl_order',
        'waktu_kontak',
        'pp_harga',
        'pp_waktu',
        'pp_pengiriman',
        'pp_kualitas',
        'pp_pembayaran',
        'tgl_jatuh_tempo',
        'status_deal',
        'status_order',
        'tipe_kontak',
        'grading',
        'DP',
        'flag_lunas',
        'tgl_pelunasan',
        'catatan_order',
        'flag_dummy',
        'follow_up_terakhir',
        'survey_order',
        'lead_prospek',
        'is_forecast',
        'forecast_month',
        'forecast_year',
        'forecast_week',
        'expected_revenue',
        'updated_at',
        'no_po',
        'link_dokumen_order'
    ];

    /**
     * Get the user that owns the Order
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function tb_customer()
    {
        return $this->belongsTo(Customer::class, 'id_customer', 'id_customer');
    }

    public function tb_produksi()
    {
        return $this->belongsTo(Produksi::class, 'sko_key', 'sko_key');
    }

    public function tb_faw()
    {
        return $this->belongsTo(Faw::class, 'sko_key', 'sko_key');
    }

    public function tb_draft()
    {
        return $this->belongsTo(DraftHitungan::class, 'sko_key', 'sko_key');
    }

    public function activityTaskOrder()
    {
        return $this->hasMany(ActivityTaskOrder::class, 'order_key', 'order_key');
    }
    
    public function user()
    {
        return $this->belongsTo(User::class, 'id_pic', 'id');
    }

    public function scopeFilter($query, $request)
    {
       return $query->when($request->status, function ($query, $status) {
            $status_deal = '';
            switch ($status) {
                case 'follow_up':
                    $status_deal = "Follow Up";
                    break;
                case 'deal':
                    $status_deal = "Deal";
                    break;
                case 'lost':
                    $status_deal = "Lost";
                    break;
            }
            $query->where('status_deal', $status_deal);
        })
        ->when($request->status_order, function ($query, $status_order) {
            $query->where('status_order', $status_order);
        })
        ->when($request->tgl_order_to_date || $request->tgl_order_from_date, function ($query) use ($request) {
            if ($request->tgl_order_from_date) {
                $query->whereDate('waktu_kontak', '>=', $request->tgl_order_from_date);
            } elseif ($request->tgl_order_to_date) {
                $query->whereDate('waktu_kontak', '<=', $request->tgl_order_to_date);
            }
        })
        ->when($request->keyword, function ($query, $search) {
            $query->where(function ($query) use ($search) {
                $query->whereHas('tb_customer', function ($query) use ($search) {
                    $query->where('nama', 'like', "%{$search}%");
                })
                ->orWhere('tb_orders.sko', 'like', "%{$search}%")
                ->orWhere('tb_orders.sko_key', 'like', "%{$search}%")
                ->orWhere('tb_orders.id_order', 'like', "%{$search}%")
                ->orWhereHas('tb_produksi', function ($query) use ($search) {
                    $query->where('nama_produk', 'like', "%{$search}%");
                });
            });
        })
        ->when($request->year, function ($query, $year) {
            $query->whereYear('waktu_kontak', $year);
        })->when($request->pic, function ($query, $pic) {
            $query->where('id_pic', $pic);
        })->when($request->status_deal, function ($query, $status_deal) {
            $query->where('status_deal', $status_deal);
        })->when($request->status_order, function ($query, $status_order) {
            $query->where('status_order', $status_order);
        });
    }

    public function tb_surat_jalan_detail()
    {
        return $this->hasMany(SuratJalanDetail::class, 'kode_order', 'sko')
                ->whereHas('tb_surat_jalan', function ($query) {
                    $query->where('status', 'submitted')
                          ->orderBy('delivery_date', 'asc');
                });
    }

    public function invoice_details()
    {
        return $this->hasMany(InvoiceDetail::class, 'kode_order', 'sko')
                ->whereHas('invoices', function ($query) {
                    $query->where('status', 'submitted')->orderBy('created_at', 'asc');
                });
    }

    public function uncompleted_tasks()
    {
        return $this->hasMany(ActivityTaskOrder::class, 'order_key', 'order_key')->whereNull('completed_at')->orderBy('due_date', 'asc');
    }

    public function tb_spks()
    {
        return $this->belongsTo(Spk::class, 'sko_key', 'sko_key');
    }
}
