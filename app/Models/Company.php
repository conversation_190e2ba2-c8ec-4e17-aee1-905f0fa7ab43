<?php

namespace App\Models;

use App\Models\Village;
use App\Models\District;
use App\Models\Province; // Import the Auth facade
use App\Models\Subdistrict;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Company extends Model
{
    use HasFactory;

    protected $table = 'tb_company';

    protected $primaryKey = 'id';

    protected $fillable = [
        'code',
        'name',
        'instansi_id',
        'business_sector',
        'email',
        'website',
        'npwp',
        'province_id',
        'district_id',
        'sub_district_id',
        'village_id',
        'zip_code',
        'address'
    ];

    public function province()
    {
        return $this->belongsTo(Province::class, 'province_id', 'id');
    }

    public function district()
    {
        return $this->belongsTo(District::class, 'district_id', 'id');
    }

    public function subdistrict()
    {
        return $this->belongsTo(Subdistrict::class, 'sub_district_id', 'id');
    }

    public function village()
    {
        return $this->belongsTo(Village::class, 'village_id', 'id');
    }

    public function customers()
    {
        return $this->hasMany(Customer::class, 'company_id', 'id');
    }

    protected static function booted()
    {
        static::creating(function ($company) {
            if (Auth::check()) {
                $company->created_by = Auth::id();  
            }
        });

        static::updating(function ($company) {
            if (Auth::check()) {
                $company->updated_by = Auth::id();
            }
        });
    }
}
