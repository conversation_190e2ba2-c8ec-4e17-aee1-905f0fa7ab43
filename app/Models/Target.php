<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Target extends Model
{
    use HasFactory;

    protected $table = 'tb_targets';

    protected $primaryKey = 'id_target';

    protected $fillable = [
        'id_pic',
        'year_target',
        'target_01',
        'target_02',
        'target_03',
        'target_04',
        'target_05',
        'target_06',
        'target_07',
        'target_08',
        'target_09',
        'target_10',
        'target_11',
        'target_12',
    ];
}
