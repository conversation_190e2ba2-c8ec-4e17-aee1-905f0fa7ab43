<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use \Illuminate\Database\Eloquent\SoftDeletes;

class SalesTarget extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = ['id'];

    protected $table = 'tb_target_sales';

    public function users()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    // public function scopeFilter($query, $filter)
    // {
    //     $query->when($filter->keyword, function ($query, $status) {
    //         $query->where(function ($query) use ($status) {
    //             $query->where('no_surat_jalan', 'like', "%{$status}%")
    //                 ->orWhere('status', 'like', "%{$status}%")
    //                 ->orWhere('created_at', 'like', "%{$status}%")
    //                 ->orWhereHas('customer', function ($query) use ($status) {
    //                     $query->where('nama', 'like', "%{$status}%")
    //                         ->orWhereHas('company', function ($query) use ($status) {
    //                             $query->where('name', 'like', "%{$status}%");
    //                         });
    //                 });
    //         });
    //     });
    // }

    protected static function boot()
    {
        parent::boot();

        // static::creating(function ($model) {
        //     if (empty($model->surat_jalan_key)) {
        //         $model->surat_jalan_key = (string) \Illuminate\Support\Str::uuid();
        //     }
        // });
    }
}
