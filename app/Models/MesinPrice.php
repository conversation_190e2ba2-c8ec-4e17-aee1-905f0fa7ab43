<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MesinPrice extends Model
{
    use HasFactory;

    protected $table = 'tb_mesin_print_price';

    protected $primaryKey = 'id_mesin_print_price';

    protected $fillable = [
        'id_mesin_print_price',
        'id_mesin',
        'type_color',
        'quantity',
        'price'
    ];

    public function tb_mesin()
    {
        return $this->belongsTo(Mesin::class, 'id_mesin');
    }

    public function color_type(){
        if ($this->attributes['type_color'] == 1) {
            return '1 Warna';
        } else if ($this->attributes['type_color'] == 2) {
            return '2 Warna';
        } else {
            return 'Full Warna';
        }
    }
}
