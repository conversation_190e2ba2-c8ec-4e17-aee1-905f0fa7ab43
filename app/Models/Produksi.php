<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\Customer;
class Produksi extends Model
{
    use HasFactory;

    protected $table = 'tb_produksis';

    protected $primaryKey = 'id_produksi';

    // protected $casts = [
    //     'finishing' => 'array',
    // ];

    protected $fillable = [
        'id_order',
        'order_key',
        'sko_key',
        'tgl_produksi',
        'kategori_produksi',
        'jenis_bahan',
        'spesifikasi',
        'finishing',
        'gramasi',
        'laminasi',
        'sisi_laminasi',
        'jenis_kertas',
        'lp_panjang',
        'lp_lebar',
        'dp_panjang',
        'dp_lebar',
        'dp_tinggi',
        'tipe_produk',
        'isi_kertas',
        'get_plano',
        'jumlah_produk',
        'harga_produk',
        'total_harga',
        'ppn',
        'ppn_percent',
        'total_ppn',
        'modal_sales',
        'progress_produksi',
        'status_produksi',
        'reject',
        'vendor',
        'tgl_deadline',
        'DP',
        'notes_lost_dp',
        'status_lunas',
        'hitung_harga_khusus',
        'notes',
        'ukuran_kode',
        'kendala_produksi',
        'link_file_final',
        'lampiran_path',
        'lampiran_filename',
        'nama_produk',
    ];
}
