<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaperPrice extends Model
{
    use HasFactory;

    protected $table = 'tb_paper_price';

    protected $primaryKey = 'id_paper_price';

    protected $fillable = [
        'id_paper_price',
        'id_jenis_kertas',
        'id_kertas_plano',
        'id_gramasi',
        'price'
    ];

    public function tb_jenis_kertas(){
        return $this->belongsTo(JenisKertas::class,'id_jenis_kertas');
    }

    public function tb_kertas_planos(){
        return $this->belongsTo(KertasPlano::class,'id_kertas_plano');
    }

    public function tb_gramasi(){
        return $this->belongsTo(Gramasi::class,'id_gramasi');
    }
}
