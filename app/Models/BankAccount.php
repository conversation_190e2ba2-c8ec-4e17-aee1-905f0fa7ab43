<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use \Illuminate\Database\Eloquent\SoftDeletes;

class BankAccount extends Model
{
    use HasFactory,SoftDeletes;

    protected $table = 'tb_bank_account';

    protected $primaryKey = 'id';

    protected $fillable = [
        'bank_account_name',
        'bank_account_no',
        'bank_name',
        'is_ppn',
        'status'
    ];
}
