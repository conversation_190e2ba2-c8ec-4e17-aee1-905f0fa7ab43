<?php

namespace Lib;

class softbox
{
    protected static $luas_permukaan_cetak_panjang; 
    protected static $luas_permukaan_cetak_lebar; 
    protected static $P1; //Panjang Plano 
    protected static $L1; //Labar Plano
    protected static $P2; //luas Panjang <PERSON>
    protected static $L2; //luas <PERSON>bar <PERSON>
    protected static $P3; 
    protected static $P4;
    protected static $L3;
    protected static $jumlah_potong;
    protected static $percentage_waste;

    public function __construct($luas_permukaan_cetak_panjang = 0,$luas_permukaan_cetak_lebar = 0,$p_plano,$l_plano, $data = [])
    {
        self::$luas_permukaan_cetak_panjang = $luas_permukaan_cetak_panjang;
        self::$luas_permukaan_cetak_lebar = $luas_permukaan_cetak_lebar;
        self::$P1 = $p_plano;
        self::$L1 = $l_plano;
    }

    public static function panjang_luas_permukaan($p,$l,$lem){
        $panjang_luas_permukaan = ($p*2)+($l*2)+$lem;
        return $panjang_luas_permukaan;
    }

    public static function lebar_luas_permukaan($l,$t,$klip){
        $lebar_luas_permukaan = ($l*2)+$t+($klip*2);
        return $lebar_luas_permukaan;
    }

    public static function panjang_luas_kemasan($p2){
        $panjang_luas_kemasan = $p2+2;
        return $panjang_luas_kemasan;
    }

    public static function lebar_luas_kemasan($l2){
        $lebar_luas_kemasan = $l2+2;
        return $lebar_luas_kemasan;
    }

    public static function P3($p1,$p2){
        $p3 = $p1-($p2*($p1/$p2));
        return $p3;
    }

    public static function P4($p1,$l2){
        $p4 = $p1-$l2;
        return $p4;
    }

    public static function L3($l1,$l2){
        $l3 = $l1-($l2*($l1/$l2));
        return $l3;
    }

    public static function combi_1($data = []){
        $panjang_luas_permukaan = self::$luas_permukaan_cetak_panjang;
        $lebar_luas_permukaan = self::$luas_permukaan_cetak_lebar;

        self::$P2 = self::panjang_luas_kemasan($panjang_luas_permukaan);
        self::$L2 = self::lebar_luas_kemasan($lebar_luas_permukaan);
        if ($data) {
            $panjang_luas_permukaan = $data['panjang_cetak'];
            $lebar_luas_permukaan = $data['lebar_cetak'];

            self::$P2 = $data['panjang_kemasan'];
            self::$L2 = $data['lebar_kemasan'];
        }
        self::$P3 = self::P3(self::$P1,self::$P2);
        self::$P4 = self::P4(self::$P1,self::$L2);
        self::$L3 = self::L3(self::$L1,self::$L2);

        $step_1 = floor(self::$L1/self::$P2 * 1);
        $step_2 = floor(self::$P4/self::$P2) * floor(self::$L1/self::$L2);
        $step_4 = floor(self::$P4/self::$L2 * 1);
        $step_5 = floor(self::$L3/self::$P2) * floor(self::$P4/self::$L2);
        $step_3 = self::$L3/self::$P2;
        if ($step_3 < 1) {
            $step_3 = 0;
        } else if ($step_3 == 1 || $step_3 == 2) {
            $step_3 = $step_4;
        } else if ($step_3 > 2) {
            $step_3 = $step_5;
        }
        
        self::$jumlah_potong = $step_1+$step_2+$step_3;
        self::$percentage_waste = self::hitungSisaKertas(self::$P1,self::$L1,self::$P2,self::$L2,self::$jumlah_potong);

        $data = [
            'p_cetak'          => $panjang_luas_permukaan,
            'l_cetak'          => $lebar_luas_permukaan,
            'p_kemasan'        => self::$P2,
            'l_kemasan'        => self::$L2,
            'jumlah_potong'    => self::$jumlah_potong,
            'percentage_waste' => self::$percentage_waste
        ];

        return $data;
    }

    public static function combi_2($data = []){
        $panjang_luas_permukaan = self::$luas_permukaan_cetak_panjang;
        $lebar_luas_permukaan = self::$luas_permukaan_cetak_lebar;

        self::$P2 = self::panjang_luas_kemasan($panjang_luas_permukaan);
        self::$L2 = self::lebar_luas_kemasan($lebar_luas_permukaan);
        if ($data) {
            $panjang_luas_permukaan = $data['panjang_cetak'];
            $lebar_luas_permukaan = $data['lebar_cetak'];

            self::$P2 = $data['panjang_kemasan'];
            self::$L2 = $data['lebar_kemasan'];
        }
        self::$L3 = self::L3(self::$L1,self::$L2);
        
        $step_1 = floor(self::$P1/self::$P2) * floor(self::$L1/self::$L2);
        $step_3 = floor(self::$P1/self::$L2 * 1);
        $step_4 = floor(self::$L3/self::$P2) * floor(self::$P1/self::$L2);
        $step_2 = self::$L3/self::$P2 ;
        if ($step_2<1) {
            $step_2 = 0;
        } else if ($step_2 == 1 || $step_2 == 2) {
            $step_2 = $step_3;
        } else if ($step_2 > 2) {
            $step_2 = $step_4;
        }

        self::$jumlah_potong = $step_1+$step_2;
        self::$percentage_waste = self::hitungSisaKertas(self::$P1,self::$L1,self::$P2,self::$L2,self::$jumlah_potong);

        $data = [
            'p_cetak'          => $panjang_luas_permukaan,
            'l_cetak'          => $lebar_luas_permukaan,
            'p_kemasan'        => self::$P2,
            'l_kemasan'        => self::$L2,
            'jumlah_potong'    => self::$jumlah_potong,
            'percentage_waste' => self::$percentage_waste
        ];

        return $data;
    }

    public static function combi_3($data = []){
        $panjang_luas_permukaan = self::$luas_permukaan_cetak_panjang;
        $lebar_luas_permukaan = self::$luas_permukaan_cetak_lebar;

        self::$P2 = self::panjang_luas_kemasan($panjang_luas_permukaan);
        self::$L2 = self::lebar_luas_kemasan($lebar_luas_permukaan);
        if ($data) {
            $panjang_luas_permukaan = $data['panjang_cetak'];
            $lebar_luas_permukaan = $data['lebar_cetak'];

            self::$P2 = $data['panjang_kemasan'];
            self::$L2 = $data['lebar_kemasan'];
        }
        
        $step_1 = floor(self::$L1/self::$P2) * floor(self::$P1/self::$L2);
        $step_2 = floor((self::$P1-(floor(self::$P1/self::$L2)*self::$L2))/self::$P2);
        $step_3 = floor($step_2*(self::$L1/self::$L2));
        
        self::$jumlah_potong = $step_1+$step_3;
        self::$percentage_waste = self::hitungSisaKertas(self::$P1,self::$L1,self::$P2,self::$L2,self::$jumlah_potong);

        $data = [
            'p_cetak'          => $panjang_luas_permukaan,
            'l_cetak'          => $lebar_luas_permukaan,
            'p_kemasan'        => self::$P2,
            'l_kemasan'        => self::$L2,
            'jumlah_potong'    => self::$jumlah_potong,
            'percentage_waste' => self::$percentage_waste
        ];

        return $data;
    }

    public static function hitungSisaKertas($p1,$l1,$p2,$l2,$jumlah_potong){
        $luas_plano = $p1*$l1;
        $luas_kemasan = ($p2*$l2)*$jumlah_potong;

        $sisa = $luas_plano - $luas_kemasan;

        $percentage_waste = $sisa/$luas_plano * 100;

        return floor($percentage_waste);
    }
}