<?php

use App\Models\Bahan;
use App\Models\Mesin;
use App\Models\Layout;
use App\Models\MesinPrice;
use App\Models\OtherPrice;
use App\Models\PaperPrice;
use App\Models\JenisKertas;
use App\Models\DraftHitungan;
use Illuminate\Support\Facades\File;
use Illuminate\Database\Eloquent\Collection;

function rumus_lebar_cetak_1($id, $data = []){
    $layout = Layout::select('rumus_lebar_cetak_1_angka_1','rumus_lebar_cetak_1_parameter_1')->find($id);
    $angka1 = $layout['rumus_lebar_cetak_1_angka_1'];
    $param1 = $layout['rumus_lebar_cetak_1_parameter_1'];

    if($angka1 == null && $param1 == null){
        return 0;
    }

    if($angka1){
        return $angka1*$data[array_param($param1)];
    }else{
        return $data[array_param($param1)];
    }
}

function rumus_lebar_cetak_2($id, $data = []){
    $layout = Layout::select('rumus_lebar_cetak_1_angka_2','rumus_lebar_cetak_1_parameter_2')->find($id);
    
    $angka2 = $layout['rumus_lebar_cetak_1_angka_2'];
    $param2 = $layout['rumus_lebar_cetak_1_parameter_2'];
    
    if($angka2 == null && $param2 == null){
        return 0;
    }

    if($angka2){
        return $angka2*$data[array_param($param2)];
    }else{
        return $data[array_param($param2)];
    }
}

function rumus_lebar_cetak_3($id, $data = []){
    $layout = Layout::select('rumus_lebar_cetak_1_angka_3','rumus_lebar_cetak_1_parameter_3')->find($id);
    
    $angka3 = $layout['rumus_lebar_cetak_1_angka_3'];
    $param3 = $layout['rumus_lebar_cetak_1_parameter_3'];

    if($angka3 == null && $param3 == null){
        return 0;
    }

    if($angka3){
        return $angka3*$data[array_param($param3)];
    }else{
        return $data[array_param($param3)];
    }
}

function rumus_lebar_cetak_4($id, $data = []){
    $layout = Layout::select('rumus_lebar_cetak_1_angka_4','rumus_lebar_cetak_1_parameter_4')->find($id);
    
    $angka4 = $layout['rumus_lebar_cetak_1_angka_4'];
    $param4 = $layout['rumus_lebar_cetak_1_parameter_4'];

    if($angka4 == null && $param4 == null){
        return 0;
    }

    if($angka4){
        return $angka4*$data[array_param($param4)];
    }else{
        return $data[array_param($param4)];
    }
}

function rumus_panjang_cetak_1($id, $data = []){
    $layout = Layout::select('rumus_panjang_cetak_1_angka_1','rumus_panjang_cetak_1_parameter_1')->find($id);
    
    $angka1 = $layout['rumus_panjang_cetak_1_angka_1'];
    $param1 = $layout['rumus_panjang_cetak_1_parameter_1'];

    if($angka1 == null && $param1 == null){
        return 0;
    }

    if($angka1){
        return $angka1*$data[array_param($param1)];
    }else{
        return $data[array_param($param1)];
    }
}

function rumus_panjang_cetak_2($id, $data = []){
    $layout = Layout::select('rumus_panjang_cetak_1_angka_2','rumus_panjang_cetak_1_parameter_2')->find($id);
    
    $angka2 = $layout['rumus_panjang_cetak_1_angka_2'];
    $param2 = $layout['rumus_panjang_cetak_1_parameter_2'];

    if($angka2 == null && $param2 == null){
        return 0;
    }
    
    if($angka2){
        return $angka2*$data[array_param($param2)];
    }else{
        return $data[array_param($param2)];
    }
}

function rumus_panjang_cetak_3($id, $data = []){
    $layout = Layout::select('rumus_panjang_cetak_1_angka_3','rumus_panjang_cetak_1_parameter_3')->find($id);
    
    $angka3 = $layout['rumus_panjang_cetak_1_angka_3'];
    $param3 = $layout['rumus_panjang_cetak_1_parameter_3'];

    if($angka3 == null && $param3 == null){
        return 0;
    }

    if($angka3){
        return $angka3*$data[array_param($param3)];
    }else{
        return $data[array_param($param3)];
    }
}

function rumus_panjang_cetak_4($id, $data = []){
    $layout = Layout::select('rumus_panjang_cetak_1_angka_4','rumus_panjang_cetak_1_parameter_4')->find($id);
    
    $angka4 = $layout['rumus_panjang_cetak_1_angka_4'];
    $param4 = $layout['rumus_panjang_cetak_1_parameter_4'];

    if($angka4 == null && $param4 == null){
        return 0;
    }

    if($angka4){
        return $angka4*$data[array_param($param4)];
    }else{
        return $data[array_param($param4)];
    }
}

function array_param($param){
    $arr = [
        "panjang" => 'p',
        "lebar" => 'l',
        "tinggi" => 't',
        "sayap" => 's',
        "t2" => 't2',
        "lem" => 'lem',
        "klip" => 'klip'
    ]; 

    return $arr[$param];
}

function get_insheet_kertas_corrugated($qty){
    $result = 0;
    
    if ($qty <= 10000) {
        $result = 200;
    } else if ($qty > 10000 && $qty <= 20000) {
        $result = 300;
    } else if ($qty > 20000 && $qty <= 50000) {
        $result = 400;
    } else if ($qty >= 50000) {
        $result = 500;
    }
    
    return $result;
}

function get_insheet_kertas_hardbox($qty){
    $result = 0;
    
    if ($qty <= 500) {
        $result = 50;
    } elseif ($qty <= 1000) {
        $result = 100;
    } elseif ($qty <= 3000) {
        $result = 200;
    } else {
        $result = 300;
    }
    
    return $result;
}

function kebutuhan_kertas_corrugated($paper_price, $data){
    $get_insheet_kertas = 0;
    $get_jumlah_sheet = 0;
    $get_harga_dasar = 0;

    $get_insheet_kertas = get_insheet_kertas_corrugated($data['quantity']);
    $get_jumlah_sheet = $data['quantity']+$get_insheet_kertas;
    $get_harga_dasar = $paper_price->price;

    $result = [
        "insheet_kertas" => $get_insheet_kertas,
        "jumlah_sheet" => $get_jumlah_sheet,
        "harga_dasar" => $get_harga_dasar
    ];

    return $result;
}

function kebutuhan_kertas_hardbox($paper_price, $data){
    $get_insheet_kertas = 0;
    $get_lembar_board = 0;
    $get_jumlah_plano_board_bawah = 0;
    $get_jumlah_plano_board_atas = 0;
    $get_harga_dasar = 0;

    $get_insheet_kertas = get_insheet_kertas_hardbox($data['quantity']);
    $get_lembar_board = $data['quantity']+$get_insheet_kertas;
    $get_jumlah_plano_board_bawah = ceil($get_lembar_board/$data['jumlah_kertas_potong']);
    $get_jumlah_plano_board_atas = ceil($get_lembar_board/$data['jumlah_kertas_potong_atas']);
    $get_harga_dasar = $paper_price->price;

    $result = [
        "insheet_kertas" => $get_insheet_kertas,
        "lembar_board" => $get_lembar_board,
        "jumlah_plano_board_bawah" => $get_jumlah_plano_board_bawah,
        "jumlah_plano_board_atas" => $get_jumlah_plano_board_atas,
        "harga_dasar" => $get_harga_dasar
    ];

    return $result;
}

function kebutuhan_kertas($data = []){
    $bahans = Bahan::find($data['id_bahans']);
    $paper_price = PaperPrice::where(['id_jenis_kertas'=>$data['id_jenis_kertas'],'id_kertas_plano'=>$data['id_kertas_plano'],'id_gramasi'=>$data['id_gramasi']])->first();
    
    if ($bahans->bahan == 'Corrugated Box'){
        return kebutuhan_kertas_corrugated($paper_price, $data);
    } else if ($bahans->bahan == 'Hardbox') {
        return kebutuhan_kertas_hardbox($paper_price, $data);
    }

    $mesin = Mesin::find($data['id_mesin']);

    $get_insheet_kertas = 0;
    $get_jumlah_tercetak = 0;
    $get_jumlah_kertas = 0;
    $get_jumlah_plano = 0;
    $get_harga_plano = 0;

    //get insheet kertas
    if ($data['quantity'] < 10001) {
        $get_insheet_kertas = $mesin->insheet;
    } else if ($data['quantity'] > 10000){
        $koefisien_percentage = $mesin->koefisien/100;
        $get_insheet_kertas = $mesin->insheet + ($koefisien_percentage * $data['quantity']);
    }

    //get jumlah tercetak
    if ($data['isi_kertas'] > 1) {
        $get_jumlah_tercetak = $data['quantity'] + ($data['isi_kertas'] * $get_insheet_kertas);
    } else {
        $get_jumlah_tercetak = $data['quantity'] + $get_insheet_kertas;
    }

    //get jumlah kertas
    $get_jumlah_kertas = $get_jumlah_tercetak / $data['isi_kertas'];
    
    //get jumlah plano
    if ($data['jumlah_kertas_potong']>0) {
        $get_jumlah_plano = ceil($get_jumlah_kertas / $data['jumlah_kertas_potong']);
    } else {
        $get_jumlah_plano = ceil($get_jumlah_kertas);
    }

    //get harga plano
    $get_harga_plano = $paper_price->price;

    $result = [
        "insheet_kertas" => $get_insheet_kertas,
        "jumlah_tercetak" => $get_jumlah_tercetak,
        "jumlah_kertas" => $get_jumlah_kertas,
        "jumlah_plano" => $get_jumlah_plano,
        "harga_plano" => $get_harga_plano
    ];

    return $result;
}

function fix_price_corrugated($kebutuhan_kertas, $data){
    $harga_bahan_corrugated = 0;
    $knife_price = 0;
    $pond_price = 0;

    $flute_percentage = 35/100;
    $jenis_kertas = JenisKertas::find($data['id_jenis_kertas']);
    if ($jenis_kertas->jenis_kertas != 'E Flute') {
        $flute_percentage = 25/100;
    }
    
    $harga_bahan_corrugated = ceil(((($data['panjang_kemasan'] * $data['lebar_kemasan'] * 100)/1000000) * $kebutuhan_kertas['harga_dasar']) + ($flute_percentage * ((($data['panjang_kemasan'] * $data['lebar_kemasan'] * 100)/1000000)* $kebutuhan_kertas['harga_dasar'])));

    $harga_bahan_corrugated = $harga_bahan_corrugated * $kebutuhan_kertas['jumlah_sheet'];

    $other_price_knife = OtherPrice::with('tb_tools')->where('id_bahans', $data['id_bahans'])->whereHas('tb_tools', function($q){
        $q->where('tools','LIKE','%Pisau%');
    })
    ->first();
    if ($other_price_knife) {
        $knife_price = $other_price_knife->price_per_item;
    }

    $other_price_pond = OtherPrice::with('tb_tools')->where('id_bahans', $data['id_bahans'])->whereHas('tb_tools', function($q){
        $q->where('tools','LIKE','%Pond%');
    })
    ->first();

    if ($other_price_pond) {
        $pond_price = $other_price_pond->price_per_item*$kebutuhan_kertas['jumlah_sheet'];
        if($pond_price <= $other_price_pond->price_minimum){
            $pond_price = $other_price_pond->price_minimum;
        }
    }

    $result =[
        "harga_bahan_corrugated" => $harga_bahan_corrugated,
        "knife_price" => $knife_price,
        "pond_price" => $pond_price
    ];
    return $result;
}

function fix_price_hardbox($kebutuhan_kertas, $data){
    $board_bawah_price = 0;
    $board_atas_price = 0;
    $knife_price = 0;
    $pond_price = 0;
    $finishing_price = 0;
    
    $board_bawah_price = ceil($kebutuhan_kertas['jumlah_plano_board_bawah']*$kebutuhan_kertas['harga_dasar']);
    $board_atas_price = ceil($kebutuhan_kertas['jumlah_plano_board_atas']*$kebutuhan_kertas['harga_dasar']);

    $other_price_knife = OtherPrice::with('tb_tools')->where('id_bahans', $data['id_bahans'])->whereHas('tb_tools', function($q){
        $q->where('tools','LIKE','%Pisau%');
    })
    ->first();
    
    if ($other_price_knife) {
        $knife_price = $other_price_knife->price_per_item;
    }

    $other_price_pond = OtherPrice::with('tb_tools')->where('id_bahans', $data['id_bahans'])->whereHas('tb_tools', function($q){
        $q->where('tools','LIKE','%Pond%');
    })
    ->first();

    if ($other_price_pond) {
        $pond_price = $other_price_pond->price_per_item*$kebutuhan_kertas['lembar_board'];
        if($pond_price <= $other_price_pond->price_minimum){
            $pond_price = $other_price_pond->price_minimum;
        }
    }

    $other_price_finishing = OtherPrice::with('tb_tools')
    ->where('id_bahans', $data['id_bahans'])
    ->whereHas('tb_tools', function($q){
        $q->where('tools','LIKE','%finishing%');
    })
    ->whereRaw('? <= quantity', (int)$data['quantity'])
    ->first();
    
    if ($other_price_finishing) {
        $finishing_price = $other_price_finishing->price_per_item * $data['quantity'];
    }

    $result =[
        "board_bawah" => $board_bawah_price,
        "board_atas" => $board_atas_price,
        "knife_price" => $knife_price,
        "pond_price" => $pond_price,
        "finishing_hardbox" => $finishing_price
    ];
    return $result;
}

function fix_price($kebutuhan_kertas = [], $data = []){
    $bahans = Bahan::find($data['id_bahans']);

    if ($bahans->bahan == 'Corrugated Box'){
        return fix_price_corrugated($kebutuhan_kertas, $data);
    }

    if ($bahans->bahan == 'Hardbox'){
        return fix_price_hardbox($kebutuhan_kertas, $data);
    }

    $mesin = Mesin::find($data['id_mesin']);

    $paper_price = 0;
    $knife_price = 0;
    $pond_price = 0;

    //get paper_price
    $paper_price = $kebutuhan_kertas['jumlah_plano']*$kebutuhan_kertas['harga_plano'];

    //get knife_price
    if (in_array($mesin->mesin,['52','58'])) {
        $other_price = OtherPrice::with('tb_tools')->whereHas('tb_tools', function($q){
            $q->where('tools','LIKE','%Pisau Umum%');
        })
        ->first();
    } else if (in_array($mesin->mesin,['66','72','74'])){
        $other_price = OtherPrice::with('tb_tools')->whereHas('tb_tools', function($q){
            $q->where('tools','LIKE','%Pisau 74%');
        })
        ->first();
    } else if (in_array($mesin->mesin,['102'])){
        $other_price = OtherPrice::with('tb_tools')->whereHas('tb_tools', function($q){
            $q->where('tools','LIKE','%Pisau 102%');
        })
        ->first();
    }
    $knife_price = $other_price->price_per_item;

    //get pond_price
    if (in_array($mesin->mesin,['52','58'])) {
        $other_price = OtherPrice::with('tb_tools')->whereHas('tb_tools', function($q){
            $q->where('tools','LIKE','%Pond Umum%');
        })
        ->first();
    } else if (in_array($mesin->mesin,['66','72','74'])){
        $other_price = OtherPrice::with('tb_tools')->whereHas('tb_tools', function($q){
            $q->where('tools','LIKE','%Pond 74%');
        })
        ->first();
    } else if (in_array($mesin->mesin,['102'])){
        $other_price = OtherPrice::with('tb_tools')->whereHas('tb_tools', function($q){
            $q->where('tools','LIKE','%Pond 102%');
        })
        ->first();
    }
    $pond_price = $kebutuhan_kertas['jumlah_kertas']*$other_price->price_per_item;
    if ($pond_price <= $other_price->price_minimum) {
        $pond_price = $other_price->price_minimum;
    }

    $result =[
        "paper_price" => $paper_price,
        "knife_price" => $knife_price,
        "pond_price" => $pond_price
    ];

    if (in_array($data['tipe'], [2,3])) {
        $result['pond_price'] = 0;
        $result['knife_price'] = 0;
    }
    return $result;
}

function print_price ($kebutuhan_kertas = [], $data = []){
    if ($data['cetak'] > 0) {
        $mesin = MesinPrice::with('tb_mesin')->where('type_color', $data['printing'])->where('id_mesin',$data['id_mesin'])->first();
        
        $B20 = $mesin->price; // harga dasar mesin
        $B21 = get_koefisien($mesin->tb_mesin->mesin); // get value koefisien per mesin
        $B28 = $kebutuhan_kertas['jumlah_kertas']; // jumlah kertas
        $databaseHargaB40 = $mesin->quantity; // harga dasar mesin
        $result = $B20 + ($B21 * ($B28 - $databaseHargaB40));
        $result = ($result <= $B20) ? $B20 : $result;
        $result = ceil($result);

        return $result;
    }
    return 0;
}

function get_koefisien($mesin_code){
    if (in_array($mesin_code, ['52','66'])) {
        return 100;
    }

    if (in_array($mesin_code, ['74'])) {
        return 140;
    }

    if (in_array($mesin_code, ['72'])) {
        return 120;
    }

    if (in_array($mesin_code, ['102'])) {
        return 250;
    }
}

function total_price($fix_price=[], $print_price = 0, $additional_cost = 0,$data=[]){
    $harga_modal = 0;
    $harga_jual = 0;

    $total = collect($fix_price)->sum() + $print_price + collect($additional_cost)->sum();
    $harga_modal = ceil($total / $data['quantity']);
    $harga_jual = [
        '25' => ceil($harga_modal/((100-25)/100)),
        '35' => ceil($harga_modal/((100-35)/100)),
        '45' => ceil($harga_modal/((100-45)/100)) 
    ];

    $result = [
        "harga_modal" => $harga_modal,
        "harga_jual" => $harga_jual
    ];

    return $result;
}

function additional_cost($kebutuhan_kertas = [], $data) {
    $result = [];
    $bahans = Bahan::find($data['id_bahans']);
    foreach ($data['other_price_ids'] as $key => $id) {

        if (in_array($id, ['QC', 'Packing'])) {
            $other_price = OtherPrice::with('tb_tools')->where(['id_bahans' => $data['id_bahans']])
            ->whereHas('tb_tools', function($q) use ($id){
                $q->where('tools','LIKE','%'.$id.'%');
            })
            ->first();
            $count = $other_price->price_minimum + (($data['quantity'] - $other_price->quantity) * $other_price->price_per_item);
            if ($count <= $other_price->price_minimum){
                $count = $other_price->price_minimum;
            }
            $result[$id] = ceil($count);
        } else if ($id == 'Transport') {
            if (in_array($bahans->bahan,['Corrugated Box', 'Hardbox'])){
                $other_price = OtherPrice::with('tb_tools')->where('id_bahans', $data['id_bahans'])
                ->whereHas('tb_tools', function($q){
                    $q->where('tools','LIKE','%Transport%');
                })
                ->first();
                $count = $other_price->price_minimum;
                $result[$id] = ceil($count);
            } else {
                $mesin = MesinPrice::with('tb_mesin')->where('id_mesin',$data['id_mesin'])->first();
    
                $other_price = OtherPrice::with('tb_tools');
    
                if($mesin->tb_mesin->mesin == 102){
                    $other_price->whereHas('tb_tools', function($q) use ($id){
                        $q->where('tools','LIKE','%102%')->where('tools','LIKE','%transport%');
                    });
                } else {
                    $other_price->whereHas('tb_tools', function($q) use ($id){
                        $q->where('tools','LIKE','%52-74%')->where('tools','LIKE','%transport%');
                    });
                }
                $other_price = $other_price->where(['id_bahans' => $data['id_bahans']])
                ->first();
    
                $count = $other_price->price_minimum;
                $result[$id] = ceil($count);
            }
        } else {
            $other_price = OtherPrice::with('tb_tools')->find($id);
            $jumlah_kertas_sheet = isset($kebutuhan_kertas['jumlah_kertas'])?$kebutuhan_kertas['jumlah_kertas']:$kebutuhan_kertas['jumlah_sheet'];
            
            if ($other_price->tipe_rumus == 2) {
                if (isset($kebutuhan_kertas['jumlah_tercetak'])) {
                    $sheet= $kebutuhan_kertas['jumlah_tercetak'];
                } else {
                    $sheet= $kebutuhan_kertas['jumlah_sheet'];
                }
                $count = (float)$other_price->price_per_item * $sheet;

                if ($count <= $other_price->price_minimum){
                    $count = $other_price->price_minimum;
                }
                $result[$other_price->tb_tools->tools] = ceil($count);

            } else if ($other_price->tipe_rumus == 3) {
                $count = $other_price->price_minimum + (($data['quantity'] - $other_price->quantity) * $other_price->price_per_item);
                if ($count <= $other_price->price_minimum){
                    $count = $other_price->price_minimum;
                }
                $result[$other_price->tb_tools->tools] = ceil($count);
            } else {
                $count = $data['panjang_kemasan'] * $data['lebar_kemasan'] * (float)$other_price->price_per_item * $jumlah_kertas_sheet;
                
                if ($count <= $other_price->price_minimum){
                    $count = $other_price->price_minimum;
                }
                $result[$other_price->tb_tools->tools] = ceil($count);
            }
        }

        if ($bahans->bahan == 'Softbox') {
            if ($data['is_hot_stamp'] > 0 || $data['is_emboss'] > 0) {
                if ($data['p_hot_stamp'] > 0 && $data['l_hot_stamp'] > 0) {
                    $other_prices_klise = OtherPrice::with('tb_tools')->whereHas('tb_tools', function($q) use ($id){
                        $q->where('tools','LIKE','%klise%');
                        $q->where('tools','LIKE','%stamp%');
                    })->first();

                    if ($other_prices_klise) {
                        $count = ($data['p_hot_stamp'] + 1) * ($data['l_hot_stamp'] + 1) * $other_prices_klise->price_per_item;
                        if ($count <= $other_prices_klise->price_minimum){
                            $count = $other_prices_klise->price_minimum;
                        }
                        $result[$other_prices_klise->tb_tools->tools] = ceil($count);
                    }

                    $other_prices_ongkos = OtherPrice::with('tb_tools')->whereHas('tb_tools', function($q) use ($id){
                        $q->where('tools','LIKE','%ongkos%');
                        $q->where('tools','LIKE','%stamp%');
                    })->first();

                    if ($other_prices_ongkos) {
                        $count = ($data['p_hot_stamp'] + 1) * ($data['l_hot_stamp'] + 1) * $other_prices_ongkos->price_per_item * $kebutuhan_kertas['jumlah_kertas'];
                        if ($count <= $other_prices_ongkos->price_minimum){
                            $count = $other_prices_ongkos->price_minimum;
                        }
                        $result[$other_prices_ongkos->tb_tools->tools] = ceil($count);
                    }
                }

                //hot stamp emboss
                if ($data['p_emboss'] > 0 && $data['l_emboss'] > 0) {
                    $other_prices_klise = OtherPrice::with('tb_tools')->whereHas('tb_tools', function($q) use ($id){
                        $q->where('tools','LIKE','%klise%');
                        $q->where('tools','LIKE','%emboss%');
                    })->first();

                    if ($other_prices_klise) {
                        $count = ($data['p_emboss'] + 1) * ($data['l_emboss'] + 1) * $other_prices_klise->price_per_item;
                        if ($count <= $other_prices_klise->price_minimum){
                            $count = $other_prices_klise->price_minimum;
                        }
                        $result[$other_prices_klise->tb_tools->tools] = ceil($count);
                    }

                    $other_prices_ongkos = OtherPrice::with('tb_tools')->whereHas('tb_tools', function($q) use ($id){
                        $q->where('tools','LIKE','%ongkos%');
                        $q->where('tools','LIKE','%emboss%');
                    })->first();

                    if ($other_prices_ongkos) {
                        $count = ($data['p_emboss'] + 1) * ($data['l_emboss'] + 1) * $other_prices_ongkos->price_per_item * $kebutuhan_kertas['jumlah_kertas'];
                        if ($count <= $other_prices_ongkos->price_minimum){
                            $count = $other_prices_ongkos->price_minimum;
                        }
                        $result[$other_prices_ongkos->tb_tools->tools] = ceil($count);
                    }
                }
            }
        }

        if ($bahans->bahan == 'Hardbox') {
            if (isset($data['is_sekat']) && !empty($data['is_sekat'])) {
                $arr_sekat = [];

                $AB38 = $data['isi_sekat_plano'];

                if ($AB38 <= 10) {
                    $arr_sekat['insheet_sekat'] = 5;
                } elseif ($AB38 <= 100) {
                    $arr_sekat['insheet_sekat'] = 2;
                } else {
                    $arr_sekat['insheet_sekat'] = 1;
                }

                $arr_sekat['jumlah_plano_sekat'] = ceil(($data['quantity']/$data['isi_sekat_plano'])+$arr_sekat['insheet_sekat']);

                $other_prices_ongkos_potong = OtherPrice::with('tb_tools')
                ->where('id_bahans', '=', $data['id_bahans'])
                ->whereHas('tb_tools', function($q) use ($id){
                    $q->where('tools','LIKE','%potong%');
                })->first();

                if ($other_prices_ongkos_potong) {
                    $arr_sekat['ongkos_potong'] = $arr_sekat['jumlah_plano_sekat'] * $other_prices_ongkos_potong->price_per_item;
                }

                $paper_price_sekat = PaperPrice::where('id_jenis_kertas',$data['id_jenis_sekat'])->first();
                $arr_sekat['harga_sekat'] = $paper_price_sekat->price * $data['tebal_sekat'];

                $result['kebutuhan_sekat'] = $arr_sekat;

                $other_prices_transport_busa = OtherPrice::with('tb_tools')
                ->where('id_bahans', '=', $data['id_bahans'])
                ->whereHas('tb_tools', function($q) use ($id){
                    $q->where('tools','LIKE','%transport busa%');
                })->first();
                
                $result['sekat_busa'] = ceil(($arr_sekat['jumlah_plano_sekat']*$arr_sekat['harga_sekat'])+$arr_sekat['ongkos_potong']+$other_prices_transport_busa->price_minimum);
            }

            if (isset($data['is_magnet_pita']) && !empty($data['is_magnet_pita'])) {
                $is_magnet_pita_option = $data['is_magnet_pita_option'] < 2 ? 'magnet' : 'pita';
                
                $other_prices_magnet_pita = OtherPrice::with('tb_tools')
                ->where('id_bahans', '=', $data['id_bahans'])
                ->whereHas('tb_tools', function($q) use ($is_magnet_pita_option){
                    $q->where('tools','LIKE','%'.$is_magnet_pita_option.'%');
                })->first();

                if ($other_prices_magnet_pita) {
                    $result['magnet/tali'] = ceil($other_prices_magnet_pita->price_per_item * $data['jumlah_magnet_pita'] * ($data['quantity'] + 20));
                }
            }
        }

    }
    
    return $result;
}

function generateCodeDraft(){
  $prefix_char = 'DPH';
  $code = $prefix_char.sprintf("%04d", 1);

  $draft = \DB::table('tb_draft_hitungan')->latest()->first();
  
  if ($draft) {
      $seq = str_replace("DPH","",$draft->code);
      $draft = (int)$seq + 1;
      $code = $prefix_char.sprintf("%04d", ($draft));
  }
  
  return $code;
}

function converting($word){
    $text = $word;
    $text = str_replace('_', ' ', $word);
    $text = strtoupper($text);
    return $text;
}

function business_sectors(){
    $string = "FnB, Retail, Kecantikan, Kesehatan, Manufaktur, Distributor Alat Kesehatan, General Trading, Parfum, Event Organizer, Creative Agency, Perhotelan, Property, IT, Keuangan, Media, Fashion, Garment, Telekomunikasi, Percetakan, Packaging, Energi, Lainnya";

    $array = explode(", ", $string); // Memecah string menjadi array

    $result = array();
    foreach ($array as $value) {
        $result[$value] = $value;
    }

    return $result;
}

function month() {
    $month = [
        "01" => "Januari",
        "02" => "Februari",
        "03" => "Maret",
        "04" => "April",
        "05" => "Mei",
        "06" => "Juni",
        "07" => "Juli",
        "08" => "Agustus",
        "09" => "September",
        "10" => "Oktober",
        "11" => "November",
        "12" => "Desember"
    ];

    return $month;
}

function getMonth($month) {
    $arr = month();
    return $arr[$month];
}

function typeEngagement($getType = null) {
    $type = [
        "1" => "To-Do",
        "2" => "Email",
        "3" => "Call",
        "4" => "Meeting",
        "5" => "Other"
    ];

    if ($getType != null) {
        return $type[$getType];
    }

    return $type;
}

function questionType($getType = null) {
    $type = [
        "single_choice" => "Single Choice",
        "multiple_choice" => "Multiple Choice",
        "essay" => "Essay"
    ];

    if ($getType != null) {
        return $type[$getType];
    }

    return $type;
}

function getRomawiBulan($bulan)
{
    $romawi = [
        1 => 'I',
        2 => 'II',
        3 => 'III',
        4 => 'IV',
        5 => 'V',
        6 => 'VI',
        7 => 'VII',
        8 => 'VIII',
        9 => 'IX',
        10 => 'X',
        11 => 'XI',
        12 => 'XII',
    ];

    return $romawi[$bulan] ?? '';
}

function upload_file($file, $path, $name){
    $filename = $name . time(). rand(1,9999) .'.' . $file->getClientOriginalExtension();
    $destinationPath = 'uploads/' . $path;

    if (!File::isDirectory($destinationPath)) {
        File::makeDirectory($destinationPath, 0755, true, true);
    }

    $file->move($destinationPath, $filename);

    return $destinationPath . '/' . $filename;
}

function convertDate($data) {
    return date('d-M-Y', strtotime($data));
}