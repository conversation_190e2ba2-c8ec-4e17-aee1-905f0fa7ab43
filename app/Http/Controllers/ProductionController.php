<?php

namespace App\Http\Controllers;

use App\Models\Faw;
use App\Models\Spk;
use App\Models\Order;
use App\Models\Vendor;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use PhpOffice\PhpWord\TemplateProcessor;
use Yajra\DataTables\Facades\DataTables;

class ProductionController extends Controller
{
    public function index()
    {
        $ongoing = Spk::leftJoin('tb_kode_orders', 'tb_spks.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->where('catatan_khusus', '<PERSON><PERSON><PERSON><PERSON>')
            ->whereIn('flag_dummy', ['Produksi Massal', 'Produksi Ulang' ]);
        $count_ongoing = $ongoing->count();

        $dummy = Spk::leftJoin('tb_kode_orders', 'tb_spks.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('flag_dummy', '=', 'Dummy')
            ->where('catatan_khusus', 'Berjalan')
            ->get();
        $count_dummy = $dummy->count();

        $done = Spk::leftJoin('tb_kode_orders', 'tb_spks.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('catatan_khusus', 'Tuntas')
            ->whereIn('flag_dummy', ['Produksi Massal', 'Produksi Ulang' ])
            ->get();
        $count_done = $done->count('catatan_khusus');

        $dummy_done = Spk::leftJoin('tb_kode_orders', 'tb_spks.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('flag_dummy', '=', 'Dummy')
            ->where('catatan_khusus', 'Tuntas')
            ->get();
        $count_dummy_done = $dummy_done->count();

        $not_acc_faw = Order::leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_faws.sko_key')
            ->where('status_faw', '=', '0')
            ->get();
        $count_not_acc_faw = $not_acc_faw->count();

        $acc_faw_not_spk = Order::leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_faws.sko_key')
            ->leftJoin('tb_spks', 'tb_orders.sko_key', '=', 'tb_spks.sko_key')
            ->select('catatan_khusus')
            ->orWhere('catatan_khusus', 'Belum SPK');
        $count_acc_faw_not_spk = $acc_faw_not_spk->count();


        return view('pages.production.index', compact('count_ongoing', 'count_dummy', 'count_done', 'count_dummy_done', 'count_not_acc_faw', 'count_acc_faw_not_spk'));
    }

    public function delete_spk($id)
    {
        // Log::info('Deleting SPK with ID: ' . $id);
        $spk = Spk::find($id);

        if ($spk) {
            Spk::where('sko_key', $spk->sko_key)->delete();
            // Log::info('SPK deleted: ' . $id);
            return response()->json([
                'success' => 'Record deleted successfully!'
            ]);
        }

        return response()->json([
            'success' => 'Record deleted successfully!'
        ]);
    }

    private function formatHarga($harga)
    {
        return (float) str_replace(',', '', $harga);
    }
    
    public function show_faw($param)
    {
        $sko_key = Crypt::decryptString($param);
        $data = Faw::leftJoin('tb_produksis', 'tb_faws.sko_key', '=', 'tb_produksis.sko_key')->where('tb_faws.sko_key', $sko_key)->get();

        return Response::json($data, 200);
    }

    public function approve_faw($id)
    {

        $current = Faw::where('id_faw', $id)->get(['status_faw', 'order_key', 'sko_key'])->first();

        if ($current->status_faw == 1) {
            $set_stat = 0;
        } else {
            $set_stat = 1;
        }

        if ( $set_stat == 1 ) {
            Spk::updateOrCreate(
                [
                    'sko_key' => $current->sko_key
                ],
                [
                    'order_key' => $current->order_key,
                    'catatan_khusus' => "Belum SPK"
                ]
            );
        } else {
            Spk::where('sko_key', $current->sko_key)->delete();
        }

        $data = Faw::where('id_faw', $id)
            ->update([
                'status_faw' => $set_stat
        ]);
        return Response::json(['data' => $data, 'success' => 'Status updated successfully!'], 200);
    }

    public function ongoing()
    {
        return view('pages.production.ongoing');
    }

    public function table_ongoing()
    {
        $query = Spk::leftJoin('tb_kode_orders', 'tb_spks.order_key', '=', 'tb_kode_orders.order_key')
        ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
        ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
        ->where('catatan_khusus', 'Berjalan')
        ->whereIn('flag_dummy', ['Produksi Massal', 'Produksi Ulang' ])
        ->get();

        return DataTables::of($query)
            ->editColumn('nama', function ($row) {

            $cust = Customer::where('id_customer', $row->id_customer)->get(['nama'])->first();
                return $cust->nama;
            })
            ->editColumn('sko', function ($row) {
                if (Auth::user()->roles == 'SUPERADMIN') {
                    $param = Crypt::encryptString($row->id_order);
                return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->sko . '</a>';
                }
            return $row->sko;
            })
            ->editColumn('tgl_faw', function ($row) {
                return $row->created_at ? date('d M Y', strtotime($row->created_at)) : '';
            })
            ->editColumn('tgl_deadline', function ($row) {
                return $row->tgl_deadline ? date('d M Y', strtotime($row->tgl_deadline)) : '';
            })
            ->editColumn('progress_produksi', function ($row) {

                // $test = `$row->flag_status_1 + $row->progress_produksi_1`;
                if (!$row->progress_produksi_dummy && !$row->progress_produksi_ve) {
                    if ($row->tgl_selesai_all) {
                        return '<span class="badge badge-light-success badge-sm">Produksi Selesai</span>';
                    } else {
                        if ($row->progress_produksi_10 && $row->flag_status_10) {
                            return '<span class="badge badge-light-warning badge-sm">Finishing</span>';
                        } elseif ($row->progress_produksi_9 && $row->flag_status_9) {
                            return '<span class="badge badge-light-warning badge-sm">Pond</span>';
                        } elseif ($row->progress_produksi_8 && $row->flag_status_8) {
                            return '<span class="badge badge-light-warning badge-sm">Jendela Mika</span>';
                        } elseif ($row->progress_produksi_7 && $row->flag_status_7) {
                            return '<span class="badge badge-light-warning badge-sm">Lapis</span>';
                        } elseif ($row->progress_produksi_6 && $row->flag_status_6) {
                            return '<span class="badge badge-light-warning badge-sm">Spot UV</span>';
                        } elseif ($row->progress_produksi_5 && $row->flag_status_5) {
                            return '<span class="badge badge-light-warning badge-sm">Emboss</span>';
                        } elseif ($row->progress_produksi_4 && $row->flag_status_4) {
                            return '<span class="badge badge-light-warning badge-sm">Poly</span>';
                        } elseif ($row->progress_produksi_3 && $row->flag_status_3) {
                            return '<span class="badge badge-light-warning badge-sm">Laminasi</span>';
                        } elseif ($row->progress_produksi_2 && $row->flag_status_2) {
                            return '<span class="badge badge-light-warning badge-sm">Cetak</span>';
                        } elseif ($row->progress_produksi_1 && $row->flag_status_1) {
                            return '<span class="badge badge-light-warning badge-sm">Pra-Cetak</span>';
                        } elseif (!$row->progress_produksi_1) {
                            return '<span class="badge badge-light-danger badge-sm">Belum Berjalan</span>';
                        } 
                    }
                } elseif ($row->progress_produksi_dummy && !$row->progress_produksi_ve) {
                    if (!$row->flag_status_dummy) {
                        return '<span class="badge badge-light-primary badge-sm">Dummy Berjalan</span>';
                    } else {
                        return '<span class="badge badge-light-success badge-sm">Dummy Selesai</span>';
                    }
                } elseif (!$row->progress_produksi_dummy && $row->progress_produksi_ve) {
                    if (!$row->flag_status_ve) {
                        return '<span class="badge badge-light-primary badge-sm">Vendor Eks Berjalan</span>';
                    } else {
                        return '<span class="badge badge-light-success badge-sm">Vendor Eks Selesai</span>';
                    }
                }
            })
            ->editColumn('catatan_khusus', function ($row) {
                if ($row->catatan_khusus === "Belum SPK") {
                    return '<span class="badge badge-danger badge-sm">Belum SPK</span>';
                } elseif ($row->catatan_khusus === "Berjalan") {
                    return '<span class="badge badge-primary badge-sm">Berjalan</span>';
                } elseif ($row->catatan_khusus === "Tuntas") {
                    return '<span class="badge badge-success badge-sm">Tuntas</span>';
                } else {
                    return '<span class="badge badge-warning badge-sm">Reject</span>';
                }
            })
            ->addColumn('action', function ($row) {
                $param = Crypt::encryptString($row->id_spk);
            return '<a class="btn btn-sm btn-my-primary text-white w-100" data-toggle="tooltip" href="' . route('production.show_production', $param) . '">SPK</a>';
            })
            ->rawColumns(['nama', 'sko', 'tgl_faw', 'progress_produksi', 'catatan_khusus', 'action'])
            ->make(true);
    }

    public function done_page()
    {
        return view('pages.production.production_done');
    }

    public function table_done()
    {
        $query = Spk::leftJoin('tb_kode_orders', 'tb_spks.order_key', '=', 'tb_kode_orders.order_key')
        ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
        ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
        ->where('catatan_khusus', 'Tuntas')
        ->whereIn('flag_dummy', ['Produksi Massal', 'Produksi Ulang' ])
        ->get();

        return DataTables::of($query)
            ->editColumn('nama', function ($row) {

            $cust = Customer::where('id_customer', $row->id_customer)->get(['nama'])->first();
                return $cust->nama;
            })
            ->editColumn('sko', function ($row) {
                if (Auth::user()->roles == 'SUPERADMIN') {
                    $param = Crypt::encryptString($row->id_order);
                    return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->sko . '</a>';
                    }
                return $row->sko;
            })
            ->editColumn('tgl_selesai', function ($row) {
                return $row->tgl_selesai_all ? date('d M Y', strtotime($row->tgl_selesai_all)) : '';
            })
            ->editColumn('tgl_pengiriman', function ($row) {
                return $row->tgl_kirim ? date('d M Y', strtotime($row->tgl_kirim)) : '';
            })
            ->editColumn('progress_produksi', function ($row) {
                // $test = `$row->flag_status_1 + $row->progress_produksi_1`;
                if (!$row->progress_produksi_dummy && !$row->progress_produksi_ve) {
                    if ($row->tgl_selesai_all) {
                        return '<span class="badge badge-light-success badge-sm">Produksi Selesai</span>';
                    } else {
                        if ($row->progress_produksi_10) {
                            return '<span class="badge badge-light-warning badge-sm">Finishing</span>';
                        } elseif ($row->progress_produksi_9) {
                            return '<span class="badge badge-light-warning badge-sm">Pond</span>';
                        } elseif ($row->progress_produksi_8) {
                            return '<span class="badge badge-light-warning badge-sm">Jendela Mika</span>';
                        } elseif ($row->progress_produksi_7) {
                            return '<span class="badge badge-light-warning badge-sm">Lapis</span>';
                        } elseif ($row->progress_produksi_6) {
                            return '<span class="badge badge-light-warning badge-sm">Spot UV</span>';
                        } elseif ($row->progress_produksi_5) {
                            return '<span class="badge badge-light-warning badge-sm">Emboss</span>';
                        } elseif ($row->progress_produksi_4) {
                            return '<span class="badge badge-light-warning badge-sm">Poly</span>';
                        } elseif ($row->progress_produksi_3) {
                            return '<span class="badge badge-light-warning badge-sm">Laminasi</span>';
                        } elseif ($row->progress_produksi_2) {
                            return '<span class="badge badge-light-warning badge-sm">Cetak</span>';
                        } elseif ($row->progress_produksi_1) {
                            return '<span class="badge badge-light-warning badge-sm">Pra-Cetak</span>';
                        } elseif (!$row->progress_produksi_1) {
                            return '<span class="badge badge-light-danger badge-sm">Belum Berjalan</span>';
                        } 
                    }
                } elseif ($row->progress_produksi_dummy && !$row->progress_produksi_ve) {
                    if (!$row->flag_status_dummy) {
                        return '<span class="badge badge-light-primary badge-sm">Dummy Berjalan</span>';
                    } else {
                        return '<span class="badge badge-light-success badge-sm">Dummy Selesai</span>';
                    }
                } elseif (!$row->progress_produksi_dummy && $row->progress_produksi_ve) {
                    if (!$row->flag_status_ve) {
                        return '<span class="badge badge-light-primary badge-sm">Vendor Eks Berjalan</span>';
                    } else {
                        return '<span class="badge badge-light-success badge-sm">Vendor Eks Selesai</span>';
                    }
                }
            })
            ->editColumn('catatan_khusus', function ($row) {
                if ($row->catatan_khusus === "Belum SPK") {
                    return '<span class="badge badge-danger badge-sm">Belum SPK</span>';
                } elseif ($row->catatan_khusus === "Berjalan") {
                    return '<span class="badge badge-primary badge-sm">Berjalan</span>';
                } elseif ($row->catatan_khusus === "Tuntas") {
                    return '<span class="badge badge-success badge-sm">Tuntas</span>';
                } else {
                    return '<span class="badge badge-warning badge-sm">Reject</span>';
                }
            })
            ->addColumn('action', function ($row) {
                $param = Crypt::encryptString($row->id_spk);
                return '<a class="btn btn-sm btn-my-primary text-white w-100" data-toggle="tooltip" href="' . route('production.show_production', $param) . '">SPK</a>';
            })
            ->rawColumns(['nama', 'sko', 'tgl_selesai', 'tgl_pengiriman', 'progress_produksi', 'catatan_khusus', 'action'])
            ->make(true);
    }

    public function dummy()
    {
        return view('pages.production.dummy');
    }


    public function table_dummy()
    {
        $query = Spk::leftJoin('tb_kode_orders', 'tb_spks.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('flag_dummy', '=', 'Dummy')
            ->where('catatan_khusus', 'Berjalan')
            ->get();

        return DataTables::of($query)
            ->editColumn('nama', function ($row) {
                $cust = Customer::where('id_customer', $row->id_customer)->get(['nama'])->first();
                return $cust->nama;
            })
            ->editColumn('sko', function ($row) {
                if (Auth::user()->roles == 'SUPERADMIN') {
                    $param = Crypt::encryptString($row->id_order);
                return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->sko . '</a>';
                }
            return $row->sko;
            })
            ->editColumn('tgl_faw', function ($row) {
                return $row->created_at ? date('d M Y', strtotime($row->created_at)) : '';
            })
            ->editColumn('tgl_deadline', function ($row) {
                return $row->tgl_deadline ? date('d M Y', strtotime($row->tgl_deadline)) : '';
            })
            ->editColumn('progress_produksi', function ($row) {

                // $test = `$row->flag_status_1 + $row->progress_produksi_1`;
                if (!$row->progress_produksi_dummy && !$row->progress_produksi_ve) {
                    if (!$row->flag_status_1 && !$row->progress_produksi_1) {
                        return '<span class="badge badge-light-danger badge-sm">Belum Berjalan</span>';
                    } elseif (!$row->flag_status_1 && $row->progress_produksi_1) {
                        return '<span class="badge badge-light-warning badge-sm">Pra-Cetak</span>';
                    } elseif (!$row->flag_status_2 && $row->progress_produksi_2) {
                        return '<span class="badge badge-light-warning badge-sm">Cetak</span>';
                    } elseif (!$row->flag_status_3 && $row->progress_produksi_3) {
                        return '<span class="badge badge-light-warning badge-sm">Laminasi</span>';
                    } elseif (!$row->flag_status_4 && $row->progress_produksi_4) {
                        return '<span class="badge badge-light-warning badge-sm">Poly</span>';
                    } elseif (!$row->flag_status_5 && $row->progress_produksi_5) {
                        return '<span class="badge badge-light-warning badge-sm">Emboss</span>';
                    } elseif (!$row->flag_status_6 && $row->progress_produksi_6) {
                        return '<span class="badge badge-light-warning badge-sm">Spot UV</span>';
                    } elseif (!$row->flag_status_7 && $row->progress_produksi_7) {
                        return '<span class="badge badge-light-warning badge-sm">Lapis</span>';
                    } elseif (!$row->flag_status_8 && $row->progress_produksi_8) {
                        return '<span class="badge badge-light-warning badge-sm">Jendela Mika</span>';
                    } elseif (!$row->flag_status_9 && $row->progress_produksi_9) {
                        return '<span class="badge badge-light-warning badge-sm">Pond</span>';
                    } elseif (!$row->flag_status_10 && $row->progress_produksi_10) {
                        return '<span class="badge badge-light-warning badge-sm">Finishing</span>';
                    } elseif ($row->flag_status_10 && $row->progress_produksi_10) {
                        return '<span class="badge badge-light-success badge-sm">Produksi Selesai</span>';
                    }
                } elseif ($row->progress_produksi_dummy && !$row->progress_produksi_ve) {
                    if (!$row->flag_status_dummy) {
                        return '<span class="badge badge-light-primary badge-sm">Dummy Berjalan</span>';
                    } else {
                        return '<span class="badge badge-light-success badge-sm">Dummy Selesai</span>';
                    }
                } elseif (!$row->progress_produksi_dummy && $row->progress_produksi_ve) {
                    if (!$row->flag_status_ve) {
                        return '<span class="badge badge-light-primary badge-sm">Vendor Eks Berjalan</span>';
                    } else {
                        return '<span class="badge badge-light-success badge-sm">Vendor Eks Selesai</span>';
                    }
                }
            })
            ->editColumn('catatan_khusus', function ($row) {
                if ($row->catatan_khusus === "Belum SPK") {
                    return '<span class="badge badge-danger badge-sm">Belum SPK</span>';
                } elseif ($row->catatan_khusus === "Berjalan") {
                    return '<span class="badge badge-primary badge-sm">Berjalan</span>';
                } elseif ($row->catatan_khusus === "Tuntas") {
                    return '<span class="badge badge-success badge-sm">Tuntas</span>';
                } else {
                    return '<span class="badge badge-warning badge-sm">Reject</span>';
                }
            })
            ->addColumn('action', function ($row) {
                $param = Crypt::encryptString($row->id_spk);
            return '<a class="btn btn-sm btn-my-primary text-white w-100" data-toggle="tooltip" href="' . route('production.show_production', $param) . '">SPK</a>';
            })
            ->rawColumns(['nama', 'sko', 'tgl_faw', 'progress_produksi', 'catatan_khusus', 'action'])
            ->make(true);
    }
    
    public function dummy_done()
    {
        return view('pages.production.dummy_done');
    }

    public function table_dummy_done()
    {
        $query = Spk::leftJoin('tb_kode_orders', 'tb_spks.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('flag_dummy', '=', 'Dummy')
            ->where('catatan_khusus', 'Tuntas')
            ->get();

        return DataTables::of($query)
            ->editColumn('nama', function ($row) {
                $cust = Customer::where('id_customer', $row->id_customer)->get(['nama'])->first();
                return $cust->nama;
            })
            ->editColumn('sko', function ($row) {
                if (Auth::user()->roles == 'SUPERADMIN') {
                    $param = Crypt::encryptString($row->id_order);
                return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->sko . '</a>';
                }
            return $row->sko;
            })
            ->editColumn('tgl_selesai', function ($row) {
                return $row->tgl_selesai_all ? date('d M Y', strtotime($row->tgl_selesai_all)) : '';
            })
            ->editColumn('tgl_pengiriman', function ($row) {
                return $row->tgl_kirim ? date('d M Y', strtotime($row->tgl_kirim)) : '';
            })
            ->editColumn('progress_produksi', function ($row) {

                // $test = `$row->flag_status_1 + $row->progress_produksi_1`;
                if (!$row->progress_produksi_dummy && !$row->progress_produksi_ve) {
                    if (!$row->flag_status_1 && !$row->progress_produksi_1) {
                        return '<span class="badge badge-light-danger badge-sm">Belum Berjalan</span>';
                    } elseif (!$row->flag_status_1 && $row->progress_produksi_1) {
                        return '<span class="badge badge-light-warning badge-sm">Pra-Cetak</span>';
                    } elseif (!$row->flag_status_2 && $row->progress_produksi_2) {
                        return '<span class="badge badge-light-warning badge-sm">Cetak</span>';
                    } elseif (!$row->flag_status_3 && $row->progress_produksi_3) {
                        return '<span class="badge badge-light-warning badge-sm">Laminasi</span>';
                    } elseif (!$row->flag_status_4 && $row->progress_produksi_4) {
                        return '<span class="badge badge-light-warning badge-sm">Poly</span>';
                    } elseif (!$row->flag_status_5 && $row->progress_produksi_5) {
                        return '<span class="badge badge-light-warning badge-sm">Emboss</span>';
                    } elseif (!$row->flag_status_6 && $row->progress_produksi_6) {
                        return '<span class="badge badge-light-warning badge-sm">Spot UV</span>';
                    } elseif (!$row->flag_status_7 && $row->progress_produksi_7) {
                        return '<span class="badge badge-light-warning badge-sm">Lapis</span>';
                    } elseif (!$row->flag_status_8 && $row->progress_produksi_8) {
                        return '<span class="badge badge-light-warning badge-sm">Jendela Mika</span>';
                    } elseif (!$row->flag_status_9 && $row->progress_produksi_9) {
                        return '<span class="badge badge-light-warning badge-sm">Pond</span>';
                    } elseif (!$row->flag_status_10 && $row->progress_produksi_10) {
                        return '<span class="badge badge-light-warning badge-sm">Finishing</span>';
                    } elseif ($row->flag_status_10 && $row->progress_produksi_10) {
                        return '<span class="badge badge-light-success badge-sm">Produksi Selesai</span>';
                    }
                } elseif ($row->progress_produksi_dummy && !$row->progress_produksi_ve) {
                    if (!$row->flag_status_dummy) {
                        return '<span class="badge badge-light-primary badge-sm">Dummy Berjalan</span>';
                    } else {
                        return '<span class="badge badge-light-success badge-sm">Dummy Selesai</span>';
                    }
                } elseif (!$row->progress_produksi_dummy && $row->progress_produksi_ve) {
                    if (!$row->flag_status_ve) {
                        return '<span class="badge badge-light-primary badge-sm">Vendor Eks Berjalan</span>';
                    } else {
                        return '<span class="badge badge-light-success badge-sm">Vendor Eks Selesai</span>';
                    }
                }
            })
            ->editColumn('catatan_khusus', function ($row) {
                if ($row->catatan_khusus === "Belum SPK") {
                    return '<span class="badge badge-danger badge-sm">Belum SPK</span>';
                } elseif ($row->catatan_khusus === "Berjalan") {
                    return '<span class="badge badge-primary badge-sm">Berjalan</span>';
                } elseif ($row->catatan_khusus === "Tuntas") {
                    return '<span class="badge badge-success badge-sm">Tuntas</span>';
                } else {
                    return '<span class="badge badge-warning badge-sm">Reject</span>';
                }
            })
            ->addColumn('action', function ($row) {
                $param = Crypt::encryptString($row->id_spk);
            return '<a class="btn btn-sm btn-my-primary text-white w-100" data-toggle="tooltip" href="' . route('production.show_production', $param) . '">SPK</a>';
            })
            ->rawColumns(['nama', 'sko', 'tgl_selesai', 'tgl_pengiriman', 'progress_produksi', 'catatan_khusus', 'action'])
            ->make(true);
    }

    public function not_acc_faw()
    {
        return view('pages.production.not_acc_faw');
    }

    public function table_not_acc_faw()
    {
        $query = DB::table('tb_customers')
            ->leftJoin('tb_orders', 'tb_customers.id_customer', '=', 'tb_orders.id_customer')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_faws.sko_key')
            ->select(['tb_customers.id_customer', 'nama', 'kode_kustomer', 'tb_orders.id_order', 'kode_order', 'sko', 'status_deal', 'tb_faws.created_at', 'tb_orders.order_key', 'tb_faws.sko_key', 'tgl_faw', 'id_faw', 'status_faw'])
            ->where('status_deal', 'like', 'Deal')
            ->where('status_faw', '!=', 1)
            ->orderBy('id_customer', 'DESC');


        return DataTables::of($query)
            ->editColumn('kode_order', function ($row) {
                if (Auth::user()->roles == 'SUPERADMIN') {
                    $param = Crypt::encryptString($row->id_order);
                    return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->kode_order . '</a>';
                }
                return $row->kode_order;
            })
            ->editColumn('sko', function ($row) {
                if (Auth::user()->roles == 'SUPERADMIN') {
                    $param = Crypt::encryptString($row->id_order);
                    return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->sko . '</a>';
                }
                return $row->sko;
            })
            ->editColumn('nama', function ($row) {
                if (Auth::user()->roles == 'SUPERADMIN') {
                    return '<a href="' . route('crm.detail_customer', $row->kode_kustomer) . '" class="text-gray-800 text-hover-primary mb-1">' . $row->nama . '</a>';
                }
                return $row->nama;
            })
            ->editColumn('status_faw_disp', function ($row) {
                if ($row->status_faw == 1) {
                    $check = 'checked';
                    $label = '<a href="javascript:void(0)" onclick="approveConfirmation(' . $row->id_faw . ')"><div class="form-check form-switch form-check-custom form-check-solid"><input class="form-check-input" style="background-color: #50cd89; border-color: #50cd89;" ' . $check . ' type="checkbox" disabled value="" id="flexSwitchDefault"/><label class="form-check-label text-success" for="flexSwitchDefault">Approved</label></div></a>';
                } else {
                    $check = '';
                    $label = '<a href="javascript:void(0)" onclick="approveConfirmation(' . $row->id_faw . ')"><div class="form-check form-switch form-check-custom form-check-solid"><input class="form-check-input" style="background-color: gray; border-color: gray;" ' . $check . ' type="checkbox" disabled value="" id="flexSwitchDefault"/><label class="form-check-label" style="color: #bfbfbf;" for="flexSwitchDefault">Need Approval</label></div></a>';
                }
                
                return $row->id_faw ? $label : 'Belum FAW';
            })
            ->editColumn('tgl_faw', function ($row) {
                return $row->tgl_faw ? date('d M Y', strtotime($row->tgl_faw)) : '';
            })
            ->addColumn('action', function ($row) {
                $param = Crypt::encryptString($row->sko_key);
                return '<div class="btn-group d-flex">
                                    <a class="btn btn-sm btn-my-primary text-white w-100 view-faw" data-toggle="tooltip" data-key="' . $param . '" href="javascript:void(0)">View FAW</a></div> ';
            })
            ->rawColumns(['kode_order', 'nama', 'sko', 'status_faw_disp', 'tgl_faw', 'action'])
            ->make(true);
    }

    public function acc_faw_not_spk()
    {
        return view('pages.production.acc_faw_not_spk');
    }

    public function table_acc_faw_not_spk()
    {
        $query = DB::table('tb_customers')
            ->leftJoin('tb_orders', 'tb_customers.id_customer', '=', 'tb_orders.id_customer')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_faws.sko_key')
            ->leftJoin('tb_spks', 'tb_orders.sko_key', '=', 'tb_spks.sko_key')
            ->select(['tb_customers.id_customer', 'nama', 'kode_kustomer', 'tb_orders.id_order', 'sko', 'status_deal', 'tb_orders.order_key', 'tb_faws.created_at', 'tb_faws.updated_at', 'tb_faws.sko_key', 'id_faw', 'status_faw', 'id_spk'])
            ->where('status_deal', 'like', 'Deal')
            ->where('catatan_khusus', 'Belum SPK')
            ->orderBy('id_customer', 'DESC');


        return DataTables::of($query)
            // ->editColumn('kode_order', function ($row) {
            //     if (Auth::user()->roles == 'SUPERADMIN') {
            //         $param = Crypt::encryptString($row->id_order);
            //         return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->kode_order . '</a>';
            //     }
            //     return $row->kode_order;
            // })
            ->editColumn('sko', function ($row) {
                if (Auth::user()->roles == 'SUPERADMIN') {
                    $param = Crypt::encryptString($row->id_order);
                    return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->sko . '</a>';
                }
                return $row->sko;
            })
            ->editColumn('nama', function ($row) {
                if (Auth::user()->roles == 'SUPERADMIN') {
                    return '<a href="' . route('crm.detail_customer', $row->kode_kustomer) . '" class="text-gray-800 text-hover-primary mb-1">' . $row->nama . '</a>';
                }
                return $row->nama;
            })
            ->editColumn('status_faw_disp', function ($row) {
                if ($row->status_faw == 1) {
                    $label = 'Approved';
                    $check = 'checked';
                } else {
                    $label = 'Need Approval';
                    $check = '';
                }
                return $row->id_faw ? '<span class="badge badge-light-success badge-lg">' . $label . '</span>' : '-';
            })
            ->editColumn('created_at', function ($row) {
                return $row->created_at ? date('d M Y', strtotime($row->created_at)) : '';
            })
            ->editColumn('updated_at', function ($row) {
                return $row->updated_at ? date('d M Y', strtotime($row->updated_at)) : '';
            })
            ->addColumn('action', function ($row) {
                $param = Crypt::encryptString($row->id_spk);
                $sko_param = Crypt::encryptString($row->sko_key);
            return '<a class="btn btn-sm btn-my-primary text-white w-100" data-toggle="tooltip" href="' . route('production.show_production', $param) . '">SPK</a>';
            })
            ->rawColumns(['kode_order', 'nama', 'sko', 'status_faw_disp', 'created_at', 'updated_at', 'action'])
            ->make(true);
    }

    public function table_production()
    {   
        $query = Spk::leftJoin('tb_kode_orders', 'tb_spks.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereIn('flag_dummy', ['Produksi Massal', 'Produksi Ulang', 'Dummy', 'Produksi_ulang'])
            ->where(function ($query) {
                if (!empty(request('tgl_deadline_from_date')) && !empty(request('tgl_deadline_to_date'))) {
                    $query->whereBetween('tgl_deadline', array(request('tgl_deadline_from_date'), request('tgl_deadline_to_date')));
                } elseif (!empty(request('tgl_deadline_from_date')) && empty(request('tgl_deadline_to_date'))) {
                    $query->where('tgl_deadline', request('tgl_deadline_from_date'));
                }
            })
            ->get();

        return DataTables::of($query)
            ->editColumn('nama', function ($row) {
                $cust = Customer::where('id_customer', $row->id_customer)->get(['nama'])->first();
                return $cust->nama ?? "";
            })
            ->editColumn('sko', function ($row) {
                if (Auth::user()->roles == 'SUPERADMIN') {
                    $param = Crypt::encryptString($row->id_order);
                    return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->sko . '</a>';
                }
                return $row->sko;
            })
            ->editColumn('tgl_faw', function ($row) {
                return $row->tgl_faw ? date('d M Y', strtotime($row->tgl_faw)) : '';
            })
            ->editColumn('tgl_deadline', function ($row) {
                return $row->tgl_deadline ? date('d M Y', strtotime($row->tgl_deadline)) : '';
            })
            ->editColumn('progress_produksi', function ($row) {

                // $test = `$row->flag_status_1 + $row->progress_produksi_1`;
                if (!$row->progress_produksi_dummy && !$row->progress_produksi_ve) {
                    if ($row->tgl_selesai_all) {
                        return '<span class="badge badge-light-success badge-sm">Produksi Selesai</span>';
                    } else {
                        while (true) {
                            if (!$row->progress_produksi_1) {
                                return '<span class="badge badge-light-danger badge-sm">Belum Berjalan</span>';
                                break;
                            } 

                            if ($row->progress_produksi_1 && !$row->flag_status_1) {
                                return '<span class="badge badge-light-warning badge-sm">Pra-Cetak</span>';
                                break;
                            } 

                            if ($row->progress_produksi_2 && !$row->flag_status_2) {
                                return '<span class="badge badge-light-warning badge-sm">Cetak</span>';
                                break;
                            }

                            if ($row->progress_produksi_3 && !$row->flag_status_3) {
                                return '<span class="badge badge-light-warning badge-sm">Laminasi</span>';
                                break;
                            }

                            if ($row->progress_produksi_4 && !$row->flag_status_4) {
                                return '<span class="badge badge-light-warning badge-sm">Poly</span>';
                                break;
                            }

                            if ($row->progress_produksi_5 && !$row->flag_status_5) {
                                return '<span class="badge badge-light-warning badge-sm">Emboss</span>';
                                break;
                            }

                            if ($row->progress_produksi_6 && !$row->flag_status_6) {
                                return '<span class="badge badge-light-warning badge-sm">Spot UV</span>';
                                break;
                            }

                            if ($row->progress_produksi_7 && !$row->flag_status_7) {
                                return '<span class="badge badge-light-warning badge-sm">Lapis</span>';
                                break;
                            }

                            if ($row->progress_produksi_8 && !$row->flag_status_8) {
                                return '<span class="badge badge-light-warning badge-sm">Jendela Mika</span>';
                                break;
                            }

                            if ($row->progress_produksi_9 && !$row->flag_status_9) {
                                return '<span class="badge badge-light-warning badge-sm">Pond</span>';
                                break;
                            }

                            if ($row->progress_produksi_10 && !$row->flag_status_10) {
                                return '<span class="badge badge-light-warning badge-sm">Finishing</span>';
                                break;
                            } else {
                                return '<span class="badge badge-danger badge-sm">Status Produksi Salah</span>';
                                break;
                            }
                        }

                        // if ($row->progress_produksi_10 && $row->flag_status_9) {
                        //     return '<span class="badge badge-light-warning badge-sm">Finishing</span>';
                        // } elseif ($row->progress_produksi_9 && $row->flag_status_8) {
                        //     return '<span class="badge badge-light-warning badge-sm">Pond</span>';
                        // } elseif ($row->progress_produksi_8 && $row->flag_status_7) {
                        //     return '<span class="badge badge-light-warning badge-sm">Jendela Mika</span>';
                        // } elseif ($row->progress_produksi_7 && $row->flag_status_6) {
                        //     return '<span class="badge badge-light-warning badge-sm">Lapis</span>';
                        // } elseif ($row->progress_produksi_6 && $row->flag_status_5) {
                        //     return '<span class="badge badge-light-warning badge-sm">Spot UV</span>';
                        // } elseif ($row->progress_produksi_5 && $row->flag_status_4) {
                        //     return '<span class="badge badge-light-warning badge-sm">Emboss</span>';
                        // } elseif ($row->progress_produksi_4 && $row->flag_status_3) {
                        //     return '<span class="badge badge-light-warning badge-sm">Poly</span>';
                        // } elseif ($row->progress_produksi_3 && $row->flag_status_2) {
                        //     return '<span class="badge badge-light-warning badge-sm">Laminasi</span>';
                        // } elseif ($row->progress_produksi_2 && $row->flag_status_1) {
                        //     return '<span class="badge badge-light-warning badge-sm">Cetak</span>';
                        // } elseif ($row->progress_produksi_1) {
                        //     return '<span class="badge badge-light-warning badge-sm">Pra-Cetak</span>';
                        // } elseif (!$row->progress_produksi_1) {
                        //     return '<span class="badge badge-light-danger badge-sm">Belum Berjalan</span>';
                        // } 
                    }
                } elseif ($row->progress_produksi_dummy && !$row->progress_produksi_ve) {
                    if (!$row->flag_status_dummy) {
                        return '<span class="badge badge-light-primary badge-sm">Dummy Berjalan</span>';
                    } else {
                        return '<span class="badge badge-light-success badge-sm">Dummy Selesai</span>';
                    }
                } elseif (!$row->progress_produksi_dummy && $row->progress_produksi_ve) {
                    if (!$row->flag_status_ve) {
                        return '<span class="badge badge-light-primary badge-sm">Vendor Eks Berjalan</span>';
                    } else {
                        return '<span class="badge badge-light-success badge-sm">Vendor Eks Selesai</span>';
                    }
                }
                
            })
            ->editColumn('catatan_khusus', function ($row) {
                if ($row->catatan_khusus === "Belum SPK") {
                    return '<span class="badge badge-danger badge-sm">Belum SPK</span>';
                } elseif ($row->catatan_khusus === "Berjalan") {
                    return '<span class="badge badge-primary badge-sm">Berjalan</span>';
                } elseif ($row->catatan_khusus === "Tuntas") {
                    return '<span class="badge badge-success badge-sm">Tuntas</span>';
                } else {
                    return '<span class="badge badge-warning badge-sm">Reject</span>';
                }
            })
            ->editColumn('last_update', function ($row) {
                return $row->updated_at ? date('d M Y', strtotime($row->updated_at)) : '';
            })
            ->addColumn('action', function ($row) {
                $param = Crypt::encryptString($row->id_spk);
                return '<div>
                            <a class="btn btn-sm btn-my-primary text-white w-100" data-toggle="tooltip" href="' . route('production.show_production', $param) . '">SPK</a>
                        </div> ';
                })
            ->rawColumns(['nama', 'tgl_faw', 'tgl_deadline', 'sko', 'progress_produksi', 'catatan_khusus', 'last_update', 'action'])
            ->make(true);
    }

    public function table_production_bermasalah()
    {
        $query = Order::leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_spks', 'tb_orders.sko_key', '=', 'tb_spks.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            // ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id_pic')
            ->leftJoin('users', 'users.id', '=', 'tb_orders.id_pic')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_fpms', 'tb_orders.sko_key', '=', 'tb_fpms.sko_key')
            ->whereNotNull('id_fpms')
            // ->orderByRaw("CAST(SUBSTRING(sko, 1, 11) AS UNSIGNED) DESC")

            ->where(function ($query) {
                if (!empty(request('tgl_deadline_from_date')) && !empty(request('tgl_deadline_to_date'))) {
                    $query->whereBetween('tgl_deadline', array(request('tgl_deadline_from_date'), request('tgl_deadline_to_date')));
                } elseif (!empty(request('tgl_deadline_from_date')) && empty(request('tgl_deadline_to_date'))) {
                    $query->where('tgl_deadline', request('tgl_deadline_from_date'));
                }
            })
            ->get();

        return DataTables::of($query)
            // ->editColumn('kode_order', function ($row) {
            //     return $row->kode_order;
            // })
            ->editColumn('sko', function ($row) {

                if (Auth::user()->roles == 'SUPERADMIN') {
                    $param = Crypt::encryptString($row->id_order);
                    return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->sko . '</a>';
                }
                return $row->sko;
            })
            ->editColumn('nama', function ($row) {
                $cust = Customer::where('id_customer', $row->id_customer)->get(['nama'])->first();
                return $cust->nama;
            })
            ->editColumn('solusi', function ($row) {
                if ($row->solusi == "perbaikan") {
                    return '<span class="badge badge-light-warning fw-bold">' . $row->solusi . '</span>';
                } elseif ($row->solusi == "produksi-ulang") {
                    return '<span class="badge badge-light-danger fw-bold">' . $row->solusi . '</span>';
                } elseif ($row->solusi == "negosiasi") {
                    return '<span class="badge badge-light-primary fw-bold">' . $row->solusi . '</span>';
                } else {
                    return '-';
                }
            })
            ->editColumn('status_order', function ($row) {
                return $row->status_order ? '<div class="badge badge-light">' . $row->status_order . '</div>' : '';
            })
            ->editColumn('tgl_order', function ($row) {
                return $row->tgl_order ? date('d M Y', strtotime($row->tgl_order)) : '';
            })
            ->editColumn('tgl_masalah', function ($row) {
                return $row->tgl_masalah ? date('d M Y', strtotime($row->tgl_masalah)) : '';
            })
            ->editColumn('total_harga', function ($row) {
                return $row->total_harga ? number_format($row->total_harga, 0, '', ',') : '';
            })
            ->editColumn('modal_sales', function ($row) {
                return $row->modal_sales ? number_format($row->modal_sales, 0, '', ',') : '';
            })
            ->editColumn('last_edited', function ($row) {
                return $row->tgl_deadline_perbaikan ? date('d M Y', strtotime($row->tgl_deadline_perbaikan)) : '';
            })
            ->rawColumns(['kode_order', 'status_order', 'sko', 'solusi', 'nama', 'last_edited'])
            ->make(true);
    }

    public function form_add_production()
    {

        $vendor = Vendor::select('vendor')->get();

        return view('pages.production.form_add_production', compact('vendor'));
    }

    public function kode_order_get(Request $request)
    {

        $data = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_faws.sko_key')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_spks', 'tb_orders.sko_key', '=', 'tb_spks.sko_key')
            ->where('tb_orders.sko', 'LIKE', '%' . $request->get('sko') . '%')
            ->where('id_faw', '!=', NULL)
            ->where('status_faw', '=', '1')
            ->whereNull('id_spk')
            ->select([
                'tb_kode_orders.kode_order as kode_order',
                'sko',
                'nama',
                'tb_orders.order_key',
                'tb_orders.sko_key',
                'lp_panjang',
                'lp_lebar',
                'jenis_kertas',
                'gramasi',
                'laminasi',
                'sisi_laminasi',
                'finishing',
                'jumlah_produk',
                'notes',
                'tb_faws.tgl_deadline',
                'waktu_produksi',
                'tgl_faw',
                'file_final',
                'path_lampiran',
                'keterangan_tambahan'
            ])
            ->limit(10)
            ->get();

        return Response::json($data, 200);
    }

    public function store_new_production(Request $request)
    {
        if (!empty($request->sko_key)) {
            $data_before = Spk::where('sko_key', $request->sko_key)
                ->get()
                ->first();
            if (!empty($request->sko_key)) {
                $data_before = Spk::where('sko_key', $request->sko_key)
                    ->get()
                    ->first();

            if (empty($data_before->tgl_flag_status_1)) {
                if ($request->flag_status_1 == '1') {
                    $tgl_flag_status_1 = date('Y-m-d H:i:s');
                } else {
                    $tgl_flag_status_1 = null;
                }
            } else {
                $tgl_flag_status_1 = $data_before->tgl_flag_status_1;
            }


            if (empty($data_before->tgl_flag_status_2)) {
                if ($request->flag_status_2 == '1') {
                    $tgl_flag_status_2 = date('Y-m-d H:i:s');
                } else {
                    $tgl_flag_status_2 = null;
                }
            } else {
                $tgl_flag_status_2 = $data_before->tgl_flag_status_2;
            }

            if (empty($data_before->tgl_flag_status_3)) {
                if ($request->flag_status_3 == '1') {
                    $tgl_flag_status_3 = date('Y-m-d H:i:s');
                } else {
                    $tgl_flag_status_3 = null;
                }
            } else {
                $tgl_flag_status_3 = $data_before->tgl_flag_status_3;
            }

            if (empty($data_before->tgl_flag_status_4)) {
                if ($request->flag_status_4 == '1') {
                    $tgl_flag_status_4 = date('Y-m-d H:i:s');
                } else {
                    $tgl_flag_status_4 = null;
                }
            } else {
                $tgl_flag_status_4 = $data_before->tgl_flag_status_4;
            }

            if (empty($data_before->tgl_flag_status_5)) {
                if ($request->flag_status_5 == '1') {
                    $tgl_flag_status_5 = date('Y-m-d H:i:s');
                } else {
                    $tgl_flag_status_5 = null;
                }
            } else {
                $tgl_flag_status_5 = $data_before->tgl_flag_status_5;
            }

            if (empty($data_before->tgl_flag_status_6)) {
                if ($request->flag_status_6 == '1') {
                    $tgl_flag_status_6 = date('Y-m-d H:i:s');
                } else {
                    $tgl_flag_status_6 = null;
                }
            } else {
                $tgl_flag_status_6 = $data_before->tgl_flag_status_6;
            }

            if (empty($data_before->tgl_flag_status_7)) {
                if ($request->flag_status_7 == '1') {
                    $tgl_flag_status_7 = date('Y-m-d H:i:s');
                } else {
                    $tgl_flag_status_7 = null;
                }
            } else {
                $tgl_flag_status_7 = $data_before->tgl_flag_status_7;
            }

            if (empty($data_before->tgl_flag_status_8)) {
                if ($request->flag_status_8 == '1') {
                    $tgl_flag_status_8 = date('Y-m-d H:i:s');
                } else {
                    $tgl_flag_status_8 = null;
                }
            } else {
                $tgl_flag_status_8 = $data_before->tgl_flag_status_8;
            }

            if (empty($data_before->tgl_flag_status_9)) {
                if ($request->flag_status_9 == '1') {
                    $tgl_flag_status_9 = date('Y-m-d H:i:s');
                } else {
                    $tgl_flag_status_9 = null;
                }
            } else {
                $tgl_flag_status_9 = $data_before->tgl_flag_status_9;
            }

            if (empty($data_before->tgl_flag_status_10)) {
                if ($request->flag_status_10 == '1') {
                    $tgl_flag_status_10 = date('Y-m-d H:i:s');
                } else {
                    $tgl_flag_status_10 = null;
                }
            } else {
                $tgl_flag_status_10 = $data_before->tgl_flag_status_10;
            }

            $status_produksi = 'Berjalan';

            if (empty($request->progress_produksi_2)) {
                $jumlah_awal_2 = null;
                $jumlah_hasil_2 = null;
                $reject_2 = null;
            } else {
                $jumlah_awal_2 = $request->jumlah_awal_pp_2;
                $jumlah_hasil_2 = $request->jumlah_hasil_pp_2;
                $reject_2 = $request->reject_pp_2;
            }

            if (empty($request->progress_produksi_3)) {
                $jumlah_awal_3 = null;
                $jumlah_hasil_3 = null;
                $reject_3 = null;
            } else {
                $jumlah_awal_3 = $request->jumlah_awal_pp_3;
                $jumlah_hasil_3 = $request->jumlah_hasil_pp_3;
                $reject_3 = $request->reject_pp_3;
            }

            if (empty($request->progress_produksi_4)) {
                $jumlah_awal_4 = null;
                $jumlah_hasil_4 = null;
                $reject_4 = null;
            } else {
                $jumlah_awal_4 = $request->jumlah_awal_pp_4;
                $jumlah_hasil_4 = $request->jumlah_hasil_pp_4;
                $reject_4 = $request->reject_pp_4;
            }
            if (empty($request->progress_produksi_5)) {
                $jumlah_awal_5 = null;
                $jumlah_hasil_5 = null;
                $reject_5 = null;
            } else {
                $jumlah_awal_5 = $request->jumlah_awal_pp_5;
                $jumlah_hasil_5 = $request->jumlah_hasil_pp_5;
                $reject_5 = $request->reject_pp_5;
            }
            if (empty($request->progress_produksi_6)) {
                $jumlah_awal_6 = null;
                $jumlah_hasil_6 = null;
                $reject_6 = null;
            } else {
                $jumlah_awal_6 = $request->jumlah_awal_pp_6;
                $jumlah_hasil_6 = $request->jumlah_hasil_pp_6;
                $reject_6 = $request->reject_pp_6;
            }
            if (empty($request->progress_produksi_7)) {
                $jumlah_awal_7 = null;
                $jumlah_hasil_7 = null;
                $reject_7 = null;
            } else {
                $jumlah_awal_7 = $request->jumlah_awal_pp_7;
                $jumlah_hasil_7 = $request->jumlah_hasil_pp_7;
                $reject_7 = $request->reject_pp_7;
            }
            if (empty($request->progress_produksi_8)) {
                $jumlah_awal_8 = null;
                $jumlah_hasil_8 = null;
                $reject_8 = null;
            } else {
                $jumlah_awal_8 = $request->jumlah_awal_pp_8;
                $jumlah_hasil_8 = $request->jumlah_hasil_pp_8;
                $reject_8 = $request->reject_pp_8;
            }
            if (empty($request->progress_produksi_9)) {
                $jumlah_awal_9 = null;
                $jumlah_hasil_9 = null;
                $reject_9 = null;
            } else {
                $jumlah_awal_9 = $request->jumlah_awal_pp_9;
                $jumlah_hasil_9 = $request->jumlah_hasil_pp_9;
                $reject_9 = $request->reject_pp_9;
            }
            if (empty($request->progress_produksi_10)) {
                $jumlah_awal_10 = null;
                $jumlah_hasil_10 = null;
                $reject_10 = null;
            } else {
                $jumlah_awal_10 = $request->jumlah_awal_pp_10;
                $jumlah_hasil_10 = $request->jumlah_hasil_pp_10;
                $reject_10 = $request->reject_pp_10;
            }

            if ($request->vendor_1 == "Lainnya") {
                $check_vendor_1 = Vendor::where('vendor', '=', $request->vendor_1_lainnya);
                $count_check_vendor_1 = $check_vendor_1->count();

                if ($count_check_vendor_1 < 1) {
                    Vendor::create([
                        'vendor' => $request->vendor_1_lainnya,
                    ]);
                }

                $vendor_1 = $request->vendor_1_lainnya;
            } else {
                $vendor_1 = $request->vendor_1;
            }

            if ($request->vendor_2 == "Lainnya") {
                $check_vendor_2 = Vendor::where('vendor', '=', $request->vendor_2_lainnya);
                $count_check_vendor_2 = $check_vendor_2->count();

                if ($count_check_vendor_2 < 1) {
                    Vendor::create([
                        'vendor' => $request->vendor_2_lainnya,
                    ]);
                }

                $vendor_2 = $request->vendor_2_lainnya;
            } else {
                $vendor_2 = $request->vendor_2;
            }

            if ($request->vendor_3 == "Lainnya") {
                $check_vendor_3 = Vendor::where('vendor', '=', $request->vendor_3_lainnya);
                $count_check_vendor_3 = $check_vendor_3->count();

                if ($count_check_vendor_3 < 1) {
                    Vendor::create([
                        'vendor' => $request->vendor_3_lainnya,
                    ]);
                }

                $vendor_3 = $request->vendor_3_lainnya;
            } else {
                $vendor_3 = $request->vendor_3;
            }

            if ($request->vendor_4 == "Lainnya") {
                $check_vendor_4 = Vendor::where('vendor', '=', $request->vendor_4_lainnya);
                $count_check_vendor_4 = $check_vendor_4->count();

                if ($count_check_vendor_4 < 1) {
                    Vendor::create([
                        'vendor' => $request->vendor_4_lainnya,
                    ]);
                }

                $vendor_4 = $request->vendor_4_lainnya;
            } else {
                $vendor_4 = $request->vendor_4;
            }

            if ($request->vendor_5 == "Lainnya") {
                $check_vendor_5 = Vendor::where('vendor', '=', $request->vendor_5_lainnya);
                $count_check_vendor_5 = $check_vendor_5->count();

                if ($count_check_vendor_5 < 1) {
                    Vendor::create([
                        'vendor' => $request->vendor_5_lainnya,
                    ]);
                }

                $vendor_5 = $request->vendor_5_lainnya;
            } else {
                $vendor_5 = $request->vendor_5;
            }

            if ($request->vendor_6 == "Lainnya") {
                $check_vendor_6 = Vendor::where('vendor', '=', $request->vendor_6_lainnya);
                $count_check_vendor_6 = $check_vendor_6->count();

                if ($count_check_vendor_6 < 1) {
                    Vendor::create([
                        'vendor' => $request->vendor_6_lainnya,
                    ]);
                }

                $vendor_6 = $request->vendor_6_lainnya;
            } else {
                $vendor_6 = $request->vendor_6;
            }

            if ($request->vendor_7 == "Lainnya") {
                $check_vendor_7 = Vendor::where('vendor', '=', $request->vendor_7_lainnya);
                $count_check_vendor_7 = $check_vendor_7->count();

                if ($count_check_vendor_7 < 1) {
                    Vendor::create([
                        'vendor' => $request->vendor_7_lainnya,
                    ]);
                }

                $vendor_7 = $request->vendor_7_lainnya;
            } else {
                $vendor_7 = $request->vendor_7;
            }

            if ($request->vendor_8 == "Lainnya") {
                $check_vendor_8 = Vendor::where('vendor', '=', $request->vendor_8_lainnya);
                $count_check_vendor_8 = $check_vendor_8->count();

                if ($count_check_vendor_8 < 1) {
                    Vendor::create([
                        'vendor' => $request->vendor_8_lainnya,
                    ]);
                }

                $vendor_8 = $request->vendor_8_lainnya;
            } else {
                $vendor_8 = $request->vendor_8;
            }

            if ($request->vendor_9 == "Lainnya") {
                $check_vendor_9 = Vendor::where('vendor', '=', $request->vendor_9_lainnya);
                $count_check_vendor_9 = $check_vendor_9->count();

                if ($count_check_vendor_9 < 1) {
                    Vendor::create([
                        'vendor' => $request->vendor_9_lainnya,
                    ]);
                }

                $vendor_9 = $request->vendor_9_lainnya;
            } else {
                $vendor_9 = $request->vendor_9;
            }

            if ($request->vendor_10 == "Lainnya") {
                $check_vendor_10 = Vendor::where('vendor', '=', $request->vendor_10_lainnya);
                $count_check_vendor_10 = $check_vendor_10->count();

                if ($count_check_vendor_10 < 1) {
                    Vendor::create([
                        'vendor' => $request->vendor_10_lainnya,
                    ]);
                }

                $vendor_10 = $request->vendor_10_lainnya;
            } else {
                $vendor_10 = $request->vendor_10;
            }

            $data = Spk::create([
                'order_key' => $request->order_key,
                'sko_key' => $request->sko_key,
                'catatan_khusus' => $status_produksi,

                'harga1' => $this->formatHarga($request->harga1),
                'harga2' => $this->formatHarga($request->harga2),
                'harga3' => $this->formatHarga($request->harga3),
                'harga4' => $this->formatHarga($request->harga4),
                'harga5' => $this->formatHarga($request->harga5),
                'harga6' => $this->formatHarga($request->harga6),
                'harga7' => $this->formatHarga($request->harga7),
                'harga8' => $this->formatHarga($request->harga8),

                'pisaupond' => $request->pisaupond,
                'klisepoly' => $request->klisepoly,
                'kliseemboss' => $request->kliseemboss,
                'klisespotuv' => $request->klisespotuv,
                'ctv' => $request->ctv,
                'kertas' => $request->kertas,
                'filmpisau' => $request->filmpisau,
                'bahanfinishing' => $request->bahanfinishing,

                'harga_pracetak' => $this->formatHarga($request->harga_pracetak),
                'harga_cetak' => $this->formatHarga($request->harga_cetak),
                'harga_laminasi' => $this->formatHarga($request->harga_laminasi),
                'harga_poly' => $this->formatHarga($request->harga_poly),
                'harga_emboss' => $this->formatHarga($request->harga_emboss),
                'harga_spotuv' => $this->formatHarga($request->harga_spotuv),
                'harga_lapis' => $this->formatHarga($request->harga_lapis),
                'harga_jendelamika' => $this->formatHarga($request->harga_jendelamika),
                'harga_pond' => $this->formatHarga($request->harga_pond),
                'harga_finishing' => $this->formatHarga($request->harga_finishing),
                    'harga_dummy' => $this->formatHarga($request->harga_dummy),

                'progress_produksi_1' => $request->progress_produksi_1,
                'jumlah_plano' => $request->jumlah_plano,
                'vendor_1' => $vendor_1,
                'tgl_produksi_1' => $request->tgl_produksi_1,
                'tgl_selesai_1' => $request->tgl_selesai_1,
                'pic_validasi_1' => $request->pic_validasi_1,
                'flag_status_1' => $request->flag_status_1,
                'tgl_flag_status_1' => $tgl_flag_status_1,
                'catatan_1' => $request->catatan_1,
                'check_1_1' => $request->check_1_1,
                'check_1_2' => $request->check_1_2,
                'check_1_3' => $request->check_1_3,
                'check_1_4' => $request->check_1_4,
                'progress_produksi_2' => $request->progress_produksi_2,
                'jumlah_awal_pp_2' => $jumlah_awal_2,
                'jumlah_hasil_pp_2' => $jumlah_hasil_2,
                'reject_pp_2' => $reject_2,
                'vendor_2' => $vendor_2,
                'tgl_produksi_2' => $request->tgl_produksi_2,
                'tgl_selesai_2' => $request->tgl_selesai_2,
                'pic_validasi_2' => $request->pic_validasi_2,
                'flag_status_2' => $request->flag_status_2,
                'tgl_flag_status_2' => $tgl_flag_status_2,
                'catatan_2' => $request->catatan_2,
                'check_2_1' => $request->check_2_1,
                'check_2_2' => $request->check_2_2,
                'check_2_3' => $request->check_2_3,
                'check_2_4' => $request->check_2_4,
                'progress_produksi_3' => $request->progress_produksi_3,
                'jumlah_awal_pp_3' => $jumlah_awal_3,
                'jumlah_hasil_pp_3' => $jumlah_hasil_3,
                'reject_pp_3' => $reject_3,
                'vendor_3' => $vendor_3,
                'tgl_produksi_3' => $request->tgl_produksi_3,
                'tgl_selesai_3' => $request->tgl_selesai_3,
                'pic_validasi_3' => $request->pic_validasi_3,
                'flag_status_3' => $request->flag_status_3,
                'tgl_flag_status_3' => $tgl_flag_status_3,
                'catatan_3' => $request->catatan_3,
                'check_3_1' => $request->check_3_1,
                'check_3_2' => $request->check_3_2,
                'check_3_3' => $request->check_3_3,
                'check_3_4' => $request->check_3_4,
                'progress_produksi_4' => $request->progress_produksi_4,
                'jumlah_awal_pp_4' => $jumlah_awal_4,
                'jumlah_hasil_pp_4' => $jumlah_hasil_4,
                'reject_pp_4' => $reject_4,
                'vendor_4' => $vendor_4,
                'tgl_produksi_4' => $request->tgl_produksi_4,
                'tgl_selesai_5' => $request->tgl_selesai_5,
                'pic_validasi_4' => $request->pic_validasi_4,
                'flag_status_4' => $request->flag_status_4,
                'tgl_flag_status_4' => $tgl_flag_status_4,
                'catatan_4' => $request->catatan_4,
                'check_4_1' => $request->check_4_1,
                'check_4_2' => $request->check_4_2,
                'check_4_3' => $request->check_4_3,
                'check_4_4' => $request->check_4_4,
                'check_4_5' => $request->check_4_5,
                'progress_produksi_5' => $request->progress_produksi_5,
                'jumlah_awal_pp_5' => $jumlah_awal_5,
                'jumlah_hasil_pp_5' => $jumlah_hasil_5,
                'reject_pp_5' => $reject_5,
                'vendor_5' => $vendor_5,
                'tgl_produksi_5' => $request->tgl_produksi_5,
                'pic_validasi_5' => $request->pic_validasi_5,
                'flag_status_5' => $request->flag_status_5,
                'tgl_flag_status_5' => $tgl_flag_status_5,
                'catatan_5' => $request->catatan_5,
                'check_5_1' => $request->check_5_1,
                'check_5_2' => $request->check_5_2,
                'check_5_3' => $request->check_5_3,
                'check_5_4' => $request->check_5_4,
                'check_5_5' => $request->check_5_5,
                'progress_produksi_6' => $request->progress_produksi_6,
                'jumlah_awal_pp_6' => $jumlah_awal_6,
                'jumlah_hasil_pp_6' => $jumlah_hasil_6,
                'reject_pp_6' => $reject_6,
                'vendor_6' => $vendor_6,
                'tgl_produksi_6' => $request->tgl_produksi_6,
                'tgl_selesai_6' => $request->tgl_selesai_6,
                'pic_validasi_6' => $request->pic_validasi_6,
                'flag_status_6' => $request->flag_status_6,
                'tgl_flag_status_6' => $tgl_flag_status_6,
                'catatan_6' => $request->catatan_6,
                'check_6_1' => $request->check_6_1,
                'check_6_2' => $request->check_6_2,
                'check_6_3' => $request->check_6_3,
                'check_6_4' => $request->check_6_4,
                'progress_produksi_7' => $request->progress_produksi_7,
                'jumlah_awal_pp_7' => $jumlah_awal_7,
                'jumlah_hasil_pp_7' => $jumlah_hasil_7,
                'reject_pp_7' => $reject_7,
                'vendor_7' => $vendor_7,
                'tgl_produksi_7' => $request->tgl_produksi_7,
                'tgl_selesai_7' => $request->tgl_selesai_7,
                'pic_validasi_7' => $request->pic_validasi_7,
                'flag_status_7' => $request->flag_status_7,
                'tgl_flag_status_7' => $tgl_flag_status_7,
                'catatan_7' => $request->catatan_7,
                'check_7_1' => $request->check_7_1,
                'check_7_2' => $request->check_7_2,
                'progress_produksi_8' => $request->progress_produksi_8,
                'jumlah_awal_pp_8' => $jumlah_awal_8,
                'jumlah_hasil_pp_8' => $jumlah_hasil_8,
                'reject_pp_8' => $reject_8,
                'vendor_8' => $vendor_8,
                'tgl_produksi_8' => $request->tgl_produksi_8,
                'tgl_selesai_8' => $request->tgl_selesai_8,
                'pic_validasi_8' => $request->pic_validasi_8,
                'flag_status_8' => $request->flag_status_8,
                'tgl_flag_status_8' => $tgl_flag_status_8,
                'catatan_8' => $request->catatan_8,
                'check_8_1' => $request->check_8_1,
                'check_8_2' => $request->check_8_2,
                'check_8_3' => $request->check_8_3,
                'check_8_4' => $request->check_8_4,
                'check_8_5' => $request->check_8_5,
                'progress_produksi_9' => $request->progress_produksi_9,
                'jumlah_awal_pp_9' => $jumlah_awal_9,
                'jumlah_hasil_pp_9' => $jumlah_hasil_9,
                'reject_pp_9' => $reject_9,
                'vendor_9' => $vendor_9,
                'tgl_produksi_9' => $request->tgl_produksi_9,
                'tgl_selesai_9' => $request->tgl_selesai_9,
                'pic_validasi_9' => $request->pic_validasi_9,
                'flag_status_9' => $request->flag_status_9,
                'tgl_flag_status_9' => $tgl_flag_status_9,
                'catatan_9' => $request->catatan_9,
                'check_9_1' => $request->check_9_1,
                'check_9_2' => $request->check_9_2,
                'check_9_3' => $request->check_9_3,
                'check_9_4' => $request->check_9_4,
                'check_9_5' => $request->check_9_5,
                'progress_produksi_10' => $request->progress_produksi_10,
                'jumlah_awal_pp_10' => $jumlah_awal_10,
                'jumlah_hasil_pp_10' => $jumlah_hasil_10,
                'reject_pp_10' => $reject_10,
                'vendor_10' => $vendor_10,
                'tgl_produksi_10' => $request->tgl_produksi_10,
                'tgl_selesai_10' => $request->tgl_selesai_10,
                'pic_validasi_10' => $request->pic_validasi_10,
                'flag_status_10' => $request->flag_status_10,
                'tgl_flag_status_10' => $tgl_flag_status_10,
                'catatan_10' => $request->catatan_10,
                'check_10_1' => $request->check_10_1,
                'check_10_2' => $request->check_10_2,
                'check_10_3' => $request->check_10_3,
                'check_10_4' => $request->check_10_4,
                'check_10_5' => $request->check_10_5,
                'progress_produksi_dummy' => $request->progress_produksi_dummy,
                'check_dummy_1' => $request->check_dummy_1,
                'check_dummy_2' => $request->check_dummy_2,
                'check_dummy_3' => $request->check_dummy_3,
                'check_dummy_4' => $request->check_dummy_4,
                'check_dummy_5' => $request->check_dummy_5,
                'check_dummy_6' => $request->check_dummy_6,
                'check_dummy_7' => $request->check_dummy_7,
                'jumlah_fix' => $request->jumlah_fix,
                'tgl_kirim' => $request->tgl_kirim,
                'tgl_selesai_all' => $request->tgl_selesai_all,
                'total_keseluruhan_harga' => $this->formatHarga($request->total_keseluruhan_harga),
            ]);

                if ($request->ajax()) {
                    return Response::json(['data' => $data], 200);
                }
                return redirect()->route('production')->with('message', [
                    'type' => 'Success',
                    'text' => 'Production Added successfully',
                ]);
            } else {
                return redirect()->route('production')->with('message', [
                    'type' => 'Failed',
                    'text' => 'No Data added!',
                ]);

                if ($request->ajax()) {
                    return Response::json(['data' => $data], 200);
                }

                switch ($request->get('update_spk')) {
                    case 'Save':
                        $param = Crypt::encryptString($request->sko_key);
                        return redirect()->route('production.show_production', $param)
                            ->with('message', [
                                'type' => 'Success',
                                'text' => 'Production updated successfully',
                            ]);
                        break;

                    case 'Submit & Export SPK':
                        $param = Crypt::encryptString($request->sko_key);
                        return redirect()->route('production.export_spk', $param);
                        break;
                    case 'Submit':
                        return redirect()->route('production')->with('message', [
                            'type' => 'Success',
                            'text' => 'Production updated successfully',
                        ]);
                        break;
                }

            }



    }
    }

    public function show_production($param)
    {

        $sko_key = Crypt::decryptString($param);
        $enc_sko_key = Crypt::encryptString($sko_key);

        $id_spk = Crypt::decryptString($param);
        $data = Spk::leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            // ->leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_spks.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->select(['tb_spks.*', 'tb_orders.sko', 'tb_customers.nama', 'tb_produksis.lp_panjang', 'tb_produksis.lp_lebar', 'tb_produksis.jenis_kertas', 'tb_produksis.gramasi', 'tb_produksis.laminasi', 'tb_produksis.sisi_laminasi', 'tb_produksis.finishing', 'tb_produksis.jumlah_produk', 'tb_produksis.notes', 'tb_faws.tgl_deadline', 'tb_faws.waktu_produksi', 'tb_faws.tgl_faw', 'tb_faws.file_final', 'tb_faws.path_lampiran', 'tb_faws.keterangan_tambahan'])
            ->where('id_spk', $id_spk)
            ->orwhere('tb_spks.sko_key', $sko_key)
            ->get()->first();

        $vendor = Vendor::select('vendor')->get();



        $deadline = Faw::where('sko_key', $param)->value('tgl_deadline');


        return view('pages.production.form_edit_production', compact('data', 'vendor', 'enc_sko_key'));
    }

    public function update_production(Request $request)
    {
        $data_before = Spk::where('sko_key', $request->sko_key)
            ->get()
            ->first();

        if (empty($data_before->tgl_flag_status_1)) {
            if ($request->flag_status_1 == '1') {
                $tgl_flag_status_1 = date('Y-m-d H:i:s');
            } else {
                $tgl_flag_status_1 = null;
            }
        } else {
            $tgl_flag_status_1 = $data_before->tgl_flag_status_1;
        }


        if (empty($data_before->tgl_flag_status_2)) {
            if ($request->flag_status_2 == '1') {
                $tgl_flag_status_2 = date('Y-m-d H:i:s');
            } else {
                $tgl_flag_status_2 = null;
            }
        } else {
            $tgl_flag_status_2 = $data_before->tgl_flag_status_2;
        }

        if (empty($data_before->tgl_flag_status_3)) {
            if ($request->flag_status_3 == '1') {
                $tgl_flag_status_3 = date('Y-m-d H:i:s');
            } else {
                $tgl_flag_status_3 = null;
            }
        } else {
            $tgl_flag_status_3 = $data_before->tgl_flag_status_3;
        }

        if (empty($data_before->tgl_flag_status_4)) {
            if ($request->flag_status_4 == '1') {
                $tgl_flag_status_4 = date('Y-m-d H:i:s');
            } else {
                $tgl_flag_status_4 = null;
            }
        } else {
            $tgl_flag_status_4 = $data_before->tgl_flag_status_4;
        }

        if (empty($data_before->tgl_flag_status_5)) {
            if ($request->flag_status_5 == '1') {
                $tgl_flag_status_5 = date('Y-m-d H:i:s');
            } else {
                $tgl_flag_status_5 = null;
            }
        } else {
            $tgl_flag_status_5 = $data_before->tgl_flag_status_5;
        }

        if (empty($data_before->tgl_flag_status_6)) {
            if ($request->flag_status_6 == '1') {
                $tgl_flag_status_6 = date('Y-m-d H:i:s');
            } else {
                $tgl_flag_status_6 = null;
            }
        } else {
            $tgl_flag_status_6 = $data_before->tgl_flag_status_6;
        }

        if (empty($data_before->tgl_flag_status_7)) {
            if ($request->flag_status_7 == '1') {
                $tgl_flag_status_7 = date('Y-m-d H:i:s');
            } else {
                $tgl_flag_status_7 = null;
            }
        } else {
            $tgl_flag_status_7 = $data_before->tgl_flag_status_7;
        }

        if (empty($data_before->tgl_flag_status_8)) {
            if ($request->flag_status_8 == '1') {
                $tgl_flag_status_8 = date('Y-m-d H:i:s');
            } else {
                $tgl_flag_status_8 = null;
            }
        } else {
            $tgl_flag_status_8 = $data_before->tgl_flag_status_8;
        }

        if (empty($data_before->tgl_flag_status_9)) {
            if ($request->flag_status_9 == '1') {
                $tgl_flag_status_9 = date('Y-m-d H:i:s');
            } else {
                $tgl_flag_status_9 = null;
            }
        } else {
            $tgl_flag_status_9 = $data_before->tgl_flag_status_9;
        }

        if (empty($data_before->tgl_flag_status_10)) {
            if ($request->flag_status_10 == '1') {
                $tgl_flag_status_10 = date('Y-m-d H:i:s');
            } else {
                $tgl_flag_status_10 = null;
            }
        } else {
            $tgl_flag_status_10 = $data_before->tgl_flag_status_10;
        }

        if (empty($data_before->tgl_flag_status_dummy)) {
            if ($request->flag_status_dummy == '1') {
                $tgl_flag_status_dummy = date('Y-m-d H:i:s');
            } else {
                $tgl_flag_status_dummy = null;
            }
        } else {
            $tgl_flag_status_dummy = $data_before->tgl_flag_status_dummy;
        }

        if (empty($data_before->tgl_flag_status_ve)) {
            if ($request->flag_status_ve == '1') {
                $tgl_flag_status_ve = date('Y-m-d H:i:s');
            } else {
                $tgl_flag_status_ve = null;
            }
        } else {
            $tgl_flag_status_ve = $data_before->tgl_flag_status_ve;
        }


        if (empty($request->jumlah_fix) && $request->catatan_khusus != "Belum SPK" && $request->catatan_khusus == "Tuntas") {
            $catatan_khusus = $data_before->catatan_khusus;
        } else {
            $catatan_khusus = $request->catatan_khusus;
        }

        if (empty($request->progress_produksi_2)) {
            $jumlah_awal_2 = null;
            $jumlah_hasil_2 = null;
            $reject_2 = null;
        } else {
            $jumlah_awal_2 = $request->jumlah_awal_pp_2;
            $jumlah_hasil_2 = $request->jumlah_hasil_pp_2;
            $reject_2 = $request->reject_pp_2;
        }

        if (empty($request->progress_produksi_3)) {
            $jumlah_awal_3 = null;
            $jumlah_hasil_3 = null;
            $reject_3 = null;
        } else {
            $jumlah_awal_3 = $request->jumlah_awal_pp_3;
            $jumlah_hasil_3 = $request->jumlah_hasil_pp_3;
            $reject_3 = $request->reject_pp_3;
        }

        if (empty($request->progress_produksi_4)) {
            $jumlah_awal_4 = null;
            $jumlah_hasil_4 = null;
            $reject_4 = null;
        } else {
            $jumlah_awal_4 = $request->jumlah_awal_pp_4;
            $jumlah_hasil_4 = $request->jumlah_hasil_pp_4;
            $reject_4 = $request->reject_pp_4;
        }
        if (empty($request->progress_produksi_5)) {
            $jumlah_awal_5 = null;
            $jumlah_hasil_5 = null;
            $reject_5 = null;
        } else {
            $jumlah_awal_5 = $request->jumlah_awal_pp_5;
            $jumlah_hasil_5 = $request->jumlah_hasil_pp_5;
            $reject_5 = $request->reject_pp_5;
        }
        if (empty($request->progress_produksi_6)) {
            $jumlah_awal_6 = null;
            $jumlah_hasil_6 = null;
            $reject_6 = null;
        } else {
            $jumlah_awal_6 = $request->jumlah_awal_pp_6;
            $jumlah_hasil_6 = $request->jumlah_hasil_pp_6;
            $reject_6 = $request->reject_pp_6;
        }
        if (empty($request->progress_produksi_7)) {
            $jumlah_awal_7 = null;
            $jumlah_hasil_7 = null;
            $reject_7 = null;
        } else {
            $jumlah_awal_7 = $request->jumlah_awal_pp_7;
            $jumlah_hasil_7 = $request->jumlah_hasil_pp_7;
            $reject_7 = $request->reject_pp_7;
        }
        if (empty($request->progress_produksi_8)) {
            $jumlah_awal_8 = null;
            $jumlah_hasil_8 = null;
            $reject_8 = null;
        } else {
            $jumlah_awal_8 = $request->jumlah_awal_pp_8;
            $jumlah_hasil_8 = $request->jumlah_hasil_pp_8;
            $reject_8 = $request->reject_pp_8;
        }
        if (empty($request->progress_produksi_9)) {
            $jumlah_awal_9 = null;
            $jumlah_hasil_9 = null;
            $reject_9 = null;
        } else {
            $jumlah_awal_9 = $request->jumlah_awal_pp_9;
            $jumlah_hasil_9 = $request->jumlah_hasil_pp_9;
            $reject_9 = $request->reject_pp_9;
        }
        if (empty($request->progress_produksi_10)) {
            $jumlah_awal_10 = null;
            $jumlah_hasil_10 = null;
            $reject_10 = null;
        } else {
            $jumlah_awal_10 = $request->jumlah_awal_pp_10;
            $jumlah_hasil_10 = $request->jumlah_hasil_pp_10;
            $reject_10 = $request->reject_pp_10;
        }
        if (empty($request->progress_produksi_ve)) {
            $jumlah_awal_ve = null;
            $jumlah_hasil_ve = null;
            $reject_ve = null;
        } else {
            $jumlah_awal_ve = $request->jumlah_awal_pp_ve;
            $jumlah_hasil_ve = $request->jumlah_hasil_pp_ve;
            $reject_ve = $request->reject_pp_ve;
        }

        if ($request->vendor_1 == "Lainnya") {
            $check_vendor_1 = Vendor::where('vendor', '=', $request->vendor_1_lainnya);
            $count_check_vendor_1 = $check_vendor_1->count();

            if ($count_check_vendor_1 < 1) {
                Vendor::create([
                    'vendor' => $request->vendor_1_lainnya,
                ]);
            }

            $vendor_1 = $request->vendor_1_lainnya;
        } else {
            $vendor_1 = $request->vendor_1;
        }

        if ($request->vendor_2 == "Lainnya") {
            $check_vendor_2 = Vendor::where('vendor', '=', $request->vendor_2_lainnya);
            $count_check_vendor_2 = $check_vendor_2->count();

            if ($count_check_vendor_2 < 1) {
                Vendor::create([
                    'vendor' => $request->vendor_2_lainnya,
                ]);
            }

            $vendor_2 = $request->vendor_2_lainnya;
        } else {
            $vendor_2 = $request->vendor_2;
        }

        if ($request->vendor_3 == "Lainnya") {
            $check_vendor_3 = Vendor::where('vendor', '=', $request->vendor_3_lainnya);
            $count_check_vendor_3 = $check_vendor_3->count();

            if ($count_check_vendor_3 < 1) {
                Vendor::create([
                    'vendor' => $request->vendor_3_lainnya,
                ]);
            }

            $vendor_3 = $request->vendor_3_lainnya;
        } else {
            $vendor_3 = $request->vendor_3;
        }

        if ($request->vendor_4 == "Lainnya") {
            $check_vendor_4 = Vendor::where('vendor', '=', $request->vendor_4_lainnya);
            $count_check_vendor_4 = $check_vendor_4->count();

            if ($count_check_vendor_4 < 1) {
                Vendor::create([
                    'vendor' => $request->vendor_4_lainnya,
                ]);
            }

            $vendor_4 = $request->vendor_4_lainnya;
        } else {
            $vendor_4 = $request->vendor_4;
        }

        if ($request->vendor_5 == "Lainnya") {
            $check_vendor_5 = Vendor::where('vendor', '=', $request->vendor_5_lainnya);
            $count_check_vendor_5 = $check_vendor_5->count();

            if ($count_check_vendor_5 < 1) {
                Vendor::create([
                    'vendor' => $request->vendor_5_lainnya,
                ]);
            }

            $vendor_5 = $request->vendor_5_lainnya;
        } else {
            $vendor_5 = $request->vendor_5;
        }

        if ($request->vendor_6 == "Lainnya") {
            $check_vendor_6 = Vendor::where('vendor', '=', $request->vendor_6_lainnya);
            $count_check_vendor_6 = $check_vendor_6->count();

            if ($count_check_vendor_6 < 1) {
                Vendor::create([
                    'vendor' => $request->vendor_6_lainnya,
                ]);
            }

            $vendor_6 = $request->vendor_6_lainnya;
        } else {
            $vendor_6 = $request->vendor_6;
        }

        if ($request->vendor_7 == "Lainnya") {
            $check_vendor_7 = Vendor::where('vendor', '=', $request->vendor_7_lainnya);
            $count_check_vendor_7 = $check_vendor_7->count();

            if ($count_check_vendor_7 < 1) {
                Vendor::create([
                    'vendor' => $request->vendor_7_lainnya,
                ]);
            }

            $vendor_7 = $request->vendor_7_lainnya;
        } else {
            $vendor_7 = $request->vendor_7;
        }

        if ($request->vendor_8 == "Lainnya") {
            $check_vendor_8 = Vendor::where('vendor', '=', $request->vendor_8_lainnya);
            $count_check_vendor_8 = $check_vendor_8->count();

            if ($count_check_vendor_8 < 1) {
                Vendor::create([
                    'vendor' => $request->vendor_8_lainnya,
                ]);
            }

            $vendor_8 = $request->vendor_8_lainnya;
        } else {
            $vendor_8 = $request->vendor_8;
        }

        if ($request->vendor_9 == "Lainnya") {
            $check_vendor_9 = Vendor::where('vendor', '=', $request->vendor_9_lainnya);
            $count_check_vendor_9 = $check_vendor_9->count();

            if ($count_check_vendor_9 < 1) {
                Vendor::create([
                    'vendor' => $request->vendor_9_lainnya,
                ]);
            }

            $vendor_9 = $request->vendor_9_lainnya;
        } else {
            $vendor_9 = $request->vendor_9;
        }

        if ($request->vendor_10 == "Lainnya") {
            $check_vendor_10 = Vendor::where('vendor', '=', $request->vendor_10_lainnya);
            $count_check_vendor_10 = $check_vendor_10->count();

            if ($count_check_vendor_10 < 1) {
                Vendor::create([
                    'vendor' => $request->vendor_10_lainnya,
                ]);
            }

            $vendor_10 = $request->vendor_10_lainnya;
        } else {
            $vendor_10 = $request->vendor_10;
        }

        if ($request->vendor_ve == "Lainnya") {
            $check_vendor_ve = Vendor::where('vendor', '=', $request->vendor_ve_lainnya);
            $count_check_vendor_ve = $check_vendor_ve->count();

            if ($count_check_vendor_ve < 1) {
                Vendor::create([
                    'vendor' => $request->vendor_ve_lainnya,
                ]);
            }

            $vendor_ve = $request->vendor_ve_lainnya;
        } else {
            $vendor_ve = $request->vendor_ve;
        }

        $data = Spk::updateOrCreate(
            [
                'sko_key' => $request->sko_key
            ],
            [
                'catatan_khusus' => $catatan_khusus,
                'harga1' => $this->formatHarga($request->harga1),
                'harga2' => $this->formatHarga($request->harga2),
                'harga3' => $this->formatHarga($request->harga3),
                'harga4' => $this->formatHarga($request->harga4),
                'harga5' => $this->formatHarga($request->harga5),
                'harga6' => $this->formatHarga($request->harga6),
                'harga7' => $this->formatHarga($request->harga7),
                'harga8' => $this->formatHarga($request->harga8),

                'pisaupond' => $request->pisaupond,
                'klisepoly' => $request->klisepoly,
                'kliseemboss' => $request->kliseemboss,
                'klisespotuv' => $request->klisespotuv,
                'ctv' => $request->ctv,
                'kertas' => $request->kertas,
                'filmpisau' => $request->filmpisau,
                'bahanfinishing' => $request->bahanfinishing,

                'harga_pracetak' => $this->formatHarga($request->harga_pracetak),
                'harga_cetak' => $this->formatHarga($request->harga_cetak),
                'harga_laminasi' => $this->formatHarga($request->harga_laminasi),
                'harga_poly' => $this->formatHarga($request->harga_poly),
                'harga_emboss' => $this->formatHarga($request->harga_emboss),
                'harga_spotuv' => $this->formatHarga($request->harga_spotuv),
                'harga_lapis' => $this->formatHarga($request->harga_lapis),
                'harga_jendelamika' => $this->formatHarga($request->harga_jendelamika),
                'harga_pond' => $this->formatHarga($request->harga_pond),
                'harga_finishing' => $this->formatHarga($request->harga_finishing),
                'harga_dummy' => $this->formatHarga($request->harga_dummy),
                'harga_ve' => $this->formatHarga($request->harga_ve),

                'progress_produksi_1' => $request->progress_produksi_1,
                'jumlah_plano' => $request->jumlah_plano,
                'vendor_1' => $vendor_1,
                'tgl_produksi_1' => $request->tgl_produksi_1,
                'tgl_selesai_1' => $request->tgl_selesai_1,
                'pic_validasi_1' => $request->pic_validasi_1,
                'flag_status_1' => $request->flag_status_1,
                'tgl_flag_status_1' => $tgl_flag_status_1,
                'catatan_1' => $request->catatan_1,
                'check_1_1' => $request->check_1_1,
                'check_1_2' => $request->check_1_2,
                'check_1_3' => $request->check_1_3,
                'check_1_4' => $request->check_1_4,
                'progress_produksi_2' => $request->progress_produksi_2,
                'jumlah_awal_pp_2' => $jumlah_awal_2,
                'jumlah_hasil_pp_2' => $jumlah_hasil_2,
                'reject_pp_2' => $reject_2,
                'vendor_2' => $vendor_2,
                'tgl_produksi_2' => $request->tgl_produksi_2,
                'tgl_selesai_2' => $request->tgl_selesai_2,
                'pic_validasi_2' => $request->pic_validasi_2,
                'flag_status_2' => $request->flag_status_2,
                'tgl_flag_status_2' => $tgl_flag_status_2,
                'catatan_2' => $request->catatan_2,
                'check_2_1' => $request->check_2_1,
                'check_2_2' => $request->check_2_2,
                'check_2_3' => $request->check_2_3,
                'check_2_4' => $request->check_2_4,
                'progress_produksi_3' => $request->progress_produksi_3,
                'jumlah_awal_pp_3' => $jumlah_awal_3,
                'jumlah_hasil_pp_3' => $jumlah_hasil_3,
                'reject_pp_3' => $reject_3,
                'vendor_3' => $vendor_3,
                'tgl_produksi_3' => $request->tgl_produksi_3,
                'tgl_selesai_3' => $request->tgl_selesai_3,
                'pic_validasi_3' => $request->pic_validasi_3,
                'flag_status_3' => $request->flag_status_3,
                'tgl_flag_status_3' => $tgl_flag_status_3,
                'catatan_3' => $request->catatan_3,
                'check_3_1' => $request->check_3_1,
                'check_3_2' => $request->check_3_2,
                'check_3_3' => $request->check_3_3,
                'check_3_4' => $request->check_3_4,
                'progress_produksi_4' => $request->progress_produksi_4,
                'jumlah_awal_pp_4' => $jumlah_awal_4,
                'jumlah_hasil_pp_4' => $jumlah_hasil_4,
                'reject_pp_4' => $reject_4,
                'vendor_4' => $vendor_4,
                'tgl_produksi_4' => $request->tgl_produksi_4,
                'tgl_selesai_5' => $request->tgl_selesai_5,
                'pic_validasi_4' => $request->pic_validasi_4,
                'flag_status_4' => $request->flag_status_4,
                'tgl_flag_status_4' => $tgl_flag_status_4,
                'catatan_4' => $request->catatan_4,
                'check_4_1' => $request->check_4_1,
                'check_4_2' => $request->check_4_2,
                'check_4_3' => $request->check_4_3,
                'check_4_4' => $request->check_4_4,
                'check_4_5' => $request->check_4_5,
                'progress_produksi_5' => $request->progress_produksi_5,
                'jumlah_awal_pp_5' => $jumlah_awal_5,
                'jumlah_hasil_pp_5' => $jumlah_hasil_5,
                'reject_pp_5' => $reject_5,
                'vendor_5' => $vendor_5,
                'tgl_produksi_5' => $request->tgl_produksi_5,
                'pic_validasi_5' => $request->pic_validasi_5,
                'flag_status_5' => $request->flag_status_5,
                'tgl_flag_status_5' => $tgl_flag_status_5,
                'catatan_5' => $request->catatan_5,
                'check_5_1' => $request->check_5_1,
                'check_5_2' => $request->check_5_2,
                'check_5_3' => $request->check_5_3,
                'check_5_4' => $request->check_5_4,
                'check_5_5' => $request->check_5_5,
                'progress_produksi_6' => $request->progress_produksi_6,
                'jumlah_awal_pp_6' => $jumlah_awal_6,
                'jumlah_hasil_pp_6' => $jumlah_hasil_6,
                'reject_pp_6' => $reject_6,
                'vendor_6' => $vendor_6,
                'tgl_produksi_6' => $request->tgl_produksi_6,
                'tgl_selesai_6' => $request->tgl_selesai_6,
                'pic_validasi_6' => $request->pic_validasi_6,
                'flag_status_6' => $request->flag_status_6,
                'tgl_flag_status_6' => $tgl_flag_status_6,
                'catatan_6' => $request->catatan_6,
                'check_6_1' => $request->check_6_1,
                'check_6_2' => $request->check_6_2,
                'check_6_3' => $request->check_6_3,
                'check_6_4' => $request->check_6_4,
                'progress_produksi_7' => $request->progress_produksi_7,
                'jumlah_awal_pp_7' => $jumlah_awal_7,
                'jumlah_hasil_pp_7' => $jumlah_hasil_7,
                'reject_pp_7' => $reject_7,
                'vendor_7' => $vendor_7,
                'tgl_produksi_7' => $request->tgl_produksi_7,
                'tgl_selesai_7' => $request->tgl_selesai_7,
                'pic_validasi_7' => $request->pic_validasi_7,
                'flag_status_7' => $request->flag_status_7,
                'tgl_flag_status_7' => $tgl_flag_status_7,
                'catatan_7' => $request->catatan_7,
                'check_7_1' => $request->check_7_1,
                'check_7_2' => $request->check_7_2,
                'progress_produksi_8' => $request->progress_produksi_8,
                'jumlah_awal_pp_8' => $jumlah_awal_8,
                'jumlah_hasil_pp_8' => $jumlah_hasil_8,
                'reject_pp_8' => $reject_8,
                'vendor_8' => $vendor_8,
                'tgl_produksi_8' => $request->tgl_produksi_8,
                'tgl_selesai_8' => $request->tgl_selesai_8,
                'pic_validasi_8' => $request->pic_validasi_8,
                'flag_status_8' => $request->flag_status_8,
                'tgl_flag_status_8' => $tgl_flag_status_8,
                'catatan_8' => $request->catatan_8,
                'check_8_1' => $request->check_8_1,
                'check_8_2' => $request->check_8_2,
                'check_8_3' => $request->check_8_3,
                'check_8_4' => $request->check_8_4,
                'check_8_5' => $request->check_8_5,
                'progress_produksi_9' => $request->progress_produksi_9,
                'jumlah_awal_pp_9' => $jumlah_awal_9,
                'jumlah_hasil_pp_9' => $jumlah_hasil_9,
                'reject_pp_9' => $reject_9,
                'vendor_9' => $vendor_9,
                'tgl_produksi_9' => $request->tgl_produksi_9,
                'tgl_selesai_9' => $request->tgl_selesai_9,
                'pic_validasi_9' => $request->pic_validasi_9,
                'flag_status_9' => $request->flag_status_9,
                'tgl_flag_status_9' => $tgl_flag_status_9,
                'catatan_9' => $request->catatan_9,
                'check_9_1' => $request->check_9_1,
                'check_9_2' => $request->check_9_2,
                'check_9_3' => $request->check_9_3,
                'check_9_4' => $request->check_9_4,
                'check_9_5' => $request->check_9_5,
                'progress_produksi_10' => $request->progress_produksi_10,
                'jumlah_awal_pp_10' => $jumlah_awal_10,
                'jumlah_hasil_pp_10' => $jumlah_hasil_10,
                'reject_pp_10' => $reject_10,
                'vendor_10' => $vendor_10,
                'tgl_produksi_10' => $request->tgl_produksi_10,
                'tgl_selesai_10' => $request->tgl_selesai_10,
                'pic_validasi_10' => $request->pic_validasi_10,
                'flag_status_10' => $request->flag_status_10,
                'tgl_flag_status_10' => $tgl_flag_status_10,
                'catatan_10' => $request->catatan_10,
                'check_10_1' => $request->check_10_1,
                'check_10_2' => $request->check_10_2,
                'check_10_3' => $request->check_10_3,
                'check_10_4' => $request->check_10_4,
                'check_10_5' => $request->check_10_5,
                'progress_produksi_dummy' => $request->progress_produksi_dummy,
                'check_dummy_1' => $request->check_dummy_1,
                'check_dummy_2' => $request->check_dummy_2,
                'check_dummy_3' => $request->check_dummy_3,
                'check_dummy_4' => $request->check_dummy_4,
                'check_dummy_5' => $request->check_dummy_5,
                'check_dummy_6' => $request->check_dummy_6,
                'check_dummy_7' => $request->check_dummy_7,
                'flag_status_dummy' => $request->flag_status_dummy,
                'tgl_flag_status_dummy' => $request->tgl_flag_status_dummy,
                'progress_produksi_ve' => $request->progress_produksi_ve,
                'jumlah_awal_pp_ve' => $jumlah_awal_ve,
                'jumlah_hasil_pp_ve' => $jumlah_hasil_ve,
                'reject_pp_ve' => $reject_ve,
                'vendor_ve' => $vendor_ve,
                'tgl_produksi_ve' => $request->tgl_produksi_ve,
                'tgl_selesai_ve' => $request->tgl_selesai_ve,
                'pic_validasi_ve' => $request->pic_validasi_ve,
                'check_ve_1' => $request->check_ve_1,
                'check_ve_2' => $request->check_ve_2,
                'check_ve_3' => $request->check_ve_3,
                'check_ve_4' => $request->check_ve_4,
                'catatan_ve' => $request->catatan_ve,
                'flag_status_ve' => $request->flag_status_ve,
                'tgl_flag_status_ve' => $tgl_flag_status_ve,
                'jumlah_fix' => $request->jumlah_fix,
                'tgl_kirim' => $request->tgl_kirim,
                'tgl_selesai_all' => $request->tgl_selesai_all,
                'total_keseluruhan_harga' => $this->formatHarga($request->total_keseluruhan_harga),
            ]
        );

        if ($request->ajax()) {
            return Response::json(['data' => $data], 200);
        }

        switch ($request->get('update_spk')) {
            case 'Save':
                $param = Crypt::encryptString($request->sko_key);
                return redirect()->route('production.show_production', $param)
                ->with('message', [
                    'type' => 'Success',
                    'text' => 'Production updated successfully',
                ]);
                break;

            case 'Export SPK':
                $param = Crypt::encryptString($request->sko_key);
                return redirect()->route('production.export_spk', $param);
                break;
            case 'Submit':
                return redirect()->route('production')->with('message', [
                    'type' => 'Success',
                    'text' => 'Production updated successfully',
                ]);
                break;

        }
    }

    public function export_spk($param)
    {
        $sko_key = Crypt::decryptString($param);
        $enc_sko_key = Crypt::encryptString($sko_key);
        $data_spk = Spk::leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select(['tb_spks.*', 'tb_orders.sko', 'tb_customers.nama', 'tb_produksis.lp_panjang', 'tb_produksis.lp_lebar', 'tb_produksis.gramasi', 'tb_produksis.laminasi', 'tb_produksis.sisi_laminasi', 'tb_produksis.finishing', 'tb_produksis.jumlah_produk', 'tb_produksis.notes'])
            ->where('tb_spks.sko_key', $sko_key)
            ->get()->first();

        return view('pages.production.form_export', compact('data_spk', 'enc_sko_key'));
    }

    public function generate_spk($param)
    {
        $sko_key = Crypt::decryptString($param);
        $data = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', 'like', 'tb_orders.sko_key')
            ->leftJoin('users', 'users.id', '=', 'tb_orders.id_pic')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_spks', 'tb_orders.sko_key', '=', 'tb_spks.sko_key')
            ->leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_faws.sko_key')
            ->where('tb_orders.sko_key', 'like', $sko_key)
            ->limit(1)
            ->get()->first();

        // spesifikasi: item.lp_panjang+' X '+item.lp_lebar+', '+
        // item.jenis_kertas+', '+
        // item.gramasi+', '+
        // item.laminasi+', '+
        // item.sisi_laminasi+', '+
        // item.finishing+', '+
        // item.jumlah_produk+' pcs, '+
        // notes
        $lp_panjang = $data->lp_panjang;
        $lp_lebar = $data->lp_lebar;
        $jenis_kertas = $data->jenis_kertas;
        $gramasi = $data->gramasi;
        $laminasi = $data->laminasi;
        $sisi_laminasi = $data->sisi_laminasi;
        $finishing = $data->finishing;
        $jumlah_produk = $data->jumlah_produk . ' pcs';
        $notes = $data->notes ? ', ' . $data->notes : '';

        $spesifikasi = $lp_panjang . 'cm X ' . $lp_lebar . 'cm, ' . $jenis_kertas . ', ' . $gramasi . ', ' . $laminasi . ', ' . $sisi_laminasi . ', ' . $finishing . ', ' . $jumlah_produk . ', ' . $notes . '.';

        $proses_1 = 'PRA-CETAK';
        $proses_2 = 'CETAK';
        $proses_3 = 'LAMINASI';
        $proses_4 = 'POLY';
        $proses_5 = 'EMBOSS';
        $proses_6 = 'SPOT UV';
        $proses_7 = 'LAPIS';
        $proses_8 = 'JENDELA MIKA';
        $proses_9 = 'POND';
        $proses_10 = 'FINISHING';

        $check_1_1 = 'Kesesuaian kertas';
        $check_1_2 = 'Kesesuaian CTP';
        $check_1_3 = 'Jadwal naik cetak';
        $check_1_4 = 'Acuan cetak';

        $check_2_1 = 'ACC cetak';
        $check_2_2 = 'Jaga warna';
        $check_2_3 = 'Hasil didiamkan satu hari';
        $check_2_4 = 'Check acak hasil cetak';

        $check_3_1 = 'Jadwal naik laminasi';
        $check_3_2 = 'SPK laminasi sesuai';
        $check_3_3 = 'Check acak hasil';

        $check_4_1 = 'Jadwal naik poly';
        $check_4_2 = 'Klise poly';
        $check_4_3 = 'Acuan poly';
        $check_4_4 = 'ACC poly';
        $check_4_5 = 'Check acak hasil';

        $check_5_1 = 'Jadwal naik emboss';
        $check_5_2 = 'Klise emboss';
        $check_5_3 = 'Acuan emboss';
        $check_5_4 = 'ACC emboss';
        $check_5_5 = 'Check acak hasil';

        $check_6_1 = 'Jadwal naik spot uv';
        $check_6_2 = 'Klise spot uv';
        $check_6_3 = 'Check acak hasil';

        $check_7_1 = 'Jadwal naik lapis';
        $check_7_2 = 'Check acak hasil';

        $check_9_1 = 'Jadwal naik pond';
        $check_9_2 = 'Pisau pond';
        $check_9_3 = 'Acuan pond';
        $check_9_4 = 'ACC pond';
        $check_9_5 = 'Check acak hasil';

        $check_10_1 = 'Jadwal naik finishing';
        $check_10_2 = 'Check acak/ sortir hasil';
        $check_10_3 = 'Packing';
        $check_10_4 = 'Label QC';
        $check_10_5 = 'Surat Jalan';


        if (!empty($data->progress_produksi_1)) {
            $kertas_plano = $data->jumlah_plano;
            $vendor_1 = $data->vendor_1;
            $tgl_produksi_1 = date('d/m/y', strtotime($data->tgl_produksi_1));
            $catatan_1 = $data->catatan_1;
        } else {
            $kertas_plano = '';
            $vendor_1 = '';
            $tgl_produksi_1 = '';
            $catatan_1 = '';
        }

        if (!empty($data->progress_produksi_2)) {
            $bahan_2 = $data->jumlah_awal_pp_2;
            $hasil_2 = $data->jumlah_hasil_pp_2;
            $reject_2 = $data->reject_pp_2;
            $vendor_2 = $data->vendor_2;
            $tgl_produksi_2 = $data->tgl_produksi_2 ? date('d/m/y', strtotime($data->tgl_produksi_2)) : '';
            $catatan_2 = $data->catatan_2;
        } else {
            $bahan_2 = '';
            $hasil_2 = '';
            $reject_2 = '';
            $vendor_2 = '';
            $tgl_produksi_2 = '';
            $catatan_2 = '';
        }

        if (!empty($data->progress_produksi_3)) {
            $bahan_3 = $data->jumlah_awal_pp_3;
            $hasil_3 = $data->jumlah_hasil_pp_3;
            $reject_3 = $data->reject_pp_3;
            $vendor_3 = $data->vendor_3;
            $tgl_produksi_3 = $data->tgl_produksi_3 ? date('d/m/y', strtotime($data->tgl_produksi_3)) : '';
            $catatan_3 = $data->catatan_3;
        } else {
            $bahan_3 = '';
            $hasil_3 = '';
            $reject_3 = '';
            $vendor_3 = '';
            $tgl_produksi_3 = '';
            $catatan_3 = '';
        }

        if (!empty($data->progress_produksi_4)) {
            $bahan_4 = $data->jumlah_awal_pp_4;
            $hasil_4 = $data->jumlah_hasil_pp_4;
            $reject_4 = $data->reject_pp_4;
            $vendor_4 = $data->vendor_4;
            $tgl_produksi_4 = $data->tgl_produksi_4 ? date('d/m/y', strtotime($data->tgl_produksi_4)) : '';
            $catatan_4 = $data->catatan_4;
        } else {
            $bahan_4 = '';
            $hasil_4 = '';
            $reject_4 = '';
            $vendor_4 = '';
            $tgl_produksi_4 = '';
            $catatan_4 = '';
        }

        if (!empty($data->progress_produksi_5)) {
            $bahan_5 = $data->jumlah_awal_pp_5;
            $hasil_5 = $data->jumlah_hasil_pp_5;
            $reject_5 = $data->reject_pp_5;
            $vendor_5 = $data->vendor_5;
            $tgl_produksi_5 = $data->tgl_produksi_5 ? date('d/m/y', strtotime($data->tgl_produksi_5)) : '';
            $catatan_5 = $data->catatan_5;
        } else {
            $bahan_5 = '';
            $hasil_5 = '';
            $reject_5 = '';
            $vendor_5 = '';
            $tgl_produksi_5 = '';
            $catatan_5 = '';
        }

        if (!empty($data->progress_produksi_6)) {
            $bahan_6 = $data->jumlah_awal_pp_6;
            $hasil_6 = $data->jumlah_hasil_pp_6;
            $reject_6 = $data->reject_pp_6;
            $vendor_6 = $data->vendor_6;
            $tgl_produksi_6 = $data->tgl_produksi_6 ? date('d/m/y', strtotime($data->tgl_produksi_6)) : '';
            $catatan_6 = $data->catatan_6;
        } else {
            $bahan_6 = '';
            $hasil_6 = '';
            $reject_6 = '';
            $vendor_6 = '';
            $tgl_produksi_6 = '';
            $catatan_6 = '';
        }

        if (!empty($data->progress_produksi_7)) {
            $bahan_7 = $data->jumlah_awal_pp_7;
            $hasil_7 = $data->jumlah_hasil_pp_7;
            $reject_7 = $data->reject_pp_7;
            $vendor_7 = $data->vendor_7;
            $tgl_produksi_7 = $data->tgl_produksi_7 ? date('d/m/y', strtotime($data->tgl_produksi_7)) : '';
            $catatan_7 = $data->catatan_7;
        } else {
            $bahan_7 = '';
            $hasil_7 = '';
            $reject_7 = '';
            $vendor_7 = '';
            $tgl_produksi_7 = '';
            $catatan_7 = '';
        }

        if (!empty($data->progress_produksi_8)) {
            $bahan_8 = $data->jumlah_awal_pp_8;
            $hasil_8 = $data->jumlah_hasil_pp_8;
            $reject_8 = $data->reject_pp_8;
            $vendor_8 = $data->vendor_8;
            $tgl_produksi_8 = $data->tgl_produksi_8 ? date('d/m/y', strtotime($data->tgl_produksi_8)) : '';
            $catatan_8 = $data->catatan_8;
        } else {
            $bahan_8 = '';
            $hasil_8 = '';
            $reject_8 = '';
            $vendor_8 = '';
            $tgl_produksi_8 = '';
            $catatan_8 = '';
        }

        if (!empty($data->progress_produksi_9)) {
            $bahan_9 = $data->jumlah_awal_pp_9;
            $hasil_9 = $data->jumlah_hasil_pp_9;
            $reject_9 = $data->reject_pp_9;
            $vendor_9 = $data->vendor_9;
            $tgl_produksi_9 = $data->tgl_produksi_9 ? date('d/m/y', strtotime($data->tgl_produksi_9)) : '';
            $catatan_9 = $data->catatan_9;
        } else {
            $bahan_9 = '';
            $hasil_9 = '';
            $reject_9 = '';
            $vendor_9 = '';
            $tgl_produksi_9 = '';
            $catatan_9 = '';
        }

        if (!empty($data->progress_produksi_10)) {
            $bahan_10 = $data->jumlah_awal_pp_10;
            $hasil_10 = $data->jumlah_hasil_pp_10;
            $reject_10 = $data->reject_pp_10;
            $vendor_10 = $data->vendor_10;
            $tgl_produksi_10 = $data->tgl_produksi_10 ? date('d/m/y', strtotime($data->tgl_produksi_10)) : '';
            $catatan_10 = $data->catatan_10;
        } else {
            $bahan_10 = '';
            $hasil_10 = '';
            $reject_10 = '';
            $vendor_10 = '';
            $tgl_produksi_10 = '';
            $catatan_10 = '';
        }

        $templateProcessor = new TemplateProcessor(storage_path('app/public/template/spk/template_SPK.docx'));
        $templateProcessor->setValue('nama_customer', htmlspecialchars($data->nama) . '_' . htmlspecialchars($data->tipe_produk) . '_' . $data->sko . '_' . $data->name);
        $templateProcessor->setValue('spesifikasi', htmlspecialchars($spesifikasi));
        $templateProcessor->setValue('tgl_faw', date('d F Y', strtotime($data->tgl_faw)));
        // $templateProcessor->setValue('tgl_deadline', date('d F Y', strtotime($data->tgl_deadline)));
       
        $templateProcessor->setValue('proses_1', $proses_1);
        $templateProcessor->setValue('kertas_plano', $kertas_plano);
        $templateProcessor->setValue('vendor_1', htmlspecialchars($vendor_1));
        $templateProcessor->setValue('tgl_produksi_1', $tgl_produksi_1);
        $templateProcessor->setValue('check_1_1', $check_1_1);
        $templateProcessor->setValue('check_1_2', $check_1_2);
        $templateProcessor->setValue('check_1_3', $check_1_3);
        $templateProcessor->setValue('check_1_4', $check_1_4);
        $templateProcessor->setValue('catatan_1', htmlspecialchars($catatan_1));


        $templateProcessor->setValue('proses_2', $proses_2);
        $templateProcessor->setValue('bahan_2', $bahan_2);
        $templateProcessor->setValue('hasil_2', $hasil_2);
        $templateProcessor->setValue('reject_2', $reject_2);
        $templateProcessor->setValue('vendor_2', $vendor_2);
        $templateProcessor->setValue('tgl_produksi_2', $tgl_produksi_2);
        $templateProcessor->setValue('check_2_1', $check_2_1);
        $templateProcessor->setValue('check_2_2', $check_2_2);
        $templateProcessor->setValue('check_2_3', $check_2_3);
        $templateProcessor->setValue('check_2_4', $check_2_4);
        $templateProcessor->setValue('catatan_2', htmlspecialchars($catatan_2));

        $templateProcessor->setValue('proses_3', $proses_3);
        $templateProcessor->setValue('bahan_3', $bahan_3);
        $templateProcessor->setValue('hasil_3', $hasil_3);
        $templateProcessor->setValue('reject_3', $reject_3);
        $templateProcessor->setValue('vendor_3', $vendor_3);
        $templateProcessor->setValue('tgl_produksi_3', $tgl_produksi_3);
        $templateProcessor->setValue('check_3_1', $check_3_1);
        $templateProcessor->setValue('check_3_2', $check_3_2);
        $templateProcessor->setValue('check_3_3', $check_3_3);
        $templateProcessor->setValue('catatan_3', htmlspecialchars($catatan_3));

        $templateProcessor->setValue('proses_4', $proses_4);
        $templateProcessor->setValue('bahan_4', $bahan_4);
        $templateProcessor->setValue('hasil_4', $hasil_4);
        $templateProcessor->setValue('reject_4', $reject_4);
        $templateProcessor->setValue('vendor_4', $vendor_4);
        $templateProcessor->setValue('tgl_produksi_4', $tgl_produksi_4);
        $templateProcessor->setValue('check_4_1', $check_4_1);
        $templateProcessor->setValue('check_4_2', $check_4_2);
        $templateProcessor->setValue('check_4_3', $check_4_3);
        $templateProcessor->setValue('check_4_4', $check_4_4);
        $templateProcessor->setValue('check_4_5', $check_4_5);
        $templateProcessor->setValue('catatan_4', htmlspecialchars($catatan_4));

        $templateProcessor->setValue('proses_5', $proses_5);
        $templateProcessor->setValue('bahan_5', $bahan_5);
        $templateProcessor->setValue('hasil_5', $hasil_5);
        $templateProcessor->setValue('reject_5', $reject_5);
        $templateProcessor->setValue('vendor_5', $vendor_5);
        $templateProcessor->setValue('tgl_produksi_5', $tgl_produksi_5);
        $templateProcessor->setValue('check_5_1', $check_5_1);
        $templateProcessor->setValue('check_5_2', $check_5_2);
        $templateProcessor->setValue('check_5_3', $check_5_3);
        $templateProcessor->setValue('check_5_4', $check_5_4);
        $templateProcessor->setValue('check_5_5', $check_5_5);
        $templateProcessor->setValue('catatan_5', htmlspecialchars($catatan_5));

        $templateProcessor->setValue('proses_6', $proses_6);
        $templateProcessor->setValue('bahan_6', $bahan_6);
        $templateProcessor->setValue('hasil_6', $hasil_6);
        $templateProcessor->setValue('reject_6', $reject_6);
        $templateProcessor->setValue('vendor_6', $vendor_6);
        $templateProcessor->setValue('tgl_produksi_6', $tgl_produksi_6);
        $templateProcessor->setValue('check_6_1', $check_6_1);
        $templateProcessor->setValue('check_6_2', $check_6_2);
        $templateProcessor->setValue('check_6_3', $check_6_3);
        $templateProcessor->setValue('catatan_6', htmlspecialchars($catatan_6));

        $templateProcessor->setValue('proses_7', $proses_7);
        $templateProcessor->setValue('bahan_7', $bahan_7);
        $templateProcessor->setValue('hasil_7', $hasil_7);
        $templateProcessor->setValue('reject_7', $reject_7);
        $templateProcessor->setValue('vendor_7', $vendor_7);
        $templateProcessor->setValue('tgl_produksi_7', $tgl_produksi_7);
        $templateProcessor->setValue('check_7_1', $check_7_1);
        $templateProcessor->setValue('check_7_2', $check_7_2);
        $templateProcessor->setValue('catatan_7', htmlspecialchars($catatan_7));

        $templateProcessor->setValue('proses_8', $proses_8);
        $templateProcessor->setValue('bahan_8', $bahan_8);
        $templateProcessor->setValue('hasil_8', $hasil_8);
        $templateProcessor->setValue('reject_8', $reject_8);
        $templateProcessor->setValue('vendor_8', $vendor_8);
        $templateProcessor->setValue('tgl_produksi_8', $tgl_produksi_8);
        $templateProcessor->setValue('catatan_8', htmlspecialchars($catatan_8));

        $templateProcessor->setValue('proses_9', $proses_9);
        $templateProcessor->setValue('bahan_9', $bahan_9);
        $templateProcessor->setValue('hasil_9', $hasil_9);
        $templateProcessor->setValue('reject_9', $reject_9);
        $templateProcessor->setValue('vendor_9', $vendor_9);
        $templateProcessor->setValue('tgl_produksi_9', $tgl_produksi_9);
        $templateProcessor->setValue('check_9_1', $check_9_1);
        $templateProcessor->setValue('check_9_2', $check_9_2);
        $templateProcessor->setValue('check_9_3', $check_9_3);
        $templateProcessor->setValue('check_9_4', $check_9_4);
        $templateProcessor->setValue('check_9_5', $check_9_5);
        $templateProcessor->setValue('catatan_9', htmlspecialchars($catatan_9));

        $templateProcessor->setValue('proses_10', $proses_10);
        $templateProcessor->setValue('bahan_10', $bahan_10);
        $templateProcessor->setValue('hasil_10', $hasil_10);
        $templateProcessor->setValue('reject_10', $reject_10);
        $templateProcessor->setValue('vendor_10', $vendor_10);
        $templateProcessor->setValue('tgl_produksi_10', $tgl_produksi_10);
        $templateProcessor->setValue('check_10_1', $check_10_1);
        $templateProcessor->setValue('check_10_2', $check_10_2);
        $templateProcessor->setValue('check_10_3', $check_10_3);
        $templateProcessor->setValue('check_10_4', $check_10_4);
        $templateProcessor->setValue('check_10_5', $check_10_5);
        $templateProcessor->setValue('catatan_10', htmlspecialchars($catatan_10));


        $datemark = date('dmy');
        $filename = 'SPK ' . $data->nama . ' ' . $datemark . '.docx';
        $templateProcessor->saveAs($filename);

        \PhpOffice\PhpWord\Settings::setOutputEscapingEnabled(true);
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filename));
        ob_clean();
        flush();
        readfile($filename);
        unlink($filename);
        exit;
    }
}
