<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Order;
use Illuminate\Http\Request;
use App\Models\ActivityTaskOrder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Yajra\DataTables\Facades\DataTables;

class SalesController extends Controller
{
    public function index(Request $request)
    {
        return view('pages.sales.index');
    }

    public function task(Request $request)
    {
        return view('pages.sales.task.index');
    }

    public function taskList(Request $request, $order_key)
    {
        $order = Order::with(['tb_customer', 'tb_produksi:sko_key,jumlah_produk,kategori_produksi', 'user:id,name'])
                        ->where('order_key', $order_key)
                        ->firstOrFail();
        return view('pages.sales.task.listTask', compact('order'));
    }

    public function dataTable(Request $request)
    {
        $data = Order::with([
            'tb_customer:id_customer,nama',
            'tb_produksi:sko_key,jumlah_produk,kategori_produksi,total_harga,nama_produk',
            'user:id,name',
            'activityTaskOrder'
            ])
            ->filter($request);

        // Filter by due_date if present
        if ($request->filled('end_due_date')) {
            $startDueDate = Carbon::parse($request->start_due_date)->format('Y-m-d');
            $endDueDate = Carbon::parse($request->end_due_date)->format('Y-m-d');
            $data = $data->whereHas('activityTaskOrder', function ($query) use ($startDueDate, $endDueDate) {
                $query->whereBetween('due_date', [$startDueDate, $endDueDate]);
            });
        }

        // If filtering by progress, use collection, otherwise keep as query builder
        if ($request->has('progress')) {
            $progress = $request->progress;
            $orders = $data->get()->filter(function ($order) use ($progress) {
            $totalTasks = $order->activityTaskOrder->count();
            $completedTasks = $order->activityTaskOrder->where('is_completed', 1)->count();
            $progressPercentage = $totalTasks > 0 ? ($completedTasks / $totalTasks) * 100 : 0;
            if ($progress != 'all') {
                return $progressPercentage == $progress;
            }
            return true;
            });
        } else {
            $data = $data->orderBy('tb_orders.waktu_kontak', 'desc');
            $orders = $data;
        }

        return DataTables::of($orders)
            ->addIndexColumn()
            ->addColumn('order', function ($row) {
            $nama_produk = $row->tb_produksi->nama_produk ?? '-';
            $kategori_produksi = $row->tb_produksi->kategori_produksi ?? '-';
            // $jumlah_produk = $row->tb_produksi->jumlah_produk ?? '-';
            // $item_sko = $row->sko ?? $row->sko_key ?? '-';
            // $order = "$kategori_produksi - $jumlah_produk [$item_sko]";
            return "<a href='" . route('sales.task.list', $row->order_key) . "' class='text-primary'>$kategori_produksi - $nama_produk</a>";
            })
            ->addColumn('created_at', fn($row) => $row->created_at ? date('d M Y H:i', strtotime($row->created_at)) : '-')
            ->addColumn('waktu_kontak', fn($row) => $row->waktu_kontak ? date('d M Y', strtotime($row->waktu_kontak)) : '-')
            ->addColumn('tanggal_jatuh_tempo', fn($row) =>
            $row->tanggal_jatuh_tempo
                ? Carbon::parse($row->tanggal_jatuh_tempo)->locale('id')->translatedFormat('l, d F Y') ?? 'N/A'
                : '-'
            )
            ->addColumn('created_by', fn($row) => $row->user->name ?? '-')
            ->editColumn('status_deal', function ($row) {
                switch ($row->status_deal) {
                    case 'Follow Up':
                        return '<div class="badge badge-light-primary fw-bold">' . $row->status_deal . '</div>';
                    case 'Deal':
                        return '<div class="badge badge-light-success fw-bold">' . $row->status_deal . '</div>';
                    case 'Lost':
                        return '<div class="badge badge-light-danger fw-bold">' . $row->status_deal . '</div>';
                    default:
                        return '';
                }
            })
            ->editColumn('status_order', fn($row) =>
            $row->status_order ? '<div class="badge badge-light">' . $row->status_order . '</div>' : ''
            )
            ->addColumn('total_harga', fn($row) =>
            $row->tb_produksi->total_harga ? 'Rp. ' . number_format($row->tb_produksi->total_harga, 0, '', '.') : ''
            )
            ->editColumn('kategori_produksi', fn($row) => $row->tb_produksi->kategori_produksi ?? "-")
            ->addColumn('action', fn($row) =>
            '<a href="' . route('sales.task.list', $row->order_key) . '" class="btn btn-sm btn-primary">Lihat Task</a>'
            )
            ->addColumn('progress', function ($row) {
            $totalTasks = $row->activityTaskOrder->count() ?? 0;
            $completedTasks = $row->activityTaskOrder->where('is_completed', 1)->count() ?? 0;
            $progressPercentage = $totalTasks > 0 ? ($completedTasks / $totalTasks) * 100 : 0;
            return '<div class="progress rounded bg-secondary" style="height: 10px;">
                <div class="progress-bar bg-success" role="progressbar" style="width: ' . $progressPercentage . '%;" aria-valuenow="' . $progressPercentage . '" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
            <div style="font-size: 12px; color: #6c757d; text-align: center; margin-top: 5px;">
                <strong>' . $completedTasks . '</strong> of <strong>' . $totalTasks . '</strong> (' . round($progressPercentage, 2) . '%)
            </div>';
            })
            ->rawColumns([
            'status_deal',
            'status_order',
            'created_at',
            'tanggal_jatuh_tempo',
            'created_by',
            'kategori_produksi',
            'order',
            'action',
            'progress'
            ])
            ->smart(true)
            ->make(true);
    }

    public function dataTableListTask(Request $request,  $order_key)
    {
        $data = ActivityTaskOrder::select(
            'id as id_task',
            'order_id',
            'order_key',
            'title',
            'detail',
            'due_date',
            'completed_at',
            'is_completed',
            'created_by_id',
            'created_at',
        )->with(['createdBy:id,name'])
        ->where('order_key', $order_key)
        ->filter($request)
            ->latest();

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('status', function ($row) {
                if ($row->is_completed) {
                    return '<span class="badge badge-success"><i class="fas fa-check-circle pe-1 text-white"></i> Completed</span>';
                } else {
                    return '<span class="badge badge-danger"><i class="fas fa-times-circle pe-1 text-white"></i> Not Completed</span>';
                }
            })
            ->addColumn('created_at', function ($row) {
                return $row->created_at ? \Carbon\Carbon::parse($row->created_at)->locale('id')->translatedFormat('d M Y') : '-';
            })
            ->addColumn('due_date', function ($row) {
                return $row->due_date ? Carbon::parse($row->due_date)->locale('id')->translatedFormat('d M Y') : '-';
            })
            ->addColumn('created_by', function ($row) {
                return $row->createdBy ? $row->createdBy->name : '-';
            })
            ->editColumn('status_order', function ($row) {
                return $row->status_order ? '<div class="badge badge-light">' . $row->status_order . '</div>' : '';
            })
            ->editColumn('completed_date', function ($row) {
                return $row->completed_at ? \Carbon\Carbon::parse($row->completed_at)->locale('id')->translatedFormat('d M Y H:i') : '-';
            })
            ->addColumn('action', function ($row){
                if (!$row->is_completed) {
                    return '<a class="btn btn-sm btn-primary edit-task" data-id="' . $row->id_task . '"><i class="fa fa-edit"></i> Edit</a>';
                }
            })
            ->rawColumns([
                'status',
                'created_by',
                'action',
                'due_date',
                'action'
            ])
            ->smart(true)
            ->make(true);
    }

    public function taskShow($id) {
        $task = ActivityTaskOrder::select('*','id as id_task')->where('id',$id)->first();
        return response()->json($task);
    }
}
