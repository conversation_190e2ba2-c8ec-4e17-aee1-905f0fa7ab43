<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\KategoriMasalah;
use Illuminate\Support\Facades\Response;
use Yajra\DataTables\Facades\DataTables;

class KategoriMasalahController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(request()->ajax())
        {
            $query = KategoriMasalah::query();
            return DataTables::of($query)
                ->addColumn('action', function($item){
                    return '
                    <div class="row">
                        <div class="col-md-6">
                            <a class="btn btn-sm btn-primary w-100 text-white edit-kategori_masalah" data-id="'.$item->id_kategori_masalah.'" href="javascript:void(0)">Edit</a>
                        </div>
                        <div class="col-md-6">
                            <a href="javascript:void(0)" class="btn btn-sm btn-danger w-100 text-white delete-link" data-id-destroy="'.$item->id_kategori_masalah.'" onclick="deleteConfirmation('.$item->id_kategori_masalah.')">Delete</a>
                        </div>
                    </div>
                    ';
                })
                ->rawColumns(['action','roles'])
                ->make();

        }
        return view('pages.superadmin.kategori_masalah.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = KategoriMasalah::create(['kategori_masalah' => $request->kategori_masalah]);

        return Response::json($data, 200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $kategori_masalah = KategoriMasalah::where('id_kategori_masalah',$id)->first();
        return response()->json($kategori_masalah);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        if($request){
            $query = KategoriMasalah::updateOrCreate(['id_kategori_masalah' => $request->id_kategori_masalah],['kategori_masalah' => $request->kategori_masalah_edit]);
        }

        return response()->json($query);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        KategoriMasalah::find($id)->delete($id);
        return response()->json([
            'success' => 'Record deleted successfully!'
        ]);
    }
}
