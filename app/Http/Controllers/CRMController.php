<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Faw;
use App\Models\Spk;
use App\Models\Lost;
use App\Models\User;
use App\Models\Bahan;
use App\Models\Mesin;
use App\Models\Order;
use App\Models\Tools;
use Ramsey\Uuid\Uuid;
use App\Models\Sumber;
use App\Models\Company;
use App\Models\Grading;
use App\Models\Gramasi;
use App\Models\Profile;
use App\Models\Village;
use App\Models\Customer;
use App\Models\District;
use App\Models\FollowUp;
use App\Models\Produksi;
use App\Models\Province;
use App\Models\KodeOrder;
use App\Models\MesinPrice;
use App\Models\OtherPrice;
use App\Models\PaperPrice;
use App\Models\TipeProduk;
use App\Models\JenisKertas;
use App\Models\JenisOngkos;
use App\Models\KertasPlano;
use App\Models\Subdistrict;
use App\Models\TipeInstansi;
use Illuminate\Http\Request;
use App\Models\KategoriMasalah;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Models\PenanggulanganMasalah;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Response;
use PhpOffice\PhpWord\TemplateProcessor;
use Yajra\DataTables\Facades\DataTables;
use Haruncpi\LaravelIdGenerator\IdGenerator;

class CRMController extends Controller
{
    public function index()
    {
        $sumber = Sumber::get();
        $pic = User::where(function ($query) {
            if (Auth::user()->roles == 'SALES') {
                $query->where('id', Auth::user()->id);
            } else {
                $query->where('roles', 'like', '%SALES%');
            }
        })->get();

        $this_year = date('Y');
        $this_month = date('m');
        $this_month_n = date('n');
        $logged_id = Auth::user()->id;
        $bulan_ke = array("", "target_01", "target_02", "target_03", "target_04", "target_05", "target_06", "target_07", "target_08", "target_09", "target_10", "target_11", "target_12");
        $this_month_target = $bulan_ke[$this_month_n];

        if (Auth::user()->roles == 'SUPERADMIN' || Auth::user()->roles == 'SALES SPV') {
            $total_target = DB::table('tb_targets')
                ->where('year_target', $this_year)
                ->sum($this_month_target);

            $total_realisasi = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
                ->where('status_deal', 'Deal')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
                ->whereMonth('tgl_order', $this_month)
                ->whereYear('tgl_order', $this_year)
                ->sum('total_harga');
        } else {
            $total_target = DB::table('tb_targets')
                ->where('year_target', $this_year)
                ->where('id_pic', $logged_id)
                ->sum($this_month_target);

            $total_realisasi = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
                ->where('status_deal', 'Deal')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
                ->whereMonth('tgl_order', $this_month)
                ->whereYear('tgl_order', $this_year)
                ->where('id_pic', $logged_id)
                ->sum('total_harga');
        }

        if ($total_target != 0) {
            $target_div = $total_realisasi / $total_target;
        } else {
            $target_div = 0;
        }
        $target_progress_raw = $target_div * 100;
        if ($target_progress_raw > 100) {
            $target_progress = 100;
        } else {
            $target_progress = $target_progress_raw;
        }

        $price_null = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_produksis', 'tb_orders.id_order', '=', 'tb_produksis.id_produksi')
            ->select(['id_order', 'kode_kustomer', 'tb_kode_orders.kode_order', 'tb_orders.order_key', 'total_harga', 'tgl_order', 'waktu_kontak', 'name', 'nama', 'status_deal', 'status_order'])
            ->where(function ($query) {
                $query->where('total_harga', '<', '1');
                $query->where('status_deal', '!=', 'Lost');
                $query->where('status_order', '=', 'FU Emas');

                if (Auth::user()->roles === 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $prioritas = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            //
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ///
            ->select(['id_order', 'kode_kustomer', 'tb_kode_orders.kode_order', 'tb_orders.order_key', 'total_harga', 'tgl_order', 'waktu_kontak', 'name', 'nama', 'status_deal', 'status_order'])
            ->where(function ($query) {
                if (Auth::user()->roles === 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->where(function ($query) {
                $query->whereIn('status_order', ['Ruby', 'Diamond']);
            });
        $count_price_null = $price_null->count();
        $count_prioritas = $prioritas->count();

        $tipe_instansi = TipeInstansi::select('id_instansi','tipe_instansi')->get();
        $sumber_order = Sumber::select('sumber')->get();
        $grading = Grading::all();
        $grading2 = $grading;
        $follow_up = FollowUp::select('follow_up')->get();
        $follow_up2 = FollowUp::select('follow_up')->get();
        $lost = Lost::select('lost')->get();
        $lost2 = $lost;
        $bahan = Bahan::select('bahan')->get();
        $bahan2 = $bahan;
        $jenis_kertas = JenisKertas::select('jenis_kertas')->get();
        $jenis_kertas2 = $jenis_kertas;
        $gramasi = Gramasi::select('gramasi')->get();
        $gramasi2 = $gramasi;
        $tipe_produk = TipeProduk::select('tipe_produk')->get();
        $tipe_produk2 = $tipe_produk;
        $kertas_plano = KertasPlano::select('kertas_plano')->get();
        $kertas_plano2 = $kertas_plano;
        $kategori_masalah = KategoriMasalah::select('kategori_masalah')->get();
        $province = Province::select('name')->get();
        $company = Company::select('id','name')->get();
        
        return view('pages.crm.index', compact(
            'sumber',
            'pic',
            'count_price_null',
            'count_prioritas',
            'tipe_instansi',
            'sumber_order',
            'grading',
            'grading2',
            'follow_up',
            'follow_up2',
            'lost',
            'lost2',
            'bahan',
            'bahan2',
            'jenis_kertas',
            'jenis_kertas2',
            'gramasi',
            'gramasi2',
            'tipe_produk',
            'tipe_produk2',
            'kertas_plano',
            'kertas_plano2',
            'kategori_masalah',
            'total_target',
            'total_realisasi',
            'target_progress',
            'province',
            'company'
        ));
    }

    public function table_customer()
    {
        $query = DB::table('tb_customers')->select(['id_customer', 'kode_kustomer', 'nama', 'grading', 'no_hp', 'email_bisnis', 'posisi_pic_cust', 'jenis_usaha', 'tipe_instansi', 'nama_instansi', 'nama_brand', 'updated_at','company_id']);

        return DataTables::of($query)
            ->editColumn('nama', function ($row) {
                return '<a href="' . route('crm.detail_customer', $row->kode_kustomer) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->nama . '</a>';
            })
            ->editColumn('grading', function ($row) {
                if ($row->grading == "Reguler") {
                    return '<div class="badge fw-bold" style="background-color: #CD7F32;">' . $row->grading . '</div>';
                } elseif ($row->grading == "Premium") {
                    return '<div class="badge fw-bold" style="background-color: #C0C0C0;">' . $row->grading . '</div>';
                } else {
                    return '<div class="badge fw-bold" style="background-color: #FFD700;">' . $row->grading . '</div>';
                }
            })
            ->editColumn('updated_at', function ($row) {
                return $row->updated_at ? date('d M Y', strtotime($row->updated_at)) : '';
            })
            ->addColumn('nama_instansi', function ($row) {
                $company_name = '-';
                if (!empty($row->company_id)) {
                    $company = Company::find($row->company_id);
                    $company_name = $company->name;
                }
                return $company_name;
            })
            ->addColumn('action', function ($row) {
                return '<a class="btn btn-sm btn-my-primary text-white w-50 edit-customer" data-toggle="tooltip" data-id="' . $row->id_customer . '" href="javascript:void(0)">Edit</a><a class="btn btn-sm btn-info text-white w-75 edit-cust-comp ms-3" data-toggle="tooltip" data-id="' . $row->id_customer . '" href="javascript:void(0)">Edit Company</a>';
            })
            ->rawColumns(['nama', 'grading', 'action','nama_instansi'])
            ->make(true);
    }

    public function store_new_cust(Request $request)
    {


        $prefix = 'RSP';
        $year = date('y');
        $config = [
            'table' => 'tb_customers',
            'field' => 'kode_kustomer',
            'length' => 8,
            'prefix' => $prefix
        ];

        $kk = IdGenerator::generate($config);
        $kode_kustomer = $kk;

        $data = Customer::create([
            'kode_kustomer' => $kode_kustomer,
            'nama' => $request->nama,
            'grading' => $request->grading,
            'no_hp' => $request->no_hp,
            'email_bisnis' => $request->email_bisnis,
            'posisi_pic_cust' => $request->posisi_pic_cust,
            'jenis_usaha' => $request->jenis_usaha,
            'tipe_instansi' => $request->tipe_instansi,
            'alamat' => $request->alamat,
            'nama_instansi' => $request->nama_instansi,
            'alamat_instansi' => $request->alamat_instansi,
            'npwp' => $request->npwp,
            'nama_brand' => $request->nama_brand,

        ]);

        return Response::json($data, 200);
    }

    public function checkPhoneNumber(Request $request)
    {
        $no_hp = $request->input('no_hp');
        $user = Customer::where('no_hp', $no_hp)->first();
        if ($user) {
            return response()->json(['valid' => false]);
        } else {
            return response()->json(['valid' => true]);
        }
    }


    public function show_cust($id)
    {
        $customer = Customer::where('id_customer', $id)->first();
        $profile = Profile::where('id_customer', $id)->first();

        return Response::json([$customer, $profile], 200);
    }

    public function edit_cust(Request $request)
    {

        $query = Customer::UpdateOrCreate(
            ['id_customer' => $request->id_customer],
            [
                'nama' => $request->nama,
                'grading' => $request->grading,
                'no_hp' => $request->no_hp,
                'email_bisnis' => $request->email_bisnis,
                'posisi_pic_cust' => $request->posisi_pic_cust,
                'jenis_usaha' => $request->jenis_usaha,
                'tipe_instansi' => $request->tipe_instansi,
                'alamat' => $request->alamat,
                'nama_instansi' => $request->nama_instansi,
                'alamat_instansi' => $request->alamat_instansi,
                'npwp' => $request->npwp,
                'nama_brand' => $request->nama_brand,
            ]
        );

        $query = Profile::UpdateOrCreate(
            ['id_customer' => $request->id_customer],
            [
                'durasi_berdiri' => $request->durasi_berdiri,
                'skala_bisnis' => $request->skala_bisnis,
                'tingkat_penjualan' => $request->tingkat_penjualan,
                'segmen_pasar' => $request->segmen_pasar,
                'channel_marketing' => $request->channel_marketing,
                'nama_campaign' => $request->nama_campaign,
                'anniv_usaha' => $request->anniv_usaha,
                'imlek' => $request->imlek,
                'idul_fitri' => $request->idul_fitri,
                'natal' => $request->natal,
                'keb_packaging' => $request->keb_packaging,
                'potensi_freq' => $request->potensi_freq,
                'profiling' => $request->profiling,
                'direct_meeting' => $request->direct_meeting,
            ]
        );

        return Response::json($query, 200);
    }

    public function table_order()
    {
        $query = DB::table('tb_orders')
            ->join('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->join('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->join('users', 'tb_orders.id_pic', '=', 'users.id')
            // ->groupBy('sko')
            ->select(['id_order', 'kode_kustomer', 'sko', 'tb_orders.order_key', 'tb_orders.sko_key', 'tgl_order', 'sumber', 'waktu_kontak', 'name', 'nama', 'status_deal', 'status_order', 'total_harga', 'modal_sales', 'kategori_produksi', 'tb_orders.updated_at as last_update', 'ppn', 'total_ppn', 'tb_orders.flag_dummy as tipe_order'])
            //     return $query->whereYear('tb_orders.created_at', $dataYear);
            // }, function ($query) {
            //     return $query->whereYear('tb_orders.created_at', date('Y'));
            // })
            ->when(request('tgl_created'), function ($query) {
                return $query->whereYear('waktu_kontak', request('tgl_created'));
            })
            ->when(request('tgl_order_from_date') && request('tgl_order_to_date'), function ($query) {
                return $query->whereBetween('waktu_kontak', [request('tgl_order_from_date'), request('tgl_order_to_date')]);
            })
            ->when(request('tgl_order_from_date') && !request('tgl_order_to_date'), function ($query) {
                return $query->whereBetween('waktu_kontak', [request('tgl_order_from_date'), date("Y-m-d")]);
            })
            ->when(Auth::user()->roles == 'SALES', function ($query) {
                return $query->where('id_pic', Auth::user()->id);
            });


        return DataTables::of($query)
            // ->editColumn('kode_order', function ($row) {
            //     $param = Crypt::encryptString($row->order_key);
            //     return '<a href="' . route('crm.detail_main_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->kode_order . '</a>';
            // })
            ->editColumn('sko', function ($row) {
                $param = Crypt::encryptString($row->id_order);
                return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->sko . '</a>';
            })
            ->editColumn('tgl_order', function ($row) {
                return $row->tgl_order ? date('d M Y', strtotime($row->tgl_order)) : '';
            })
            ->editColumn('waktu_kontak', function ($row) {
                return $row->waktu_kontak ? date('d M y H:i', strtotime($row->waktu_kontak)) : '';
            })
            ->addColumn('selisih_hari', function ($row) {
                return $row->selisih_hari = $this->hitungSelisihTanggal($row->tgl_order, $row->waktu_kontak) . " Hari";
            })
            ->editColumn('pic', function ($row) {
                return $row->name ? $row->name : '';
            })
            ->editColumn('nama', function ($row) {
                return $row->nama ? '<a href="' . route('crm.detail_customer', $row->kode_kustomer) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->nama . '</a>' : '-';
            })
            ->editColumn('total_harga', function ($row) {
                return $row->total_harga ? number_format($row->total_harga, 0, '', ',') : '';
            })
            ->editColumn('total_ppn', function ($row) {
                return $row->total_ppn ? number_format($row->total_ppn, 0, '', ',') : '';
            })
            ->editColumn('modal_sales', function ($row) {
                return $row->modal_sales ? number_format($row->modal_sales, 0, '', ',') : '';
            })
            ->editColumn('status_deal', function ($row) {
                if ($row->status_deal == 'Follow Up') {
                    return '<div class="badge badge-light-primary fw-bold">' . $row->status_deal . '</div>';
                } elseif ($row->status_deal == 'Deal') {
                    return '<div class="badge badge-light-success fw-bold">' . $row->status_deal . '</div>';
                } elseif ($row->status_deal == 'Lost') {
                    return '<div class="badge badge-light-danger fw-bold">' . $row->status_deal . '</div>';
                } else {
                    return '';
                }
            })
            ->editColumn('status_order', function ($row) {
                return $row->status_order ? '<div class="badge badge-light">' . $row->status_order . '</div>' : '';
            })
            ->editColumn('kategori_produksi', function ($row) {
                return $row->kategori_produksi;
            })
            ->editColumn('last_update', function ($row) {
                return $row->last_update ? date('d M Y', strtotime($row->last_update)) : '';
            })
            ->addColumn('action', function ($row) {
                $param = Crypt::encryptString($row->sko_key);
                $last_string = substr($row->sko, -1);
                return '<div class="btn-group d-flex">
                        <a class="btn btn-sm btn-my-primary text-white w-100 edit-produk" data-toggle="tooltip" data-key="' . $row->sko_key . '" href="javascript:void(0)">Produk</a>
                        <a class="btn btn-sm btn-success text-white w-100 edit-order" data-toggle="tooltip" data-id="' . $row->id_order . '" href="javascript:void(0)">Order</a>
                        <a href="javascript:void(0)" class="btn btn-sm btn-danger w-100 text-white delete-link" data-key="' . $row->id_order . '" onclick="deleteConfirmation(' . $row->id_order . ')"><i class="fa-solid fa-trash"></i></a>
                        </div> ';
            })
            // ->rawColumns(['kode_order', 'sko', 'tgl_order', 'waktu_kontak', 'selisih_hari', 'pic', 'nama', 'total_harga', 'modal_sales', 'status_deal', 'status_order', 'kategori_produksi', 'created_at', 'action'])
            ->rawColumns(['sko', 'tgl_order', 'waktu_kontak', 'selisih_hari', 'pic', 'nama', 'total_harga', 'total_ppn', 'modal_sales', 'status_deal', 'status_order', 'kategori_produksi', 'last_update', 'action'])
            ->make(true);
    }

    private function hitungSelisihTanggal($tanggal1, $tanggal2)
    {
        if ($tanggal1 == null) {
            $tanggal1 = $tanggal2;
        }

        // Menghitung selisih dalam detik
        $selisih_hari = null;
        if (!empty($tanggal1) && !empty($tanggal2)) {
            $selisih = strtotime($tanggal2) - strtotime($tanggal1);
            $selisih_hari = abs(round($selisih / (60 * 60 * 24)));
        }
        // Konversi selisih detik ke hari
        // $selisih_hari = floor($selisih_detik / (60 * 60 * 24));

        return $selisih_hari;
    }


    public function get_customer(Request $request)
    {

        $keyword = $request->get('term');
        if (!empty($keyword)) {
            $data = Customer::select(['id_customer', 'nama', 'nama_instansi', 'no_hp'])
                ->when(is_numeric($keyword), function($query) use ($keyword) {
                    return $query->where('no_hp', 'LIKE', "%{$keyword}%");
                }, function($query) use ($keyword) {
                    return $query->where('nama', 'LIKE', "%{$keyword}%");
                })
                ->limit(5)
                ->get();
        } else {
            $data = Customer::select(['id_customer', 'nama', 'nama_instansi', 'no_hp'])
                ->limit(5)
                ->get();
        }

        $list_nama = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                $list_nama[] = array(
                    "id_customer" => $row->id_customer,
                    "nama" => $row->nama . ' - ' . $row->nama_instansi . ' - ' . $row->no_hp
                );
            }
        }
        return Response::json($list_nama, 200);
    }

    public function get_pic(Request $request)
    {
        $input = $request->all();

        if (!empty($input['term'])) {
            $data = User::select(['id', 'name'])
                ->where('roles', 'LIKE', "%SALES%")
                ->where('name', 'LIKE', "%{$input['term']}%")
                ->get();
        } else {
            $data = User::select(['id', 'name'])
                ->where('roles', 'LIKE', "%SALES%")
                ->limit(5)
                ->get();
        }

        $list_nama = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                $list_nama[] = array(
                    "id" => $row->id,
                    "name" => $row->name
                );
            }
        }
        return Response::json($list_nama, 200);
    }

    public function main_kode_order_get(Request $request)
    {

        $data = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->where('tb_kode_orders.kode_order', 'LIKE', '%' . $request->get('kode_order') . '%')
            ->where('tb_orders.id_customer', 'LIKE', '%' . $request->get('id_customer') . '%')
            ->select(['tb_kode_orders.kode_order as kode_order', 'tb_orders.order_key'])
            ->limit(10)
            ->get();

        return Response::json($data, 200);
    }

    public function store_new_order(Request $request)
    {

        if ($request->status_deal == 'Follow Up') {
            $status_order = $request->status_order1;
            $follow_up_terakhir = $request->status_order1;
        } elseif ($request->status_deal == 'Deal') {
            $status_order = $request->status_order2;
        } elseif ($request->status_deal == 'Lost') {
            $status_order = $request->status_order3;
        }

        $DP_store = (float)str_replace(',', '', $request->DP);

        $sko_key = Uuid::uuid4()->toString();

        if (empty($request->main_kode_order)) {
            $order_key = Uuid::uuid4()->toString();
        } else {
            $order_key = $request->order_key;
        }

        if ($request->tgl_order != '') {

            $time_order = date('dm', strtotime($request->tgl_order));
            if ($request->flag_dummy == 'Dummy') {
                $flagd = 'D';
            } elseif ($request->flag_dummy == 'Produksi Massal') {
                $flagd = 'P';
            } elseif ($request->flag_dummy == 'Produksi Ulang') {
                $flagd = 'R';
            } elseif ($request->flag_dummy == 'Jasa Lainnya') {
                $flagd = 'J';
            } else {
                $flagd = '';
            }

            $produk_ke_romawi = array("", "I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX", "X", "XI", "XII", "XIII", "XIV", "XV", "XVI", "XVII", "XVIII", "XIX", "XX");
            if (empty($request->produk_ke)) {
                $pk = $produk_ke_romawi['1'];
            } else {
                $pk = $produk_ke_romawi[$request->produk_ke];
            }

            if (empty($request->main_kode_order)) {
                if ($request->sumber == 'Online') {
                    $XX = '01';
                } elseif ($request->sumber == 'Offline') {
                    $XX = '02';
                } elseif ($request->sumber == 'Repeat Order') {
                    $XX = '03';
                } elseif ($request->sumber == 'Relasi') {
                    $XX = '04';
                } elseif ($request->sumber == 'Relasi Customer') {
                    $XX = '05';
                } elseif ($request->sumber == 'Canvassing') {
                    $XX = '06';
                }
                $kk = Customer::where('id_customer', $request->id_customer)->get(['kode_kustomer'])->first();


                $prefix_kode_order = $XX . '/' . $kk->kode_kustomer . '/';

                //check first
                $kodeOrderCheck = KodeOrder::count();

                if ($kodeOrderCheck > 0) {
                    // Get the last order id
                    $lastorderId = KodeOrder::orderBy('id', 'desc')->first()->kode_order;

                    // Get last 3 digits of last order id
                    $lastIncreament = substr($lastorderId, -4);

                    // Make a new order id with appending last increment + 1
                    $kode_order = $prefix_kode_order . str_pad($lastIncreament + 1, 4, 0, STR_PAD_LEFT);
                } else {
                    // Make a new order id with appending last increment + 1
                    $kode_order = $prefix_kode_order . '0001';
                }


                KodeOrder::create([
                    'order_key' => $order_key,
                    'kode_order' => $kode_order,
                ]);



                $sko = $kode_order . '/' . $pk . '/' . $time_order . '/' . $flagd;
            } else {
                $sko = $request->main_kode_order . '/' . $pk . '/' . $time_order . '/' . $flagd;
            }
        } else {
            $sko = null;
        }
        $expected_revenue = (int)str_replace(',', '', $request->expected_revenue);
        $waktu_kontak_fix = Carbon::createFromFormat('d/m/Y H:i', $request->waktu_kontak)->format('Y-m-d H:i');
        $data = Order::create([
            'id_customer' => $request->id_customer,
            'order_key' => $order_key,
            'sko_key' => $sko_key,
            'sko' => $sko,
            'id_pic' => $request->id_pic,
            'produk_ke' => $request->produk_ke,
            'sumber' => $request->sumber,
            'tgl_order' => $request->tgl_order,
            'waktu_kontak' => $waktu_kontak_fix,
            'pp_harga' => $request->pp_harga ? $request->pp_harga : 0,
            'pp_waktu' => $request->pp_waktu ? $request->pp_waktu : 0,
            'pp_pengiriman' => $request->pp_pengiriman ? $request->pp_pengiriman : 0,
            'pp_kualitas' => $request->pp_kualitas ? $request->pp_kualitas : 0,
            'pp_pembayaran' => $request->pp_pembayaran ? $request->pp_pembayaran : 0,
            'status_deal' => $request->status_deal,
            'status_order' => $status_order,
            'tipe_kontak' => $request->tipe_kontak,
            'no_po' => $request->no_po,
            'link_dokumen_order' => $request->link_dokumen_order,
            'flag_lunas' => $request->flag_lunas,
            'catatan_order' => $request->catatan_order,
            'flag_dummy' => $request->flag_dummy,
            'follow_up_terakhir' => ($request->status_deal == 'Follow Up') ? $follow_up_terakhir : null,
            'survey_order' => $request->survey_order ? $request->survey_order : 0,
            'lead_prospek' => $request->lead_prospek ? $request->lead_prospek : 0,
            'is_forecast' => $request->forecast ? $request->forecast : false,
            'forecast_month' => $request->forecast_month ? $request->forecast_month : null,
            'forecast_year' => $request->forecast_year ? $request->forecast_year : null,
            'forecast_week' => $request->forecast_week ? $request->forecast_week : null,
            'expected_revenue' => isset($request->expected_revenue) ? $expected_revenue : 0,
        ]);

        if ($request->jenis_kertas == 'Lainnya') {
            $jenis_kertas = $request->jenis_kertas_lainnya;
        } else {
            $jenis_kertas = $request->jenis_kertas;
        }

        if ($request->gramasi == 'Lainnya') {
            $gramasi = $request->gramasi_lainnya;
        } else {
            $gramasi = $request->gramasi;
        }

        // $finishing = array($request->finishing);
        if ($request->finishing == '') {
            $finishingfix = null;
        } else {
            $finishingfix = implode(',', $request->finishing);
        }

        if ($request->hitung_harga_khusus) {
            $flag_harga_khusus = $request->hitung_harga_khusus;
        } else {
            $flag_harga_khusus = '0';
        }

        if ($request->kendala_produksi != '1') {
            $flag_kendala_produksi = '0';
        } else {
            $flag_kendala_produksi = '1';
        }

        $harga_produk = (float)str_replace(',', '', $request->harga_produk);
        $total_harga = (float)str_replace(',', '', $request->total_harga);
        $modal_sales = (float)str_replace(',', '', $request->modal_sales);
        $total_ppn = (float)str_replace(',', '', $request->total_ppn);
        $ppn_value = $request->ppn == "PPN" ? true : false;

        $data_produk = Produksi::create([
            'order_key' => $order_key,
            'sko_key' => $sko_key,
            // 'id_order' => $kode_order,
            'kategori_produksi' => $request->kategori_produksi,
            'jenis_bahan' => $request->jenis_bahan,
            'dp_panjang' => $request->dp_panjang,
            'dp_lebar' => $request->dp_lebar,
            'dp_tinggi' => $request->dp_tinggi,
            'lp_panjang' => $request->lp_panjang,
            'lp_lebar' => $request->lp_lebar,
            'jenis_kertas' => $jenis_kertas,
            'gramasi' => $gramasi,
            'laminasi' => $request->laminasi,
            'sisi_laminasi' => $request->sisi_laminasi,
            'finishing' => $finishingfix,
            'tipe_produk' => $request->tipe_produk,
            'isi_kertas' => $request->isi_kertas,
            'get_plano' => $request->get_plano,
            'jumlah_produk' => $request->jumlah_produk,
            'harga_produk' => $harga_produk,
            'total_harga' => $total_harga,
            'ppn' => $ppn_value,
            'ppn_percent' => $request->ppn_percent,
            'total_ppn' => $total_ppn,
            'modal_sales' => $modal_sales,
            'notes' => $request->notes,
            'nama_produk' => $request->nama_produk,
            'hitung_harga_khusus' => $flag_harga_khusus,
            'kendala_produksi' => $flag_kendala_produksi
        ]);

        if ($request->ajax()) {
            return Response::json(['data_oder' => $data, 'data_produk' => $data_produk], 200);
        }
        return redirect()->route('crm')->with('message', [
            'type' => 'Success',
            'text' => 'Order Added successfully',
        ]);
    }

    public function show_order($id)
    {
        // $order = Order::where('id_order',$id)->first();
        $order = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('invoice_details', 'tb_orders.sko', '=', 'invoice_details.kode_order')
            ->where('id_order', 'LIKE', $id)
            ->select([
                'tb_customers.id_customer as id_customer', 'id_order', 'tb_orders.order_key', 'tb_orders.sko_key', 'sumber', 
                'tb_kode_orders.kode_order', 'sko', 'produk_ke', 'tgl_order', 'waktu_kontak', 'name', 'nama', 
                'users.id as id_pic', 'status_deal', 'status_order', 'tipe_kontak', 'DP', 'flag_lunas', 'catatan_order', 
                'flag_dummy', 'follow_up_terakhir', 'tgl_pelunasan', 'survey_order', 'lead_prospek', 'pp_harga', 
                'pp_waktu', 'pp_pengiriman', 'pp_kualitas', 'pp_pembayaran', 'is_forecast', 'expected_revenue', 
                'forecast_month', 'forecast_year', 'forecast_week', 'link_dokumen_order', 'no_po',
                DB::raw('COUNT(invoice_details.id) as invoice_count')
            ])
            ->groupBy('tb_orders.id_order', 'tb_customers.id_customer', 'tb_orders.order_key', 'tb_orders.sko_key', 
                     'sumber', 'tb_kode_orders.kode_order', 'sko', 'produk_ke', 'tgl_order', 'waktu_kontak', 'name', 
                     'nama', 'users.id', 'status_deal', 'status_order', 'tipe_kontak', 'DP', 'flag_lunas', 
                     'catatan_order', 'flag_dummy', 'follow_up_terakhir', 'tgl_pelunasan', 'survey_order', 
                     'lead_prospek', 'pp_harga', 'pp_waktu', 'pp_pengiriman', 'pp_kualitas', 'pp_pembayaran', 
                     'is_forecast', 'expected_revenue', 'forecast_month', 'forecast_year', 'forecast_week', 
                     'link_dokumen_order', 'no_po')
            ->limit(1)
            ->get();

        // dd(Response::json($order, 200));

        return Response::json($order, 200);
    }

    public function show_produk($key)
    {
        // $order = Order::where('id_order',$id)->first();
        $order = DB::table('tb_produksis')
            ->where('tb_produksis.sko_key', 'LIKE', $key)
            ->select(['*'])
            ->limit(1)
            ->get();

        // dd($order[0]->finishing);
        $order['selesai'] = explode(',', $order[0]->finishing);

        // dd(Response::json($order, 200));

        return Response::json($order, 200);
    }

    public function edit_order(Request $request)
    {
        if ($request->status_deal == 'Follow Up') {
            $status_order = $request->status_order1;
            $follow_up_terakhir = $request->status_order1;
        } elseif ($request->status_deal == 'Deal') {
            $status_order = $request->status_order2;
        } elseif ($request->status_deal == 'Lost') {
            $status_order = $request->status_order3;
        }

        $DP_store = (float)str_replace(',', '', $request->DP);
        $order_key = $request->order_key;
        $time_order = date('dm', strtotime($request->tgl_order));

        if ($request->flag_dummy_edit == 'Dummy') {
            $flagd = 'D';
        } elseif ($request->flag_dummy_edit == 'Produksi Massal') {
            $flagd = 'P';
        } elseif ($request->flag_dummy_edit == 'Produksi Ulang') {
            $flagd = 'R';
        } elseif ($request->flag_dummy_edit == 'Jasa Lainnya') {
            $flagd = 'J';
        } else {
            $flagd = '';
        }

        $produk_ke_romawi = array("", "I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX", "X", "XI", "XII", "XIII", "XIV", "XV", "XVI", "XVII", "XVIII", "XIX", "XX");
        if (empty($request->produk_ke)) {
            $pk = $produk_ke_romawi['1'];
        } else {
            $pk = $produk_ke_romawi[$request->produk_ke];
        }

        if (!empty($request->tgl_order) && (empty($request->kode_order)) && (empty($request->main_order_key))) {
            // echo '1';
            if ($request->sumber == 'Online') {
                $XX = '01';
            } elseif ($request->sumber == 'Offline') {
                $XX = '02';
            } elseif ($request->sumber == 'Repeat Order') {
                $XX = '03';
            } elseif ($request->sumber == 'Relasi') {
                $XX = '04';
            } elseif ($request->sumber == 'Relasi Customer') {
                $XX = '05';
            } elseif ($request->sumber == 'Canvassing') {
                $XX = '06';
            }
            $kk = Customer::where('id_customer', $request->id_cust_edit)->get(['kode_kustomer'])->first();


            $prefix_kode_order = $XX . '/' . $kk->kode_kustomer . '/';

            //check first
            $kodeOrderCheck = KodeOrder::count();

            if ($kodeOrderCheck > 0) {
                // Get the last order id
                $lastorderId = KodeOrder::orderBy('id', 'desc')->first()->kode_order;

                // Get last 3 digits of last order id
                $lastIncreament = substr($lastorderId, -4);

                // Make a new order id with appending last increment + 1
                $kode_order = $prefix_kode_order . str_pad($lastIncreament + 1, 4, 0, STR_PAD_LEFT);
            } else {
                // Make a new order id with appending last increment + 1
                $kode_order = $prefix_kode_order . '0001';
            }

            KodeOrder::create([
                'order_key' => $order_key,
                'kode_order' => $kode_order,
            ]);

            $sko = $kode_order . '/' . $pk . '/' . $time_order . '/' . $flagd;
            $order_key_update = $request->order_key;
            // }else{
            //     $sko = $request->kode_order.'/I/'.$time_order.'/'.$flagd;
            // }
        } elseif (!empty($request->tgl_order) && (!empty($request->kode_order)) && (empty($request->main_order_key))) {
            // echo '2';
            $sko = $request->kode_order . '/' . $pk . '/' . $time_order . '/' . $flagd;
            $order_key_update = $request->order_key;
        } elseif (!empty($request->tgl_order) && (empty($request->kode_order)) && (!empty($request->main_order_key))) {
            // echo '3';
            $sko = $request->kode_order . '/' . $pk . '/' . $time_order . '/' . $flagd;
            $order_key_update = $request->main_order_key;
        } elseif (!empty($request->tgl_order) && (!empty($request->kode_order)) && (!empty($request->main_order_key))) {
            // echo '4';
            $sko = $request->kode_order . '/' . $pk . '/' . $time_order . '/' . $flagd;
            $order_key_update = $request->main_order_key;
        } elseif (empty($request->tgl_order) && (!empty($request->kode_order)) && (!empty($request->main_order_key))) {
            // echo '5';
            $sko = null;
            $order_key_update = $request->main_order_key;
        } else {
            // echo '6';
            $sko = null;
            $order_key_update = $request->order_key;
        }
        // dd([$request->flag_lunas, $request->pp_pembayaran, $request->pp_pembayaran ? $request->pp_pembayaran : false]);
        $waktu_kontak_fix = Carbon::createFromFormat('d/m/Y H:i', $request->waktu_kontak)->format('Y-m-d H:i');
        $expected_revenue = (int)str_replace(',', '', $request->expected_revenue);
        $data = Order::updateOrCreate(
            ['id_order' => $request->id_order],
            [
                'id_pic' => $request->id_pic,
                'order_key' => $order_key_update,
                'sko' => $sko,
                'produk_ke' => $request->produk_ke,
                'sumber' => $request->sumber,
                'tgl_order' => $request->tgl_order,
                'waktu_kontak' => $waktu_kontak_fix,
                'pp_harga' => $request->pp_harga ? $request->pp_harga : 0,
                'pp_waktu' => $request->pp_waktu ? $request->pp_waktu : 0,
                'pp_pengiriman' => $request->pp_pengiriman ? $request->pp_pengiriman : 0,
                'pp_kualitas' => $request->pp_kualitas ? $request->pp_kualitas : 0,
                'pp_pembayaran' => $request->pp_pembayaran ? $request->pp_pembayaran : 0,
                'status_deal' => $request->status_deal,
                'status_order' => $status_order,
                'tipe_kontak' => $request->tipe_kontak,
                'no_po' => $request->no_po,
                'link_dokumen_order' => $request->link_dokumen_order,
                'flag_lunas' => $request->flag_lunas,
                'tgl_pelunasan' => $request->tgl_pelunasan,
                'catatan_order' => $request->catatan_order_edit,
                'flag_dummy' => $request->flag_dummy_edit,
                'follow_up_terakhir' => ($request->status_deal == 'Follow Up') ? $follow_up_terakhir : $request->follow_up_terakhir,
                'survey_order' => $request->survey_order ? $request->survey_order : 0,
                'lead_prospek' => $request->lead_prospek ? $request->lead_prospek : 0,
                'is_forecast' => $request->forecast ? $request->forecast : false,
                'forecast_month' => $request->forecast_month ? $request->forecast_month : null,
                'forecast_year' => $request->forecast_year ? $request->forecast_year : null,
                'forecast_week' => $request->forecast_week ? $request->forecast_week : null,
                'expected_revenue' => isset($request->expected_revenue) ? $expected_revenue : 0,
            ]
        );

        return Response::json($data, 200);
    }

    public function edit_produk(Request $request)
    {

        if ($request->jenis_kertas == 'Lainnya') {
            $jenis_kertas = $request->jenis_kertas_lainnya;
        } else {
            $jenis_kertas = $request->jenis_kertas;
        }

        if ($request->gramasi == 'Lainnya') {
            $gramasi = $request->gramasi_lainnya;
        } else {
            $gramasi = $request->gramasi;
        }

        // $finishing = array($request->finishing);
        if ($request->finishing == '') {
            $finishingfix = null;
        } else {
            $finishingfix = implode(',', $request->finishing);
        }

        if ($request->hitung_harga_khusus) {
            $flag_harga_khusus = $request->hitung_harga_khusus;
        } else {
            $flag_harga_khusus = '0';
        }

        if ($request->kendala_produksi != '1') {
            $flag_kendala_produksi = '0';
        } else {
            $flag_kendala_produksi = '1';
        }

        $harga_produk = (float)str_replace(',', '', $request->harga_produk);
        $total_harga = (float)str_replace(',', '', $request->total_harga);
        $modal_sales = (float)str_replace(',', '', $request->modal_sales);
        $total_ppn_edit = (float)str_replace(',', '', $request->total_ppn_edit);
        // dd($modal_sales, $total_ppn_edit, $request->id_produksi);
        // dd($total_harga);
        // dd($request->ppn_edit);
        $ppn_value = $request->ppn_edit == "PPN" ? true : false;
        // dd($request->id_produksi);

        $data = Produksi::updateOrCreate(
            ['id_produksi' => $request->id_produksi],
            [
                'kategori_produksi' => $request->kategori_produksi,
                'jenis_bahan' => $request->jenis_bahan,
                'dp_panjang' => $request->dp_panjang,
                'dp_lebar' => $request->dp_lebar,
                'dp_tinggi' => $request->dp_tinggi,
                'lp_panjang' => $request->lp_panjang,
                'lp_lebar' => $request->lp_lebar,
                'jenis_kertas' => $jenis_kertas,
                'gramasi' => $gramasi,
                'laminasi' => $request->laminasi,
                'sisi_laminasi' => $request->sisi_laminasi,
                'finishing' => $finishingfix,
                'tipe_produk' => $request->tipe_produk,
                'isi_kertas' => $request->isi_kertas,
                'get_plano' => $request->get_plano,
                'jumlah_produk' => $request->jumlah_produk,
                'harga_produk' => $harga_produk,
                'total_harga' => $total_harga,
                'ppn' => $ppn_value,
                'ppn_percent' => $ppn_value ? $request->ppn_percent_edit : 0,
                'total_ppn' => $ppn_value ? $total_ppn_edit : 0,
                'modal_sales' => $modal_sales,
                'notes' => $request->notes,
                'nama_produk' => $request->nama_produk_edit,
                'hitung_harga_khusus' => $flag_harga_khusus,
                'kendala_produksi' => $flag_kendala_produksi
            ]
        );

        return Response::json($data, 200);
    }

    public function delete_order($param)
    {

        $ok = Order::find($param);

        $check_ko = Order::where('order_key', $ok->order_key)->get();

        $check_ko_count = $check_ko->count();

        if ($check_ko_count < 2) {
            KodeOrder::where('order_key', $ok->order_key)->delete();
        }

        Faw::where('sko_key', $ok->sko_key)->delete();
        PenanggulanganMasalah::where('sko_key', $ok->sko_key)->delete();
        Produksi::where('sko_key', $ok->sko_key)->delete();
        Spk::where('sko_key', $ok->sko_key)->delete();
        Order::where('sko_key', $ok->sko_key)->delete();

        return response()->json([
            'success' => 'Record deleted successfully!'
        ]);
    }

    public function direct_order(Request $request, $id)
    {

        $data_cust = Customer::where('kode_kustomer', $id)->first();

        $sumber = Sumber::select('sumber')->get();
        $follow_up = FollowUp::select('follow_up')->get();
        $lost = Lost::select('lost')->get();
        $bahan = Bahan::select('bahan')->get();
        $jenis_kertas = JenisKertas::select('jenis_kertas')->get();
        $gramasi = Gramasi::select('gramasi')->get();
        $tipe_produk = TipeProduk::select('tipe_produk')->get();
        $kertas_plano = KertasPlano::select('kertas_plano')->get();

        return view('pages.crm.direct_order', compact('data_cust', 'sumber', 'follow_up', 'lost', 'bahan', 'jenis_kertas', 'gramasi', 'tipe_produk', 'kertas_plano'));
    }

    public function detail_customer(Request $request, $kode_kustomer)
    {

        $data_cust = Customer::where('kode_kustomer', '=', $kode_kustomer)->first();

        $total_omzet = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_Key', '=', 'tb_kode_orders.order_key')
            ->where('tb_customers.kode_kustomer', '=', $kode_kustomer)
            ->where('tb_orders.status_deal', '=', 'Deal')
            ->sum('tb_produksis.total_harga');

        $total_potential = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_Key', '=', 'tb_kode_orders.order_key')
            ->where('tb_customers.kode_kustomer', '=', $kode_kustomer)
            ->where('tb_orders.status_deal', '!=', 'Deal')
            ->sum('tb_produksis.total_harga');
        
        return view('pages.crm.customer_detail', compact('data_cust','total_omzet', 'total_potential'));
    }

    public function table_transaksi_customer($id)
    {
        $query = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_Key', '=', 'tb_kode_orders.order_key')
            ->select(['tb_orders.id_order', 'tgl_order', 'kode_order', 'sko', 'tgl_jatuh_tempo', 'status_deal', 'status_order', 'total_harga'])
            ->where('tb_orders.id_customer', '=', $id)
            ->orderBy('tgl_order', 'DESC');

        return DataTables::of($query)
            ->editColumn('sko', function ($row) {
                $param = Crypt::encryptString($row->id_order);
                return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->kode_order . '</a>';
            })
            ->editColumn('tgl_order', function ($row) {
                return $row->tgl_order ? date('d-m-Y', strtotime($row->tgl_order)) : '-';
            })
            ->editColumn('total_harga', function ($row) {
                return 'Rp. ' . number_format($row->total_harga, 0, '', '.');
            })
            ->rawColumns(['sko'])
            ->make(true);
    }

    public function table_harga_rinci()
    {
        $query = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->select(['tb_orders.id_customer', 'kode_kustomer', 'tb_orders.id_order', 'tb_orders.sko_key', 'tgl_order', 'status_deal', 'waktu_kontak', 'name', 'tb_customers.grading as grading', 'nama', 'tb_produksis.updated_at as hk_tgl_updated', 'modal_sales', 'total_harga'])
            ->where('tb_produksis.hitung_harga_khusus', 'like', '1')
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });

        return DataTables::of($query)
            ->editColumn('waktu_kontak', function ($row) {
                return $row->waktu_kontak ? date('d M y H:i', strtotime($row->waktu_kontak)) : '';
            })
            ->editColumn('modal_sales', function ($row) {
                return $row->modal_sales ? number_format($row->modal_sales, 0, '', ',') : '-';
            })
            ->editColumn('total_harga', function ($row) {
                return $row->total_harga ? number_format($row->total_harga, 0, '', ',') : '-';
            })
            ->editColumn('nama', function ($row) {
                return '<a href="' . route('crm.detail_customer', $row->kode_kustomer) . '" class="text-gray-800 text-hover-primary mb-1">' . $row->nama . '</a>';
            })
            ->editColumn('grading', function ($row) {
                if ($row->grading == "Reguler") {
                    return '<div class="badge fw-bold" style="background-color: #CD7F32;">' . $row->grading . '</div>';
                } elseif ($row->grading == "Premium") {
                    return '<div class="badge fw-bold" style="background-color: #C0C0C0;">' . $row->grading . '</div>';
                } else {
                    return '<div class="badge fw-bold" style="background-color: #FFD700;">' . $row->grading . '</div>';
                }
            })
            ->editColumn('status_deal', function ($row) {
                if ($row->status_deal == 'Follow Up') {
                    return '<div class="badge badge-light-primary fw-bold">' . $row->status_deal . '</div>';
                } elseif ($row->status_deal == 'Deal') {
                    return '<div class="badge badge-light-success fw-bold">' . $row->status_deal . '</div>';
                } elseif ($row->status_deal == 'Lost') {
                    return '<div class="badge badge-light-danger fw-bold">' . $row->status_deal . '</div>';
                } else {
                    return '';
                }
            })
            ->editColumn('hk_tgl_updated', function ($row) {
                return $row->hk_tgl_updated ? date('d M Y', strtotime($row->hk_tgl_updated)) : '-';
            })
            ->addColumn('action', function ($row) {
                return '<a class="btn btn-sm btn-my-primary text-white w-100 harga_rinci" data-toggle="tooltip" data-key="' . $row->sko_key . '" href="javascript:void(0)">Hitung Harga</a>';
            })
            ->rawColumns(['kode_order', 'sko', 'waktu_kontak', 'modal_sales', 'total_harga', 'nama', 'grading', 'status_deal', 'hk_tgl_updated', 'action'])
            ->make(true);
    }

    public function table_faw(Request $request)
    {
        $statusFAW = $request->input('status_faw');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');
        $query = DB::table('tb_customers')
            ->select(['tb_customers.id_customer', 'nama', 'kode_kustomer', 'tb_orders.id_order', 'kode_order', 'sko', 'tgl_order', 'status_deal', 'tb_orders.order_key', 'tb_faws.sko_key', 'tb_faws.updated_at as tgl_update_faw', 'id_faw', 'status_faw'])
            ->leftJoin('tb_orders', 'tb_customers.id_customer', '=', 'tb_orders.id_customer')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_faws.sko_key')
            ->where('status_deal', 'like', 'Deal')
            ->where('flag_dummy', '!=', 'Jasa Lainnya')
            ->where(function ($query) use ($statusFAW, $fromDate, $toDate) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
                if ($fromDate) {
                    $query->where('tb_orders.tgl_order', '>=', $fromDate);
                }
                if ($toDate) {
                    $query->where('tb_orders.tgl_order', '<=', $toDate);
                }
                switch ($statusFAW) {
                    case '-':
                        $query->whereNull('id_faw');
                        break;
                    case '0':
                    case '1':
                        $query->where('status_faw', '=', $statusFAW);
                        break;
                }
            });

        return DataTables::of($query)
            ->editColumn('kode_order', function ($row) {
                // $param = Crypt::encryptString($row->id_order);
                return '<a href="#" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->kode_order . '</a>';
            })
            ->editColumn('sko', function ($row) {
                $param = Crypt::encryptString($row->id_order);
                return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->sko .'</a>';
            })
            ->editColumn('nama', function ($row) {
                return '<a href="' . route('crm.detail_customer', $row->kode_kustomer) . '" class="text-gray-800 text-hover-primary mb-1">' . $row->nama . '</a>';
            })
            ->editColumn('tgl_order', function ($row) {
                return $row->tgl_order ? date('d M Y', strtotime($row->tgl_order)) : '-';
            })
            ->editColumn('tgl_update_faw', function ($row) {
                return $row->tgl_update_faw ? date('d M Y', strtotime($row->tgl_update_faw)) : '-';
            })
            ->editColumn('status_faw_disp', function ($row) {
                if ($row->status_faw == 1) {
                    $check = 'checked';
                    $label = '<a href="javascript:void(0)" onclick="approveConfirmation(' . $row->id_faw . ')"><div class="form-check form-switch form-check-custom form-check-solid"><input class="form-check-input" style="background-color: #50cd89; border-color: #50cd89;" ' . $check . ' type="checkbox" disabled value="" id="flexSwitchDefault"/><label class="form-check-label text-success" for="flexSwitchDefault">Approved</label></div></a>';
                } else {
                    $check = '';
                    $label = '<a href="javascript:void(0)" onclick="approveConfirmation(' . $row->id_faw . ')"><div class="form-check form-switch form-check-custom form-check-solid"><input class="form-check-input" style="background-color: gray; border-color: gray;" ' . $check . ' type="checkbox" disabled value="" id="flexSwitchDefault"/><label class="form-check-label" style="color: #bfbfbf;" for="flexSwitchDefault">Need Approval</label></div></a>';
                }
                if (Auth::user()->roles == 'SUPERADMIN') {
                    return $row->id_faw ? $label : 'Belum FAW';
                } else {
                    return $row->id_faw ? '<span class="badge badge-light-success badge-lg">' . $label . '</span>' : '-';
                }
            })
            ->addColumn('action', function ($row) {
                $param = Crypt::encryptString($row->id_order);

                $faw = Faw::where('sko_key', '=', $row->sko_key)->count();

                if ($faw > 0) {
                    return '<div class="btn-group d-flex">
                                <a class="btn btn-sm btn-light w-100 disabled" data-toggle="tooltip" href="#">Buat FAW</a>
                                <a class="btn btn-sm btn-my-primary w-100 text-white" data-toggle="tooltip" href="' . route('crm.detail_order', [$param, '#kt_tab_pane_5']) . '">Lihat FAW</a>
                                </div>';
                } else {
                    return '<div class="btn-group d-flex">
                            <a class="btn btn-sm btn-primary w-100" data-toggle="tooltip" href="' . route('crm.form_add_faw', $param) . '">Buat FAW</a>
                            <a class="btn btn-sm btn-light w-100 disabled" data-toggle="tooltip" data-id="' . $row->sko . '" href="#">Lihat FAW</a>
                            </div>';
                }
            })
            ->rawColumns(['sko', 'nama', 'tgl_order', 'tgl_update_faw', 'status_faw_disp', 'action'])
            ->make(true);
    }

    public function form_add_faw($param)
    {

        $param = Crypt::decryptString($param);
        $data_order = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', 'like', 'tb_orders.sko_key')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->where('tb_orders.id_order', 'like', $param)
            ->limit(1)
            ->select(['tb_orders.*', 'kode_order', 'sko', 'nama', 'jenis_kertas', 'gramasi', 'jumlah_produk', 'harga_produk', 'total_harga', 'notes'])
            ->get()->first();

        return view('pages.crm.form_faw', compact('data_order'));
    }

    public function store_faw(Request $request)
    {
        $data_order = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', 'like', 'tb_orders.sko_key')
            ->leftJoin('users', 'users.id', '=', 'tb_orders.id_pic')
            ->where('tb_orders.sko_key', 'like', $request->sko_key)
            ->limit(1)
            ->get()->first();
        $sko_key = $data_order->sko_key;

        try {
            Order::updateOrCreate(
                [
                    'sko_key' => $request->sko_key
                ],
                [
                    'status_order' => 'FAW'
                ]
            );

            $faw_model = new Faw();
            $faw_model->order_key = $request->order_key;
            $faw_model->sko_key = $request->sko_key;
            $faw_model->tgl_faw = Carbon::now();
            $faw_model->keterangan_tambahan = $request->keterangan_tambahan;
            $faw_model->file_final = $request->link_file_final;
            $filenameSimpan = '';
            if ($request->hasFile('lampiran')) {
                $filenameWithExt = $request->file('lampiran')->getClientOriginalName();
                $filename = pathinfo($filenameWithExt, PATHINFO_FILENAME);
                $extension = $request->file('lampiran')->getClientOriginalExtension();
                $filenameSimpan = $filename . '_' . time() . '.' . $extension;
                $fullpath = 'public/' . $sko_key . '/faw/lampiran';
                $path = $request->file('lampiran')->storeAs($fullpath, $filenameSimpan);
            } else {
                $filenameSimpan = '';
            }
            $faw_model->path_lampiran = $filenameSimpan;
            $faw_model->waktu_produksi = $request->waktu_produksi;
            $faw_model->tgl_deadline = $request->tgl_deadline;
            $faw_model->tgl_deadline_konsumen = $request->tgl_deadline_konsumen;
            $faw_model->keterangan_tambahan = $request->keterangan_tambahan;
            $faw_model->status_faw = '0';
            $faw_model->save();

            $param = Crypt::encryptString($request->id_order);

            return redirect()->route('crm.detail_order', $param)->with('success', 'Berhasil menambahkan FAW');
        } catch (\Throwable $th) {
            return redirect()->back()->with('errors', 'Gagal menambahkan FAW');
        }
    }

    public function approve_faw($id)
    {

        $current = Faw::where('id_faw', $id)->get(['status_faw', 'order_key', 'sko_key'])->first();
        
        if ($current->status_faw == 1) {
            $set_stat = 0;
        } else {
            $set_stat = 1;
        }

        if ( $set_stat == 1 ) {
            Spk::updateOrCreate(
                [
                    'sko_key' => $current->sko_key
                ],
                [
                    'order_key' => $current->order_key,
                    'catatan_khusus' => "Belum SPK"
                ]
            );
        } else {
            Spk::where('sko_key', $current->sko_key)->delete();
        }

        $data = Faw::where('id_faw', $id)
            ->update([
                'status_faw' => $set_stat
            ]);
        return Response::json(['data' => $data, 'success' => 'Status updated successfully!'], 200);
    }

    public function faw_preview($id)
    {
        $data_order = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('tb_produksis', 'tb_produksis.order_key', 'like', 'tb_orders.order_key')
            ->leftJoin('users', 'users.id', '=', 'tb_orders.id_pic')
            ->where('tb_orders.id_order', 'like', $id)
            ->limit(1)
            ->get()->first();

        return view('pages.crm.faw_preview', compact('data_order'));
    }

    public function generate_faw($param)
    {

        $data = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', 'like', 'tb_orders.sko_key')
            ->leftJoin('users', 'users.id', '=', 'tb_orders.id_pic')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_faws.sko_key')
            ->where('tb_orders.sko_key', 'like', $param)
            ->limit(1)
            ->get()->first();

        $lampiran_path = storage_path('app/public/' . $data->sko_key . '/faw/lampiran/' . $data->path_lampiran);

        // $jns_bahan = $data->jenis_bahan ? $data->jenis_bahan : '😊';
        $jns_kertas = $data->jenis_kertas ? $data->jenis_kertas : '';
        $gramasi = $data->gramasi ? $data->gramasi : '';
        $laminasi = $data->laminasi ? $data->laminasi : '';
        $sisi_laminasi = $data->sisi_laminasi ? $data->sisi_laminasi : '';

        $bahan =   $jns_kertas . ', ' . $gramasi;
        $laminasi = $laminasi . ', ' . $sisi_laminasi;

        $ukuran = $data->dp_panjang . 'x' . $data->dp_lebar . 'x' . $data->dp_tinggi . 'cm';

        $templateProcessor = new TemplateProcessor(storage_path('app/public/template/faw/template_FAW.docx'));
        $templateProcessor->setValue('kode_order', $data->kode_order);
        $templateProcessor->setValue('tipe_produk', htmlspecialchars($data->tipe_produk));
        $templateProcessor->setValue('tgl_faw', $data->tgl_faw);
        $templateProcessor->setValue('tgl_deadline', $data->tgl_deadline);
        $templateProcessor->setValue('bahan', $bahan);
        $templateProcessor->setValue('laminasi', $laminasi);
        $templateProcessor->setValue('ukuran', $ukuran);
        $templateProcessor->setValue('jumlah_produk', $data->jumlah_produk);
        $templateProcessor->setValue('harga_produk', $data->harga_produk);
        $templateProcessor->setValue('notes', $data->notes);
        $templateProcessor->setValue('keterangan_tambahan', $data->keterangan_tambahan);
        $templateProcessor->setValue('waktu_produksi', $data->waktu_produksi);
        $templateProcessor->setValue('finishing', $data->finishing);
        $templateProcessor->setValue('nama_pic', $data->name);
        $templateProcessor->setValue('nama_customer', $data->nama);
        $templateProcessor->setImageValue('lampiran_produk', array('path' => $lampiran_path, 'width' => '6cm', 'height' => '6cm', 'ratio' => true));
        $templateProcessor->setImageValue('lampiran_produk_large', array('path' => $lampiran_path, 'width' => '12cm', 'height' => '12cm', 'ratio' => true));

        $datemark = date('dmy');
        $filename = 'FAW ' . $data->nama . ' ' . $datemark . '.docx';
        $templateProcessor->saveAs($filename);

        \PhpOffice\PhpWord\Settings::setOutputEscapingEnabled(true);
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filename));
        ob_clean();
        flush();
        readfile($filename);
        unlink($filename);
        exit;
    }

    public function edit_faw($param)
    {
        $data = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('tb_faws', 'tb_orders.sko_key', 'like', 'tb_faws.sko_key')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->where('tb_orders.sko_key', 'like', $param)
            ->limit(1)
            ->select('*')
            ->get()->first();

        return view('pages.crm.form_edit_faw', compact('data'));
    }

    public function update_faw(Request $request)
    {

        $data_order = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', 'like', 'tb_orders.sko_key')
            ->leftJoin('users', 'users.id', '=', 'tb_orders.id_pic')
            ->leftJoin('tb_faws', 'tb_orders.sko_key', 'like', 'tb_faws.sko_key')
            ->where('tb_orders.sko_key', 'like', $request->sko_key)
            ->limit(1)
            ->get()->first();
        $sko_key = $data_order->sko_key;

        $faw_model = Faw::where('sko_key', $request->sko_key)->first();
        $faw_model->sko_key = $request->sko_key;
        $faw_model->tgl_faw = $request->tgl_faw;
        $faw_model->keterangan_tambahan = $request->keterangan_tambahan;
        $faw_model->file_final = $request->link_file_final;

        if (!empty($request->file('lampiran'))) {
            if ($request->hasFile('lampiran')) {
                $filenameWithExt = $request->file('lampiran')->getClientOriginalName();
                $filename = pathinfo($filenameWithExt, PATHINFO_FILENAME);
                $extension = $request->file('lampiran')->getClientOriginalExtension();
                $filenameSimpan = $filename . '_' . time() . '.' . $extension;
                $fullpath = 'public/' . $sko_key . '/faw/lampiran';
                $path = $request->file('lampiran')->storeAs($fullpath, $filenameSimpan);
            } else {
                $filenameSimpan = '';
            }
            $faw_model->path_lampiran = $filenameSimpan;
        }

        $faw_model->waktu_produksi = $request->waktu_produksi;
        $faw_model->tgl_deadline = $request->tgl_deadline;
        $faw_model->keterangan_tambahan = $request->keterangan_tambahan;
        $faw_model->update();

        $param = Crypt::encryptString($request->id_order);

        return redirect()->route('crm.detail_order', $param);
    }

    public function delete_lampiran($param)
    {

        $data = Faw::where('id_faw', $param)->get()->first();

        $fullpath = storage_path('app/public/' . $data->sko_key . '/faw/lampiran/' . $data->path_lampiran);

        if ($fullpath) {
            unlink($fullpath);
            Faw::where('id_faw', $param)->update(['path_lampiran' => null]);
        }

        return redirect()->route('crm.edit_faw', $data->sko_key);
    }

    public function generate_invoice_dp($param)
    {
        $data = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', 'like', 'tb_orders.sko_key')
            ->leftJoin('users', 'users.id', '=', 'tb_orders.id_pic')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_faws.sko_key')
            ->where('tb_orders.sko_key', 'like', $param)
            ->limit(1)
            ->get()->first();

        if (empty($data->alamat_instansi)) {
            $alamat = $data->alamat;
        } else {
            $alamat = $data->alamat_instansi;
        }

        $spesifikasi = $data->lp_panjang . 'x' . $data->lp_lebar . ', ' . $data->jenis_bahan . ', ' . $data->jenis_kertas;
        $ppn = $data->total_harga * 11 / 100;
        $grand_total = $data->total_harga + $ppn;
        $dp = $data->DP;
        $sisa_payment = $grand_total - $dp;
        $status_pembayaran = 'BELUM LUNAS';
        $metode_pembayaran = 'TRANSFER';
        $nama_pic = $data->name;

        $templateProcessor = new TemplateProcessor(storage_path('app/public/template/invoice/template_invoice_dp.docx'));
        $templateProcessor->setValue('tgl_invoice', date('d F Y'));
        $templateProcessor->setValue('nama_instansi', htmlspecialchars($data->nama_instansi));
        $templateProcessor->setValue('nama_customer', $data->nama);
        $templateProcessor->setValue('no_hp', $data->no_hp);
        $templateProcessor->setValue('alamat', htmlspecialchars($alamat));
        $templateProcessor->setValue('kategori', htmlspecialchars($data->tipe_produk));
        $templateProcessor->setValue('spesifikasi', $spesifikasi);
        $templateProcessor->setValue('harga_produk', number_format($data->harga_produk, 0, '', ','));
        $templateProcessor->setValue('jumlah_produk', $data->jumlah_produk);
        $templateProcessor->setValue('total_harga', number_format($data->total_harga, 0, '', ','));
        $templateProcessor->setValue('ppn', number_format($ppn, 0, '', ','));
        $templateProcessor->setValue('grand_total', number_format($grand_total, 0, '', ','));
        $templateProcessor->setValue('dp', number_format($dp, 0, '', ','));
        $templateProcessor->setValue('sisa_payment', number_format($sisa_payment, 0, '', ','));
        $templateProcessor->setValue('status_pembayaran', $status_pembayaran);
        $templateProcessor->setValue('metode_pembayaran', $metode_pembayaran);
        $templateProcessor->setValue('nama_pic', $nama_pic);

        // $path = storage_path('app/public/'.$data->order_key.'/invoice');
        $datemark = date('dmy');
        $filename = 'Invoice DP ' . $data->nama . ' ' . $datemark . '.docx';
        $templateProcessor->saveAs($filename);

        \PhpOffice\PhpWord\Settings::setOutputEscapingEnabled(true);
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filename));
        ob_clean();
        flush();
        readfile($filename);
        unlink($filename);
        exit;
    }

    public function generate_invoice($param)
    {
        $data = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', 'like', 'tb_orders.sko_key')
            ->leftJoin('users', 'users.id', '=', 'tb_orders.id_pic')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_faws.sko_key')
            ->where('tb_orders.sko_key', 'like', $param)
            ->limit(1)
            ->get()->first();

        if (empty($data->alamat_instansi)) {
            $alamat = $data->alamat;
        } else {
            $alamat = $data->alamat_instansi;
        }

        $spesifikasi = $data->lp_panjang . 'x' . $data->lp_lebar . ', ' . $data->jenis_bahan . ', ' . $data->jenis_kertas;
        $ppn = $data->total_harga * 11 / 100;
        $grand_total = $data->total_harga + $ppn;
        $dp = '0';
        $paid = $grand_total - $dp;
        $status_pembayaran = 'BELUM LUNAS';
        $metode_pembayaran = 'TRANSFER';
        $nama_pic = $data->name;

        $templateProcessor = new TemplateProcessor(storage_path('app/public/template/invoice/template_invoice.docx'));
        $templateProcessor->setValue('tgl_invoice', date('d F Y'));
        $templateProcessor->setValue('nama_instansi', htmlspecialchars($data->nama_instansi));
        $templateProcessor->setValue('nama_customer', $data->nama);
        $templateProcessor->setValue('no_hp', $data->no_hp);
        $templateProcessor->setValue('alamat', htmlspecialchars($alamat));
        $templateProcessor->setValue('kategori', htmlspecialchars($data->tipe_produk));
        $templateProcessor->setValue('spesifikasi', $spesifikasi);
        $templateProcessor->setValue('harga_produk', number_format($data->harga_produk, 0, '', ','));
        $templateProcessor->setValue('jumlah_produk', $data->jumlah_produk);
        $templateProcessor->setValue('total_harga', number_format($data->total_harga, 0, '', ','));
        $templateProcessor->setValue('ppn', number_format($ppn, 0, '', ','));
        $templateProcessor->setValue('grand_total', number_format($grand_total, 0, '', ','));
        $templateProcessor->setValue('paid', number_format($paid, 0, '', ','));
        $templateProcessor->setValue('status_pembayaran', $status_pembayaran);
        $templateProcessor->setValue('metode_pembayaran', $metode_pembayaran);
        $templateProcessor->setValue('nama_pic', $nama_pic);

        $datemark = date('dmy');
        $filename = 'Invoice Lunas ' . $data->nama . ' ' . $datemark . '.docx';
        $templateProcessor->saveAs($filename);

        \PhpOffice\PhpWord\Settings::setOutputEscapingEnabled(true);
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filename));
        ob_clean();
        flush();
        readfile($filename);
        unlink($filename);
        exit;
    }

    public function generate_surat_jalan($param)
    {
        $data = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', 'like', 'tb_orders.sko_key')
            ->leftJoin('users', 'users.id', '=', 'tb_orders.id_pic')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_spks', 'tb_orders.sko_key', '=', 'tb_spks.sko_key')
            ->where('tb_orders.sko_key', 'like', $param)
            ->limit(1)
            ->get()->first();

        if (empty($data->alamat_instansi)) {
            $alamat = $data->alamat;
        } else {
            $alamat = $data->alamat_instansi;
        }


        $lp_panjang = $data->lp_panjang;
        $lp_lebar = $data->lp_lebar;
        $jenis_kertas = $data->jenis_kertas;
        $gramasi = $data->gramasi;
        $laminasi = $data->laminasi;
        $sisi_laminasi = $data->sisi_laminasi;
        $finishing = $data->finishing;
        $jumlah_produk = $data->jumlah_produk . ' pcs';
        $notes = $data->notes ? ', ' . $data->notes : '';

        $spesifikasi = $lp_panjang . 'cm X ' . $lp_lebar . 'cm, ' . $jenis_kertas . ', ' . $gramasi . ', ' . $laminasi . ', ' . $sisi_laminasi . ', ' . $finishing . ', ' . $jumlah_produk . ', ' . $notes . '.';
        $tgl_kirim = $data->tgl_kirim ? date('d F Y', strtotime($data->tgl_kirim)) : '';
        $alamat = $data->alamat_instansi ? $data->alamat_instansi : $data->alamat;
        $nama_customer = $data->nama_instansi ? $data->nama_instansi : $data->nama;
        $tipe_produk = $data->tipe_produk;
        $jumlah_kirim = $data->jumlah_fix;
        $jenis_bahan = $data->jenis_bahan;

        $templateProcessor = new TemplateProcessor(storage_path('app/public/template/surat_jalan/template_surat_jalan.docx'));
        $templateProcessor->setValue('tgl_kirim', $tgl_kirim);
        $templateProcessor->setValue('alamat', htmlspecialchars($alamat));
        $templateProcessor->setValue('nama_instansi', htmlspecialchars($nama_customer));
        $templateProcessor->setValue('jenis_bahan', htmlSpecialchars($jenis_bahan));
        $templateProcessor->setValue('tipe_produk', htmlspecialchars($tipe_produk));
        $templateProcessor->setValue('spesifikasi', htmlSpecialchars($spesifikasi));
        $templateProcessor->setValue('jumlah_kirim', $jumlah_kirim);


        $datemark = date('dmy');
        $filename = 'Surat Jalan ' . $data->nama . ' ' . $datemark . '.docx';
        $templateProcessor->saveAs($filename);

        \PhpOffice\PhpWord\Settings::setOutputEscapingEnabled(true);
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filename));
        ob_clean();
        flush();
        readfile($filename);
        unlink($filename);
        exit;
    }

    public function detail_order($param)
    {
        $param = Crypt::decryptString($param);
        $param_encrypt = Crypt::encryptString($param);
        $data_order = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', 'like', 'tb_orders.sko_key')
            ->leftJoin('users', 'users.id', '=', 'tb_orders.id_pic')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_faws.sko_key')
            ->leftJoin('tb_spks', 'tb_orders.sko_key', '=', 'tb_spks.sko_key')
            ->where('tb_orders.id_order', 'like', $param)
            ->select('tb_orders.*', 'tb_customers.*', 'tb_kode_orders.kode_order', 'users.name', 'tb_produksis.*', 'tb_faws.id_faw', 'tb_spks.id_spk')
            ->limit(1)
            ->get()->first();

        $data_faw = DB::table('tb_faws')
            ->leftJoin('tb_kode_orders', 'tb_faws.sko_key', '=', 'tb_faws.sko_key')
            ->where('tb_faws.sko_key', $data_order->sko_key)
            ->limit(1)
            ->get()->first();

        $data_spk = DB::table('tb_spks')
            ->where('sko_key', $data_order->sko_key)
            ->get()
            ->first();

        return view('pages.crm.order_detail', compact('data_order', 'param_encrypt', 'data_faw', 'data_spk'));
    }

    public function form_edit_order(Request $request, $param)
    {

        $param = Crypt::decryptString($param);

        $param_encrypt = Crypt::encryptString($param);

        $pic = User::all();

        $data = Order::leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('users', 'users.id', '=', 'tb_orders.id_pic')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->where('tb_orders.id_order', 'like', $param)
            ->get()
            ->first();

        $sumber = Sumber::all();
        $follow_up = FollowUp::all();
        $follow_up2 = FollowUp::all();
        $lost = Lost::all();

        return view('pages.crm.form_edit_order', compact('data', 'pic', 'param_encrypt', 'sumber', 'follow_up', 'follow_up2', 'lost'));
    }

    public function form_edit_produk(Request $request, $param)
    {

        $param = Crypt::decryptString($param);

        $param_encrypt = Crypt::encryptString($param);

        $pic = User::all();

        $data = Order::leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('users', 'users.id', '=', 'tb_orders.id_pic')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->where('tb_orders.id_order', 'like', $param)
            ->get()
            ->first();

        $bahan = Bahan::all();
        $jenis_kertas = JenisKertas::all();
        $gramasi = Gramasi::all();
        $tipe_produk = TipeProduk::all();
        $kertas_plano = KertasPlano::all();

        return view('pages.crm.form_edit_produk', compact('data', 'pic', 'param_encrypt', 'bahan', 'jenis_kertas', 'gramasi', 'tipe_produk', 'kertas_plano'));
    }

    public function show_fpm($id)
    {
        // $order = Order::where('id_order',$id)->first();
        $order = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_fpms', 'tb_orders.sko_key', '=', 'tb_fpms.sko_key')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('tb_orders.sko_key', 'LIKE', $id)
            ->select(['tb_customers.id_customer as idcust', 'tb_produksis.*', 'tb_fpms.*', 'users.name', 'id_order', 'tb_orders.order_key', 'tb_orders.sko_key', 'sumber', 'tb_kode_orders.kode_order', 'sko', 'tgl_order', 'waktu_kontak', 'name', 'nama', 'users.id', 'status_deal', 'status_order', 'tipe_kontak', 'tb_customers.grading', 'DP', 'flag_lunas', 'catatan_order', 'flag_dummy'])
            ->limit(1)
            ->get();

        // dd(Response::json($order, 200));

        return Response::json($order, 200);
    }

    public function table_kendala()
    {
        $query = DB::table('tb_orders')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('tb_fpms', 'tb_orders.sko_key', '=', 'tb_fpms.sko_key')
            ->select(['tb_customers.id_customer', 'kode_kustomer', 'nama', 'id_order', 'tb_kode_orders.kode_order', 'tb_fpms.tgl_masalah', 'sko', 'tb_orders.order_key', 'tb_orders.sko_key', 'tb_fpms.updated_at as last_edited', 'tb_fpms.updated_at as last_update', 'solusi'])
            ->where('tb_produksis.kendala_produksi', '=', '1')
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });

        return DataTables::of($query)
            // ->editColumn('kode_order', function ($row) {
            //     $param = Crypt::encryptString($row->order_key);
            //     return '<a href="' . route('crm.detail_main_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->kode_order . '</a>';
            // })
            ->editColumn('sko', function ($row) {
                $param = Crypt::encryptString($row->id_order);
                return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->sko . '</a>';
            })
            ->editColumn('nama', function ($row) {
                return '<a href="' . route('crm.detail_customer', $row->kode_kustomer) . '" class="text-gray-800 text-hover-primary mb-1">' . $row->nama . '</a>';
            })
            ->editColumn('tgl_masalah', function ($row) {
                return $row->tgl_masalah ? date('d M Y', strtotime($row->tgl_masalah)) : '';
            })
            ->editColumn('solusi', function ($row) {
                if ($row->solusi == "perbaikan") {
                    return '<span class="badge badge-light-warning fw-bold">' . $row->solusi . '</span>';
                } elseif ($row->solusi == "produksi-ulang") {
                    return '<span class="badge badge-light-danger fw-bold">' . $row->solusi . '</span>';
                } elseif ($row->solusi == "negosiasi") {
                    return '<span class="badge badge-light-primary fw-bold">' . $row->solusi . '</span>';
                } else {
                    return '-';
                }
            })
            ->editColumn('last_update', function ($row) {
                return $row->last_update ? date('d M Y', strtotime($row->last_update)) : '';
            })
            ->addColumn('action', function ($row) {
                if (!empty($row->solusi)) {
                    return '<div class="btn-group d-flex">
                            <a class="btn btn-sm btn-success w-100 fpm" data-toggle="tooltip" data-key="' . $row->sko_key . '" href="javascript:void(0)">FPM</a>
                            <a class="btn btn-sm btn-my-primary w-100 text-white solusi" data-toggle="tooltip" data-key="' . $row->sko_key . '">Form Solusi</a>
                            </div>';
                } else {
                    return '<div class="btn-group d-flex">
                            <a class="btn btn-sm btn-success w-100 fpm" data-toggle="tooltip" data-key="' . $row->sko_key . '" href="javascript:void(0)">FPM</a>
                            <a class="btn btn-sm btn-light w-100 disabled" data-toggle="tooltip" data-key="' . $row->sko_key . '">Form Solusi</a>
                            </div>';
                }
            })
            ->rawColumns(['sko', 'nama', 'tgl_masalah', 'solusi', 'last_update', 'action'])
            ->make(true);
    }

    public function form_fpm(Request $request)
    {

        $data = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', 'like', 'tb_orders.sko_key')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->where('tb_orders.sko_key', 'like', $request->sko_key)
            ->limit(1)
            ->select(['tb_orders.*', 'kode_order', 'sko', 'name', 'nama', 'jenis_kertas', 'gramasi', 'jumlah_produk', 'harga_produk', 'total_harga'])
            ->get()->first();

        $cost = (float)str_replace(',', '', $request->cost);

        $data_fpm = PenanggulanganMasalah::updateOrCreate(
            ['sko_key' => $request->sko_key],
            [
                'order_key' => $request->order_key,
                'tgl_masalah' => $request->tgl_masalah,
                'kategori_masalah' => $request->kategori_masalah,
                'detail_masalah' => $request->detail_masalah,
                'tgl_deadline_perbaikan' => $request->tgl_deadline_perbaikan,
                'detail_solusi' => $request->detail_solusi,
                'cost' => $cost
            ]
        );
        if ($request->ajax()) {
            return Response::json(['data' => $data_fpm], 200);
        }
        return redirect()->route('crm')->with('message', [
            'type' => 'Success',
            'text' => 'Production Added successfully',
        ]);
    }

    public function store_table_solusi(Request $request)
    {
        $cost = (float)str_replace(',', '', $request->cost);

        $data_fpm = PenanggulanganMasalah::updateOrCreate(
            ['sko_key' => $request->sko_key],
            [
                'detail_masalah' => $request->detail_masalah,
                'tgl_deadline_perbaikan' => $request->tgl_deadline_solusi,
                'detail_solusi' => $request->detail_solusi,
                'cost' => $cost
            ]
        );
        if ($request->ajax()) {
            return Response::json(['data' => $data_fpm], 200);
        }
        return redirect()->route('crm')->with('message', [
            'type' => 'Success',
            'text' => 'Production Added successfully',
        ]);
    }

    public function form_solusi(Request $request)
    {
        $sko_key = request()->get('sko_key');
        $solusi = request()->get('solusi');

        $data = Order::leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_fpms', 'tb_orders.sko_key', '=', 'tb_fpms.sko_key')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_faws.sko_key')
            ->where('tb_orders.sko_key', 'LIKE', $sko_key)
            ->select(['tb_customers.id_customer as idcust', 'tb_produksis.*', 'tb_fpms.*', 'tb_faws.*', 'users.name', 'id_order', 'tb_orders.order_key', 'tb_orders.sko_key', 'sumber', 'tb_kode_orders.kode_order', 'sko', 'tgl_order', 'waktu_kontak', 'name', 'nama', 'users.id', 'status_deal', 'status_order', 'tipe_kontak', 'tb_customers.grading', 'DP', 'flag_lunas', 'catatan_order', 'flag_dummy'])
            ->get()->first();


        // dd($data);
        return view('pages.crm.form_solusi', compact('data', 'solusi'));
    }

    public function store_solusi(Request $request)
    {

        $sko_key = $request->sko_key;
        $cost = (float)str_replace(',', '', $request->cost);

        $data = PenanggulanganMasalah::updateOrCreate(
            ['sko_key' => $sko_key],
            [
                'solusi' => $request->solusi,
                'detail_solusi' => $request->detail_solusi,
                'cost' => $cost,
                'tgl_deadline_perbaikan' => $request->tgl_deadline
            ]
        );

        if ($request->solusi == 'produksi-ulang') {

            $data_before = Order::leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
                ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
                ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
                ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
                ->leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_faws.sko_key')
                ->leftJoin('tb_spks', 'tb_orders.sko_key', '=', 'tb_spks.sko_key')
                ->where('tb_orders.sko_key', 'LIKE', $sko_key)
                ->get()
                ->first();
            $sko_old = $data_before->sko;
            $sko_new = substr_replace($sko_old, "R", -1);
            $sko_key_new = Uuid::uuid4()->toString();
            $order_key_new = Uuid::uuid4()->toString();

            // dd($data_before);

            Order::create([
                'id_customer' => $data_before->id_customer,
                'order_key' => $request->order_key,
                'sko_key' => $sko_key_new,
                'sko' => $sko_new,
                'id_pic' => $data_before->id_pic,
                'sumber' => $data_before->sumber,
                'produk_ke' => $data_before->produk_ke,
                'tgl_order' => $data_before->tgl_order,
                'waktu_kontak' => $data_before->waktu_kontak,
                'status_deal' => $data_before->status_deal,
                'status_order' => $data_before->status_order,
                'tipe_kontak' => $data_before->tipe_kontak,
                'DP' => $data_before->DP,
                'flag_lunas' => $data_before->flag_lunas,
                'catatan_order' => $data_before->catatan_order,
                'flag_dummy' => 'Produksi Ulang'
            ]);

            Produksi::create([
                'order_key' => $request->order_key,
                'sko_key' => $sko_key_new,
                'kategori_produksi' => $data_before->kategori_produksi,
                'jenis_bahan' => $data_before->jenis_bahan,
                'dp_panjang' => $data_before->dp_panjang,
                'dp_lebar' => $data_before->dp_lebar,
                'dp_tinggi' => $data_before->dp_tinggi,
                'lp_panjang' => $data_before->lp_panjang,
                'lp_lebar' => $data_before->lp_lebar,
                'jenis_kertas' => $data_before->jenis_kertas,
                'gramasi' => $data_before->gramasi,
                'laminasi' => $data_before->laminasi,
                'sisi_laminasi' => $data_before->sisi_laminasi,
                'finishing' => $data_before->finishingfix,
                'tipe_produk' => $data_before->tipe_produk,
                'isi_kertas' => $data_before->isi_kertas,
                'get_plano' => $data_before->get_plano,
                'jumlah_produk' => $data_before->jumlah_produk,
                'harga_produk' => $data_before->harga_produk,
                'total_harga' => $data_before->total_harga,
                'modal_sales' => $data_before->modal_sales,
                'notes' => $data_before->notes,
                'nama_produk' => $data_before->nama_produk,
                'hitung_harga_khusus' => $data_before->flag_harga_khusus,
                'kendala_produksi' => $data_before->flag_kendala_produksi
            ]);

            // Spk::create([
            //     'order_key' => $request->order_key,
            //     'sko_key' => $sko_key_new,
            //     'catatan_khusus' => "$data_before->catatan_khusus",
            //     'progress_produksi_1' => $data_before->progress_produksi_1,
            //     'jumlah_plano' => $data_before->jumlah_plano,
            //     'vendor_1' => $data_before->vendor_1,
            //     'tgl_produksi_1' => $data_before->tgl_produksi_1,
            //     'pic_validasi_1' => $data_before->pic_validasi_1,
            //     'flag_status_1' => $data_before->flag_status_1,
            //     'tgl_flag_status_1' => $data_before->tgl_flag_status_1,
            //     'catatan_1' => $data_before->catatan_1,
            //     'check_1_1' => $data_before->check_1_1,
            //     'check_1_2' => $data_before->check_1_2,
            //     'check_1_3' => $data_before->check_1_3,
            //     'check_1_4' => $data_before->check_1_4,
            //     'progress_produksi_2' => $data_before->progress_produksi_2,
            //     'jumlah_awal_pp_2' => $data_before->jumlah_awal_pp_2,
            //     'jumlah_hasil_pp_2' => $data_before->jumlah_hasil_pp_2,
            //     'reject_pp_2' => $data_before->reject_pp_2,
            //     'vendor_2' => $data_before->vendor_2,
            //     'tgl_produksi_2' => $data_before->tgl_produksi_2,
            //     'pic_validasi_2' => $data_before->pic_validasi_2,
            //     'flag_status_2' => $data_before->flag_status_2,
            //     'tgl_flag_status_2' => $data_before->tgl_flag_status_2,
            //     'catatan_2' => $data_before->catatan_2,
            //     'check_2_1' => $data_before->check_2_1,
            //     'check_2_2' => $data_before->check_2_2,
            //     'check_2_3' => $data_before->check_2_3,
            //     'check_2_4' => $data_before->check_2_4,
            //     'progress_produksi_3' => $data_before->progress_produksi_3,
            //     'jumlah_awal_pp_3' => $data_before->jumlah_awal_pp_3,
            //     'jumlah_hasil_pp_3' => $data_before->jumlah_hasil_pp_3,
            //     'reject_pp_3' => $data_before->reject_pp_3,
            //     'vendor_3' => $data_before->vendor_3,
            //     'tgl_produksi_3' => $data_before->tgl_produksi_3,
            //     'pic_validasi_3' => $data_before->pic_validasi_3,
            //     'flag_status_3' => $data_before->flag_status_3,
            //     'tgl_flag_status_3' => $data_before->tgl_flag_status_3,
            //     'catatan_3' => $data_before->catatan_3,
            //     'check_3_1' => $data_before->check_3_1,
            //     'check_3_2' => $data_before->check_3_2,
            //     'check_3_3' => $data_before->check_3_3,
            //     'check_3_4' => $data_before->check_3_4,
            //     'progress_produksi_4' => $data_before->progress_produksi_4,
            //     'jumlah_awal_pp_4' => $data_before->jumlah_awal_pp_4,
            //     'jumlah_hasil_pp_4' => $data_before->jumlah_hasil_pp_4,
            //     'reject_pp_4' => $data_before->reject_pp_4,
            //     'vendor_4' => $data_before->vendor_4,
            //     'tgl_produksi_4' => $data_before->tgl_produksi_4,
            //     'pic_validasi_4' => $data_before->pic_validasi_4,
            //     'flag_status_4' => $data_before->flag_status_4,
            //     'tgl_flag_status_4' => $data_before->tgl_flag_status_4,
            //     'catatan_4' => $data_before->catatan_4,
            //     'check_4_1' => $data_before->check_4_1,
            //     'check_4_2' => $data_before->check_4_2,
            //     'check_4_3' => $data_before->check_4_3,
            //     'check_4_4' => $data_before->check_4_4,
            //     'check_4_5' => $data_before->check_4_5,
            //     'progress_produksi_5' => $data_before->progress_produksi_5,
            //     'jumlah_awal_pp_5' => $data_before->jumlah_awal_pp_5,
            //     'jumlah_hasil_pp_5' => $data_before->jumlah_hasil_pp_5,
            //     'reject_pp_5' => $data_before->reject_pp_5,
            //     'vendor_5' => $data_before->tgl_produksi_5,
            //     'pic_validasi_5' => $data_before->pic_validasi_5,
            //     'flag_status_5' => $data_before->flag_status_5,
            //     'tgl_flag_status_5' => $data_before->tgl_flag_status_5,
            //     'catatan_5' => $data_before->catatan_5,
            //     'check_5_1' => $data_before->check_5_1,
            //     'check_5_2' => $data_before->check_5_2,
            //     'check_5_3' => $data_before->check_5_3,
            //     'check_5_4' => $data_before->check_5_4,
            //     'check_5_5' => $data_before->check_5_5,
            //     'progress_produksi_6' => $data_before->progress_produksi_6,
            //     'jumlah_awal_pp_6' => $data_before->jumlah_awal_pp_6,
            //     'jumlah_hasil_pp_6' => $data_before->jumlah_hasil_pp_6,
            //     'reject_pp_6' => $request->reject_pp_6,
            //     'vendor_6' => $data_before->vendor_6,
            //     'tgl_produksi_6' => $data_before->tgl_produksi_6,
            //     'pic_validasi_6' => $data_before->pic_validasi_6,
            //     'flag_status_6' => $data_before->flag_status_6,
            //     'tgl_flag_status_6' => $data_before->tgl_flag_status_6,
            //     'catatan_6' => $data_before->catatan_6,
            //     'check_6_1' => $data_before->check_6_1,
            //     'check_6_2' => $data_before->check_6_2,
            //     'check_6_3' => $data_before->check_6_3,
            //     'check_6_4' => $data_before->check_6_4,
            //     'progress_produksi_7' => $data_before->progress_produksi_7,
            //     'jumlah_awal_pp_7' => $data_before->jumlah_awal_pp_7,
            //     'jumlah_hasil_pp_7' => $data_before->jumlah_hasil_pp_7,
            //     'reject_pp_7' => $data_before->reject_pp_7,
            //     'vendor_7' => $data_before->vendor_7,
            //     'tgl_produksi_7' => $data_before->tgl_produksi_7,
            //     'pic_validasi_7' => $data_before->pic_validasi_7,
            //     'flag_status_7' => $data_before->flag_status_7,
            //     'tgl_flag_status_7' => $data_before->tgl_flag_status_7,
            //     'catatan_7' => $data_before->catatan_7,
            //     'progress_produksi_8' => $data_before->progress_produksi_8,
            //     'jumlah_awal_pp_8' => $data_before->jumlah_awal_pp_8,
            //     'jumlah_hasil_pp_8' => $data_before->jumlah_hasil_pp_8,
            //     'reject_pp_8' => $data_before->reject_pp_8,
            //     'vendor_8' => $data_before->vendor_8,
            //     'tgl_produksi_8' => $data_before->tgl_produksi_8,
            //     'pic_validasi_8' => $data_before->pic_validasi_8,
            //     'flag_status_8' => $data_before->flag_status_8,
            //     'tgl_flag_status_8' => $data_before->tgl_flag_status_8,
            //     'catatan_8' => $data_before->catatan_8,
            //     'check_8_1' => $data_before->check_8_1,
            //     'check_8_2' => $data_before->check_8_2,
            //     'check_8_3' => $data_before->check_8_3,
            //     'check_8_4' => $data_before->check_8_4,
            //     'check_8_5' => $data_before->check_8_5,
            //     'progress_produksi_9' => $data_before->progress_produksi_9,
            //     'jumlah_awal_pp_9' => $data_before->jumlah_awal_pp_9,
            //     'jumlah_hasil_pp_9' => $data_before->jumlah_hasil_pp_9,
            //     'reject_pp_9' => $data_before->reject_pp_9,
            //     'vendor_9' => $data_before->vendor_9,
            //     'tgl_produksi_9' => $data_before->tgl_produksi_9,
            //     'pic_validasi_9' => $data_before->pic_validasi_9,
            //     'flag_status_9' => $data_before->flag_status_9,
            //     'tgl_flag_status_9' => $data_before->tgl_flag_status_9,
            //     'catatan_9' => $data_before->catatan_9,
            //     'check_9_1' => $data_before->check_9_1,
            //     'check_9_2' => $data_before->check_9_2,
            //     'check_9_3' => $data_before->check_9_3,
            //     'check_9_4' => $data_before->check_9_4,
            //     'check_9_5' => $data_before->check_9_5,
            //     'progress_produksi_10' => $data_before->progress_produksi_10,
            //     'jumlah_awal_pp_10' => $data_before->jumlah_awal_pp_10,
            //     'jumlah_hasil_pp_10' => $data_before->jumlah_hasil_pp_10,
            //     'reject_pp_10' => $data_before->reject_pp_10,
            //     'vendor_10' => $data_before->vendor_10,
            //     'tgl_produksi_10' => $data_before->tgl_produksi_10,
            //     'pic_validasi_10' => $data_before->pic_validasi_10,
            //     'flag_status_10' => $data_before->flag_status_10,
            //     'tgl_flag_status_10' => $data_before->tgl_flag_status_10,
            //     'catatan_10' => $data_before->catatan_10,
            //     'check_10_1' => $data_before->check_10_1,
            //     'check_10_2' => $data_before->check_10_2,
            //     'check_10_3' => $data_before->check_10_3,
            //     'check_10_4' => $data_before->check_10_4,
            //     'check_10_5' => $data_before->check_10_5,
            // ]);

            // $faw_model = new Faw();
            // $faw_model->order_key = $request->order_key;
            // $faw_model->sko_key = $sko_key_new;
            // $faw_model->tgl_faw = Carbon::now();
            // $faw_model->keterangan_tambahan = $request->keterangan_tambahan;
            // $faw_model->file_final = $request->link_file_final;
            // if ($request->hasFile('lampiran')) {
            //     $filenameWithExt = $request->file('lampiran')->getClientOriginalName();
            //     $filename = pathinfo($filenameWithExt, PATHINFO_FILENAME);
            //     $extension = $request->file('lampiran')->getClientOriginalExtension();
            //     $filenameSimpan = $filename . '_' . time() . '.' . $extension;
            //     $fullpath = 'public/' . $sko_key . '/faw/lampiran';
            //     $path = $request->file('lampiran')->storeAs($fullpath, $filenameSimpan);
            // } else {
            //     $filenameSimpan = '';
            // }
            // $faw_model->path_lampiran = $filenameSimpan;
            // $faw_model->waktu_produksi = $request->waktu_produksi;
            // $faw_model->tgl_deadline = $request->tgl_deadline;
            // $faw_model->keterangan_tambahan = $request->keterangan_tambahan;
            // $faw_model->status_faw = 'Need Approval';
            // $faw_model->save();
        }

        return redirect()->route('crm')->with('message', [
            'type' => 'Success',
            'text' => 'Solusi Penanganan masalah berhasil dibuat',
        ]);
    }

    public function generate_offering_letter($param)
    {
        $data = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', 'like', 'tb_orders.id_customer')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', 'like', 'tb_orders.sko_key')
            ->leftJoin('users', 'users.id', '=', 'tb_orders.id_pic')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_faws', 'tb_orders.sko_key', '=', 'tb_faws.sko_key')
            ->where('tb_orders.sko_key', 'like', $param)
            ->limit(1)
            ->get()->first();

        if (empty($data->alamat_instansi)) {
            $alamat = $data->alamat;
        } else {
            $alamat = $data->alamat_instansi;
        }

        $spesifikasi = $data->lp_panjang . 'x' . $data->lp_lebar . ', ' . $data->jenis_bahan . ', ' . $data->jenis_kertas;
        $nama_pic = $data->name;

        $templateProcessor = new TemplateProcessor(storage_path('app/public/template/offering_letter/ol.docx'));
        $templateProcessor->setValue('tgl_penawaran', date('d F Y'));
        $templateProcessor->setValue('nama_instansi', htmlspecialchars($data->nama_instansi));
        $templateProcessor->setValue('nama_customer', $data->nama);
        $templateProcessor->setValue('no_hp', $data->no_hp);
        $templateProcessor->setValue('alamat', htmlspecialchars($alamat));
        $templateProcessor->setValue('tipe_produk', htmlspecialchars($data->tipe_produk));
        $templateProcessor->setValue('spesifikasi', $spesifikasi);
        $templateProcessor->setValue('harga_produk', number_format($data->harga_produk, 0, '', ','));
        $templateProcessor->setValue('jumlah_produk', $data->jumlah_produk);
        $templateProcessor->setValue('total_harga', number_format($data->total_harga, 0, '', ','));

        $datemark = date('dmy');
        $filename = 'Surat Penawaran ' . $data->nama . ' ' . $datemark . '.docx';
        $templateProcessor->saveAs($filename);

        \PhpOffice\PhpWord\Settings::setOutputEscapingEnabled(true);
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filename));
        ob_clean();
        flush();
        readfile($filename);
        unlink($filename);
        exit;
    }

    public function price_null(Request $request)
    {
        $pic = User::all();

        return view('pages.crm.price_null', compact('pic'));
    }

    public function table_price_null()
    {
        $query = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_produksis', 'tb_orders.id_order', '=', 'tb_produksis.id_produksi')
            ->select(['id_order', 'kode_kustomer', 'id_pic', 'total_harga', 'tgl_order', 'tb_customers.grading as grading', 'waktu_kontak', 'name', 'nama', 'status_deal', 'status_order'])
            ->where(function ($query) {
                $query->where('status_deal', '!=', 'Lost');
                $query->where('total_harga', '<', '1');
                $query->Where('status_order', '=', 'FU Emas');
                
                if (Auth::user()->roles === 'SALES') {
                    $query->where('tb_orders.id_pic', Auth::user()->id);
                }
            })
            ->where(function ($query) {
                $fromDate = request('waktu_kontak_from_date');
                $toDate = request('waktu_kontak_to_date');
                if ($fromDate) {
                    $query->where('waktu_kontak', '>=', $fromDate);
                }
                if ($toDate) {
                    $query->where('waktu_kontak', '<=', $toDate);
                }
            });

        return DataTables::of($query)
            ->editColumn('waktu_kontak', function ($row) {
                return $row->waktu_kontak ? date('d M y H:i', strtotime($row->waktu_kontak)) : '';
            })
            ->editColumn('pic', function ($row) {
                return $row->name ? $row->name : '';
            })
            ->editColumn('nama', function ($row) {
                return '<a href="' . route('crm.detail_customer', $row->kode_kustomer) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->nama . '</a>';
            })
            ->editColumn('grading', function ($row) {
                if ($row->grading == "Reguler") {
                    return '<div class="badge fw-bold" style="background-color: #CD7F32;">' . $row->grading . '</div>';
                } elseif ($row->grading == "Premium") {
                    return '<div class="badge fw-bold" style="background-color: #C0C0C0;">' . $row->grading . '</div>';
                } else {
                    return '<div class="badge fw-bold" style="background-color: #FFD700;">' . $row->grading . '</div>';
                }
            })
            ->editColumn('status_order', function ($row) {
                return $row->status_order ? '<div class="badge badge-light">' . $row->status_order . '</div>' : '';
            })
            ->rawColumns(['grading', 'status_order', 'nama', 'id_order'])
            ->make(true);
    }
    public function prioritas(Request $request)
    {
        $pic = User::all();

        return view('pages.crm.prioritas', compact('pic'));
    }

    public function table_prioritas()
    {
        $query = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select(['id_order', 'kode_kustomer', 'total_harga', 'tgl_order', 'waktu_kontak', 'users.name', 'tb_customers.grading as grading', 'nama', 'status_deal', 'status_order'])
            ->whereIn('status_order', ['Ruby', 'Diamond'])
            ->where(function ($query) {
                if (Auth::user()->roles === 'SALES') {
                    $query->where('tb_orders.id_pic', Auth::user()->id);
                }
            })
            ->where(function ($query) {
                $fromDate = request('waktu_kontak_from_date');
                $toDate = request('waktu_kontak_to_date');
                if ($fromDate) {
                    $query->where('waktu_kontak', '>=', $fromDate);
                }
                if ($toDate) {
                    $query->where('waktu_kontak', '<=', $toDate);
                }
            });

        return DataTables::of($query)
            ->editColumn('waktu_kontak', function ($row) {
                return $row->waktu_kontak ? date('d M y H:i', strtotime($row->waktu_kontak)) : '';
            })

            ->editColumn('pic', function ($row) {
                return $row->name ? $row->name : '';
            })

            ->editColumn('nama', function ($row) {
                return '<a href="' . route('crm.detail_customer', $row->kode_kustomer) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->nama . '</a>';
            })

            ->editColumn('total_harga', function ($row) {
                return $row->total_harga ? number_format($row->total_harga, 0, '', ',') : '';
            })
            ->editColumn('grading', function ($row) {
                if ($row->grading == "Reguler") {
                    return '<div class="badge fw-bold" style="background-color: #CD7F32;">' . $row->grading . '</div>';
                } elseif ($row->grading == "Premium") {
                    return '<div class="badge fw-bold" style="background-color: #C0C0C0;">' . $row->grading . '</div>';
                } else {
                    return '<div class="badge fw-bold" style="background-color: #FFD700;">' . $row->grading . '</div>';
                }
            })
            ->editColumn('status_order', function ($row) {
                return $row->status_order ? '<div class="badge badge-light">' . $row->status_order . '</div>' : '';
            })
            ->rawColumns(['waktu_kontak', 'pic', 'nama', 'total_harga', 'grading', 'status_order'])
            ->make(true);
    }

    public function detail_main_order(Request $request, $param)
    {
        $param_decrypted = Crypt::decryptString($param);

        $data = Order::where('order_key', '=', $param_decrypted);

        return view('pages.crm.main_order_detail', compact('data', 'param'));
    }

    public function table_main_order($param)
    {
        $order_key = Crypt::decryptString($param);
        $query = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_Key', '=', 'tb_kode_orders.order_key')
            ->select(['tb_orders.id_order', 'tgl_order', 'kode_order', 'sko', 'tgl_jatuh_tempo', 'status_deal', 'status_order', 'total_harga'])
            ->where('tb_orders.order_key', '=', $order_key)
            ->orderBy('tgl_order', 'DESC');

        return DataTables::of($query)
            ->editColumn('sko', function ($row) {
                $param = Crypt::encryptString($row->id_order);
                return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->sko . '</a>';
            })
            ->rawColumns(['sko'])
            ->make(true);
    }

    public function get_jenis_kertas(Request $request)
    {

        $input = $request->all();

        if (!empty($input['term'])) {
            $data = JenisKertas::select(['id_jenis_kertas', 'jenis_kertas'])
                ->where('jenis_kertas', 'LIKE', "%{$input['term']}%")->get();
        } else {
            $data = JenisKertas::select(['id_jenis_kertas', 'jenis_kertas'])->get();
        }

        $list_jenis_kertas = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                $list_jenis_kertas[] = array(
                    "id_jenis_kertas" => $row->id_jenis_kertas,
                    "jenis_kertas" => $row->jenis_kertas
                );
            }
        }
        return Response::json($list_jenis_kertas, 200);
    }

    public function get_kertas_plano(Request $request)
    {

        $input = $request->all();

        if (!empty($input['term'])) {
            $data = KertasPlano::select(['id_kertas_plano', 'kertas_plano'])
                ->where('kertas_plano', 'LIKE', "%{$input['term']}%")->get();
        } else {
            $data = KertasPlano::select(['id_kertas_plano', 'kertas_plano'])->get();
        }

        $paper_price =array();

        if (!empty($input['id_jenis_kertas'])) {
            $paper_price = PaperPrice::where('id_jenis_kertas',$input['id_jenis_kertas'])->pluck('id_kertas_plano')->toArray();
        }

        $list_kertas_plano = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                if(count($paper_price) > 0){
                    if (in_array($row->id_kertas_plano, $paper_price)) {
                        $list_kertas_plano[] = array(
                            "id_kertas_plano" => $row->id_kertas_plano,
                            "kertas_plano" => $row->kertas_plano
                        );
                    }
                }else{
                    $list_kertas_plano[] = array(
                        "id_kertas_plano" => $row->id_kertas_plano,
                        "kertas_plano" => $row->kertas_plano
                    );
                }
            }
        }
        return Response::json($list_kertas_plano, 200);
    }

    public function get_gramasi(Request $request)
    {

        $input = $request->all();

        if (!empty($input['term'])) {
            $data = Gramasi::select(['id_gramasi', 'gramasi'])
                ->where('gramasi', 'LIKE', "%{$input['term']}%")->get();
        } else {
            $data = Gramasi::select(['id_gramasi', 'gramasi'])->get();
        }

        $paper_price =array();

        if (!empty($input['id_jenis_kertas']) && !empty($input['id_kertas_plano'])) {
            $paper_price = PaperPrice::where('id_jenis_kertas',$input['id_jenis_kertas'])
            ->where('id_kertas_plano',$input['id_kertas_plano'])
            ->pluck('id_gramasi')->toArray();
        }

        $list_gramasi = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                if(count($paper_price) > 0){
                    if (in_array($row->id_gramasi, $paper_price)) {
                        $list_gramasi[] = array(
                            "id_gramasi" => $row->id_gramasi,
                            "gramasi" => $row->gramasi
                        );
                    }
                }else{
                    $list_gramasi[] = array(
                        "id_gramasi" => $row->id_gramasi,
                        "gramasi" => (string)$row->gramasi
                    );
                }
            }
        }
        
        return Response::json($list_gramasi, 200);
    }

    public function get_bahan(Request $request)
    {
        $input = $request->all();

        if (!empty($input['term'])) {
            $data = Bahan::select(['id_bahans','bahan'])
                ->where('bahan', 'LIKE', "%{$input['term']}%")->get();
        } else {
            $data = Bahan::select(['id_bahans','bahan'])->get();
        }

        $list_bahan = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                $list_bahan[] = array(
                    "id_bahans" => $row->id_bahans,
                    "bahan" => $row->bahan
                );
            }
        }
        return Response::json($list_bahan, 200);
    }

    public function get_mesin(Request $request)
    {
        $input = $request->all();

        if (!empty($input['term'])) {
            $data = Mesin::select(['id_mesin','mesin'])
                ->where('mesin', 'LIKE', "%{$input['term']}%")->get();
        } else {
            $data = Mesin::select(['id_mesin', 'mesin'])->get();
        } 

        $mesin_price =array();

        if (!empty($input['color'])) {
            $mesin_price = MesinPrice::where('type_color',$input['color'])
            ->pluck('id_mesin')->toArray();
        }

        $list_mesin = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                if(count($mesin_price) > 0){
                    if (in_array($row->id_mesin, $mesin_price)) {
                        $list_mesin[] = array(
                            "id_mesin" => $row->id_mesin,
                            "mesin" => $row->mesin
                        );
                    }
                }else{
                    $list_mesin[] = array(
                        "id_mesin" => $row->id_mesin,
                        "mesin" => $row->mesin
                    );
                }
            }
        }
        return Response::json($list_mesin, 200);
    }

    public function get_jenis_ongkos(Request $request)
    {

        $input = $request->all();

        if (!empty($input['term'])) {
            $data = JenisOngkos::select(['id_jenis_ongkos', 'jenis_ongkos'])
                ->where('jenis_ongkos', 'LIKE', "%{$input['term']}%")->get();
        } else {
            $data = JenisOngkos::select(['id_jenis_ongkos', 'jenis_ongkos'])->get();
        }

        $list_jenis_ongkos = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                $list_jenis_ongkos[] = array(
                    "id_jenis_ongkos" => $row->id_jenis_ongkos,
                    "jenis_ongkos" => $row->jenis_ongkos
                );
            }
        }
        return Response::json($list_jenis_ongkos, 200);
    }

    public function get_tools(Request $request)
    {

        $input = $request->all();

        if (!empty($input['term'])) {
            $data = Tools::select(['id_tools', 'tools'])
                ->where('tools', 'LIKE', "%{$input['term']}%")->get();
        } else {
            $data = Tools::select(['id_tools', 'tools'])->get();
        }

        $list_tools = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                $list_tools[] = array(
                    "id_tools" => $row->id_tools,
                    "tools" => $row->tools
                );
            }
        }
        return Response::json($list_tools, 200);
    } 

    public function get_other_price(Request $request)
    {

        $input = $request->all();

        if (!empty($input['term'])) {
            $keyword = $input['term'];
            $data = OtherPrice::with(['tb_jenis_ongkos','tb_tools'])
                ->whereHas('tb_tools', function($q) use ($keyword){
                    $q->where('tools', 'LIKE', "%".$keyword."%");
                })->get();
        } else {
            $data = OtherPrice::with(['tb_jenis_ongkos','tb_tools'])->get();
        }

        $list_tools = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                $list_tools[] = array(
                    "id_other_price" => $row->id_other_price,
                    "name" => $row->tb_tools->tools.'-'.$row->tb_jenis_ongkos->jenis_ongkos
                );
            }
        }
        return Response::json($list_tools, 200);
    } 

    public function get_province(Request $request)
    {
        $input = $request->all();

        if (!empty($input['q'])) {
            $data = Province::select(['id','name'])
                ->where('name', 'LIKE', "%{$input['q']}%")->get();
        } else {
            $data = Province::select(['id','name'])->get();
        }

        $list_province = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                $list_province[] = array(
                    "id" => $row->id,
                    "name" => $row->name
                );
            }
        }
        return Response::json($list_province, 200);
    }

    public function get_district(Request $request)
    {
        $input = $request->all();
        
        $province_id = request()->has('province_id') ? request()->input('province_id') : null;

        $data = District::select(['id','name']);
        if (!empty($province_id)) {
            $data->where('province_id', $province_id);
        }
        if (!empty($input['q'])) {
            $data->where('name', 'LIKE', "%{$input['q']}%");
        }

        $data = $data->get();

        $list_district = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                $list_district[] = array(
                    "id" => $row->id,
                    "name" => $row->name
                );
            }
        }
        return Response::json($list_district, 200);
    }

    public function get_sub_district(Request $request)
    {
        $input = $request->all();

        $district_id = request()->has('district_id') ? request()->input('district_id') : null;
        $data = Subdistrict::select(['id','name']);
        if (!empty($district_id)) {
            $data->where('district_id', $district_id);
        }
        if (!empty($input['q'])) {
            $data->where('name', 'LIKE', "%{$input['q']}%");
        }

        $data = $data->get();

        $list_subdistrict = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                $list_subdistrict[] = array(
                    "id" => $row->id,
                    "name" => $row->name
                );
            }
        }
        return Response::json($list_subdistrict, 200);
    }

    public function get_village(Request $request)
    {
        $input = $request->all();
        $sub_district_id = request()->has('sub_district_id') ? request()->input('sub_district_id') : null;
        $data = Village::select(['id','name']);
        if (!empty($sub_district_id)) {
            $data->where('sub_district_id', $sub_district_id);
        }
        if (!empty($input['q'])) {
            $data->where('name', 'LIKE', "%{$input['q']}%");
        }

        $data = $data->get();

        $list_village = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                $list_village[] = array(
                    "id" => $row->id,
                    "name" => $row->name
                );
            }
        }
        return Response::json($list_village, 200);
    }

    public function show_cust_company($id)
    {
        $customer = Customer::with('company')->where('id_customer', $id)->first();
        
        return Response::json([$customer], 200);
    }

    public function edit_cust_comp(Request $request)
    {
        $company = Company::find($request->company_id_edit);
        $query = Customer::where(['id_customer' => $request->id_cust_comp])->update(
            [
                'company_id' => $request->company_id_edit,
                'nama_instansi' => $company->name,
                'jenis_usaha' => $company->business_sector,
                'alamat_instansi' => $company->address,
                'npwp' => $company->npwp
            ]
        );

        return Response::json($query, 200);
    }

    public function getOrder(Request $request)
    {
        $input = $request->all();

        $query = Order::with('invoice_details')
            ->select(['id_order', 'sko'])
            ->where('id_customer', $input['customer_id'])
            ->where('status_deal', 'DEAL');
        if (request()->has('invoice')) {
            $query->whereDoesntHave('invoice_details', function($q) {
                    $q->whereColumn('kode_order', 'sko');
                });
        }

        if (!empty($input['q'])) {
            $query->where('sko', 'LIKE', "%{$input['q']}%");
        }

        $data = $query->limit(10)->get();

        $list_order = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                $list_order[] = array(
                    "id" => $row->sko,
                    "name" => $row->sko
                );
            }
        }
        return Response::json($list_order, 200);
    }
}
