<?php

namespace App\Http\Controllers\Price;

use App\Models\OtherPrice;
use App\Models\JenisKertas;
use App\Models\KertasPlano;
use App\Models\Gramasi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use App\Http\Controllers\Controller;
use App\Models\JenisOngkos;
use App\Models\Tools;
use App\Models\Bahan;

class OtherPriceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (request()->ajax()) {
            $query = OtherPrice::with('tb_jenis_ongkos', 'tb_tools')->get();
            return DataTables::of($query)
                ->editColumn('jenis_ongkos', function ($item) {
                    return $item->tb_jenis_ongkos->jenis_ongkos;
                })
                ->editColumn('tools', function ($item) {
                    return $item->tb_tools->tools;
                })
                ->editColumn('price_per_item', function ($item) {
                    return $item->price_per_item;
                })
                ->editColumn('price_minimum', function ($item) {
                    return 'Rp. ' . number_format($item->price_minimum, 0, '', ',');
                })
                ->addColumn('action', function ($item) {
                    return '
                    <div class="row">
                        <div class="col-md-6">
                            <a class="btn btn-sm btn-primary w-100 text-white edit-paper-price" data-id="' . $item->id_other_price . '" href="javascript:void(0)">Edit</a>
                        </div>
                        <div class="col-md-6">
                            <a href="javascript:void(0)" class="btn btn-sm btn-danger w-100 text-white delete-link" data-id-destroy="' . $item->id_other_price . '" onclick="deleteConfirmation(' . $item->id_other_price . ')">Delete</a>
                        </div>
                    </div>
                    ';
                })
                ->rawColumns(['action', 'roles'])
                ->make();
        }
        $with['jenis_ongkos'] = JenisOngkos::get();
        $with['tools'] = Tools::get();
        $with['bahans'] = Bahan::get();
        return view('pages.superadmin.price.other.index', $with);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = OtherPrice::create([
            'id_jenis_ongkos' => $request->id_jenis_ongkos,
            'id_tools' => $request->id_tools,
            'id_bahans' => $request->id_bahans,
            'price_per_item' => $request->price_per_item,
            'price_minimum' => $request->price_minimum,
            'quantity' => $request->quantity,
            'is_optional' => isset($request->is_optional)?1:0,
            'tipe_rumus' => $request->tipe_rumus
        ]);

        return Response::json($data, 200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $paper_price = OtherPrice::with(['tb_jenis_ongkos', 'tb_tools', 'tb_bahans'])->where('id_other_price', $id)->first();
        return response()->json($paper_price);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        if ($request) {
            $query = OtherPrice::updateOrCreate(['id_other_price' => $request->id_other_price], [
                'id_jenis_ongkos' => $request->id_jenis_ongkos_edit,
                'id_tools' => $request->id_tools_edit,
                'id_bahans' => $request->id_bahans_edit,
                'price_per_item' => $request->price_per_item_edit,
                'price_minimum' => $request->price_minimum_edit,
                'quantity' => $request->quantity_edit,
                'is_optional' => isset($request->is_optional_edit)?1:0,
                'tipe_rumus' => $request->tipe_rumus_edit
            ]);
        }

        return response()->json($query);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        OtherPrice::find($id)->delete($id);
        return response()->json([
            'success' => 'Record deleted successfully!'
        ]);
    }
}
