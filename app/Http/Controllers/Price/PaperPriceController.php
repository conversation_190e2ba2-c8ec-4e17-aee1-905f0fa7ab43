<?php

namespace App\Http\Controllers\Price;

use App\Models\PaperPrice;
use App\Models\JenisKertas;
use App\Models\KertasPlano;
use App\Models\Gramasi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Controllers\Controller;

class PaperPriceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(request()->ajax())
        {
            $query = PaperPrice::with(['tb_jenis_kertas','tb_kertas_planos','tb_gramasi'])->get();
            return DataTables::of($query)
                ->editColumn('jenis_kertas', function($item){
                    return $item->tb_jenis_kertas->jenis_kertas;
                })
                ->editColumn('kertas_plano', function($item){    
                    return $item->tb_kertas_planos->kertas_plano;
                })
                ->editColumn('gramasi', function($item){    
                    return $item->tb_gramasi->gramasi ?? '-';
                })
                ->editColumn('price', function($item){
                    return 'Rp. '.number_format($item->price, 0, '', ',');
                })
                ->addColumn('action', function($item){
                    return '
                    <div class="row">
                        <div class="col-md-6">
                            <a class="btn btn-sm btn-primary w-100 text-white edit-paper-price" data-id="'.$item->id_paper_price.'" href="javascript:void(0)">Edit</a>
                        </div>
                        <div class="col-md-6">
                            <a href="javascript:void(0)" class="btn btn-sm btn-danger w-100 text-white delete-link" data-id-destroy="'.$item->id_paper_price.'" onclick="deleteConfirmation('.$item->id_paper_price.')">Delete</a>
                        </div>
                    </div>
                    ';
                })
                ->rawColumns(['action','roles'])
                ->make();

        }
        $with['jenis_kertas'] = JenisKertas::get();
        $with['kertas_plano'] = KertasPlano::get();
        $with['gramasi'] = Gramasi::get();
        return view('pages.superadmin.price.paper.index', $with);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = PaperPrice::create([
            'id_jenis_kertas' => $request->id_jenis_kertas,
            'id_kertas_plano' => $request->id_kertas_plano,
            'id_gramasi' => $request->id_gramasi,
            'price' => $request->amount
        ]);

        return Response::json($data, 200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $paper_price = PaperPrice::with(['tb_jenis_kertas','tb_kertas_planos','tb_gramasi'])->where('id_paper_price',$id)->first();
        return response()->json($paper_price);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        if($request){
            $query = PaperPrice::updateOrCreate(['id_paper_price' => $request->id_paper_price],[
                'id_jenis_kertas' => $request->id_jenis_kertas_edit,
                'id_kertas_plano' => $request->id_kertas_plano_edit,
                'id_gramasi' => $request->id_gramasi_edit,
                'price' => $request->amount_edit
            ]);
        }

        return response()->json($query);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        PaperPrice::find($id)->delete($id);
        return response()->json([
            'success' => 'Record deleted successfully!'
        ]);
    }
}
