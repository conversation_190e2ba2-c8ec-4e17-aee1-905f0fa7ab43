<?php

namespace App\Http\Controllers\Price;

use App\Models\PaperPrice;
use App\Models\JenisKertas;
use App\Models\KertasPlano;
use App\Models\Gramasi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Controllers\Controller;
use App\Models\Mesin;
use App\Models\MesinPrice;

class CuttingPlatController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (request()->ajax()) {
            $query = MesinPrice::with('tb_mesin')->get();
            return DataTables::of($query)
                ->editColumn('mesin', function ($item) {
                    return $item->tb_mesin->mesin;
                })
                ->editColumn('type_color', function ($item) {
                    return $item->color_type();
                })
                ->editColumn('quantity', function ($item) {
                    return $item->quantity;
                })
                ->editColumn('price', function ($item) {
                    return 'Rp. ' . number_format($item->price, 0, '', ',');
                })
                ->addColumn('action', function ($item) {
                    return '
                    <div class="row">
                        <div class="col-md-6">
                            <a class="btn btn-sm btn-primary w-100 text-white edit-paper-price" data-id="' . $item->id_mesin_print_price . '" href="javascript:void(0)">Edit</a>
                        </div>
                        <div class="col-md-6">
                            <a href="javascript:void(0)" class="btn btn-sm btn-danger w-100 text-white delete-link" data-id-destroy="' . $item->id_mesin_print_price . '" onclick="deleteConfirmation(' . $item->id_mesin_print_price . ')">Delete</a>
                        </div>
                    </div>
                    ';
                })
                ->rawColumns(['action', 'roles'])
                ->make();
        }
        $with['mesin'] = Mesin::get();
        return view('pages.superadmin.price.cutting_plat.index', $with);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = MesinPrice::create([
            'id_mesin' => $request->id_mesin,
            'type_color' => $request->type_color,
            'quantity' => $request->quantity,
            'price' => $request->amount
        ]);

        return Response::json($data, 200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $mesin_price = MesinPrice::with(['tb_mesin'])->where('id_mesin_print_price', $id)->first();
        return response()->json($mesin_price);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        if ($request) {
            $query = MesinPrice::updateOrCreate(['id_mesin_print_price' => $request->id_mesin_print_price], [
                'id_mesin' => $request->id_mesin_edit,
                'type_color' => $request->type_color_edit,
                'quantity' => $request->quantity_edit,
                'price' => $request->amount_edit
            ]);
        }

        return response()->json($query);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        MesinPrice::find($id)->delete($id);
        return response()->json([
            'success' => 'Record deleted successfully!'
        ]);
    }
}
