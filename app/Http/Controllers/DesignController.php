<?php

namespace App\Http\Controllers;

use App\Models\Bahan;
use App\Models\TipeProduk;
use Illuminate\Http\Request;
use App\Models\KategoriLayout;
use App\Models\KertasPlano;
use App\Models\Layout;
use App\Models\OtherPrice;
use App\Models\DraftHitungan;
use App\Models\Gramasi;
use App\Models\Mesin;
use Harunc<PERSON>\LaravelIdGenerator\IdGenerator;
use Illuminate\Support\Facades\Response;
use PDO;

class DesignController extends Controller
{
    public function index(){
        $with['other_price'] = OtherPrice::with(['tb_jenis_ongkos','tb_tools','tb_bahans'])
        ->where('is_optional', true)->get();
        return view('pages.design.index', $with);
    }

    public function layout(){
        $with['kategori_layout'] = KategoriLayout::with('tb_layouts')->get();
        return view('pages.design.layout', $with);
    }

    public function get_layout(Request $request){
        $search = null;
        if(request()->has('search')){
            $search = $request->search;
        }
        $tipe_produk = TipeProduk::all();
        $layouts = Layout::leftJoin('tb_kategori_layouts','id_kategori','=','id_kategori_layout');
        if($search != null){
            $layouts->where('tb_layouts.nama_layout', 'LIKE', '%' . $search . '%');
        }
        $layouts = $layouts->paginate(4);

        return view('pages.design.get_layout', compact('tipe_produk','layouts'));
    }

    public function add_new_layout(){

        $kategori = KategoriLayout::all();
        return view('pages.design.add_new_layout', compact('kategori'));
    }

    public function edit_layout($id){
        $kategori = KategoriLayout::all();
        $layout = Layout::find($id);
        return view('pages.design.add_new_layout', compact('kategori','layout'));
    }

    public function store_layout(Request $request){
        $prefix = '#';
        $config = [
            'table' => 'tb_layouts',
            'field' => 'kode_layout',
            'length' => 5,
            'prefix' => $prefix
        ];

        $input = [];
        $kl = IdGenerator::generate($config);
        $kode_layout = $kl;


        if (request()->has('kode_layout')) {
            $kode_layout = $request->kode_layout;
        }

        $no_urut_layout = substr($kode_layout, 1);
        
        $gambar_produk = null;
        $gambar_layout = null;

        if($request->hasFile('path_gambar_produk')){
            $gambar_produk = $request->file('path_gambar_produk');
            $imageName = 'img-produk-' . $kode_layout.'.'.$gambar_produk->getClientOriginalExtension();
            $imageName = str_replace("#","",$imageName);
            $gambar_produk->move(public_path('designs'), $imageName);
            $gambar_produk = $imageName;
        }
        
        if($request->hasFile('path_gambar_layout')){
            $gambar_layout = $request->file('path_gambar_layout');
            $imageName = 'img-layout-' . $kode_layout.'.'.$gambar_layout->getClientOriginalExtension();
            $imageName = str_replace("#","",$imageName);
            $gambar_layout->move(public_path('designs'), $imageName);
            $gambar_layout = $imageName;
        }

        if($request->tambahan_sisi_kertas == ''){
            $tambahan_sisi_kertas_fix = null;
        }else{
            $tambahan_sisi_kertas_fix = implode(',', $request->tambahan_sisi_kertas);
        }

        $input = [
            'nama_layout' => $request->nama_layout,
            'id_kategori_layout' => $request->id_kategori_layout,
            'path_gambar_produk' => $gambar_produk,
            'path_gambar_layout' => $gambar_layout,
            'jenis_box' => $request->jenis_box,
            'dimensi_panjang' => $request->dimensi_panjang,
            'dimensi_lebar' => $request->dimensi_lebar,
            'dimensi_tinggi' => $request->dimensi_tinggi,
            'tambahan_sisi_kertas' => $tambahan_sisi_kertas_fix,
            'rumus_lebar_cetak_1_angka_1' => $request->rumus_lebar_cetak_1_angka_1,
            'rumus_lebar_cetak_1_parameter_1' => $request->rumus_lebar_cetak_1_parameter_1,
            'rumus_lebar_cetak_1_angka_2' => $request->rumus_lebar_cetak_1_angka_2,
            'rumus_lebar_cetak_1_parameter_2' => $request->rumus_lebar_cetak_1_parameter_2,
            'rumus_lebar_cetak_1_angka_3' => $request->rumus_lebar_cetak_1_angka_3,
            'rumus_lebar_cetak_1_parameter_3' => $request->rumus_lebar_cetak_1_parameter_3,
            'rumus_lebar_cetak_1_angka_4' => $request->rumus_lebar_cetak_1_angka_4,
            'rumus_lebar_cetak_1_parameter_4' => $request->rumus_lebar_cetak_1_parameter_4,
            'rumus_panjang_cetak_1_angka_1' => $request->rumus_panjang_cetak_1_angka_1,
            'rumus_panjang_cetak_1_parameter_1' => $request->rumus_panjang_cetak_1_parameter_1,
            'rumus_panjang_cetak_1_angka_2' => $request->rumus_panjang_cetak_1_angka_2,
            'rumus_panjang_cetak_1_parameter_2' => $request->rumus_panjang_cetak_1_parameter_2,
            'rumus_panjang_cetak_1_angka_3' => $request->rumus_panjang_cetak_1_angka_3,
            'rumus_panjang_cetak_1_parameter_3' => $request->rumus_panjang_cetak_1_parameter_3,
            'rumus_panjang_cetak_1_angka_4' => $request->rumus_panjang_cetak_1_angka_4,
            'rumus_panjang_cetak_1_parameter_4' => $request->rumus_panjang_cetak_1_parameter_4,
            'rumus_lebar_cetak_2_angka_1' => $request->rumus_lebar_cetak_2_angka_1,
            'rumus_lebar_cetak_2_parameter_1' => $request->rumus_lebar_cetak_2_parameter_1,
            'rumus_lebar_cetak_2_angka_2' => $request->rumus_lebar_cetak_2_angka_2,
            'rumus_lebar_cetak_2_parameter_2' => $request->rumus_lebar_cetak_2_parameter_2,
            'rumus_lebar_cetak_2_angka_3' => $request->rumus_lebar_cetak_2_angka_3,
            'rumus_lebar_cetak_2_parameter_3' => $request->rumus_lebar_cetak_2_parameter_3,
            'rumus_panjang_cetak_2_angka_1' => $request->rumus_panjang_cetak_2_angka_1,
            'rumus_panjang_cetak_2_parameter_1' => $request->rumus_panjang_cetak_2_parameter_1,
            'rumus_panjang_cetak_2_angka_2' => $request->rumus_panjang_cetak_2_angka_2,
            'rumus_panjang_cetak_2_parameter_2' => $request->rumus_panjang_cetak_2_parameter_2,
            'rumus_panjang_cetak_2_angka_3' => $request->rumus_panjang_cetak_2_angka_3,
            'rumus_panjang_cetak_2_parameter_3' => $request->rumus_panjang_cetak_2_parameter_3,
            'rumus_panjang_cetak_2_angka_4' => $request->rumus_panjang_cetak_2_angka_4,
            'rumus_panjang_cetak_2_parameter_4' => $request->rumus_panjang_cetak_2_parameter_4,
            'rumus_lebar_cetak_3_angka_1' => $request->rumus_lebar_cetak_3_angka_1,
            'rumus_lebar_cetak_3_parameter_1' => $request->rumus_lebar_cetak_3_parameter_1,
            'rumus_lebar_cetak_3_angka_2' => $request->rumus_lebar_cetak_3_angka_2,
            'rumus_lebar_cetak_3_parameter_2' => $request->rumus_lebar_cetak_3_parameter_2,
            'rumus_lebar_cetak_3_angka_3' => $request->rumus_lebar_cetak_3_angka_3,
            'rumus_lebar_cetak_3_parameter_3' => $request->rumus_lebar_cetak_3_parameter_3,
            'rumus_lebar_cetak_3_angka_4' => $request->rumus_lebar_cetak_3_angka_4,
            'rumus_lebar_cetak_3_parameter_4' => $request->rumus_lebar_cetak_3_parameter_4,
            'rumus_panjang_cetak_3_angka_1' => $request->rumus_panjang_cetak_3_angka_1,
            'rumus_panjang_cetak_3_parameter_1' => $request->rumus_panjang_cetak_3_parameter_1,
            'rumus_panjang_cetak_3_angka_2' => $request->rumus_panjang_cetak_3_angka_2,
            'rumus_panjang_cetak_3_parameter_2' => $request->rumus_panjang_cetak_3_parameter_2,
            'rumus_panjang_cetak_3_angka_3' => $request->rumus_panjang_cetak_3_angka_3,
            'rumus_panjang_cetak_3_parameter_3' => $request->rumus_panjang_cetak_3_parameter_3,
            'rumus_panjang_cetak_3_angka_4' => $request->rumus_panjang_cetak_3_angka_4,
            'rumus_panjang_cetak_3_parameter_4' => $request->rumus_panjang_cetak_3_parameter_4,
            'tipe_rumus' => $request->tipe_rumus,

        ];

        if (request()->has('id_layout')) {
            $input['kode_layout'] = $kode_layout;
            $data = Layout::where('id_layout', $request->id_layout)->update($input);
        } else{
            $input['kode_layout'] = $kode_layout;
            $data = Layout::create($input);
        }

        return redirect()->route('design')->with('message', [
            'type' => 'Success',
            'text' => 'Layout Added successfully',
        ]);
        
    }

    public function store_kategori_layout(Request $request){
        $data = KategoriLayout::create(['nama_kategori' => $request->nama_kategori, 'keterangan' => $request->keterangan]);

        return Response::json($data, 200);
    }

    public function list_layout(Request $request){
        $search = null;
        if(request()->has('search')){
            $search = $request->search;
        }
        $layouts = Layout::where('id_kategori_layout', $request->id_kategori);
        if($search != null){
            $layouts->where('nama_layout', 'LIKE', '%' . $search . '%');
        }
        $layouts = $layouts->paginate(4);
        $with['layouts'] = $layouts;
        return view('pages.design.list_layout', $with);
    }

    public function counting_cutting_paper(Request $request){
        $data = [];
        $luas_permukaan_cetak_panjang = 0;
        $luas_permukaan_cetak_lebar = 0;

        $layouts = Layout::find($request->id_layout);
        $bahans = Bahan::find($request->id_bahans);
        
        if (!request()->has('is_force')) {
            $input = [];
    
            if (request()->has('s_panjang')) {
                $input['p'] = (float)$request->s_panjang;
            }
    
            if (request()->has('s_lebar')) {
                $input['l'] = (float)$request->s_lebar;
            }
    
            if (request()->has('s_tinggi')) {
                $input['t'] = (float)$request->s_tinggi;
            }
    
            if (request()->has('s_lem')) {
                $input['lem'] = (float)$request->s_lem;
            }
    
            if (request()->has('s_klip')) {
                $input['klip'] = (float)$request->s_klip;
            }
    
            if (request()->has('s_sayap')) {
                $input['s'] = (float)$request->s_sayap;
            }
    
            if (request()->has('s_t2')) {
                $input['t2'] = (float)$request->s_t2;
            }
            
            if ($layouts->tipe_rumus == 2) {
                $p_cetak         = $input['p']*2+$input['l']*2+4;
                $l_cetak         = $input['t']+(0.5*$input['l'])*2;
                $p_kemasan        = ($input['p']*2+$input['l']*2+4)+3;
                $l_kemasan        = ($input['t']+(0.5*$input['l'])*2)+3;

                if (request()->has('s_sayap')) {
                    $p_cetak         =  $input['p']+($input['t']*4)+(2*$input['s'])+(2*$input['t2']);
                    $l_cetak         = ($input['t']*3)+($input['l']*2);
                    $p_kemasan        = $p_cetak + 2;
                    $l_kemasan        = $l_cetak + 2;
                }
                if ($request->tipe==1) {
                    $result = [
                        'p_cetak'          => $p_cetak,
                        'l_cetak'          => $l_cetak,
                        'p_kemasan'        => $p_kemasan,
                        'l_kemasan'        => $l_kemasan,
                        'jumlah_potong'    => 0,
                        'percentage_waste' => 0
                    ];

                } else {
                    //get ukuran kertas
                    $p_plano = 0;
                    $l_plano = 0;
                    
                    $kertas_plano = KertasPlano::select('kertas_plano')->find($request->id_kertas_plano);
                    $kertas_plano = explode('/',$kertas_plano->kertas_plano);
                    $p_plano = (float)$kertas_plano[0];
                    $l_plano = (float)$kertas_plano[1];
                    

                    $all_combi = [];

                    $all_combi['combi_1'] = (new \Lib\softbox($p_cetak,$l_cetak,$p_plano,$l_plano))->combi_1($data);
                    $all_combi['combi_2'] = (new \Lib\softbox($p_cetak,$l_cetak,$p_plano,$l_plano))->combi_2($data);
                    $all_combi['combi_3'] = (new \Lib\softbox($p_cetak,$l_cetak,$p_plano,$l_plano))->combi_3($data);
                    
                    $result = collect($all_combi)->sortBy('jumlah_potong', SORT_REGULAR, true)->first();
                }
                return response()->json($result,200);
            }
            
            if ($layouts->tipe_rumus == 1 && $bahans->bahan != 'Hardbox') {
                //get luas permukaan cetak lebar kemasan
                $rumus_lebar_cetak_1 = rumus_lebar_cetak_1($request->id_layout, $input);
                $rumus_lebar_cetak_2 = rumus_lebar_cetak_2($request->id_layout, $input);
                $rumus_lebar_cetak_3 = rumus_lebar_cetak_3($request->id_layout, $input);
                $rumus_lebar_cetak_4 = rumus_lebar_cetak_4($request->id_layout, $input);
                
                $luas_permukaan_cetak_lebar = $rumus_lebar_cetak_1+$rumus_lebar_cetak_2+$rumus_lebar_cetak_3+$rumus_lebar_cetak_4;
                
                //get luas permukaan cetak panjang kemasan
                $rumus_panjang_cetak_1 = rumus_panjang_cetak_1($request->id_layout, $input);
                $rumus_panjang_cetak_2 = rumus_panjang_cetak_2($request->id_layout, $input);
                $rumus_panjang_cetak_3 = rumus_panjang_cetak_3($request->id_layout, $input);
                $rumus_panjang_cetak_4 = rumus_panjang_cetak_4($request->id_layout, $input);
                
                $luas_permukaan_cetak_panjang = $rumus_panjang_cetak_1+$rumus_panjang_cetak_2+$rumus_panjang_cetak_3+$rumus_panjang_cetak_4;
            }

            if ($request->tipe == 3) {
                $input['id_layout'] = $request->id_layout;
                $input['id_kertas_plano'] = $request->id_kertas_plano;
                $data = $this->cover_hardbox_calculating($input);
            } else if ($request->tipe == 2 && $layouts->tipe_rumus == 2) {
                $input['id_layout'] = $request->id_layout;
                $input['id_kertas_plano'] = $request->id_kertas_plano;
                $data = $this->cover_corrugated_calculating($input);
            }
        } else {
            $data = $request->all();
            if ($request->tipe == 1 && $bahans->bahan == 'Corrugated Box') {
                $result = [
                    "p_cetak" => $request->panjang_cetak,
                    "l_cetak" => $request->lebar_cetak,
                    "p_kemasan" => $request->panjang_kemasan,
                    "l_kemasan" => $request->lebar_kemasan,    
                    "jumlah_potong" => 0,
                    "percentage_waste" => 0
                ];
                return response()->json($result,200);
            }
        }

        if ($bahans->bahan == 'Hardbox'){
            if (!request()->has('is_force')) {
                $data = $this->hardbox_calculating($input);
            } else {
                $data = $request->all();
            }
        }

        if ($bahans->bahan != 'Corrugated Box') {
            //get ukuran kertas
            $p_plano = 0;
            $l_plano = 0;
            
            $kertas_plano = KertasPlano::select('kertas_plano')->find($request->id_kertas_plano);
            $kertas_plano = explode('/',$kertas_plano->kertas_plano);
            $p_plano = (float)$kertas_plano[0];
            $l_plano = (float)$kertas_plano[1];
            

            $all_combi = [];

            $all_combi['combi_1'] = (new \Lib\softbox($luas_permukaan_cetak_panjang,$luas_permukaan_cetak_lebar,$p_plano,$l_plano))->combi_1($data);
            $all_combi['combi_2'] = (new \Lib\softbox($luas_permukaan_cetak_panjang,$luas_permukaan_cetak_lebar,$p_plano,$l_plano))->combi_2($data);
            $all_combi['combi_3'] = (new \Lib\softbox($luas_permukaan_cetak_panjang,$luas_permukaan_cetak_lebar,$p_plano,$l_plano))->combi_3($data);
            
            $result = collect($all_combi)->sortBy('jumlah_potong', SORT_REGULAR, true)->first();
        } else {
            $result = [
                'p_cetak'          => $luas_permukaan_cetak_panjang,
                'l_cetak'          => $luas_permukaan_cetak_lebar,
                'p_kemasan'        => $luas_permukaan_cetak_panjang + 2,
                'l_kemasan'        => $luas_permukaan_cetak_lebar + 2,
                'jumlah_potong'    => 0,
                'percentage_waste' => 0
            ];
        }
        
        return response()->json($result,200);
    }

    public function cover_corrugated_calculating($input){
        $p_cetak         = $input['p']*2+$input['l']*2+4;
        $l_cetak         = $input['t']+(0.5*$input['l'])*2;
        $p_kemasan        = ($input['p']*2+$input['l']*2+4)+3;
        $l_kemasan        = ($input['t']+(0.5*$input['l'])*2)+3;

        if (request()->has('s_sayap')) {
            $p_cetak         =  $input['p']+($input['t']*4)+(2*$input['s'])+(2*$input['t2']);
            $l_cetak         = ($input['t']*3)+($input['l']*2);
            $p_kemasan        = $p_cetak + 2;
            $l_kemasan        = $l_cetak + 2;
        }

        $data = [
            'panjang_cetak'          => floor($p_cetak),
            'lebar_cetak'          => floor($l_cetak),
            'panjang_kemasan'        => floor($p_kemasan),
            'lebar_kemasan'        => floor($l_kemasan)
        ];

        return $data;
    }

    public function hardbox_calculating($input, $type = false){
        $panjang_cetak = floor($input['p']+$input['t']*2);
        $lebar_cetak = floor($input['l']+$input['t']*2);

        $luas_permukaan_cetak_panjang = $panjang_cetak + 1;
        $luas_permukaan_cetak_lebar = $lebar_cetak + 1;

        $data = [
            'panjang_cetak' => $panjang_cetak,
            'lebar_cetak' => $lebar_cetak,
            'panjang_kemasan' => $luas_permukaan_cetak_panjang,
            'lebar_kemasan' => $luas_permukaan_cetak_lebar,
        ];

        return $data;
    }

    public function cover_hardbox_calculating($input, $type = false){
        $panjang_cetak_bawah = floor($input['p']+$input['t']*2);
        $lebar_cetak_bawah = floor($input['l']+$input['t']*2);

        $luas_permukaan_cetak_panjang_bawah = $panjang_cetak_bawah + 1 + 5;
        $luas_permukaan_cetak_lebar_bawah = $lebar_cetak_bawah  + 1 + 5;

        $bawah = [
            'panjang_cetak' => $panjang_cetak_bawah,
            'lebar_cetak' => $lebar_cetak_bawah,
            'panjang_kemasan' => $luas_permukaan_cetak_panjang_bawah,
            'lebar_kemasan' => $luas_permukaan_cetak_lebar_bawah,
        ];
        
        $layouts = Layout::find($input['id_layout']);
        
        //hardbox magnet
        if ($layouts->tipe_rumus == 3) {
            $panjang_cetak = $input['p']+1;
            $lebar_cetak = ($input['l']*2)+($input['t']*2)+1;
        } else if ($layouts->jenis_box == 2) { //hardbox slide
            $input['p'] = $input['p']+1;
            $input['l'] = $input['l']+1;
            $input['t'] = $input['t']+1;

            $panjang_cetak = ($input['p']*2)+($input['t']*2);
            $lebar_cetak = $input['l']+$input['t'];
        } else {
            $input['p'] = $input['p']+1;
            $input['l'] = $input['l']+1;

            $panjang_cetak = $input['p']+$input['t']*2;
            $lebar_cetak = $input['l']+$input['t']*2;
        }

        $luas_permukaan_cetak_panjang = $panjang_cetak + 1 + 5;
        $luas_permukaan_cetak_lebar = $lebar_cetak + 1 + 5;

        $atas = [
            'panjang_cetak' => $panjang_cetak,
            'lebar_cetak' => $lebar_cetak,
            'panjang_kemasan' => $luas_permukaan_cetak_panjang,
            'lebar_kemasan' => $luas_permukaan_cetak_lebar,
        ];
        
        $data=[];

        $p_kemasan = $bawah['panjang_kemasan']+$atas['panjang_kemasan']+0.5+2;
        $l_kemasan = $bawah['lebar_kemasan'] > $atas['lebar_kemasan'] ? $bawah['lebar_kemasan'] + 2 : $atas['lebar_kemasan'] + 2;

        $data['panjang_cetak'] = $p_kemasan - 2;
        $data['lebar_cetak'] = $l_kemasan - 2;
        $data['panjang_kemasan'] = $p_kemasan;
        $data['lebar_kemasan'] = $l_kemasan;

        return $data;
    }

    public function call_layout(Request $request){
        $result = Layout::find($request->id);
        return response()->json($result,200);
    }

    public function calculating_price(Request $request){
        $input = $request->all();
        
        $bahans = Bahan::find($input['id_bahans']);
        
        $kebutuhan_kertas = kebutuhan_kertas($input);
        
        $fix_price = fix_price($kebutuhan_kertas,$input);
        
        $print_price = 0;
        if (!in_array($bahans->bahan,['Corrugated Box', 'Hardbox'])){
            $print_price = print_price($kebutuhan_kertas,$input);
        }
        
        $additional_cost = null;
        if (request()->has('other_price_ids')) {
            $additional_cost = additional_cost($kebutuhan_kertas, $input);
        }
        
        if (in_array($bahans->bahan,['Hardbox'])){
            unset($additional_cost['kebutuhan_sekat']);
        }
        $total_price = total_price($fix_price,$print_price,$additional_cost,$input); 
        
        $result = [
            "cetak" => $print_price,
            "kebutuhan_kertas" => $kebutuhan_kertas,
            "additional_cost" => $additional_cost,
            "harga_modal" => $total_price['harga_modal'],
            "harga_jual" => collect($total_price['harga_jual'])->sortByDesc(function ($value, $key) {
                return $key;
            })->first(),
            "prices" => $fix_price,
            "quantity" => $input['quantity']

        ];

        return response()->json($result,200);
    }

    public function save_draft(Request $request) 
    {
        $additional_cost = null;
        if (request()->has('additional_cost')) {
            $additional_cost = json_decode($request->additional_cost, true);
            $additional_cost['cetak'] = $request->print_cost;
        }
        $input = [
            'code' => generateCodeDraft(),
            'id_layout' => $request->id_layout,
            'id_bahan' => $request->id_bahans,
            'id_jenis_kertas' => $request->id_jenis_kertas,
            'id_kertas_plano' => $request->id_kertas_plano,
            'id_gramasi' => $request->id_gramasi,
            'dimensi_p' => $request->dimensi_p,
            'dimensi_l' => $request->dimensi_l,
            'dimensi_t' => $request->dimensi_t,
            'luas_permukaan' => $request->panjang_cetak.' x '.$request->lebar_cetak,
            'luas_kertas' => $request->panjang_kemasan.' x '.$request->lebar_kemasan,
            'detail' => null,
            'kebutuhan_kertas' => $request->kebutuhan_kertas,
            'additional_cost' => json_encode($additional_cost),
            'description' => $request->description,
            'quantity' => $request->quantity,
            'harga_modal' => $request->harga_modal,
            'type' => $request->tipe,
            'jumlah_potong' => $request->jumlah_kertas_potong,
            'waste' => $request->wasting,
            'status' => 1
        ];
        $draft = DraftHitungan::create($input);

        if ($draft) {
            return response()->json(["status_code" => 200, "message" => "success"],200);
        } 

    }

    public function getBoxAtas(){
        return view('pages.design.get_box_atas');
    }

    public function getHitungBoxAtas(Request $request){
        //get ukuran kertas
        $luas_permukaan_cetak_panjang = 0;
        $luas_permukaan_cetak_lebar = 0;
        $p_plano = 0;
        $l_plano = 0;
        
        if (!request()->has('is_force')) {
            $input = $request->all();

            $layouts = Layout::find($request->id_layout);
            
            //hardbox magnet
            if ($layouts->tipe_rumus == 3) {
                $panjang_cetak = $input['p']+1;
                $lebar_cetak = ($input['l']*2)+($input['t']*2)+1;
            } else if ($layouts->jenis_box == 2) { //hardbox slide
                $input['p'] = $input['p']+1;
                $input['l'] = $input['l']+1;
                $input['t'] = $input['t']+1;

                $panjang_cetak = ($input['p']*2)+($input['t']*2);
                $lebar_cetak = $input['l']+$input['t'];
            } else {
                $input['p'] = $input['p']+1;
                $input['l'] = $input['l']+1;

                $panjang_cetak = $input['p']+$input['t']*2;
                $lebar_cetak = $input['l']+$input['t']*2;
            }

            $luas_permukaan_cetak_panjang = $panjang_cetak + 1;
            $luas_permukaan_cetak_lebar = $lebar_cetak + 1;

            $data = [
                'panjang_cetak' => $panjang_cetak,
                'lebar_cetak' => $lebar_cetak,
                'panjang_kemasan' => $luas_permukaan_cetak_panjang,
                'lebar_kemasan' => $luas_permukaan_cetak_lebar,
            ];

        } else {
            $data = $request->all();
        }
        
        if ($request->id_kertas_plano) {
            $kertas_plano = KertasPlano::select('kertas_plano')->find($request->id_kertas_plano);
            $kertas_plano = explode('/',$kertas_plano->kertas_plano);
            $p_plano = (float)$kertas_plano[0];
            $l_plano = (float)$kertas_plano[1];
        }

        $all_combi = [];

        $all_combi['combi_1'] = (new \Lib\softbox($luas_permukaan_cetak_panjang,$luas_permukaan_cetak_lebar,$p_plano,$l_plano))->combi_1($data);
        $all_combi['combi_2'] = (new \Lib\softbox($luas_permukaan_cetak_panjang,$luas_permukaan_cetak_lebar,$p_plano,$l_plano))->combi_2($data);
        $all_combi['combi_3'] = (new \Lib\softbox($luas_permukaan_cetak_panjang,$luas_permukaan_cetak_lebar,$p_plano,$l_plano))->combi_3($data);
        
        $result = collect($all_combi)->sortBy('jumlah_potong', SORT_REGULAR, true)->first();
        
        return response()->json($result,200);
    }

    public function list_mesin(Request $request){
        if (isset($request->id_gramasi)) {
            $gramasi = Gramasi::find($request->id_gramasi);
            if ($gramasi->gramasi >= 350) {
                $data = Mesin::select(['id_mesin','mesin'])
                ->whereIn('mesin', ["74", "102"])->get();
            } else {
                $data = Mesin::select(['id_mesin','mesin'])
                ->whereIn('mesin', ["52", "74", "102"])->get();
            }

            if (count($data) > 0) {
                foreach ($data as $row) {
                    $list_mesin[] = array(
                        "id_mesin" => $row->id_mesin,
                        "mesin" => $row->mesin
                    );
                }
            }
            return Response::json($list_mesin, 200);
        }
    }
}