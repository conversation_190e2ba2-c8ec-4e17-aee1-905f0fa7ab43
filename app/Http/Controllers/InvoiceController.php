<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Invoice;
use App\Models\Customer;
use App\Models\BankAccount;
use App\Models\Order;
use Illuminate\Http\Request;
use App\Models\InvoiceDetail;
use App\Models\QuotationDetail;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpWord\TemplateProcessor;
use Yajra\DataTables\Facades\DataTables;

class InvoiceController extends Controller
{
    public function customers(Request $request)
    {
        $customers = Customer::select('id_customer as id', 'nama', 'nama_instansi', 'no_hp')
            ->when($request->input('q'), function ($query, $keyword) {
                $query->where('nama', 'like', '%' . $keyword . '%');
            })
            ->orderBy('nama', 'asc')
            ->paginate(10);

        return response()->json($customers);
    }

    public function index()
    {
        // $customers = Customer::all();
        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();
        $total_overdue_invoices = Invoice::where('tanggal_jatuh_tempo', '<', now())
                                    ->where('status_paid', 'unpaid')
                                    ->sum('sisa_pelunasan');


        // Ambil semua invoice unpaid dengan tanggal jatuh tempo atau jatuh tempo tambahan dalam 30 hari terakhir
        $upcomingInvoices = Invoice::where('sisa_pelunasan', '>', 0)
            ->where(function ($query) use ($endDate) {
                $query->where(function ($q) use ($endDate) {
                    // If tanggal_jatuh_tempo_tambahan is not null, use it
                    $q->whereNotNull('tanggal_jatuh_tempo_tambahan')
                        ->whereBetween('tanggal_jatuh_tempo_tambahan', [
                            now(),
                            $endDate
                        ]);
                })
                ->orWhere(function ($q) use ($endDate) {
                    // If tanggal_jatuh_tempo_tambahan is null, use tanggal_jatuh_tempo
                    $q->whereNull('tanggal_jatuh_tempo_tambahan')
                        ->whereBetween('tanggal_jatuh_tempo', [
                            now(),
                            $endDate
                        ]);
                });
            })
            ->get();

        $total_upcoming_invoices = $upcomingInvoices->sum('sisa_pelunasan');

        $total_invoices_paid_this_month = 0;
        $total_paid_dp_this_month = Invoice::whereBetween('tanggal_dp', [
                                                now()->startOfMonth(),
                                                now()->endOfMonth()
                                            ])
                                            ->get()
                                            ->sum(function ($invoice) {
                                                return $invoice->dp_terbayar;
                                            }) ?? 0;
        $total_paid_pelunasan_this_month = Invoice::whereBetween('tanggal_pelunasan', [
                                                now()->startOfMonth(),
                                                now()->endOfMonth()
                                            ])
                                            ->get()
                                            ->sum(function ($invoice) {
                                                return $invoice->total_pembayaran;
                                            }) ?? 0;
        $total_invoices_paid_this_month = $total_paid_dp_this_month + $total_paid_pelunasan_this_month;

        $total_invoices_this_month = Invoice::whereBetween('created_at', [
                                                now()->startOfMonth(),
                                                now()->endOfMonth()
                                            ])
                                            ->where('status', 'submitted')
                                            ->sum('total');
        return view('pages.invoices.index', compact(
            'total_overdue_invoices',
            'total_upcoming_invoices',
            'total_invoices_paid_this_month',
            'total_invoices_this_month'
        ));
    }

    public function create(Request $request)
    {
        $data_order = null;
        if (isset($request->sko_key)) {
            $data_order = Order::with(['tb_produksi:sko_key,nama_produk,jumlah_produk,notes,harga_produk'])
                ->where('sko_key', $request->sko_key)
                ->first();
                
        }
        if (!$request->type || !in_array($request->type, ['sales', 'proforma'])) {
            return redirect(route('invoice.index'));
        }
        
        return view('pages.invoices.create-edit', compact('data_order'));
    }

    public function edit(Request $request, $id)
    {
        $data = Invoice::with(['details', 'customer', 'createdBy'])
            ->where('invoice_key', $id)
            ->where('invoice_type', $request->type)
            ->firstOrFail();
        return view('pages.invoices.create-edit', compact('data'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|exists:tb_customers,id_customer',
            'tanggal_jatuh_tempo' => 'nullable|date',
            'status' => 'required|in:draft,submitted',
            'nama_produk' => 'required|array',
            'nama_produk.*' => 'required|string',
            'specification' => 'nullable|array',
            'specification.*' => 'nullable|string',
            'qty' => 'required|array',
            'qty.*' => 'required|integer|min:1',
            'price' => 'required|array',
            'price.*' => 'required|min:0',
            'id_product' => 'nullable|array',
            'id_product.*' => 'nullable|integer',
            'kode_order' => 'nullable|array',
            'kode_order.*' => 'nullable|string',
            'is_ppn' => 'nullable|boolean',
            'ppn' => 'nullable|numeric|min:0',
            'biaya_pengiriman' => 'nullable|min:0',
            'total_pembayaran' => 'nullable|min:0',
            'potongan' => 'nullable|min:0',
            'down_payment' => 'nullable|min:0',
            // 'status_paid' => 'nullable|in:paid,unpaid',
            'lampiran' => 'nullable',
            'note' => 'nullable|string',
        ]);

        $input = $request->all();
        
        try {
            DB::transaction(function () use ($input, $request) {

                $customer = Customer::find($input['customer_id']);


                $potongan = str_replace('.', '', $input['potongan'] ?? 0);
                $biaya_pengiriman = str_replace('.', '', $input['biaya_pengiriman'] ?? 0);
                $down_payment = str_replace('.', '', $input['down_payment'] ?? 0);
                $total_pembayaran = str_replace('.', '', $input['total_pembayaran'] ?? 0);

                $input_invoice = [
                    'no_po' => $input['no_po'] ?? '',
                    'tanggal_jatuh_tempo_tambahan' => $input['tanggal_jatuh_tempo_tambahan'] ?? null,
                    'tanggal_pelunasan' => $input['tanggal_pelunasan'] ?? null,
                    'invoice_type' => 'sales',
                    'customer_id' => $input['customer_id'],
                    'company_id' => $customer->company_id ?? null,
                    'tanggal_invoice' => Carbon::now(),
                    'tanggal_jatuh_tempo' => $input['tanggal_jatuh_tempo'] ?? null,
                    'is_ppn' => isset($input['is_ppn']) ? true : false,
                    'diskon' => $potongan,
                    'biaya_pengiriman' => $biaya_pengiriman,
                    'dp_terbayar' => $down_payment,
                    'persentase_ppn' => $input['ppn'] ?? 0,
                    'total_ppn' => $input['ppn'] ? ($input['ppn'] * ($biaya_pengiriman - $potongan)) / 100 : 0,
                    'status' => $input['status'],
                    'note' => $input['note'],
                    'total_pembayaran' => $total_pembayaran,
                    'created_by_id' => auth()->id(),
                    'tanggal_dp' => $input['tanggal_dp'] ?? null,
                    'tanggal_pelunasan' => $input['tanggal_pelunasan'] ?? null,
                    'file_lampiran' => $input['lampiran'] ?? null,
                ];

                // $file_lampiran = '';
                // if ($request->file('lampiran')) {
                //     $file_lampiran = upload_file($request->file('lampiran'), 'invoices', 'invoice');
                //     $input_invoice['file_lampiran'] = $file_lampiran;
                // }

                if (!$input['id']) {
                    $no_invoice = Invoice::generateInvoiceNumber();

                    $input_invoice['no_invoice'] = $no_invoice['no_invoice'];
                    $input_invoice['no_number'] = $no_invoice['no_number'];

                    if ($down_payment > 0) {
                        $input_invoice['tanggal_dp'] = Carbon::now();
                    }
                }

                $invoice = Invoice::updateOrCreate([
                    'invoice_key' => $input['id'] ?? null,
                ], $input_invoice);

                $totalQuantity = 0;

                $collectId = [];

                foreach ($input['nama_produk'] as $key => $nama_produk) {
                    if (empty($input['nama_produk'][$key]) || empty($input['qty'][$key])) {
                        continue;
                    }

                    $price = str_replace('.', '', $input['price'][$key]);

                    $detailData = [
                        'kode_order' => $input['kode_order'][$key] ?? "",
                        'product_name' => $nama_produk ?? "",
                        'spesifikasi' => $input['specification'][$key] ?? "",
                        'quantity' => $input['qty'][$key] ?? 0,
                        'harga_satuan' => $price ?? 0,
                        'jumlah_harga' => $price * ($input['qty'][$key] ?? 0),
                        'invoice_key' => $invoice->invoice_key,
                    ];

                    $invoiceDetail = $invoice->details()->updateOrCreate(
                        ['id' => $input['id_product'][$key] ?? null],
                        $detailData
                    );

                    $collectId[] = $invoiceDetail->id;
                }

                if (count($collectId) > 0) {
                    InvoiceDetail::where('invoice_key', $invoice->invoice_key)
                        ->whereNotIn('id', $collectId)->delete();
                }

                $subTotal = 0;
                foreach ($invoice->details as $detail) {
                    $totalQuantity += $detail->quantity;
                    $subTotal += $detail->quantity * $detail->harga_satuan;
                    // $totalPrice += $detail->quantity * $detail->price;
                }

                $is_ppn = intval($invoice->is_ppn) === 1;

                $subTotal = $subTotal < 0 ? 0 : $subTotal;

                $totalPPN = $is_ppn ? ($subTotal * ($invoice->persentase_ppn / 100)) : 0;
                $total = $subTotal + $totalPPN - $invoice->diskon;
                $total += $invoice->biaya_pengiriman;
                $sisa_pelunasan = $total - $invoice->dp_terbayar - $total_pembayaran;

                $invoice->update([
                    'total_quantity' => $totalQuantity,
                    'subtotal' => $subTotal,
                    'total' => $total,
                    'total_ppn' => $totalPPN,
                    'sisa_pelunasan' => $sisa_pelunasan,
                    'total_price' => $total,
                ]);
                
                if ($sisa_pelunasan <= 0) {
                    $invoice->update([
                        'sisa_pelunasan' => 0,
                        'status_paid' => 'paid',
                        'status' => 'submitted',
                        'tanggal_pelunasan' => date('Y-m-d')
                    ]);
                } else if ($sisa_pelunasan > 0){
                    $invoice->update([
                        'status_paid' => 'unpaid'
                    ]);
                }
            });

            return redirect(route('invoice.index'))
                ->with('success', 'Invoice saved successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to save invoice: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function show($id)
    {
        $data = Invoice::with(['details', 'customer', 'createdBy'])
            ->where('invoice_key', $id)
            ->firstOrFail();
        return view('pages.invoices.show', compact('data'));
    }

    public function destroy($id)
    {
        try {
            DB::transaction(function () use ($id) {
                $invoice = Invoice::where('invoice_key', $id)->firstOrFail();
                $invoice->details()->delete();
                $invoice->delete();
            });

            return response()->json([
                'status' => true,
                'message' => 'Invoice deleted successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to delete invoice: ' . $e->getMessage(),
            ]);
        }
    }

    public function dataTable(Request $request)
    {
        $data = Invoice::select(
            'id',
            'invoice_key',
            'company_id',
            'customer_id',
            'no_invoice',
            'file_invoice',
            'status',
            'total_quantity',
            'total',
            'created_by_id',
            'created_at',
            'invoice_type',
            'dp_terbayar',
            'sisa_pelunasan',
            'total_pembayaran',
            'status_paid',
            'tanggal_jatuh_tempo',
            'tanggal_jatuh_tempo_tambahan',
            'tanggal_dp',
            'tanggal_pelunasan'
        )->with(['details', 'customer', 'createdBy']);
        if (request()->has('status_jatuh_tempo') && request()->get('status_jatuh_tempo') == 4) {
            $data->where('tanggal_jatuh_tempo', '<', now())
            ->where('status_paid', 'unpaid');
        } else if (request()->has('status_jatuh_tempo') && request()->get('status_jatuh_tempo') == 5) {
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now()->endOfMonth();
            $data->where('status_paid', 'unpaid')
            ->where(function ($query) use ($endDate) {
                $query->where(function ($q) use ($endDate) {
                    // If tanggal_jatuh_tempo_tambahan is not null, use it
                    $q->whereNotNull('tanggal_jatuh_tempo_tambahan')
                        ->whereBetween('tanggal_jatuh_tempo_tambahan', [
                            now(),
                            $endDate
                        ]);
                })
                ->orWhere(function ($q) use ($endDate) {
                    // If tanggal_jatuh_tempo_tambahan is null, use tanggal_jatuh_tempo
                    $q->whereNull('tanggal_jatuh_tempo_tambahan')
                        ->whereBetween('tanggal_jatuh_tempo', [
                            now(),
                            $endDate
                        ]);
                });
            })->sum('sisa_pelunasan');
        } else if (request()->has('status_jatuh_tempo') && request()->get('status_jatuh_tempo') == 6) {
            $data->where(function($query) {
                $query->whereBetween('tanggal_dp', [
                    now()->startOfMonth(),
                    now()->endOfMonth()
                ])
                ->orWhereBetween('tanggal_pelunasan', [
                    now()->startOfMonth(), 
                    now()->endOfMonth()
                ]);
            });
        } else if (request()->has('status_jatuh_tempo') && request()->get('status_jatuh_tempo') == 7) {
            $data->whereBetween('created_at', [
                    now()->startOfMonth(),
                    now()->endOfMonth()
                ])
                ->where('status', 'submitted');
        } else {
            $data->filter($request);
        }
        $data = $data->latest();

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('status_badge', function ($row) {
                $status = strtoupper($row->status);
                $badgeClass = '';

                switch ($status) {
                    case 'DRAFT':
                        $badgeClass = 'bg-info';
                        break;
                    case 'SUBMITTED':
                        $badgeClass = 'bg-primary';
                        break;
                }

                return '<span class="badge ' . $badgeClass . '">' . $status . '</span>';
            })
            ->addColumn('total_price', function ($row) {
                return $row->total ? "Rp " . number_format($row->total, 0, '', ',') . "" : '-';
            })
            ->addColumn('dp_terbayar', function ($row) {
                return $row->dp_terbayar ? "Rp " . number_format($row->dp_terbayar, 0, '', ',') . "" : '-';
            })
            ->addColumn('sisa_pelunasan', function ($row) {
                return $row->sisa_pelunasan ? "Rp " . number_format($row->sisa_pelunasan, 0, '', ',') . "" : '-';
            })
            ->addColumn('total_pembayaran', function ($row) {
                return $row->total_pembayaran ? "Rp " . number_format($row->total_pembayaran, 0, '', ',') . "" : '-';
            })
            ->addColumn('created_at', function ($row) {
                return $row->created_at ? date('d M Y H:i', strtotime($row->created_at)) : '-';
            })
            ->addColumn('tanggal_dp', function ($row) {
                return $row->tanggal_dp ? date('d M Y H:i', strtotime($row->tanggal_dp)) : '-';
            })
            ->addColumn('tanggal_pelunasan', function ($row) {
                return $row->tanggal_pelunasan ? date('d M Y H:i', strtotime($row->tanggal_pelunasan)) : '-';
            })
            ->addColumn('tanggal_jatuh_tempo', function ($row) {
                return $row->tanggal_jatuh_tempo ? Carbon::parse($row->tanggal_jatuh_tempo)->locale('id')->translatedFormat('l, d F Y') ?? 'N/A' : '-';
            })
            ->addColumn('tanggal_jatuh_tempo_tambahan', function ($row) {
                return $row->tanggal_jatuh_tempo_tambahan ? Carbon::parse($row->tanggal_jatuh_tempo_tambahan)->locale('id')->translatedFormat('l, d F Y') ?? 'N/A' : '-';
            })
            ->addColumn('status_paid', function ($row) {
                $statusPaid = strtoupper($row->status_paid ?? 'unpaid');
                $badgeClass = '';
                $icon = '';

                switch ($statusPaid) {
                    case 'PAID':
                        $badgeClass = 'bg-success';
                        $icon = '<i class="fas fa-check-circle text-white"></i>';
                        break;
                    case 'UNPAID':
                        $badgeClass = 'bg-danger';
                        $icon = '<i class="fas fa-exclamation-circle text-white"></i>';
                        break;
                }

                return '<span class="badge ' . $badgeClass . '">' . $icon . ' ' . $statusPaid . '</span>';
            })
            ->addColumn('created_by', function ($row) {
                return $row->createdBy ? $row->createdBy->name : '-';
            })
            ->addColumn('kode_order', function ($row) {
                return $row->details->first()->kode_order ?? '-';
            })
            ->addColumn('nama_produk', function ($row) {
                return $row->details->first()->product_name ?? '-';
            })
            ->addColumn('action', function ($row) {
                $editUrl = route('invoice.edit', $row->invoice_key) . "?type=sales";
                $generateDocUrl = route('invoice.generateInvoice', $row->invoice_key);
                $detailUrl = route('invoice.show', $row->invoice_key);

                $btn = '<div class="btn-group d-flex">';
                $btn .= '<a href="' . $generateDocUrl . '?type=proforma" target="_blank" class="btn btn-sm btn-my-primary">Proforma</a>';
                $btn .= '<a href="' . $generateDocUrl . '?type=sales" target="_blank" class="btn btn-sm btn-primary">Sales</a>';
            
                $btn .= '<a type="button" class="btn btn-info" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-ellipsis-v"></i>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" style="max-width: 80px">';

                $btn .= '<li><a class="dropdown-item py-3 px-5" href="' . $detailUrl . '"><i class="fas fa-eye p-0"></i> View</a></li>';

                $btn .= '<li><a class="dropdown-item py-3 px-5" href="' . $editUrl . '"><i class="fas fa-edit p-0"></i> Edit</a></li>';
                if (strtoupper($row->status) !== 'SUBMITTED') {
                    $btn .= '<li><button class="dropdown-item py-3 px-5 deleteData" data-id="' . $row->invoice_key . '" data-input=\'' . json_encode($row) . '\'><i class="fas fa-trash-alt p-0"></i> Delete</button></li>';
                }

                $btn .= '</ul>';
                $btn .= '</div>';
                return $btn;
            })
            ->rawColumns(['action', 'status_badge', 'created_at', 'created_by', 'status_paid', 'produk'])
            ->smart(true)
            ->make(true);
    }


    public function generateInvoice($id)
    {
        $data = Invoice::with(['details'])
            ->where('invoice_key', $id)
            ->firstOrFail();

        $invoice_type = request()->input('type');
        $is_ppn = $data->is_ppn == 1;
        if ($invoice_type === "sales") {
            if ($is_ppn) {
                $nama_bank = "Bank Syariah Indonesia (BSI)";
                $rek_bank = "**********";
                $pemrek_bank = "PT Billah Berdikari Indonesia";
                
                $bankAccount = BankAccount::where('is_ppn', true)->where('status', true)->first();

                if ($bankAccount) {
                    $nama_bank = $bankAccount->bank_name;
                    $rek_bank = $bankAccount->bank_account_no;
                    $pemrek_bank = $bankAccount->bank_account_name;
                }

                $templateProcessor = new TemplateProcessor(public_path('templates/invoices/template_sales_invoice_ppn.docx'));
                $templateProcessor->setValue('ppn', number_format($data->total_ppn, 0, ',', '.'));
                $templateProcessor->setValue('nama_bank', $nama_bank);
                $templateProcessor->setValue('no_rekening', $rek_bank . " a.n. ".$pemrek_bank);
            } else {
                $nama_bank = "Bank Central Asia (BCA)";
                $rek_bank = "**********";
                $pemrek_bank = "CV Billah Berdikari Indonesia";
                
                $bankAccount = BankAccount::where('is_ppn', false)->where('status', true)->first();
                
                if ($bankAccount) {
                    $nama_bank = $bankAccount->bank_name;
                    $rek_bank = $bankAccount->bank_account_no;
                    $pemrek_bank = $bankAccount->bank_account_name;
                }

                $templateProcessor = new TemplateProcessor(public_path('templates/invoices/template_sales_invoice_no_ppn.docx'));
                $templateProcessor->setValue('potongan', number_format($data->diskon, 0, ',', '.'));
                $templateProcessor->setValue('nama_bank', $nama_bank);
                $templateProcessor->setValue('no_rekening', $rek_bank . " a.n. ".$pemrek_bank);
            }

            $tgl_jatuh_tempo = $data->tanggal_jatuh_tempo ? \Carbon\Carbon::parse($data->tanggal_jatuh_tempo)->locale('id')->isoFormat('D MMMM YYYY') : "";
            $templateProcessor->setValue('tanggal_jatuh_tempo', $tgl_jatuh_tempo);
            $templateProcessor->setValue('subtotal', number_format($data->subtotal, 0, ',', '.'));
        } else if ($invoice_type == "proforma") {
            if ($is_ppn) {
                $nama_bank = "Bank Syariah Indonesia (BSI)";
                $rek_bank = "**********";
                $pemrek_bank = "PT Billah Berdikari Indonesia";
                
                $bankAccount = BankAccount::where('is_ppn', true)->where('status', true)->first();

                if ($bankAccount) {
                    $nama_bank = $bankAccount->bank_name;
                    $rek_bank = $bankAccount->bank_account_no;
                    $pemrek_bank = $bankAccount->bank_account_name;
                }

                $templateProcessor = new TemplateProcessor(public_path('templates/invoices/template_performa_invoice_ppn.docx'));
                $templateProcessor->setValue('ppn', "VAT (PPN $data->persentase_ppn%)");
                $templateProcessor->setValue('total_ppn', number_format($data->total_ppn, 0, ',', '.'));

                $templateProcessor->setValue('nama_bank', $nama_bank);
                $templateProcessor->setValue('no_rekening', $rek_bank . " a.n. ".$pemrek_bank);
            } else {
                $nama_bank = "Bank Central Asia (BCA)";
                $rek_bank = "**********";
                $pemrek_bank = "CV Billah Berdikari Indonesia";
                
                $bankAccount = BankAccount::where('is_ppn', false)->where('status', true)->first();
                
                if ($bankAccount) {
                    $nama_bank = $bankAccount->bank_name;
                    $rek_bank = $bankAccount->bank_account_no;
                    $pemrek_bank = $bankAccount->bank_account_name;
                }
                $templateProcessor = new TemplateProcessor(public_path('templates/invoices/template_performa_invoice_no_ppn.docx'));

                $templateProcessor->setValue('nama_bank', $nama_bank);
                $templateProcessor->setValue('no_rekening', $rek_bank . " a.n. ".$pemrek_bank);
            }

            $templateProcessor->setValue('subtotal', "Subtotal");
            $templateProcessor->setValue('subtotal_harga', number_format($data->subtotal, 0, ',', '.'));
            $templateProcessor->setValue('down_payment', number_format($data->dp_terbayar, 0, ',', '.'));
        } else {
            return redirect()->back()->with('error', 'Invalid invoice type');
        }

        $templateProcessor->setValue('no_po', $data->no_po ?? "");
        $templateProcessor->setValue('biaya_pengiriman', number_format($data->biaya_pengiriman, 0, ',', '.'));
        $templateProcessor->setValue('diskon', number_format($data->diskon, 0, ',', '.'));
        $templateProcessor->setValue('no_invoice', $data->no_invoice ?? "");
        $tgl_penawaran = $data->created_at->locale('id')->isoFormat('D MMMM YYYY');
        $templateProcessor->setValue('tanggal_invoice', $tgl_penawaran);
        $templateProcessor->setValue('sisa_pelunasan', number_format($data->sisa_pelunasan, 0, ',', '.'));
        $templateProcessor->setValue('down_payment_terbayar', number_format($data->dp_terbayar, 0, ',', '.'));

        $customer = $data->customer;
        $nama_instansi = $customer->company->name ?? "";
        $templateProcessor->setValue('nama_instansi', $nama_instansi);
        $nama_customer = $customer->nama ?? "";
        $templateProcessor->setValue('nama_customer', $nama_customer);
        $alamat_customer = $data->customer->address ?? "";
        $templateProcessor->setValue('alamat_instansi', $alamat_customer);

        $templateProcessor->cloneRow('nama_produk', count($data->details));

        $templateProcessor->setValue('total', number_format($data->total, 0, ',', '.'));

        foreach ($data->details as $index => $detail) {
            $rowNumber = $index + 1;

            if ($invoice_type === "sales") {
                $templateProcessor->setValue('kode_order#' . $rowNumber, $detail['kode_order'] ?? '');
            }

            $templateProcessor->setValue('nama_produk#' . $rowNumber, $detail['product_name'] ?? '');
            $templateProcessor->setValue('spesifikasi#' . $rowNumber, $detail['spesifikasi'] ?? '');
            $templateProcessor->setValue('quantity#' . $rowNumber, $detail['quantity'] ?? '');
            $number_format = number_format($detail['harga_satuan'] ?? 0, 0, ',', '.');
            $templateProcessor->setValue('harga_satuan#' . $rowNumber, $number_format);
            $templateProcessor->setValue('jumlah_harga#' . $rowNumber, number_format($detail['jumlah_harga'] ?? 0, 0, ',', '.'));
        }


        $datemark = date('dmy');
        $filename = "Invoice - {$data->no_invoice} - {$nama_instansi} - {$datemark}.docx";
        $templateProcessor->saveAs($filename);

        \PhpOffice\PhpWord\Settings::setOutputEscapingEnabled(true);
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filename));
        ob_clean();
        flush();
        readfile($filename);
        unlink($filename);
        exit;
    }

    public function removeColumnByName($templateProcessor, $columnName)
    {
        // Dapatkan isi file document.xml yang berisi tabel
        $zip = new \ZipArchive();
        $filePath = public_path('templates/invoices/template_performa_invoice.docx');
        $zip->open($filePath);

        // Ambil isi XML dari word/document.xml
        $xmlContent = $zip->getFromName('word/document.xml');
        $zip->close();

        // Parse XML
        $xml = simplexml_load_string($xmlContent);
        $namespaces = $xml->getNamespaces(true);
        $xml->registerXPathNamespace('w', $namespaces['w']);

        // Ambil semua baris dalam tabel
        $rows = $xml->xpath('//w:tbl//w:tr');

        // Cari nama kolom di header tabel (misalnya PPN)
        $headerRow = $rows[0]; // Anggap baris pertama adalah header
        $cells = $headerRow->xpath('.//w:tc');

        // Temukan kolom yang sesuai dengan nama yang diberikan
        $columnIndex = -1;
        foreach ($cells as $index => $cell) {
            $text = (string)$cell->xpath('.//w:t')[0]; // Ambil teks dari cell
            if (strpos($text, $columnName) !== false) {
                $columnIndex = $index; // Menyimpan index kolom
                break;
            }
        }

        // Jika kolom ditemukan, hapus kolom tersebut di setiap baris
        if ($columnIndex >= 0) {
            foreach ($rows as $row) {
                $cells = $row->xpath('.//w:tc');
                if (isset($cells[$columnIndex])) {
                    unset($cells[$columnIndex]);  // Hapus kolom berdasarkan index
                }
            }
        }

        // Simpan kembali XML yang telah dimodifikasi
        $modifiedXmlContent = $xml->asXML();

        // Menulis kembali XML ke dalam file Word
        $zip->open($filePath, \ZipArchive::CREATE);
        $zip->deleteName('word/document.xml');
        $zip->addFromString('word/document.xml', $modifiedXmlContent);
        $zip->close();
    }
}
