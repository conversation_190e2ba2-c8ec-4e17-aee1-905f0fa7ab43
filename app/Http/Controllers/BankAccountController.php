<?php

namespace App\Http\Controllers;

use App\Models\BankAccount;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class BankAccountController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(request()->ajax())
        {
            $query = BankAccount::query();
            return DataTables::of($query)
                ->editColumn('status', function($item){
                    return $item->status == true ? '<span class="badge badge-info">Default</span>' : '';
                })
                ->editColumn('is_ppn', function($item){
                    return $item->is_ppn == true? '<span class="badge badge-success"><i class="fa fa-check text-white"></i></span>' : '';
                })
                ->addColumn('action', function($item){
                    return '
                    <div class="row">
                        <div class="col-md-6">
                            <a class="btn btn-sm btn-primary w-100 text-white edit-bank-account" data-id="'.$item->id.'" href="javascript:void(0)">Edit</a>
                        </div>
                        <div class="col-md-6">
                            <a href="javascript:void(0)" class="btn btn-sm btn-danger w-100 text-white delete-link" data-id-destroy="'.$item->id.'" onclick="deleteConfirmation('.$item->id.')">Delete</a>
                        </div>
                    </div>
                    ';
                })
                ->rawColumns(['action','is_ppn','status'])
                ->make();

        }
        return view('pages.superadmin.bank_account.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'bank_account_name' => 'required|string|max:255',
            'bank_account_no' => 'required|string|max:20',
            'bank_name' => 'required|string|max:255'
        ]);

        $data = $request->only(['bank_account_name', 'bank_account_no', 'bank_name', 'status', 'is_ppn']);
        $data['is_ppn'] = $request->has('is_ppn') ? true : false;
        $data['status'] = $request->has('status') ? true : false;

        if($request->id_bank_account){
            $bankAccount = BankAccount::where('id', $request->id_bank_account)->update($data);
            return response()->json([
                'success' => true,
                'message' => 'Bank account updated successfully.',
                'data' => $bankAccount
            ]);
        }

        $bankAccount = BankAccount::create($data);

        return response()->json([
            'success' => true,
            'message' => 'Bank account created successfully.',
            'data' => $bankAccount
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $user = BankAccount::where('id',$id)->first();
        return response()->json($user);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit()
    {
        
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        BankAccount::find($id)->delete($id);
        return response()->json([
            'success' => 'Record deleted successfully!'
        ]);
    }
}
