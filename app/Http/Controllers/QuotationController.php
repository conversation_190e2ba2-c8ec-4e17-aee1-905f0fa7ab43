<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Customer;
use App\Models\Quotation;
use Illuminate\Http\Request;
use App\Models\QuotationDetail;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpWord\TemplateProcessor;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Invoice;
class QuotationController extends Controller
{
    public function customers(Request $request)
    {
        $customers = Customer::select('id_customer as id', 'nama', 'nama_instansi', 'no_hp')
            ->when($request->input('q'), function ($query, $keyword) {
                $query->where('nama', 'like', '%' . $keyword . '%');
            })
            ->orderBy('nama', 'asc')
            ->paginate(10);

        return response()->json($customers);
    }

    public function index()
    {
        // $customers = Customer::all();
        return view('pages.quotations.index');
    }

    public function create()
    {
        return view('pages.quotations.create-edit');
    }

    public function edit($id)
    {
        $data = Quotation::with(['details', 'customer', 'createdBy'])
            ->where('quotation_key', $id)
            ->firstOrFail();
        return view('pages.quotations.create-edit', compact('data'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|exists:tb_customers,id_customer',
            'status' => 'required|in:submitted,draft',
            'product_name' => 'required|array',
            'product_name.*' => 'required|string|max:255',
            'specification' => 'nullable|array',
            'specification.*' => 'nullable|string|max:255',
            'qty' => 'required|array',
            'qty.*' => 'required|integer|min:1',
            'price' => 'required|array',
            'price.*' => 'required|min:0',
        ]);

        $input = $request->all();

        try {
            DB::transaction(function () use ($input) {

                $customer = Customer::find($input['customer_id']);


                $input_qoutation = [
                    'customer_id' => $input['customer_id'],
                    'company_id' => $customer->company_id ?? null,
                    'status' => $input['status'],
                    'created_by_id' => auth()->id(),
                ];

                if(!$input['id']) {
                    $no_quotation = Quotation::generateQuotationNumber();
                    $input_qoutation['no_quotation'] = $no_quotation['no_quotation'] ?? null;
                    $input_qoutation['no_number'] = $no_quotation['no_number'] ?? null;
                }
                
                $quotation = Quotation::updateOrCreate([
                    'quotation_key' => $input['id'] ?? null,
                ], $input_qoutation);

                $totalQuantity = 0;
                $totalPrice = 0;

                $collectId = [];
                foreach ($input['product_name'] as $key => $product_name) {
                    if (empty($input['product_name'][$key]) || empty($input['qty'][$key]) || empty($input['price'][$key])) {
                        continue;
                    }

                    $price = str_replace('.', '', $input['price'][$key]);

                    $detailData = [
                        'product_name' => $product_name ?? "",
                        'spesifikasi' => $input['specification'][$key] ?? "",
                        'quantity' => $input['qty'][$key] ?? 0,
                        'price' => $price ?? 0,
                        'quotation_key' => $quotation->quotation_key,
                    ];

                    $quotationDetail = $quotation->details()->updateOrCreate(
                        ['id' => $input['id_product'][$key] ?? null],
                        $detailData
                    );

                    $collectId[] = $quotationDetail->id;
                }

                if (count($collectId) > 0) {
                    QuotationDetail::where('quotation_key', $quotation->quotation_key)
                        ->whereNotIn('id', $collectId)->delete();
                }

                foreach ($quotation->details as $detail) {
                    $totalQuantity += $detail->quantity;
                    $totalPrice += $detail->quantity * $detail->price;
                }

                $quotation->update([
                    'total_quantity' => $totalQuantity,
                    'total_price' => $totalPrice,
                ]);
            });

            return redirect(route('quotation.index'))
                ->with('success', 'Quotation saved successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to save quotation: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function show($id)
    {
        $data = Quotation::with(['details', 'customer', 'createdBy'])
            ->where('quotation_key', $id)
            ->firstOrFail();
        return view('pages.quotations.show', compact('data'));
    }

    public function destroy($id)
    {
        try {
            DB::transaction(function () use ($id) {
                $invoice = Quotation::where('quotation_key', $id)->firstOrFail();
                $invoice->details()->delete();
                $invoice->delete();
            });

            return response()->json([
                'status' => true,
                'message' => 'Quotation deleted successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to delete quotation: ' . $e->getMessage(),
            ]);
        }
    }

    public function dataTable(Request $request)
    {
        $data = Quotation::select(
            'id',
            'quotation_key',
            'company_id',
            'customer_id',
            'no_quotation',
            'file_quotation',
            'status',
            'total_quantity',
            'total_price',
            'created_by_id',
            'created_at'
        )->with(['details', 'customer', 'createdBy'])
        ->filter($request)
            ->latest();

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('status_badge', function ($row) {
                $status = strtoupper($row->status);
                $badgeClass = '';

                switch ($status) {
                    case 'DRAFT':
                        $badgeClass = 'bg-info';
                        break;
                    case 'SUBMITTED':
                        $badgeClass = 'bg-primary';
                        break;
                }

                return '<span class="badge ' . $badgeClass . '">' . $status . '</span>';
            })
            ->addColumn('total_price', function ($row) {
                return $row->total_price ? "Rp " . number_format($row->total_price, 0, '', ',') . "" : '-';
            })
            ->addColumn('created_at', function ($row) {
                return $row->created_at ? date('d M Y H:i', strtotime($row->created_at)) : '-';
            })
            ->addColumn('created_by', function ($row) {
                return $row->createdBy ? $row->createdBy->name : '-';
            })
            ->addColumn('action', function ($row) {
                $editUrl = route('quotation.edit', $row->quotation_key);
                $generateDocUrl = route('quotation.generateQuotation', $row->quotation_key);
                $detailUrl = route('quotation.show', $row->quotation_key);

                $generateDocButton = '<a href="' . $generateDocUrl . '" target="_blank" class="btn btn-sm px-4 btn-secondary"><i class="fas fa-file-alt p-0"></i></a>';

                $dropdownMenu = '<div class="btn-group">
                                    <button type="button" class="btn" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end" style="max-width: 80px">';

                $dropdownMenu .= '<li><a class="dropdown-item py-3 px-5" href="' . $detailUrl . '"><i class="fas fa-eye p-0"></i> View</a></li>';

                if (strtoupper($row->status) !== 'SUBMITTED') {
                    $dropdownMenu .= '<li><a class="dropdown-item py-3 px-5" href="' . $editUrl . '"><i class="fas fa-edit p-0"></i> Edit</a></li>';
                    $dropdownMenu .= '<li><button class="dropdown-item py-3 px-5 deleteData" data-id="' . $row->quotation_key . '" data-input=\'' . json_encode($row) . '\'><i class="fas fa-trash-alt p-0"></i> Delete</button></li>';
                }

                $dropdownMenu .= '</ul></div>';

                return $generateDocButton . ' ' . $dropdownMenu;
            })
            ->rawColumns(['action', 'status_badge', 'created_at', 'created_by'])
            ->smart(true)
            ->make(true);
    }


    public function generateQuotation($id)
    {
        $data = Quotation::with(['details'])
                            ->where('quotation_key', $id)
                            ->firstOrFail();

        $templateProcessor = new TemplateProcessor(public_path('templates/quotations/template_quotation.docx'));
        $tgl_penawaran = $data->created_at->locale('id')->isoFormat('D MMMM YYYY');
        $templateProcessor->setValue('tgl_penawaran', $tgl_penawaran);
        $templateProcessor->setValue('nomor', $data->no_quotation);
        $nama_instansi = $data->customer->company->name ?? $data->customer->nama ?? "";
        $templateProcessor->setValue('nama_instansi', $nama_instansi);

        $templateProcessor->cloneRow('produk_name', count($data->details));

        foreach ($data->details as $index => $detail) {
            $rowNumber = $index + 1;
        
            $templateProcessor->setValue('produk_name#' . $rowNumber, $detail['product_name'] ?? '');
            $templateProcessor->setValue('spesifikasi#' . $rowNumber, $detail['spesifikasi'] ?? '');
            $templateProcessor->setValue('jumlah_produk#' . $rowNumber, $detail['quantity'] ? number_format($detail['quantity'], 0, '', '.') : '');
            $number_format = number_format($detail['price'] ?? 0, 0, '', ',');
            $templateProcessor->setValue('satuan_produk#' . $rowNumber, $number_format);
            $total_harga = $detail['price'] && $detail['quantity'] ? $detail['price'] * $detail['quantity'] : 0;
            $total_number_format = number_format($total_harga, 0, '', ',');
            $templateProcessor->setValue('total_harga#' . $rowNumber, $total_number_format);
        }

        $datemark = date('dmy');
        $filename = "Surat Penawaran - {$data->no_quotation} - {$nama_instansi} - {$datemark}.docx";
        $templateProcessor->saveAs($filename);

        \PhpOffice\PhpWord\Settings::setOutputEscapingEnabled(true);
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filename));
        ob_clean();
        flush();
        readfile($filename);
        unlink($filename);
        exit;
    }

    public function generateInvoice($id)
    {
        try {
            DB::transaction(function () use ($id) {
                $quotation = Quotation::with(['details', 'customer'])->where('quotation_key', $id)->firstOrFail();

                $invoiceCode = '';
                // Clone Quotation Details to Invoice Details
                foreach ($quotation->details as $detail) {
                    // Create Invoice
                    $invoice = new \App\Models\Invoice();
                    $invoice->quotation_key = $quotation->quotation_key;
                    $invoice->customer_id = $quotation->customer_id;
                    $invoice->company_id = $quotation->company_id;
                    $invoice->status = 'draft';
                    $invoice->created_by_id = auth()->id();

                    $no_invoice = Invoice::generateInvoiceNumber();
                    $invoice->no_invoice = $no_invoice['no_invoice'];
                    $invoice->no_number = $no_invoice['no_number'];
                    
                    $invoiceCode .= $no_invoice['no_invoice']. ' ';
                    $invoice->save();

                    $invoiceDetail = new \App\Models\InvoiceDetail();
                    $invoiceDetail->invoice_key = $invoice->invoice_key;
                    $invoiceDetail->product_name = $detail->product_name;
                    $invoiceDetail->spesifikasi = $detail->spesifikasi;
                    $invoiceDetail->quantity = $detail->quantity;
                    $invoiceDetail->harga_satuan = $detail->price;
                    $invoiceDetail->jumlah_harga = $detail->price * ($detail->quantity ?? 0);
                    $invoiceDetail->save();
                }
            });

            return response()->json([
                'status' => true,
                'message' => 'Invoice generated successfully.',
                'data' => [
                    'invoice_code' => $invoiceCode,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to generate invoice: ' . $e->getMessage(),
            ]);
        }
    }
}
