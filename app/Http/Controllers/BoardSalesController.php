<?php

namespace App\Http\Controllers;

use App\Models\Deal;
use App\Models\Lost;
use App\Models\Order;
use App\Models\Company;
use App\Models\FollowUp;
use Illuminate\Http\Request;
use App\Models\ActivityTaskOrder;
use Illuminate\Support\Facades\DB;

class BoardSalesController extends Controller
{
    public function index()
    {
        $companies = Company::withCount(['customers', 'customers as orders_count' => function ($query) {
            $query->withCount('orders');
        }])
            ->orderBy('name', 'asc')
            ->get();

        return view('pages.sales.boards.index', compact('companies'));
    }

    public function board()
    {
        $follow_up_list = FollowUp::select('id_follow_up as id', 'follow_up as name')
                                    ->orderBy('position', 'asc')
                                    ->latest()
                                    ->get();

        $deal_list = Deal::select('id_deal as id', 'deal as name')
                            ->orderBy('position', 'asc')
                            ->latest()
                            ->get();
        
        $lost_list = Lost::select('id_lost as id', 'lost as name')
                            // ->orderBy('position', 'asc')
                            ->latest()
                            ->get();

        return view('pages.sales.boards.board', compact('follow_up_list', 'deal_list', 'lost_list'));
    }

    public function orderCompany(Request $request)
    {
        $orders = Order::with(['uncompleted_tasks','tb_customer','tb_customer.company:name,id', 'tb_produksi:jenis_bahan,sko_key,jumlah_produk,kategori_produksi,nama_produk,total_harga'])
                        // ->whereHas('tb_customer', function ($query) use ($company) {
                        //     $query->where('company_id', $company->id);
                        // })
                        ->filter($request)
                        ->orderBy('tb_orders.updated_at', 'desc')
                        // ->orderBy('position_status_order', 'asc')
                        ->paginate(8);

        $orders = $orders->toArray();
        $data = $orders['data'];
        unset($orders['data']);
        $meta = $orders;

        return response()->json([
            'status' => true,
            'data' => $data,
            'meta' => $meta,
        ]);
    }

    public function detailBoard($order_key)
    {
        $order = Order::with(['tb_customer', 'tb_produksi:sko_key,jumlah_produk,kategori_produksi,nama_produk','tb_customer.company:name,id'])
            ->where('order_key', $order_key)
            ->firstOrFail();

        return response()->json([
            'status' => true,
            'data' => $order,
        ]);
    }

    public function storeActivityTask($order_key, Request $request)
    {
        $request->validate([
            'title' => 'required',
            'detail' => 'required',
        ]);


        try {
            $order = Order::where('order_key', $order_key)->firstOrFail();
            $input = $request->all();

            DB::transaction(function () use ($input, $order) {
                ActivityTaskOrder::updateOrCreate(
                    ['id' => $input['id'] ?? null],
                    [
                        'order_id' => $order->id,
                        'order_key' => $order->order_key,
                        'title' => $input['title'] ?? "",
                        'detail' => $input['detail'] ?? "",
                        'due_date' => $input['due_date'] ?? "",
                        'created_by_id' => auth()->user()->id,
                        'completed_at' => isset($input['mark_done']) && $input['mark_done'] == '1' ? now() : null,
                        'is_completed' => isset($input['mark_done']) && $input['mark_done'] == '1' ? true : null,
                    ]
                );
            });

            return response()->json([
                'status' => true,
                'message' => 'Activity Task created successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to create Activity Task',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function activityTask($order_key, Request $request)
    {
        $data = ActivityTaskOrder::with(['createdBy:id,name'])
                                ->where('order_key', $order_key)
                                ->latest()
                                ->get(['id as id_key', 'order_key', 'title', 'detail', 'due_date', 'created_by_id', 'created_at', 'is_completed']);

        return response()->json([
            'status' => true,
            'data' => $data,
        ]);
    }

    public function deleteActivityTask($order_key, $activity_task_id)
    {
        try {
            $activityTask = ActivityTaskOrder::findOrFail($activity_task_id);
            $activityTask->delete();

            return response()->json([
                'status' => true,
                'message' => 'Activity Task deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to delete Activity Task',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function markDoneActivityTask($order_key, $activity_task_id, Request $request)
    {
        try {
            $activityTask = ActivityTaskOrder::findOrFail($activity_task_id);
            $activityTask->completed_at = now();
            $activityTask->is_completed = true;
            $activityTask->save();

            return response()->json([
                'status' => true,
                'message' => 'Activity Task marked as done successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to mark Activity Task as done',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function changeOrder($order_key, Request $request)
    {
        $order = Order::where('order_key', $order_key)->firstOrFail();
        $order->status_order = $request->status_order;
        $order->updated_at = now();
        $order->save();

        return response()->json([
            'status' => true,
            'message' => 'Order status updated successfully',
        ]);
    }

    public function changeLost($order_key, Request $request)
    {        
        $request->validate([
            'reason' => 'required',
        ]);
        try {
            DB::transaction(function () use ($request, $order_key) {
                $order = Order::where('order_key', $order_key)->firstOrFail();
                $order->status_order = $request->reason;
                $order->status_deal = 'Lost';
                $order->updated_at = now();
                $order->save();
            });

            return response()->json([
                'status' => true,
                'message' => 'Order status updated to Lost successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to update order status to Lost',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
