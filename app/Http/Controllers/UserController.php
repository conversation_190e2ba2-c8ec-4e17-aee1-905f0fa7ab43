<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Requests\UserRequest;
use Illuminate\Support\Facades\Hash;
use Illuminate\Auth\Events\Registered;
use Yajra\DataTables\Facades\DataTables;
use Laravel\Fortify\Contracts\CreatesNewUsers;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(request()->ajax())
        {
            $query = User::query();
            return DataTables::of($query)
                ->editColumn('roles', function($item){
                    return $item->roles ? '<span class="badge badge-secondary">'.$item->roles.'</span>' : '';
                })
                ->addColumn('action', function($item){
                    // return '
                    // <div class="row">
                    // <div class="col-md-6">
                    // <a class="btn btn-sm btn-primary w-100 text-white edit-user" data-id="'.$item->id.'" href="javascript:void(0)">Edit</a>
                    // </div>
                    // <div class="col-md-6">
                    // <form class="" action="'.route('user.destroy', $item->id).'"method="POST">
                    // <button class="btn btn-sm btn-danger w-100 text-white delete-link">
                    // Delete
                    // </button>'.method_field('delete'). csrf_field().'</form>
                    // </div>
                    // </div>
                    // ';
                    return '
                    <div class="row">
                        <div class="col-md-6">
                            <a class="btn btn-sm btn-primary w-100 text-white edit-user" data-id="'.$item->id.'" href="javascript:void(0)">Edit</a>
                        </div>
                        <div class="col-md-6">
                            <a href="javascript:void(0)" class="btn btn-sm btn-danger w-100 text-white delete-link" data-id-destroy="'.$item->id.'" onclick="deleteConfirmation('.$item->id.')">Delete</a>
                        </div>
                    </div>
                    ';
                })
                ->rawColumns(['action','roles'])
                ->make();

        }
        return view('pages.superadmin.user.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('pages.dashboard.user.register');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request,
    CreatesNewUsers $creator)
    {
        event(new Registered($user = $creator->create($request->all())));

        return redirect()->route('user.index');
        // return response()->json($creator);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $user = User::where('id',$id)->first();
        return response()->json($user);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(User $user)
    {
        return view('pages.superadmin.user.edit',[
            'item' => $user
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // $data = $request->all();
        // // $data['name'] = $request->name_edit;
        // // $data['email'] = $request->email_edit;

        // $user->update($data);

        $query = User::where('id', $id)->first();

        if($query){
            $query->update(['name' => $request->name_edit,
                            'email' => $request->email_edit,
                            'roles' => $request->roles_edit,
                            'password' => Hash::make($request->password_edit)]);
        }

        return redirect()->route('user.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // $user->delete();
        User::find($id)->delete($id);
        // return redirect()->route('user.index');
        return response()->json([
            'success' => 'Record deleted successfully!'
        ]);
    }
}
