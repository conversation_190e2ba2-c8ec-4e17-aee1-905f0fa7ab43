<?php

namespace App\Http\Controllers;

use App\Models\Invoice;
use App\Models\InvoiceDetail;
use App\Models\Order;
use App\Models\Produksi;
use App\Models\SalesTarget;
use App\Models\SurveyLink;
use App\Models\KategoriMasalah;
use App\Models\User;
use App\Models\Grading;
use App\Models\SurveyResponses;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\Facades\DataTables;

class ReportController extends Controller
{
    public function index(Request $request)
    {
        return view('pages.report.index');
    }

    public function forecast(Request $request)
    {
        $forecast_week = Carbon::now()->weekOfMonth;
        $forecast_month = Carbon::now()->format('m');
        $forecast_year = Carbon::now()->year;
        
        $total_orders_forecast = Order::where('is_forecast', 1)
            ->where('forecast_week', $forecast_week)
            ->where('forecast_month', $forecast_month)
            ->where('forecast_year', $forecast_year)
            ->count();

        $total_expected_revenue_forecast = Order::where('is_forecast', 1)
            ->where('forecast_week', $forecast_week)
            ->where('forecast_month', $forecast_month)
            ->where('forecast_year', $forecast_year)
            ->sum('expected_revenue');
        
        $total_orders_projection = Order::where('is_forecast', 1)
            ->where('status_deal', 'Deal')
            ->where('forecast_week', $forecast_week)
            ->where('forecast_month', $forecast_month)
            ->where('forecast_year', $forecast_year)
            ->count();

        $total_expected_revenue_projection = Order::where('is_forecast', 1)
            ->where('status_deal', 'Deal')
            ->where('forecast_week', $forecast_week)
            ->where('forecast_month', $forecast_month)
            ->where('forecast_year', $forecast_year)
            ->sum('expected_revenue');

        // Pass the data to the view
        $data = [
            'forecast' => [
                'total_orders' => $total_orders_forecast,
                'total_expected_revenue' => $total_expected_revenue_forecast
            ],
            'projection' => [
                'total_orders' => $total_orders_projection,
                'total_expected_revenue' => $total_expected_revenue_projection
            ]
        ];
        
        return view('pages.report.forecast', $data);
    }

    public function getDataForecast(Request $request)
    {
        if(request()->ajax())
        {
            $query = Order::with(['tb_customer','user'])->where('is_forecast', 1)
                ->select('tb_orders.*', DB::RAW('SUM(tb_orders.expected_revenue) as total_expected_revenue'), DB::RAW('count(tb_orders.id_order) as total_order'))
                ->join('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
                ->where('tb_orders.is_forecast', 1)
                ->orderBy('forecast_year', 'desc')
                ->orderBy('forecast_week', 'desc')
                ->groupBy('tb_orders.forecast_year', 'tb_orders.forecast_week', 'tb_orders.forecast_month', 'tb_orders.id_customer');
            if ($request->has('start_date') && $request->has('end_date')) {
                $start_date = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
                $end_date = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
                $query->whereBetween('tb_orders.created_at', [$start_date, $end_date]);
            }
            if ($request->has('id_customer')) {
                $query->where('tb_orders.id_customer', $request->id_customer);
            }
            if ($request->has('forecast_month')) {
                $query->where('tb_orders.forecast_month', $request->forecast_month);
            }
            if ($request->has('forecast_year')) {
                $query->where('tb_orders.forecast_year', $request->forecast_year);
            }
            if ($request->has('forecast_week')) {
                $query->where('tb_orders.forecast_week', $request->forecast_week);
            }
            return DataTables::of($query)
                ->editColumn('forecast_week', function ($row) {
                    return $row->forecast_week ? 'Week '.$row->forecast_week : '-';
                })
                ->editColumn('forecast_month', function ($row) {
                    if ($row->forecast_month) {
                        return getMonth($row->forecast_month);
                    } else {
                        return '-';
                    }
                })
                ->addColumn('total_order', function ($row) {
                    return $row->total_order;
                })
                ->addColumn('total_expected_revenue', function ($row) {
                    return 'Rp. '.number_format($row->total_expected_revenue, 0, ',', '.');
                })
                ->rawColumns(['total_expected_revenue','total_order'])
                ->make();
                
        }
        return view('pages.report.forecast');
    }

    public function downloadExcelForecast(Request $request)
    {
        $fileName = 'forecast_report_' . Carbon::now()->format('Ymd_His') . '.xlsx';
        $query = Order::with(['tb_customer','user'])
            ->select('tb_orders.*', DB::RAW('SUM(tb_orders.expected_revenue) as total_expected_revenue'), DB::RAW('count(tb_orders.id_order) as total_order'))
            ->join('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('tb_orders.is_forecast', 1);

        if ($request->has('week') && $request->week != '') {
            $query->where('tb_orders.forecast_week', $request->week);
        }
        if ($request->has('month') && $request->month != '') {
            $query->where('tb_orders.forecast_month', $request->month);
        }
        if ($request->has('year') && $request->year != '') {
            $query->where('tb_orders.forecast_year', $request->year);
        }

        $query->orderBy('forecast_year', 'desc')
            ->orderBy('forecast_week', 'desc')
            ->groupBy('tb_orders.forecast_year', 'tb_orders.forecast_week', 'tb_orders.forecast_month', 'tb_orders.id_customer');

        $data = $query->get();
        
        $exportData = $data->map(function ($row) {
            return [
                'Customer' => $row->tb_customer->nama ?? '-',
                'Company' => $row->tb_customer->nama_instansi ?? '-',
                'Status Deal' => $row->status_deal ?? '-',
                'Status Order' => $row->status_order ?? '-',
                'PIC' => $row->user->name ?? '-',
                'Forecast Year' => $row->forecast_year,
                'Forecast Week' => $row->forecast_week ? 'Week ' . $row->forecast_week : '-',
                'Forecast Month' => getMonth($row->forecast_month),
                'Total Orders' => $row->total_order,
                'Total Expected Revenue' => $row->total_expected_revenue,
            ];
        });

        return \Excel::download(new \App\Exports\ForecastExport($exportData->toArray()), $fileName);
    }

    public function responses(Request $request)
    {
        // Pass the data to the view
        $data = [];
        $total_order = DB::table('tb_customers')
        ->select(['tb_customers.id_customer', 'nama', 'kode_kustomer', 'tb_orders.id_order', 'kode_order', 'sko', 'tgl_order', 'status_deal', 'tb_orders.order_key','tb_survey_link.generate_link', 'tb_survey_link.survey_id as id_survey', 'tb_survey_link.submited_date'])
        ->leftJoin('tb_orders', 'tb_customers.id_customer', '=', 'tb_orders.id_customer')
        ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
        ->leftJoin('tb_survey_link', 'tb_orders.id_order', '=', 'tb_survey_link.order_id')
        ->where('status_deal', 'like', 'Deal')
        ->where('tb_orders.flag_dummy', '=', 'Produksi Massal')
        ->where('tb_orders.status_order', '=', 'Selesai Produksi')->get()->count();
        
        $surveyLinkQuery = SurveyLink::join('tb_orders', 'tb_survey_link.order_id', '=', 'tb_orders.id_order')
            ->where('tb_orders.flag_dummy', '=', 'Produksi Massal');

        $total_generate_link = (clone $surveyLinkQuery)->count();

        $total_submited = (clone $surveyLinkQuery)
            ->whereNotNull('submited_date')
            ->count();

        $total_responses = SurveyResponses::select('question_text', DB::raw('COUNT(*) as total_count'), DB::raw('SUM(bobot) as total_sum'))
            ->groupBy('question_text')
            ->orderBy('total_sum', 'desc')
            ->get();
        
        $data = [
            "total_order" => $total_order??0,
            "total_generated_link" => $total_generate_link??0,
            "total_submited" => $total_submited??0,
            "data_responses" => $total_responses
        ];
        return view('pages.report.responses', $data);
    }

    public function tracker(Request $request)
    {
        $data = [];
        return view('pages.report.tracker', $data);
    }

    public function trackingOrder(Request $request)
    {
        $data = [];
        $per_page = 10;
        if ($request->has('per_page')) {
            $per_page = $request->input('per_page');
        }
        $orders = Order::with('tb_customer','tb_spks','tb_faw','tb_surat_jalan_detail','tb_surat_jalan_detail.tb_surat_jalan','tb_produksi','invoice_details','invoice_details.invoices')
        ->where('status_deal', 'Deal');
        if ($request->has('kode_order')) {
            $kode_order = $request->input('kode_order');
            $orders->where(function($query) use ($kode_order) {
                $query->where('sko', 'LIKE', '%'.$kode_order.'%')
                      ->orWhereHas('tb_produksi', function($a) use ($kode_order) {
                          $a->where('nama_produk', 'LIKE', '%'.$kode_order.'%');
                      })
                      ->orWhereHas('tb_customer', function($q) use ($kode_order) {
                          $q->where('nama', 'LIKE', '%'.$kode_order.'%');
                      });
            });
        }
        
        if ($request->has('tgl_deadline')) {
            if (null !== $request->input('tgl_deadline')) {
                $orders->whereHas('tb_faw', function($q) use ($request) {
                    $q->where('tgl_deadline', 'LIKE', '%'.$request->input('tgl_deadline').'%');
                });
            }
        }

        if ($request->has('start_tgl_order') && $request->has('end_tgl_order')) {
            $orders->whereBetween('tgl_order', [$request->input('start_tgl_order'), $request->input('end_tgl_order')]);
        }
        $orders = $orders->paginate($per_page);
        $kategori_masalah = KategoriMasalah::select('kategori_masalah')->get();
        $data['kategori_masalah'] = $orders;
        $data['orders'] = $orders;
        $data['per_page'] = $per_page;
        return view('pages.report.trackingOrder', $data);
    }

    public function target(Request $request)
    {
        $data = [];
        return view('pages.report.target.index', $data);
    }

    public function getSales(Request $request)
    {
        if(request()->ajax())
        {
            $query = User::where('roles', 'SALES');
            return DataTables::of($query)
                ->editColumn('roles', function($item){
                    return $item->roles ? '<span class="badge badge-secondary">'.$item->roles.'</span>' : '';
                })
                ->editColumn('status', function($item){
                    return $item->status ? '<span class="badge badge-success">Aktif</span>' : '<span class="badge badge-danger">Tidak Aktif</span>';
                })
                ->addColumn('action', function($item){
                    return '
                    <div class="row">
                        <div class="col-md-6">
                            <a class="btn btn-sm btn-info w-100 text-white" target="_blank" href="'.route('report.target.targetSales', ["id" => $item->id]).'">Target</a>
                        </div>
                        <div class="col-md-6">
                            <a class="btn btn-sm btn-primary w-100 text-white edit-user" data-id="'.$item->id.'" href="javascript:void(0)">Edit</a>
                        </div>
                    </div>
                    ';
                })
                ->rawColumns(['action','roles','status'])
                ->make();

        }
        return view('pages.report.target.getSales');
    }

    public function targetSales(Request $request, $id)
    {
        if(request()->ajax())
        {
            $query = SalesTarget::with(['users'])
                ->select('tb_target_sales.*')
                ->where('user_id', $id);

            if ($request->has('month') && !is_null($request->month)) {
                $query->where('month', '=', $request->month);
            }
            if ($request->has('year') && !is_null($request->year)) {
                $query->where('year', '=', $request->year);
            }

            $query->groupBy('tb_target_sales.user_id', 'tb_target_sales.month', 'tb_target_sales.year');

            $results = $query->get();
            
            return DataTables::of($results)
                ->addColumn('action', function($item){
                    return '<div class="d-flex g-3">
                        <a class="btn btn-sm btn-primary me-3" href="'.route('report.target.editTarget', ["id" => $item->id]).'">Edit</a>
                        <a href="javascript:void(0)" class="btn btn-sm btn-danger text-white delete-link" data-id-destroy="'.$item->id.'" onclick="deleteConfirmation('.$item->id.')">Delete</a>
                    </div>
                    ';
                })
                ->editColumn('month', function($item){
                    return getMonth($item->month);
                })
                ->editColumn('target_revenue', function($item){
                    return 'Rp '.number_format($item->target_revenue, 0, ',', '.');
                })
                ->editColumn('target_day', function($item){
                    return 'Rp '.number_format($item->target_day, 0, ',', '.');
                })
                ->rawColumns(['action'])
                ->make();
        }
        $data['sales'] = User::findOrFail($id);
        return view('pages.report.target.targetSales', $data);
    }

    public function createTarget(Request $request, $sales_id = null)
    {
        $data = [];
        $sales = User::findOrFail($sales_id);
        $data['sales'] = $sales;
        return view('pages.report.target.create-edit', $data);
    }

    public function editTarget(Request $request, $id = null)
    {
        $data = [];
        $target = SalesTarget::with('users')->findOrFail($id);
        $data['sales'] = $target->users;
        $data['target'] = $target;
        return view('pages.report.target.create-edit', $data);
    }

    public function storeTarget(Request $request)
    {
        try {
            DB::beginTransaction();

            $data = $request->only(['user_id', 'month', 'year','days', 'target_revenue', 'target_day']);
            $target_revenue = str_replace('.', '', $request->input('target_revenue') ?? 0);
            $target_day = str_replace('.', '', $request->input('target_day') ?? 0);

            $data['target_revenue'] = $target_revenue;
            $data['target_day'] = $target_day;  

            SalesTarget::updateOrCreate(
                ['id' => $request->input('id')],
                $data
            );

            DB::commit();
            return redirect()->route('report.target.targetSales', ['id' => $request->input('user_id')])
                ->with('success', 'Target sales saved successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->route('report.target.targetSales', ['id' => $request->input('user_id')])
                ->with('error', 'Failed to save target sales.');
        }
    }

    public function updateSales(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $data = [];

        // Handle profile photo upload
        if ($request->hasFile('photo_edit')) {
            $file = $request->file('photo_edit'); 
            $filenameSimpanSign = upload_file($file, 'photo-profile', 'pp');
        } else {
            $filenameSimpanSign = '';
        }

        $data['profile_photo_path'] = $filenameSimpanSign;

        if ($request->has('status_edit')) {
            $data['status'] = $request->input('status_edit');
        }
        
        $user->update($data);

        return response()->json(['success' => true, 'message' => 'Sales data updated successfully.']);
    }

    public function deleteTarget($id)
    {
        try {
            DB::transaction(function () use ($id) {
                $target_sales = SalesTarget::findOrFail($id);
                $target_sales->delete();
            });

            return response()->json([
                'status' => true,
                'message' => 'Target sales deleted successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to delete Target sales: ' . $e->getMessage(),
            ]);
        }
    }

    public function achievement(Request $request)
    {
        $data = [];
        return view('pages.report.target.achievement', $data);
    }

    public function getDataAchievement(Request $request)
    {
        $data = [];
        $month = $request->input('month');
        $year = $request->input('year');
        $query = SalesTarget::with('users')
            ->select('tb_target_sales.*', DB::raw('IFNULL(tb_target_sales.target_revenue / NULLIF(tb_target_sales.days, 0), 0) as avg_revenue_per_day'))
            ->where('month', $month)
            ->where('year', $year);
        $query->groupBy('tb_target_sales.user_id', 'tb_target_sales.month', 'tb_target_sales.year');
        $results = $query->get();
        
        $startDate = Carbon::createFromDate($year, $month, 1)->startOfMonth()->format('Y-m-d');
        $endDate = Carbon::createFromDate($year, $month, 1)->endOfMonth()->format('Y-m-d');

        // Get total todays in the month
        $totalTodays = Order::with(['tb_produksi','user'])
            ->select(
            'tb_orders.*',
            'tb_orders.id_pic',
            DB::raw('SUM(tb_produksis.total_harga) as total_per_today'),
            DB::raw('COUNT(tb_orders.id_order) as total_count')
            )
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->whereBetween('tb_orders.tgl_order', [$startDate, date('Y-m-d')])
            ->where('tb_orders.status_deal', 'Deal')
            ->where('forecast_month', $month)
            ->where('forecast_year', $year)
            ->groupBy('tb_orders.id_pic')
            ->get()
            ->keyBy('id_pic');
        
        // Get total invoice per sales (user_id) for the given month/year
        $invoices = Order::with(['tb_produksi','user'])
            ->select(
            'tb_orders.*',
            'tb_orders.id_pic',
            DB::raw('SUM(tb_produksis.total_harga) as total_invoice'),
            DB::raw('COUNT(tb_orders.id_order) as total_count')
            )
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->whereBetween('tb_orders.tgl_order', [$startDate, $endDate])
            ->where('tb_orders.status_deal', 'Deal')
            ->where('forecast_month', $month)
            ->where('forecast_year', $year)
            ->groupBy('tb_orders.id_pic')
            ->get()
            ->keyBy('id_pic');
        $results->each(function ($item) use ($invoices,$totalTodays) {
            $invoiceData = $invoices->get($item->user_id);
            $totalperToday = $totalTodays->get($item->user_id);
            $item->total_invoice = $invoiceData->total_invoice ?? 0;
            $item->total_count = $invoiceData->total_count ?? 0;
            $item->total_per_today = $totalperToday->total_per_today ?? 0;
            if ($item->target_revenue > 0) {
            $item->achievement = ($item->total_invoice / $item->target_revenue) * 100;
            } else {
            $item->achievement = 0;
            }
        });
        $results = $results->sortByDesc('achievement')->values()->all();
        $results = collect($results)->map(function ($item) {
            return [
                'user_id' => $item->user_id,
                'photo_url' => $item->users->profile_photo_path ?? '-',
                'name' => $item->users->name ?? '-',
                'month' => getMonth($item->month),
                'year' => $item->year,
                'original_total_invoice' => $item->total_invoice ?? 0,
                'days' => $item->days,
                'target_revenue_formatted' => 'Rp. '.number_format($item->target_revenue, 0, ',', '.'),
                'avg_revenue_per_day_formatted' => 'Rp. '.number_format($item->avg_revenue_per_day, 0, ',', '.'),
                'total_invoice_formatted' => 'Rp. '.number_format($item->total_invoice, 0, ',', '.'),
                'percentage_achievement_formatted' => number_format($item->achievement, 2).' %',
                'total_per_today_formatted' => 'Rp. '.number_format($item->total_per_today, 0, ',', '.'),
                'target_revenue' => $item->target_revenue,
                'avg_revenue_per_day' => $item->avg_revenue_per_day,
                'total_invoice' => $item->total_invoice,
                'percentage_achievement' => number_format($item->achievement, 2),
                'total_per_today' => $item->total_per_today
            ];
        })->where('target_revenue', '>', 0)->toArray();

        $data['targets'] = $results;
        $data['invoices'] = $invoices;
        return view('pages.report.target.getDataAchievement', $data);
    }

    public function getDataProyeksi(Request $request)
    {
        $proyeksi = [];
        $query = Order::with(['tb_customer','user'])
            ->select('tb_orders.*', DB::RAW('SUM(tb_orders.expected_revenue) as total_expected_revenue'), DB::RAW('count(tb_orders.id_order) as total_order'))
            ->join('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('tb_orders.is_forecast', 1)
            ->whereIn('tb_orders.status_order', ['Ruby', 'Platinum', 'Diamond'])
            ->orderBy('forecast_year', 'desc')
            ->orderBy('forecast_week', 'desc')
            ->groupBy('tb_orders.forecast_year', 'tb_orders.forecast_week', 'tb_orders.forecast_month', 'tb_orders.id_customer');

        if ($request->has('start_date') && $request->has('end_date')) {
            $start_date = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
            $end_date = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            $query->whereBetween('tb_orders.created_at', [$start_date, $end_date]);
        }

        if ($request->has('month') && !is_null($request->month)) {
            $query->where('tb_orders.forecast_month', $request->month);
        }
        if ($request->has('year') && !is_null($request->year)) {
            $query->where('tb_orders.forecast_year', $request->year);
        }
        $proyeksi = $query->get();
        
        return view('pages.report.target.getDataProyeksi', compact('proyeksi'));
    }

    public function getDataLost(Request $request)
    {
        $month = $request->input('month');
        $year = $request->input('year');
        $proyeksi = [];
        $query = Order::with(['tb_customer','user'])
            ->select('tb_orders.*', DB::RAW('SUM(tb_orders.expected_revenue) as total_expected_revenue'), DB::RAW('count(tb_orders.id_order) as total_order'))
            ->join('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('tb_orders.status_deal', 'Lost')
            ->orderBy('forecast_year', 'desc')
            ->orderBy('forecast_week', 'desc')
            ->groupBy('tb_orders.forecast_year', 'tb_orders.forecast_week', 'tb_orders.forecast_month', 'tb_orders.id_customer');

        if ($request->has('month')) {
            $query->where('tb_orders.forecast_month', $month);
        }
        if ($request->has('year')) {
            $query->where('tb_orders.forecast_year', $year);
        }

        if ($request->has('start_date') && $request->has('end_date')) {
            $start_date = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
            $end_date = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            $query->whereBetween('tb_orders.created_at', [$start_date, $end_date]);
        }

        if ($request->has('forecast_month') && !is_null($request->forecast_month)) {
            $query->where('tb_orders.forecast_month', $request->forecast_month);
        }
        if ($request->has('forecast_year') && !is_null($request->forecast_year)) {
            $query->where('tb_orders.forecast_year', $request->forecast_year);
        }
        if ($request->has('forecast_week') && !is_null($request->forecast_week)) {
            $query->where('tb_orders.forecast_week', $request->forecast_week);
        }
        $data = $query->get();
        
        return view('pages.report.target.getDataLost', compact('data'));
    }

    public function grading(Request $request)
    {
        // Pass the data to the view
        $data = [];
        return view('pages.report.grading', $data);
    }

    public function getDataGrading(Request $request)
    {
        if(request()->ajax())
        {
            $orders = DB::table('tb_orders')
                ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
                ->leftJoin('tb_company', 'tb_company.id', '=', 'tb_customers.company_id')
                ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
                ->leftJoin('tb_kode_orders', 'tb_orders.order_Key', '=', 'tb_kode_orders.order_key')
                ->select([
                    'tb_orders.id_order',
                    'tb_orders.tgl_order',
                    'tb_kode_orders.kode_order',
                    'tb_orders.sko',
                    'tb_orders.tgl_jatuh_tempo',
                    'tb_orders.status_deal',
                    'tb_orders.status_order',
                    'tb_produksis.total_harga',
                    'tb_customers.nama as customer_name',
                    'tb_customers.id_customer',
                    'tb_company.name as company_name',
                    'tb_company.id as company_id'
                ])
                ->where('tb_orders.status_deal', 'Deal')
                ->where('tb_orders.flag_dummy', 'Produksi Massal')
                ->orderBy('tb_orders.tgl_order', 'DESC')
                ->get()
                ->groupBy('company_name');

            // Add total order and sum of total_harga for each customer
            $orders = $orders->map(function ($ordersGroup, $company_name) {
                $sum_total_harga = $ordersGroup->sum(function ($order) {
                    return (float) $order->total_harga;
                });
                return [
                    'company_name' => $company_name??'-',
                    'total_order' => $ordersGroup->count(),
                    'sum_total_harga' => $sum_total_harga,
                ];
            })->sortByDesc('sum_total_harga')->values();
            return DataTables::of($orders)
                ->addColumn('company_name', function ($row) {
                    return $row['company_name'] ?? '-';
                })
                ->addColumn('total_order', function ($row) {
                    return number_format($row['total_order'], 0, ',', '.');
                })
                ->addColumn('total_revenue', function ($row) {
                    return 'Rp. ' . number_format($row['sum_total_harga'], 0, ',', '.');
                })
                ->addColumn('grading', function ($row) {
                    $grading = Grading::select('grading')->where('minimum_omzet', '<=', $row['sum_total_harga'])->first();
                    if ($grading) {
                        $badges = ['primary', 'success', 'info', 'warning', 'danger'];
                        $randomBadge = $badges[array_rand($badges)];
                        return '<span class="badge badge-'.$randomBadge.'">'.$grading->grading.'</span>';
                    }
                    return '-';
                })
                ->rawColumns(['total_revenue','grading'])
                ->make(true);
        }
        return view('pages.report.grading');
    }
}
