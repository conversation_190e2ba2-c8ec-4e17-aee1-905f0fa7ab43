<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Company;
use App\Models\CompanyEngagement;
use App\Models\Customer;
use Ramsey\Uuid\Uuid;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Response;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Database\Eloquent\Builder;
use App\Models\TipeInstansi;
use App\Models\Province;
use App\Models\District;
use App\Models\Subdistrict;
use App\Models\Village;
use App\Models\CompanyNote;
use App\Models\Order;
class CompanyController extends Controller
{
    public function __construct()
    {
        
    }

    public function table_company()
    {
        $query = DB::table('tb_company')->select(['tb_company.*','tb_province.name as province_name'])
        ->leftJoin('tb_province', 'tb_province.id', '=', 'tb_company.province_id')
        ->orderBy('tb_company.updated_at', 'desc');

        return DataTables::of($query)
            ->editColumn('created_at', function ($row) {
                return $row->created_at ? date('d M Y', strtotime($row->created_at)) : '';
            })
            ->editColumn('updated_at', function ($row) {
                return $row->updated_at ? date('d M Y', strtotime($row->updated_at)) : '';
            })
            ->addColumn('total_pic_customer', function ($row) {
                $count_customer = Customer::where('company_id', $row->id)->count() ?? 0;
                return $count_customer;
            })
            ->addColumn('province_name', function ($row) {
                return $row->province_name ?? '';
            })
            ->addColumn('action', function ($row) {
                return '<a class="btn btn-sm btn-my-primary text-white" data-toggle="tooltip" data-id="' . $row->id . '" href="'.route('company.edit_company', $row->id).'"><i class="fa fa-pencil text-white"></i> Edit</a><a class="btn btn-sm btn-primary ms-2 text-white" data-toggle="tooltip" data-id="' . $row->id . '" href="'.route('company.detail_company', $row->code).'"><i class="fa fa-eye text-white"></i> Detail</a>';
            })
            ->rawColumns(['province_name','action','total_pic_customer'])
            ->make(true);
    }

    public function edit_company($id) {
        try {
            $with = [];
            $company = Company::find($id);
            $tipe_instansi = TipeInstansi::select('id_instansi','tipe_instansi')->get();

            $province = [];
            $district = [];
            $subdistrict = [];
            $village = [];

            if (!empty($company->province_id)) {
                $province = Province::where('id', $company->province_id)->get();
            }
            if (!empty($company->district_id)) {
                $district = District::where('id', $company->district_id)->get();
            }
            if (!empty($company->sub_district_id)) {
                $subdistrict = Subdistrict::where('id', $company->sub_district_id)->get();
            }
            if (!empty($company->village_id)) {
                $village = Village::where('id', $company->village_id)->get();
            }

            $with['data'] = $company;
            $with['tipe_instansi'] = $tipe_instansi;
            $with['province'] = $province;
            $with['district'] = $district;
            $with['subdistrict'] = $subdistrict;
            $with['village'] = $village;
            
            return view('pages.crm.company.edit_company', $with);
        } catch (\Throwable $th) {
            abort(503);
        }
    }

    public function store_company(Request $request) {
        try {
            $input = $request->all();
            if (!isset($input['id'])) {
                $input['code'] = Uuid::uuid4()->__toString();
            }

            // Handle null string values
            $nullableFields = ['email', 'website', 'npwp', 'zip_code', 'province_id', 'district_id', 'village_id'];
            foreach ($nullableFields as $field) {
                if (!$request->has($field) || $request->$field === "null" || $request->$field === null) {
                    unset($input[$field]);
                }
            }
            Company::updateOrCreate(['id'=>$input['id']??null], $input);
            
            return response()->json(["status"=>200, "message"=>"success"],200);
        } catch (\Exception $th) {
            dd($th->getMessage());
        }
    }

    public function detail_company($id) {
        try {
            $with = [];
            $company = Company::with(["province","district","subdistrict","village"])->where('code',$id)->first();
            $with['data'] = $company;
            return view('pages.crm.company.detail_company', $with);
        } catch (\Throwable $th) {
            abort(503);
        }
    }

    public function detail(Request $request) {
        $with = [];
        $tab = explode("#",$request->tab);
        $company_id = $tab[1];
        switch ($tab[0]) {
            case 'pic-customer':
                $customer = Customer::where('company_id', $company_id)->get();
                $with['data'] = $customer;
                $with['company_id'] = $company_id;
                return view('pages.crm.company.detail.pic-customer', $with);
                break;

            case 'forecast':
                // $orders = Order::with(['tb_produksi', 'tb_customer'])
                //     ->where('status_deal', 'Deal')
                //     ->whereHas('tb_customer', function ($query) use ($company_id) {
                //         $query->where('company_id', $company_id);
                //     })
                //     ->get()
                //     ->groupBy(['tb_customer.nama']);
                $orders = DB::table('tb_orders')
                    ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
                    ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
                    ->leftJoin('tb_kode_orders', 'tb_orders.order_Key', '=', 'tb_kode_orders.order_key')
                    ->select([
                        'tb_orders.id_order',
                        'tb_orders.tgl_order',
                        'tb_kode_orders.kode_order',
                        'tb_orders.sko',
                        'tb_orders.tgl_jatuh_tempo',
                        'tb_orders.status_deal',
                        'tb_orders.status_order',
                        'tb_produksis.total_harga',
                        'tb_customers.nama as customer_name',
                        'tb_customers.id_customer'
                    ])
                    ->where('tb_orders.status_deal', 'Deal')
                    ->where('tb_customers.company_id', $company_id)
                    ->orderBy('tb_orders.tgl_order', 'DESC')
                    ->get()
                    ->groupBy('customer_name');

                // Add total order and sum of total_harga for each customer
                $orders = $orders->map(function ($ordersGroup, $customerName) {
                    $sum_total_harga = $ordersGroup->sum(function ($order) {
                        return (float) $order->total_harga;
                    });
                    $id_customer = $ordersGroup->first()->id_customer ?? null;
                    return [
                        'customer_id' => $id_customer,
                        'customer_name' => $customerName,
                        'orders' => $ordersGroup,
                        'total_order' => $ordersGroup->count(),
                        'sum_total_harga' => $sum_total_harga,
                    ];
                });
                
                $with['data'] = $orders;
                $with['company_id'] = $company_id;
                return view('pages.crm.company.detail.forecast', $with);
                break;

            case 'engagement':
                $engagement = CompanyEngagement::with('user')->where('company_id', $company_id)->orderBy('due_date', 'asc')->get();
                $with['data'] = $engagement;
                $with['company_id'] = $company_id;
                return view('pages.crm.company.detail.engagement', $with);
                break;
            
            default:
                $notes = CompanyNote::where('company_id', $company_id)->orderBy('updated_at', 'desc')->get();
                $with['data'] = $notes;
                $with['company_id'] = $company_id;
                return view('pages.crm.company.detail.journey', $with);
                break;
        }
    }

    public function add_note(Request $request) {
        try {
            $input = $request->all();
            CompanyNote::create([
                'company_id'=>$input['company_id']??null,
                'message'=>$input['message']
            ]);
            
            return response()->json(["status"=>200, "message"=>"success"],200);
        } catch (\Exception $th) {
            dd($th->getMessage());
        }
    }

    public function getCustomer(Request $request) {
        try {
            $input = $request->all();

            $data = Customer::select(['id_customer as id','nama', 'no_hp'])->whereNull('company_id');
            if (!empty($input['q'])) {
                $searchTerm = $input['q'];

                // Add the grouped OR conditions for 'nama' and 'no_hp'
                $data->where(function (Builder $query) use ($searchTerm) {
                    $query->where('nama', 'LIKE', "%{$searchTerm}%")
                        ->orWhere('no_hp', 'LIKE', "%{$searchTerm}%");
                });
            }

            $data = $data->get();

            $list_customer = [];

            if (count($data) > 0) {
                foreach ($data as $row) {
                    $list_customer[] = array(
                        "id" => $row->id,
                        "name" => $row->nama.' ('.$row->no_hp.')'
                    );
                }
            }
            return Response::json($list_customer, 200);
        } catch (\Exception $th) {
            dd($th->getMessage());
        }
    }

    public function add_pic_cust(Request $request) {
        try {
            $input = $request->all();
            
            $company = Company::find($input['company_id']);
            Customer::where('id_customer', $input['customer_id'])->update([
                'company_id' => $input['company_id'],
                'nama_instansi' => $company->name,
                'jenis_usaha' => $company->business_sector,
                'alamat_instansi' => $company->address,
                'npwp' => $company->npwp
            ]);
            
            return response()->json(["status"=>200, "message"=>"success"],200);
        } catch (\Exception $th) {
            return response()->json(["status"=>500, "message"=>"failed"],500);
        }
    }

    public function remove_pic_cust(Request $request) {
        try {
            $input = $request->all();
            
            Customer::where('id_customer', $input['id_customer'])->update([
                'company_id' => NULL
            ]);
            
            return response()->json(["status"=>200, "message"=>"success"],200);
        } catch (\Exception $th) {
            return response()->json(["status"=>500, "message"=>"failed"],500);
        }
    }

    public function add_engagement(Request $request) {
        try {
            $input = $request->all();
            // dd($input);
            CompanyEngagement::create([
                'company_id'=>$input['company_id']??null,
                'type'=>$input['type'],
                'title'=>$input['title'],
                'due_date'=>$input['due_date'],
                'detail'=>$input['detail']
            ]);
            
            return response()->json(["status"=>200, "message"=>"success"],200);
        } catch (\Exception $th) {
            dd($th->getMessage());
        }
    }

    public function existCompany(Request $request) {
        try {
            $input = $request->all();
            $company = Company::whereRaw('LOWER(name) = ?', [strtolower($input['name'])])->first();
            if ($company) {
                return response()->json(["status"=>200, "message"=>"exist", "data"=>$company],200);
            } else {
                return response()->json(["status"=>200, "message"=>"not found"],200);
            }
        } catch (\Exception $th) {
            dd($th->getMessage());
        }
    }
}
