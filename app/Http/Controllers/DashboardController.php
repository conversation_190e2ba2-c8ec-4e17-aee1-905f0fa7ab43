<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Spk;
use App\Models\Lost;
use App\Models\User;
use App\Models\Order;
use App\Models\Sumber;
use App\Models\Vendor;
use App\Models\Produksi;
use App\Models\TipeProduk;
use App\Models\JenisKertas;
use App\Models\TipeInstansi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;
use Yajra\DataTables\Facades\DataTables;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        if (Auth::user()->roles == 'SALES' || Auth::user()->roles == 'SALES SPV') {
            return $this->sales_report();
        } elseif (Auth::user()->roles == 'PRODUKSI' || Auth::user()->roles == 'PRODUKSI SPV') {
            return $this->production_report();
        } else {
            return $this->superadmin_report();
            // return view('pages.index');
        }
    }

    public function sales_report()
    {
        $pic_sales = User::where('roles', 'SALES')->orWhere('roles', 'SALES SPV')->get()->toArray();

        $lost = Lost::orderBy('lost', 'ASC')->get('lost');
        $sumber = Sumber::orderBy('sumber', 'ASC')->get('sumber');
        $pic_list = User::where('roles', 'SALES')->orWhere('roles', 'SALES SPV')->get()->toArray();
        $instansi = TipeInstansi::orderBy('tipe_instansi', 'ASC')->get('tipe_instansi');
        $tipe_produk = TipeProduk::all()->toArray();
        $this_year = date('Y');
        $this_month = date('m');
        $this_month_l = date('n');
        $last_month = date('m', strtotime('-1 month'));
        $last_2month = date('m', strtotime('-2 month'));

        $logged_id = Auth::user()->id;
        $bulan_ke = array("", "target_01", "target_02", "target_03", "target_04", "target_05", "target_06", "target_07", "target_08", "target_09", "target_10", "target_11", "target_12");
        $this_month_target = $bulan_ke[$this_month_l];

        $total_target = DB::table('tb_targets')
            ->where('year_target', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->sum($this_month_target);

        $total_realisasi = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->sum('total_harga');

        $omzet_deal = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->where('flag_dummy', 'Produksi Massal')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->sum('total_harga');

        $modal_sales = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->sum('modal_sales');

        $omzet_repeat = Order::leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '=', 'Produksi Massal')
            ->where('sumber', 'Repeat Order')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->sum('total_harga');

        $omzet_other = Order::leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '=', 'Produksi Massal')
            ->whereNotIn('sumber', ['Repeat Order', 'Online', 'Online Lintas'])
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } 
            })
            ->sum('total_harga');

        $potensi_all = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'LIKE', '%pack%')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            // ->where(function($query) use ($this_month, $this_year) {
            //     $query->where(function ($subquery) use ($this_month, $this_year) {
            //         $subquery->where('tgl_order', '!=', '')
            //                 ->whereMonth('tgl_order', $this_month)
            //                 ->whereYear('tgl_order', $this_year);
            //     })
            //     ->orWhere(function ($subquery) use ($this_month, $this_year) {
            //         $subquery->whereMonth('waktu_kontak', $this_month)
            //                 ->whereYear('waktu_kontak', $this_year);
            //     });
            // })
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->sum('total_harga');

        $potensi_followup = Order::leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', '=', 'Follow Up')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->sum('total_harga');

        $potensi_lost = Order::leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', '=', 'Lost')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->sum('total_harga');

        $annual_target = DB::table('tb_targets')
            ->select(DB::raw('SUM(target_' . $last_2month . ') as last_2month, SUM(target_' . $last_month . ') as last_month, SUM(target_' . $this_month . ') as this_month'))
            ->where('year_target', $this_year)
            ->when(Auth::user()->roles == 'SALES', function ($query) {
                return $query->where('id_pic', Auth::user()->id);
            })
            ->first();
        $annual_realisasi = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select(DB::raw('SUM(
                            CASE
                                when MONTH(tgl_order) = ' . $this_month . ' then
                                total_harga
                                end
                            ) as this_month,
                            SUM(
                                CASE
                                    when MONTH(tgl_order) = ' . $last_month . ' then
                                    total_harga
                                    end
                                ) as last_month,
                            SUM(
                            CASE
                                when MONTH(tgl_order) = ' . $last_2month . ' then
                                total_harga
                                end
                            ) as last_2month'))
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereYear('tgl_order', $this_year)
            ->when(Auth::user()->roles == 'SALES', function ($query) {
                return $query->where('id_pic', Auth::user()->id);
            })
            ->first();

        $data_sumbers = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->when(Auth::user()->roles == 'SALES', function ($query) {
                return $query->where('id_pic', Auth::user()->id);
            });
        $x = 0;
        foreach ($sumber as $smb) {
            $x++;
            $data_sumbers->addSelect(DB::raw(
                'SUM(
                                            CASE
                                                when sumber = "' . $smb['sumber'] . '" then
                                                total_harga
                                                end
                                            ) as "' . $smb['sumber'] . '"'
            ));
        }
        $data_sumber = $data_sumbers->get()->toArray();

        $data_pics = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year);
        $x = 0;
        foreach ($pic_list as $pic) {
            $x++;
            $data_pics->addSelect(DB::raw(
                'SUM(
                                            CASE
                                                when id_pic = "' . $pic['id'] . '" then
                                                total_harga
                                                end
                                            ) as "' . $pic['name'] . '"'
            ));
        }
        $data_pic = $data_pics->get()->toArray();

        $data_instansis = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->when(Auth::user()->roles == 'SALES', function ($query) {
                return $query->where('id_pic', Auth::user()->id);
            });
        $x = 0;
        foreach ($instansi as $ins) {
            $x++;
            $data_instansis->addSelect(DB::raw(
                'SUM(
                                            CASE
                                                when tipe_instansi = "' . $ins['tipe_instansi'] . '" then
                                                total_harga
                                                end
                                            ) as "' . $ins['tipe_instansi'] . '"'
            ));
        }
        $data_instansi = $data_instansis->get()->toArray();

        $data_tipe_produks = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->when(Auth::user()->roles == 'SALES', function ($query) {
                return $query->where('id_pic', Auth::user()->id);
            });
        $x = 0;
        foreach ($tipe_produk as $tp) {
            $x++;
            $data_tipe_produks->addSelect(DB::raw(
                'SUM(
                                            CASE
                                                when tipe_produk = "' . $tp['tipe_produk'] . '" then
                                                total_harga
                                                end
                                            ) as "' . $tp['tipe_produk'] . '"'
            ));
        }
        $data_tipe_produk = $data_tipe_produks->get()->toArray();


        if ($total_target != 0) {
            $target_div = $total_realisasi / $total_target;
        } else {
            $target_div = 0;
        }
        $target_progress_raw = $target_div * 100;
        if ($target_progress_raw > 100) {
            $target_progress = 100;
        } else {
            $target_progress = $target_progress_raw;
        }

        $price_null = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select(['id_order', 'kode_kustomer', 'tb_kode_orders.kode_order', 'tb_orders.order_key', 'total_harga', 'tgl_order', 'waktu_kontak', 'name', 'nama', 'status_deal', 'status_order'])
            ->where('status_deal', '!=', 'Lost')
            ->where('total_harga', '<', '1')
            ->Where('status_order', '=', 'FU Emas')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_price_null = $price_null->count();

        $prioritas = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select(['id_order', 'kode_kustomer', 'tb_kode_orders.kode_order', 'tb_orders.order_key', 'total_harga', 'tgl_order', 'waktu_kontak', 'name', 'nama', 'status_deal', 'status_order'])
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                $query->where('status_order', '=', 'Ruby')
                    ->orWhere('status_order', '=', 'Diamond')
                    ->orWhere('status_order', '=', 'Platinum');

                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_prioritas = $prioritas->count();

        $omzet_deal_row = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->whereNotIn('sumber', ['Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->where('flag_dummy', 'Produksi Massal')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }

            });
        $count_omzet_deal = $omzet_deal_row->sum('total_harga');

        $potensi_all_row = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereNotIn('sumber', ['Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_potensi_all = $potensi_all_row->sum('total_harga');

        if ($count_potensi_all == 0) {
            $avg_closing_rate_per_omzet = 0;
        } else {
            $avg_closing_rate_per_omzet = $count_omzet_deal / $count_potensi_all * 100;
        }

        $konsumen_deal = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->distinct('no_hp')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_konsumen_deal = $konsumen_deal->count();

        $kontak_masuk = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->distinct('no_hp')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_kontak_masuk = $kontak_masuk->count();

        $kontak_masuk_emas = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->distinct('no_hp')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_kontak_masuk_emas = $kontak_masuk_emas->count();  
            
        if ($count_kontak_masuk == 0) {
            $avg_closing_rate_per_customer = 0;
        } else {
            $avg_closing_rate_per_customer = ($count_konsumen_deal / $count_kontak_masuk_emas) * 100;
        }

        $list_customer_repeat = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select(DB::raw("count(tb_orders.id_customer) as cro, nama, SUM(total_harga) as total_harga, sumber, SUM(modal_sales) AS total_modal_sales"))
            ->where('total_harga', '>', '0')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('status_deal', 'DEAL')
            ->groupBy('tb_orders.id_customer', 'sumber')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->orderBy('total_harga', 'DESC')
            ->get();

        // ______________________________________________FOLLOW UP___________________________________________________
        
        $data_fu = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                    ->where('status_order', 'FU');

                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_total_fu = $data_fu->count();
        $total_data_fu = $data_fu->get();
        $sum_gt_fu = $data_fu->sum('total_harga');

        $data_fu_emas = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                ->where('status_order', 'FU Emas');
                
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_total_fu_emas = $data_fu_emas->count();
        $total_data_fu_emas = $data_fu_emas->get();
        $sum_gt_emas = $data_fu_emas->sum('total_harga');

        $data_fu_bronze = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                ->where('status_order', 'Bronze');
                
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_total_fu_bronze = $data_fu_bronze->count();
        $total_data_fu_bronze = $data_fu_bronze->get();
        $sum_gt_bronze = $data_fu_bronze->sum('total_harga');

        $data_fu_platinum = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                ->where('status_order', 'Platinum');
                
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_total_fu_platinum = $data_fu_platinum->count();
        $total_data_fu_platinum = $data_fu_platinum->get();
        $sum_gt_platinum = $data_fu_platinum->sum('total_harga');

        $data_fu_ruby = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                ->where('status_order', 'Ruby');
                
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_total_fu_ruby = $data_fu_ruby->count();
        $total_data_fu_ruby = $data_fu_ruby->get();
        $sum_gt_ruby = $data_fu_ruby->sum('total_harga');

        $data_fu_diamond = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                ->where('status_order', 'Diamond');
                
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_total_fu_diamond = $data_fu_diamond->count();
        $total_data_fu_diamond = $data_fu_diamond->get();
        $sum_gt_diamond = $data_fu_diamond->sum('total_harga');

        $data_fu_deal = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Deal');
                
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_total_fu_deal = $data_fu_deal->count();
        $total_data_fu_deal = $data_fu_deal->get();
        $sum_gt_deal = $data_fu_deal->sum('total_harga');

        // _________________________________________________________________________________________________________________

        $i = 0;
        foreach ($lost as $l) {
            $i++;
            $data_lost[$i] = Order::whereNotNull('id_order')
                ->whereMonth('waktu_kontak', $this_month)
                ->whereYear('waktu_kontak', $this_year)
                ->where(function ($query) {
                    if (Auth::user()->roles == 'SALES') {
                        $query->where('id_pic', Auth::user()->id);
                    }
                });
            $data_lost[$i]->where("status_order", $l->lost);
            $data_lost[$i]->get();

            $count_data_lost[$i] = $data_lost[$i]->count();
        }

        // __________________________________________________________________________________________________________________________

        $list_lost_data = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select(['tb_customers.nama', 'status_order', 'tipe_produk', 'total_harga'])
            ->where('status_deal', 'Lost')
            ->where('total_harga', '!=', '0')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->orderBy('tb_produksis.total_harga', 'DESC');

        $list_lost = $list_lost_data->get();
        $list_lost_total = $list_lost_data->sum('total_harga');

        return view('pages.dashboard.sales', compact(
            'pic_sales',
            'lost',
            'total_target',
            'total_realisasi',
            'target_progress',
            'count_price_null',
            'count_prioritas',
            'modal_sales',
            'potensi_all',
            'omzet_deal',
            'omzet_repeat',
            'potensi_followup',
            'potensi_lost',
            'avg_closing_rate_per_omzet',
            'count_kontak_masuk',
            'count_kontak_masuk_emas',
            'avg_closing_rate_per_customer',
            'list_customer_repeat',
            'count_total_fu',
            'total_data_fu',
            'sum_gt_fu',
            'count_total_fu_emas',
            'total_data_fu_emas',
            'sum_gt_emas',
            'count_total_fu_bronze',
            'total_data_fu_bronze',
            'sum_gt_bronze',
            'count_total_fu_platinum',
            'total_data_fu_platinum',
            'sum_gt_platinum',
            'count_total_fu_ruby',
            'total_data_fu_ruby',
            'sum_gt_ruby',
            'count_total_fu_diamond',
            'total_data_fu_diamond',
            'sum_gt_diamond',
            'count_total_fu_deal',
            'total_data_fu_deal',
            'sum_gt_deal',
            'annual_target',
            'annual_realisasi',
            'count_data_lost',
            'sumber',
            'data_sumber',
            'pic_list',
            'data_pic',
            'instansi',
            'data_instansi',
            'tipe_produk',
            'data_tipe_produk',
            'list_lost',
            'list_lost_total',
            'omzet_other'
        ));
    }

    public function get_sales_report(Request $request)
    {
        $selected_pic = $request->pic_sales;
        $selected_month = $request->month;
        $selected_month_l = Carbon::createFromFormat('m', $selected_month)->month;
        $selected_year = $request->year;

        $carbonDate = Carbon::createFromFormat('!m Y', $selected_month . ' ' . now()->year);

        // dd($selected_month, Carbon::createFromFormat('m', $selected_month)->month, Carbon::createFromFormat('m', $selected_month)->englishMonth, $carbonDate->month, $carbonDate->englishMonth);

        $pic_sales = User::where('roles', 'SALES')->orWhere('roles', 'SALES SPV')->get()->toArray();

        $lost = Lost::orderBy('lost', 'ASC')->get('lost');
        // $sumber = Sumber::all()->toArray();
        $sumber = Sumber::orderBy('sumber', 'ASC')->get('sumber');
        $pic_list = User::where('roles', 'SALES')->orWhere('roles', 'SALES SPV')->get()->toArray();
        $instansi = TipeInstansi::orderBy('tipe_instansi', 'ASC')->get('tipe_instansi');
        $tipe_produk = TipeProduk::all()->toArray();
        $this_month_l = date('n');

        $timestamp = strtotime("$selected_year-$selected_month-01");
        $formattedDate = date('m Y', $timestamp);

        list($this_month, $this_year) = explode(' ', $formattedDate);
        list($last_month, $last_month_year) = explode(' ', date('m Y', strtotime('first day of last month', $timestamp)));
        list($last_2month, $last_2month_year) = explode(' ', date('m Y', strtotime('first day of -2 months', $timestamp)));

        $logged_id = Auth::user()->id;
        $bulan_ke = array("", "target_01", "target_02", "target_03", "target_04", "target_05", "target_06", "target_07", "target_08", "target_09", "target_10", "target_11", "target_12");
        $this_month_target = $bulan_ke[$selected_month_l];

        $total_target = DB::table('tb_targets')
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->where('year_target', $selected_year)
            ->sum($this_month_target);

        $total_realisasi = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->sum('total_harga');

        $omzet_deal = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->where('flag_dummy', 'Produksi Massal')
            ->where('kategori_produksi', 'LIKE', '%pack%')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->sum('total_harga');
            
        $modal_sales = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereNotIn('kategori_produksi', ['Dummy', 'PPN', 'Jasa Desain', 'Lain-lain'])
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->sum('modal_sales');

        $omzet_repeat = Order::leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '=', 'Produksi Massal')
            ->where('sumber', 'Repeat Order')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->sum('total_harga');
            
        $omzet_other = Order::leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '=', 'Produksi Massal')
            ->where('sumber', 'like', '%relasi%')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->sum('total_harga');

        $potensi_all = Order::leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            // ->where('kategori_produksi', '!=', 'Dummy')
            // ->where('kategori_produksi', '!=', 'Lain-lain')
            ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            // ->where(function($query) use ($selected_month, $selected_year) {
            //     $query->where(function ($subquery) use ($selected_month, $selected_year) {
            //         $subquery->where('status_deal', '=', 'Deal')
            //                 ->whereMonth('tgl_order', $selected_month)
            //                 ->whereYear('tgl_order', $selected_year);
            //     })
            //     ->orWhere(function ($subquery) use ($selected_month, $selected_year) {
            //         $subquery->whereMonth('waktu_kontak', $selected_month)
            //                 ->whereYear('waktu_kontak', $selected_year);
            //     });
            // })
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->sum('total_harga');

        $potensi_followup = Order::leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', '=', 'Follow Up')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->sum('total_harga');

        $potensi_lost = Order::leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', '=', 'Lost')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->sum('total_harga');

        $annual_target = DB::table('tb_targets')
            ->select(DB::raw(
                "
                SUM(CASE WHEN year_target = '$last_2month_year' THEN target_$last_2month ELSE 0 END) as last_2month,
                SUM(CASE WHEN year_target = '$last_month_year' THEN target_$last_month ELSE 0 END) as last_month,
                SUM(CASE WHEN year_target = '$this_year' THEN target_$this_month ELSE 0 END) as this_month
                "
            ))
            // ->where('year_target', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->first();

        // dd($annual_target, $this_month, $this_year, $last_month, $last_month_year, $last_2month, $last_2month_year); 
        $annual_realisasi = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select(DB::raw('SUM(
                            CASE
                                when MONTH(tgl_order) = ' . $selected_month . ' then
                                total_harga
                                end
                            ) as this_month,
                            SUM(
                                CASE
                                    when MONTH(tgl_order) = ' . $last_month . ' then
                                    total_harga
                                    end
                                ) as last_month,
                            SUM(
                            CASE
                                when MONTH(tgl_order) = ' . $last_2month . ' then
                                total_harga
                                end
                            ) as last_2month'))
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereNotIn('kategori_produksi', ['Dummy', 'PPN', 'Jasa Desain'])
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->first();

        $data_sumbers = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            });
        $x = 0;
        foreach ($sumber as $smb) {
            $x++;
            $data_sumbers->addSelect(DB::raw(
                'SUM(
                                            CASE
                                                when sumber = "' . $smb['sumber'] . '" then
                                                total_harga
                                                end
                                            ) as "' . $smb['sumber'] . '"'
            ));
        }
        $data_sumber = $data_sumbers->get()->toArray();

        $data_pics = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year);
        $x = 0;
        foreach ($pic_list as $pic) {
            $x++;
            $data_pics->addSelect(DB::raw(
                'SUM(
                                            CASE
                                                when id_pic = "' . $pic['id'] . '" then
                                                total_harga
                                                end
                                            ) as "' . $pic['name'] . '"'
            ));
        }
        $data_pic = $data_pics->get()->toArray();

        $data_instansis = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            });
        $x = 0;
        foreach ($instansi as $ins) {
            $x++;
            $data_instansis->addSelect(DB::raw(
                'SUM(
                                            CASE
                                                when tipe_instansi = "' . $ins['tipe_instansi'] . '" then
                                                total_harga
                                                end
                                            ) as "' . $ins['tipe_instansi'] . '"'
            ));
        }
        $data_instansi = $data_instansis->get()->toArray();

        $data_tipe_produks = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            });
        $x = 0;
        foreach ($tipe_produk as $tp) {
            $x++;
            $data_tipe_produks->addSelect(DB::raw(
                'SUM(
                                            CASE
                                                when tipe_produk = "' . $tp['tipe_produk'] . '" then
                                                total_harga
                                                end
                                            ) as "' . $tp['tipe_produk'] . '"'
            ));
        }
        $data_tipe_produk = $data_tipe_produks->get()->toArray();

        if ($total_target != 0) {
            $target_div = $total_realisasi / $total_target;
        } else {
            $target_div = 0;
        }
        $target_progress_raw = $target_div * 100;
        if ($target_progress_raw > 100) {
            $target_progress = 100;
        } else {
            $target_progress = $target_progress_raw;
        }

        $price_null = Order::leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select(['id_order', 'kode_kustomer', 'tb_kode_orders.kode_order', 'tb_orders.order_key', 'total_harga', 'tgl_order', 'waktu_kontak', 'name', 'nama', 'status_deal', 'status_order'])
            ->where('status_deal', '!=', 'Lost')
            ->where('total_harga', '<', '1')
            ->Where('status_order', '=', 'FU Emas')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            });
        $count_price_null = $price_null->count();

        $prioritas = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select(['id_order', 'kode_kustomer', 'tb_kode_orders.kode_order', 'tb_orders.order_key', 'total_harga', 'tgl_order', 'waktu_kontak', 'name', 'nama', 'status_deal', 'status_order'])
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) {
                $query->where('status_order', '=', 'Ruby')
                    ->orWhere('status_order', '=', 'Diamond')
                    ->orWhere('status_order', '=', 'Platinum');
            })
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            });
        $count_prioritas = $prioritas->count();

        $omzet_deal_row = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->where('flag_dummy', 'Produksi Massal')
            // ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            });
        $count_omzet_deal = $omzet_deal_row->sum('total_harga');

        $potensi_all_row = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            });
        $count_potensi_all = $potensi_all_row->sum('total_harga');

        if ($count_potensi_all == 0) {
            $avg_closing_rate_per_omzet = 0;
        } else {
            $avg_closing_rate_per_omzet = ($count_omzet_deal / $count_potensi_all) * 100;
        }
        
        $konsumen_deal = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->distinct('no_hp')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            });
        $count_konsumen_deal = $konsumen_deal->count();

        $kontak_masuk = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->distinct('no_hp')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            });
        $count_kontak_masuk = $kontak_masuk->count();

        $kontak_masuk_emas_2 = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->distinct('no_hp')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            });
        $count_kontak_masuk_emas_2 = $kontak_masuk_emas_2->count();

        if ($count_kontak_masuk == 0) {
            $avg_closing_rate_per_customer = 0;
        } else {
            $avg_closing_rate_per_customer = $count_konsumen_deal / $count_kontak_masuk_emas_2 * 100;
        }

        $kontak_masuk_emas = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->distinct('no_hp')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            });

        $count_kontak_masuk_emas = $kontak_masuk_emas->count();

        $list_customer_repeat = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select(DB::raw("count(tb_orders.id_customer) as cro, nama, SUM(total_harga) as total_harga, sumber, SUM(modal_sales) AS total_modal_sales"))
            ->where('total_harga', '>', '0')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('status_deal', 'DEAL')
            ->groupBy('tb_orders.id_customer', 'sumber')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->orderBy('total_harga', 'DESC')
            ->get();

        // _______________________________________________________FOLLOW UP___________________________________________________________
        
        $data_fu = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                    ->where('status_order', 'FU');
            })
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->orderBy('total_harga', 'DESC');
        $count_total_fu = $data_fu->count();
        $total_data_fu = $data_fu->get();
        $sum_gt_fu = $data_fu->sum('total_harga');

        $data_fu_emas = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                ->where('status_order', 'FU Emas');
            })
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->orderBy('total_harga', 'DESC');
        $count_total_fu_emas = $data_fu_emas->count();
        $total_data_fu_emas = $data_fu_emas->get();
        $sum_gt_emas = $data_fu_emas->sum('total_harga');

        $data_fu_bronze = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->where('status_order', 'Bronze')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->orderBy('total_harga', 'DESC');
        $count_total_fu_bronze = $data_fu_bronze->count();
        $total_data_fu_bronze = $data_fu_bronze->get();
        $sum_gt_bronze = $data_fu_bronze->sum('total_harga');

        $data_fu_platinum = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                ->where('status_order', 'Platinum');
            })
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->orderBy('total_harga', 'DESC');
        $count_total_fu_platinum = $data_fu_platinum->count();
        $total_data_fu_platinum = $data_fu_platinum->get();
        $sum_gt_platinum = $data_fu_platinum->sum('total_harga');

        $data_fu_ruby = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                ->where('status_order', 'Ruby');
            })
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->orderBy('total_harga', 'DESC');
        $count_total_fu_ruby = $data_fu_ruby->count();
        $total_data_fu_ruby = $data_fu_ruby->get();
        $sum_gt_ruby = $data_fu_ruby->sum('total_harga');

        $data_fu_diamond = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                ->where('status_order', 'Diamond');
            })
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->orderBy('total_harga', 'DESC');
        $count_total_fu_diamond = $data_fu_diamond->count();
        $total_data_fu_diamond = $data_fu_diamond->get();
        $sum_gt_diamond = $data_fu_diamond->sum('total_harga');
        
        if ($selected_year > 2022) {
            $data_fu_deal = DB::table('tb_orders')
                ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
                ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
                ->where('status_deal', 'Deal')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
                ->whereMonth('tgl_order', $selected_month)
                ->whereYear('tgl_order', $selected_year)
                ->where(function ($query) {
                    $query->where('status_deal', 'Deal');
                })
                ->where(function ($query) use ($selected_pic) {
                    if (Auth::user()->roles == 'SALES') {
                        $query->where('id_pic', Auth::user()->id);
                    } else {
                        if ($selected_pic != 'All') {
                            $query->where('id_pic', $selected_pic);
                        }
                    }
                })
                ->orderBy('total_harga', 'DESC');
        } else {
            $data_fu_deal = DB::table('tb_orders')
                ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
                ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
                ->select('nama', 'total_harga')
                ->where('status_deal', 'Deal')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('notes', 'NOT LIKE', '%jasa desain%')
                ->where('kategori_produksi', 'like', '%pack%')
                ->whereMonth('tgl_order', $selected_month)
                ->whereYear('tgl_order', $selected_year)
                ->where(function ($query) {
                    $query->where('status_deal', 'Deal');
                })
                ->where(function ($query) use ($selected_pic) {
                    if (Auth::user()->roles == 'SALES') {
                        $query->where('id_pic', Auth::user()->id);
                    } else {
                        if ($selected_pic != 'All') {
                            $query->where('id_pic', $selected_pic);
                        }
                    }
                })
                ->orderBy('total_harga', 'DESC');
        }
        $count_total_fu_deal = $data_fu_deal->count();
        $total_data_fu_deal = $data_fu_deal->get();
        $sum_gt_deal = $data_fu_deal->sum('total_harga');

        // _________________________________________________________________________________________________________________

        $data_losts = Order::whereNotNull('id_order')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            });
        $dl = 0;
        foreach ($lost as $l) {
            $dl++;
            $data_losts->addSelect(DB::raw(
                'COUNT(
                                                CASE
                                                    when status_order = "' . $l['lost'] . '" then
                                                    id_order
                                                    end
                                                ) as "' . $dl . '"'
            ));
        }
        $data_lost = $data_losts->get()->toArray();
        // __________________________________________________________________________________________________________________________

        $list_lost_data = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select(['tb_customers.nama', 'status_order', 'tipe_produk', 'total_harga'])
            ->where('status_deal', 'Lost')
            ->where('total_harga', '!=', '0')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->orderBy('tb_orders.updated_at', 'DESC');
        
        $list_lost = $list_lost_data->get();
        $list_lost_total = $list_lost_data->sum('total_harga');

        $table_detail_deal = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('total_harga', '>', '0')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('status_deal', 'DEAL')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            });

        $total_detail_deal = $table_detail_deal->sum('total_harga');
        $total_modal_deal = $table_detail_deal->sum('modal_sales');

        $data = array([
            'lost' => $lost,
            'selected_month' => $carbonDate->month,
            'bulan_target' => $carbonDate->englishMonth,
            'last_month' => $last_month,
            'total_target' => $total_target,
            'total_realisasi' => $total_realisasi,
            'omzet_deal' => $omzet_deal,
            'modal_sales' => $modal_sales,
            'omzet_repeat' => $omzet_repeat,
            'potensi_all' => $potensi_all,
            'potensi_followup' => $potensi_followup,
            'potensi_lost' => $potensi_lost,
            'annual_target' => $annual_target,
            'annual_realisasi' => $annual_realisasi,
            'sumber' => $sumber,
            'data_sumber' => $data_sumber,
            'data_pic' => $data_pic,
            'data_instansi' => $data_instansi,
            'data_tipe_produk' => $data_tipe_produk,
            'target_progress' => $target_progress,
            'count_price_null' => $count_price_null,
            'count_prioritas' => $count_prioritas,
            'count_omzet_deal' => $count_omzet_deal,
            'count_potensi_all' => $count_potensi_all,
            'avg_closing_rate_per_omzet' => $avg_closing_rate_per_omzet,
            'count_konsumen_deal' => $count_konsumen_deal,
            'count_kontak_masuk' => $count_kontak_masuk,
            'count_kontak_masuk_emas' => $count_kontak_masuk_emas,
            'avg_closing_rate_per_customer' => $avg_closing_rate_per_customer,
            'count_total_fu' => $count_total_fu,
            'total_data_fu' => $total_data_fu,
            'sum_gt_fu' => $sum_gt_fu,
            'count_total_fu_emas' => $count_total_fu_emas,
            'total_data_fu_emas' => $total_data_fu_emas,
            'sum_gt_emas' => $sum_gt_emas,
            'count_total_fu_bronze' => $count_total_fu_bronze,
            'total_data_fu_bronze' => $total_data_fu_bronze,
            'sum_gt_bronze' => $sum_gt_bronze,
            'count_total_fu_platinum' => $count_total_fu_platinum,
            'total_data_fu_platinum' => $total_data_fu_platinum,
            'sum_gt_platinum' => $sum_gt_platinum,
            'count_total_fu_ruby' => $count_total_fu_ruby,
            'total_data_fu_ruby' => $total_data_fu_ruby,
            'sum_gt_ruby' => $sum_gt_ruby,
            'count_total_fu_diamond' => $count_total_fu_diamond,
            'total_data_fu_diamond' => $total_data_fu_diamond,
            'sum_gt_diamond' => $sum_gt_diamond,
            'count_total_fu_deal' => $count_total_fu_deal,
            'total_data_fu_deal' => $total_data_fu_deal,
            'sum_gt_deal' => $sum_gt_deal,
            'count_data_lost' => $data_losts,
            'data_lost' => $data_lost,
            'list_lost_total' => $list_lost_total,
            'omzet_other' => $omzet_other,
            'total_detail_deal' => $total_detail_deal,
            'total_modal_deal' => $total_modal_deal
        ]);
        return Response::json($data, 200);
    }

    public function get_sumber(Request $request)
    {
        $sumber = Sumber::orderBy('sumber', 'ASC')->get('sumber');
        $selected_pic = request('pic_sales');
        $selected_month = request('month');
        $selected_year = request('year');

        $data_sumbers = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year);
        $x = -1;
        foreach ($sumber as $smb) {
            $x++;
            $data_sumbers->addSelect(DB::raw(
                'SUM(
                                                CASE
                                                    when sumber = "' . $smb['sumber'] .
                    '" then
                                                    total_harga
                                                    end
                                                ) as "' . $x . '"'
            ));
        }
        $data_sumber = $data_sumbers->get()->toArray();

        return Response::json($data_sumber, 200);
    }

    public function get_tipe_instansi(Request $request)
    {
        $instansi = TipeInstansi::orderBy('tipe_instansi', 'ASC')->get('tipe_instansi');
        $selected_pic = request('pic_sales');
        $selected_month = request('month');
        $selected_year = request('year');

        $data_instansis = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year);
        $x = 0;
        foreach ($instansi as $ins) {
            $x++;
            $data_instansis->addSelect(DB::raw(
                'SUM(
                                                CASE
                                                    when tipe_instansi = "' . $ins['tipe_instansi'] . '" then
                                                    total_harga
                                                    end
                                                ) as "' . $x . '"'
            ));
        }
        $data_instansi = $data_instansis->get()->toArray();

        return Response::json($data_instansi, 200);
    }

    public function get_tipe_produk(Request $request)
    {
        $tipe_produk = TipeProduk::orderBy('tipe_produk', 'ASC')->get('tipe_produk');
        $selected_pic = request('pic_sales');
        $selected_month = request('month');
        $selected_year = request('year');

        $data_tipe_produks = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year);
        $x = 0;
        foreach ($tipe_produk as $tp) {
            $x++;
            $data_tipe_produks->addSelect(DB::raw(
                'SUM(
                                            CASE
                                                when tipe_produk = "' . $tp['tipe_produk'] . '" then
                                                total_harga
                                                end
                                            ) as "' . $tp['tipe_produk'] . '"'
            ));
        }
        $data_tipe_produk = $data_tipe_produks->get()->toArray();

        return Response::json($data_tipe_produk, 200);
    }

    public function table_get_sales_repeat(Request $request)
    {
        $selected_pic = $request->pic_sales;
        $selected_month = $request->month;
        $selected_year = $request->year;
        $list_customer_repeat = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select(DB::raw("count(tb_orders.id_customer) as cro, nama, SUM(total_harga) as total_harga, sumber, SUM(modal_sales) AS total_modal_sales"))
            ->where('total_harga', '>', '0')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('status_deal', 'DEAL')
            ->groupBy('tb_orders.id_customer', 'sumber')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } elseif (Auth::user()->roles === 'SALES SPV' || Auth::user()->roles === 'SUPERADMIN') {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
                
            })
            ->orderBy('total_harga', 'DESC')
            ->get();
        
        return DataTables::of($list_customer_repeat)
            ->editColumn('nama', function ($row) {
                return $row->nama;
            })
            ->editColumn('total_harga', function ($row) {
                return $row->total_harga;
            })
            ->addColumn('cro', function ($row) {
                return $row->cro;
            })
            ->addColumn('sumber', function ($row) {
                return $row->sumber;
            })

            ->addColumn('modal_sales', function ($row) {
                return $row->total_modal_sales;
            })
            ->make(true);
    }

    public function table_get_sales_lost(Request $request)
    {
        $selected_pic = request('pic_sales');
        $selected_month = request('month');
        $selected_year = request('year');
        // $query = DB::table('tb_customers')->select(['id_customer','kode_kustomer','nama','no_hp','tipe_instansi','nama_instansi','updated_at'])->orderBy('id_customer','DESC');
        $list_lost = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select(['tb_customers.nama', 'status_order', 'tipe_produk', 'total_harga'])
            ->where('status_deal', 'Lost')
            ->where('total_harga', '!=', '0')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
        ->orderBy('total_harga', 'DESC');

        return DataTables::of($list_lost)
            ->editColumn('nama', function ($row) {
                return $row->nama;
            })
            ->editColumn('status_order', function ($row) {
                return $row->status_order;
            })
            ->addColumn('tipe_produk', function ($row) {
                return $row->tipe_produk;
            })
            ->editColumn('total_harga', function ($row) {
                return $row->total_harga ? 'Rp' . number_format($row->total_harga, 0, '', ',') : '';
            })
            ->rawColumns(['nama', 'status_order', 'tipe_produk', 'total_harga'])
            ->make(true);
    }
    
    public function table_omzet_online(Request $request)
    {
        $selected_pic = request('pic_sales');
        $selected_month = request('month');
        $selected_year = request('year');

        $listOmzet = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select('sumber', DB::raw('SUM(total_harga) as total_harga'))
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } else {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
            })
            ->groupBy('sumber')
            ->orderBy('total_harga', 'DESC');

        return DataTables::of($listOmzet)
            ->addColumn('sumber', function ($row) {
                return $row->sumber;
            })
            ->editColumn('total_harga', function ($row) {
                return $row->total_harga ? 'Rp' . number_format($row->total_harga, 0, '', ',') : '';
            })
            ->rawColumns(['sumber', 'total_harga'])
            ->make(true);
    }

    public function production_report()
    {
        $pic_produksi = User::where('roles', 'PRODUKSI')->orWhere('roles', 'PRODUKSI SPV')->get()->toArray();
        $this_year = date('Y');
        $this_month = date('m');
        $logged_id = Auth::user()->id;
        $jenis_kertas = JenisKertas::orderBy('jenis_kertas', 'ASC')->get('jenis_kertas');
        $vendor = Vendor::all()->toArray();

        $total_produksi = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->sum('jumlah_fix');
        
        $softbox_total = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('jenis_bahan', 'Softbox')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->sum('jumlah_fix');

        $hardbox_total = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('jenis_bahan', 'Hardbox')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->sum('jumlah_fix');

        $corrugated_total = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('jenis_bahan', 'Corrugated Box')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->sum('jumlah_fix');

        $produksi_lainnya = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('jenis_bahan', 'Corrugated Box')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->sum('jumlah_fix');
            
        $spk_total = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereIn('catatan_khusus', ['Tuntas', 'Berjalan'])
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->where(function ($q) {
                $q->where('flag_dummy', 'Produksi Massal')
                ->orWhereRaw("SUBSTRING(sko, -1) = 'P'");
            });
        $count_spk_total = $spk_total->count();

        $spk_tuntas = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('catatan_khusus', 'Tuntas')
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->where(function ($q) {
                $q->where('flag_dummy', 'Produksi Massal')
                ->orWhereRaw("SUBSTRING(sko, -1) = 'P'");
            })
            ->where('catatan_khusus', 'Tuntas');
        $count_spk_tuntas = $spk_tuntas->count();

        $spk_reject = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->where(function ($q) {
                $q->whereRaw("SUBSTRING(sko, -1) = 'R'")
                ->orWhere('flag_dummy', 'Produksi Ulang');
            })
            ->where('catatan_khusus', 'Reject');
        $count_spk_reject = $spk_reject->count();

        $spk_berjalan = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('catatan_khusus', 'Berjalan')
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->where(function ($q) {
                $q->where('flag_dummy', 'Produksi Massal')
                ->orWhereRaw("SUBSTRING(sko, -1) = 'P'");
            })
            ->where('catatan_khusus', 'Berjalan');
        $count_spk_berjalan = $spk_berjalan->count();

        // _______________________________________________________________________________________________________

        $spk_total_dummy = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->where(function ($q) {
                $q->whereRaw("SUBSTRING(sko, -1) = 'D'");
            });
        $count_spk_total_dummy = $spk_total_dummy->count();

        $spk_tuntas_dummy = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->where('catatan_khusus', 'Tuntas')
            ->where(function ($q) {
                $q->whereRaw("SUBSTRING(sko, -1) = 'D'");
            });
        $count_spk_tuntas_dummy = $spk_tuntas_dummy->count();

        $spk_berjalan_dummy = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->where('catatan_khusus', 'Berjalan')
            ->where(function ($q) {
                $q->whereRaw("SUBSTRING(sko, -1) = 'D'");
            });
        $count_spk_berjalan_dummy = $spk_berjalan_dummy->count();

        // _________________________________________________________________________________________________________________
        $data_jenis_kertas = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('status_deal', 'Deal')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year);
        $x = 0;
        foreach ($jenis_kertas as $jk) {
            $x++;
            $data_jenis_kertas->addSelect(DB::raw(
                'SUM(
                                                CASE
                                                    when jenis_kertas = "' . $jk['jenis_kertas'] . '" then
                                                    jumlah_produk
                                                    end
                                                ) as "' . $jk['jenis_kertas'] . '", get_plano'
            ));
        }
        $data_jk = $data_jenis_kertas->get()->toArray();

        $data_produksi_berjalan = Spk::leftJoin('tb_orders', 'tb_orders.sko_key', '=', 'tb_spks.sko_key')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', '=', 'tb_orders.id_customer')
            ->leftJoin('tb_faws', 'tb_faws.sko_key', '=', 'tb_spks.sko_key')
            ->where('status_deal', 'Deal')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            //  ->whereMonth('tgl_order', $this_month)
            //  ->whereYear('tgl_order', $this_year)
            //  ->where('catatan_khusus','Berjalan')
            ->select(['nama', 'sko', 'tb_faws.tgl_deadline', 'catatan_khusus', 'keterangan_tambahan'])
            ->get();



        return view('pages.dashboard.production', compact(
            'pic_produksi',
            'total_produksi',
            'softbox_total',
            'hardbox_total',
            'corrugated_total',
            'produksi_lainnya',
            'count_spk_total',
            'count_spk_tuntas',
            'count_spk_reject',
            'count_spk_berjalan',
            'count_spk_total_dummy',
            'count_spk_tuntas_dummy',
            'count_spk_berjalan_dummy',
            'jenis_kertas',
            'data_jk',
            'data_produksi_berjalan'
        ));
    }

    public function get_production_report(Request $request)
    {
        $pic_produksi = User::where('roles', 'PRODUKSI')->orWhere('roles', 'PRODUKSI SPV')->get()->toArray();
        $this_year = date('Y');
        $this_month = date('m');
        $selected_month = $request->month;
        $selected_month_l = Carbon::createFromFormat('m', $selected_month)->month;
        $selected_year = $request->year;
        $logged_id = Auth::user()->id;
        $jenis_kertas = JenisKertas::orderBy('jenis_kertas', 'ASC')->get('jenis_kertas');

        $total_produksi = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $selected_month)
            ->whereYear('tgl_faw', $selected_year)
            ->sum('jumlah_fix');

        $softbox_total = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('jenis_bahan', 'Softbox')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $selected_month)
            ->whereYear('tgl_faw', $selected_year)
            ->sum('jumlah_fix');

        $hardbox_total = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('jenis_bahan', 'Hardbox')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $selected_month)
            ->whereYear('tgl_faw', $selected_year)
            ->sum('jumlah_fix');

        $corrugated_total = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('jenis_bahan', 'Corrugated Box')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $selected_month)
            ->whereYear('tgl_faw', $selected_year)
            ->sum('jumlah_fix');
        
        $produksi_lainnya = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereNotNull('jumlah_fix')
            ->whereNull('jenis_bahan')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $selected_month)
            ->whereYear('tgl_faw', $selected_year)
            ->sum('jumlah_fix');

        $spk_total = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereIn('catatan_khusus', ['Tuntas', 'Berjalan'])
            ->whereMonth('tgl_faw', $selected_month)
            ->whereYear('tgl_faw', $selected_year)
            ->where(function ($q) {
                $q->where('flag_dummy', 'Produksi Massal')
                ->orWhereRaw("SUBSTRING(sko, -1) = 'P'");
            });
        $count_spk_total = $spk_total->count();

        $spk_tuntas = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('catatan_khusus', 'Tuntas')
            ->whereMonth('tgl_faw', $selected_month)
            ->whereYear('tgl_faw', $selected_year)
            ->where(function ($q) {
                $q->where('flag_dummy', 'Produksi Massal')
                ->orWhereRaw("SUBSTRING(sko, -1) = 'P'");
            })
            ->where('catatan_khusus', 'Tuntas');
        $count_spk_tuntas = $spk_tuntas->count();

        $spk_reject = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereMonth('tgl_faw', $selected_month)
            ->whereYear('tgl_faw', $selected_year)
            ->where(function ($q) {
                $q->whereRaw("SUBSTRING(sko, -1) = 'R'")
                ->orWhere('flag_dummy', 'Produksi Ulang');
            })
            ->where('catatan_khusus', 'Reject');
        $count_spk_reject = $spk_reject->count();

        $spk_berjalan = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('catatan_khusus', 'Berjalan')
            ->whereMonth('tgl_faw', $selected_month)
            ->whereYear('tgl_faw', $selected_year)
            ->where(function ($q) {
                $q->where('flag_dummy', 'Produksi Massal')
                ->orWhereRaw("SUBSTRING(sko, -1) = 'P'");
            })
            ->where('catatan_khusus', 'Berjalan');
        $count_spk_berjalan = $spk_berjalan->count();

        // _______________________________________________________________________________________________________

        $spk_total_dummy = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereMonth('tgl_faw', $selected_month)
            ->whereYear('tgl_faw', $selected_year)
            ->where(function ($q) {
                $q->whereRaw("SUBSTRING(sko, -1) = 'D'");
            });
        $count_spk_total_dummy = $spk_total_dummy->count();

        $spk_tuntas_dummy = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereMonth('tgl_faw', $selected_month)
            ->whereYear('tgl_faw', $selected_year)
            ->where('catatan_khusus', 'Tuntas')
            ->where(function ($q) {
                $q->whereRaw("SUBSTRING(sko, -1) = 'D'");
            });
        $count_spk_tuntas_dummy = $spk_tuntas_dummy->count();

        $spk_berjalan_dummy = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereMonth('tgl_faw', $selected_month)
            ->whereYear('tgl_faw', $selected_year)
            ->where('catatan_khusus', 'Berjalan')
            ->where(function ($q) {
                $q->whereRaw("SUBSTRING(sko, -1) = 'D'");
            });
        $count_spk_berjalan_dummy = $spk_berjalan_dummy->count();

        // _________________________________________________________________________________________________________________
        $data_jenis_kertas = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('status_deal', 'Deal')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year);
        $x = 0;
        foreach ($jenis_kertas as $jk) {
            $x++;
            $data_jenis_kertas->addSelect(DB::raw(
                'SUM(
                                                CASE
                                                    when jenis_kertas = "' . $jk['jenis_kertas'] . '" then
                                                    jumlah_produk
                                                    end
                                                ) as "' . $jk['jenis_kertas'] . '", get_plano'
            ));
        }
        $data_jk = $data_jenis_kertas->get()->toArray();

        $data_produksi_berjalan = Spk::leftJoin('tb_orders', 'tb_orders.sko_key', '=', 'tb_spks.sko_key')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', '=', 'tb_orders.id_customer')
            ->leftJoin('tb_faws', 'tb_faws.sko_key', '=', 'tb_spks.sko_key')
            ->where('status_deal', 'Deal')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year)
            //  ->whereMonth('tgl_order', $this_month)
            //  ->whereYear('tgl_order', $this_year)
            //  ->where('catatan_khusus','Berjalan')
            ->select(['nama', 'sko', 'tb_faws.tgl_deadline', 'catatan_khusus', 'keterangan_tambahan'])
            ->get();

        $data = array([
            'selected_month' => $selected_month,
            'selected_year' => $selected_year,
            'total_produksi' => $total_produksi,
            'softbox_total' => $softbox_total,
            'hardbox_total' => $hardbox_total,
            'corrugated_total' => $corrugated_total,
            'produksi_lainnya' => $produksi_lainnya,
            'count_spk_total' => $count_spk_total,
            'count_spk_tuntas' => $count_spk_tuntas,
            'count_spk_reject' => $count_spk_reject,
            'count_spk_berjalan' => $count_spk_berjalan,
            'count_spk_total_dummy' => $count_spk_total_dummy,
            'count_spk_tuntas_dummy' => $count_spk_tuntas_dummy,
            'count_spk_berjalan_dummy' => $count_spk_berjalan_dummy,
            'jenis_kertas' => $jenis_kertas,
            'data_jk' => $data_jk,
            'data_produksi_berjalan' => $data_produksi_berjalan
        ]);
        return Response::json($data, 200);
    }

    public function get_jenis_kertas()
    {
        $selected_month = request('month');
        $selected_year = request('year');

        $jenis_kertas = JenisKertas::orderBy('jenis_kertas', 'ASC')->get('jenis_kertas');

        $data_jenis_kertas = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('status_deal', 'Deal')
            ->whereMonth('tgl_order', $selected_month)
            ->whereYear('tgl_order', $selected_year);
        $x = 0;
        foreach ($jenis_kertas as $jk) {
            $x++;
            $data_jenis_kertas->addSelect(DB::raw(
                'SUM(
                                                CASE
                                                    when jenis_kertas = "' . $jk['jenis_kertas'] . '" then
                                                    jumlah_produk
                                                    end
                                                ) as "' . $x . '", get_plano'
            ));
        }
        $data_jk = $data_jenis_kertas->get()->toArray();

        return Response::json($data_jk, 200);
    }

    public function table_get_produksi_berjalan()
    {
        $selected_month = request('month');
        $selected_year = request('year');
        // $query = DB::table('tb_customers')->select(['id_customer','kode_kustomer','nama','no_hp','tipe_instansi','nama_instansi','updated_at'])->orderBy('id_customer','DESC');
        $data_produksi = Spk::leftJoin('tb_orders', 'tb_orders.sko_key', '=', 'tb_spks.sko_key')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', '=', 'tb_orders.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_faws', 'tb_faws.sko_key', '=', 'tb_spks.sko_key')
            ->where('catatan_khusus', 'Berjalan')
            ->where('status_deal', 'Deal')
            ->whereMonth('tgl_deadline', $selected_month)
            ->whereYear('tgl_deadline', $selected_year)
            ->select([
                'tb_spks.*', 'nama', 'name', 'sko', 'tb_faws.tgl_deadline', 'keterangan_tambahan', 'tgl_faw'
            ]);

        return DataTables::of($data_produksi)
            ->editColumn('nama', function ($row) {
                return $row->nama;
            })
            ->editColumn('pic', function ($row) {
                return $row->name ? $row->name : '';
            })
            ->editColumn('tgl_faw', function ($row) {
                return date('d M Y', strtotime($row->tgl_faw));
            })
            ->editColumn('tgl_deadline', function ($row) {
                return date('d M Y', strtotime($row->tgl_deadline));
            })
            ->editColumn('progress_produksi', function ($row) {

                // $test = `$row->flag_status_1 + $row->progress_produksi_1`;
                if (!$row->progress_produksi_dummy && !$row->progress_produksi_ve) {
                    if ($row->tgl_selesai_all) {
                        return '<span class="badge badge-light-success badge-sm">Produksi Selesai</span>';
                    } else {
                        while (true) {
                            if (!$row->progress_produksi_1) {
                                return '<span class="badge badge-light-danger badge-sm">Belum Berjalan</span>';
                                break;
                            } 

                            if ($row->progress_produksi_1 && !$row->flag_status_1) {
                                return '<span class="badge badge-light-warning badge-sm">Pra-Cetak</span>';
                                break;
                            } 

                            if ($row->progress_produksi_2 && !$row->flag_status_2) {
                                return '<span class="badge badge-light-warning badge-sm">Cetak</span>';
                                break;
                            }

                            if ($row->progress_produksi_3 && !$row->flag_status_3) {
                                return '<span class="badge badge-light-warning badge-sm">Laminasi</span>';
                                break;
                            }

                            if ($row->progress_produksi_4 && !$row->flag_status_4) {
                                return '<span class="badge badge-light-warning badge-sm">Poly</span>';
                                break;
                            }

                            if ($row->progress_produksi_5 && !$row->flag_status_5) {
                                return '<span class="badge badge-light-warning badge-sm">Emboss</span>';
                                break;
                            }

                            if ($row->progress_produksi_6 && !$row->flag_status_6) {
                                return '<span class="badge badge-light-warning badge-sm">Spot UV</span>';
                                break;
                            }

                            if ($row->progress_produksi_7 && !$row->flag_status_7) {
                                return '<span class="badge badge-light-warning badge-sm">Lapis</span>';
                                break;
                            }

                            if ($row->progress_produksi_8 && !$row->flag_status_8) {
                                return '<span class="badge badge-light-warning badge-sm">Jendela Mika</span>';
                                break;
                            }

                            if ($row->progress_produksi_9 && !$row->flag_status_9) {
                                return '<span class="badge badge-light-warning badge-sm">Pond</span>';
                                break;
                            }

                            if ($row->progress_produksi_10 && !$row->flag_status_10) {
                                return '<span class="badge badge-light-warning badge-sm">Finishing</span>';
                                break;
                            } else {
                                return '<span class="badge badge-danger badge-sm">Status Produksi Salah</span>';
                                break;
                            }
                        }
                    }
                } elseif ($row->progress_produksi_dummy && !$row->progress_produksi_ve) {
                    if (!$row->flag_status_dummy) {
                        return '<span class="badge badge-light-primary badge-sm">Dummy Berjalan</span>';
                    } else {
                        return '<span class="badge badge-light-success badge-sm">Dummy Selesai</span>';
                    }
                } elseif (!$row->progress_produksi_dummy && $row->progress_produksi_ve) {
                    if (!$row->flag_status_ve) {
                        return '<span class="badge badge-light-primary badge-sm">Vendor Eks Berjalan</span>';
                    } else {
                        return '<span class="badge badge-light-success badge-sm">Vendor Eks Selesai</span>';
                    }
                }
                
            })
            ->editColumn('keterangan_tambahan', function ($row) {
                return '<div class="alert alert-secondary" role="alert">' . $row->keterangan_tambahan . '</div>';
            })
            ->rawColumns(['progress_produksi', 'keterangan_tambahan'])
            ->make(true);
    }

    public function superadmin_report()
    {
        $pic_sales = User::where('roles', 'SALES')->orWhere('roles', 'SALES SPV')->get()->toArray();

        $lost = Lost::orderBy('lost', 'ASC')->get('lost');
        $sumber = Sumber::orderBy('sumber', 'ASC')->get('sumber');
        $pic_list = User::where('roles', 'SALES')->orWhere('roles', 'SALES SPV')->get()->toArray();
        $instansi = TipeInstansi::orderBy('tipe_instansi', 'ASC')->get('tipe_instansi');
        $tipe_produk = TipeProduk::all()->toArray();
        $this_month_l = date('n');
        list($this_month, $this_year) = explode(' ', date('m Y'));
        list($last_month, $last_month_year) = explode(' ', date('m Y', strtotime('first day of last month')));
        list($last_2month, $last_2month_year) = explode(' ', date('m Y', strtotime('first day of -2 months')));

        $logged_id = Auth::user()->id;
        $bulan_ke = array("", "target_01", "target_02", "target_03", "target_04", "target_05", "target_06", "target_07", "target_08", "target_09", "target_10", "target_11", "target_12");
        $this_month_target = $bulan_ke[$this_month_l];

        $total_target = DB::table('tb_targets')
            ->where('year_target', $this_year)
            ->sum($this_month_target);

        $total_realisasi = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->sum('total_harga');

        $omzet_deal = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->where('flag_dummy', 'Produksi Massal')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->sum('total_harga');

        $modal_sales = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereNotIn('kategori_produksi', ['Dummy', 'PPN', 'Jasa Desain', 'Lain-lain'])
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->sum('modal_sales');

        $omzet_repeat = Order::leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '=', 'Produksi Massal')
            ->where('sumber', 'Repeat Order')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->sum('total_harga');

        $omzet_other = DB::table('tb_produksis')
            ->leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('flag_dummy', '=', 'Produksi Massal')
            ->whereNotIn('sumber', ['Repeat Order', 'Online', 'Online Lintas'])
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->sum('total_harga');

        $potensi_all = Order::leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            // ->where(function($query) use ($this_month, $this_year) {
            //     $query->where(function ($subquery) use ($this_month, $this_year) {
            //         $subquery->where('tgl_order', '!=', '')
            //                 ->whereMonth('tgl_order', $this_month)
            //                 ->whereYear('tgl_order', $this_year);
            //     })
            //     ->orWhere(function ($subquery) use ($this_month, $this_year) {
            //         $subquery->whereMonth('waktu_kontak', $this_month)
            //                 ->whereYear('waktu_kontak', $this_year);
            //     });
            // })
            ->sum('total_harga');

        $potensi_followup = Order::leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', '=', 'Follow Up')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->sum('total_harga');

        $potensi_lost = Order::leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', '=', 'Lost')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->sum('total_harga');

        $annual_target = DB::table('tb_targets')
            ->select(DB::raw(
                "
                SUM(CASE WHEN year_target = '$last_2month_year' THEN target_$last_2month ELSE 0 END) as last_2month,
                SUM(CASE WHEN year_target = '$last_month_year' THEN target_$last_month ELSE 0 END) as last_month,
                SUM(CASE WHEN year_target = '$this_year' THEN target_$this_month ELSE 0 END) as this_month
                "
            ))
            // ->where('year_target', $this_year)
            ->first();

        $annual_realisasi = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select(DB::raw(
                '
                SUM(CASE WHEN MONTH(tgl_order) = ' . $this_month . ' AND YEAR(tgl_order) = ' . $this_year . ' THEN total_harga END) AS this_month,
                SUM(CASE WHEN MONTH(tgl_order) = ' . $last_month . ' AND YEAR(tgl_order) = ' . $last_month_year . ' THEN total_harga END) AS last_month,
                SUM(CASE WHEN MONTH(tgl_order) = ' . $last_2month . ' AND YEAR(tgl_order) = ' . $last_2month_year . ' THEN total_harga END) AS last_2month
                '
            ))
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereNotIn('kategori_produksi', ['Dummy', 'PPN', 'Jasa Desain'])
            // ->whereYear('tgl_order', $this_year)
            ->first();
        
        $data_sumbers = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year);
        $x = 0;
        foreach ($sumber as $smb) {
            $x++;
            $data_sumbers->addSelect(DB::raw(
                'SUM(
                                            CASE
                                                when sumber = "' . $smb['sumber'] . '" then
                                                total_harga
                                                end
                                            ) as "' . $smb['sumber'] . '"'
            ));
        }
        $data_sumber = $data_sumbers->get()->toArray();

        $data_pics = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year);
        $x = 0;
        foreach ($pic_list as $pic) {
            $x++;
            $data_pics->addSelect(DB::raw(
                'SUM(
                                            CASE
                                                when id_pic = "' . $pic['id'] . '" then
                                                total_harga
                                                end
                                            ) as "' . $pic['name'] . '"'
            ));
        }
        $data_pic = $data_pics->get()->toArray();

        $data_instansis = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year);
        $x = 0;
        foreach ($instansi as $ins) {
            $x++;
            $data_instansis->addSelect(DB::raw(
                'SUM(
                                            CASE
                                                when tipe_instansi = "' . $ins['tipe_instansi'] . '" then
                                                total_harga
                                                end
                                            ) as "' . $ins['tipe_instansi'] . '"'
            ));
        }
        $data_instansi = $data_instansis->get()->toArray();

        $data_tipe_produks = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year);
        $x = 0;
        foreach ($tipe_produk as $tp) {
            $x++;
            $data_tipe_produks->addSelect(DB::raw(
                'SUM(
                                            CASE
                                                when tipe_produk = "' . $tp['tipe_produk'] . '" then
                                                total_harga
                                                end
                                            ) as "' . $tp['tipe_produk'] . '"'
            ));
        }
        $data_tipe_produk = $data_tipe_produks->get()->toArray();
        

        if ($total_target != 0) {
            $target_div = $total_realisasi / $total_target;
        } else {
            $target_div = 0;
        }
        $target_progress_raw = $target_div * 100;
        if ($target_progress_raw > 100) {
            $target_progress = 100;
        } else {
            $target_progress = $target_progress_raw;
        }

        $price_null = Order::leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select(['id_order', 'kode_kustomer', 'tb_kode_orders.kode_order', 'tb_orders.order_key', 'total_harga', 'tgl_order', 'waktu_kontak', 'name', 'nama', 'status_deal', 'status_order'])
            ->where('status_deal', '!=', 'Lost')
            ->where('total_harga', '<', '1')
            ->Where('status_order', '=', 'FU Emas')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_price_null = $price_null->count();

        $prioritas = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select(['id_order', 'kode_kustomer', 'tb_kode_orders.kode_order', 'tb_orders.order_key', 'total_harga', 'tgl_order', 'waktu_kontak', 'name', 'nama', 'status_deal', 'status_order'])
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                $query->where('status_order', '=', 'Ruby')
                    ->orWhere('status_order', '=', 'Diamond')
                    ->orWhere('status_order', '=', 'Platinum');

                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_prioritas = $prioritas->count();

        $omzet_deal_row = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->where('flag_dummy', 'Produksi Massal')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }

            });
        $count_omzet_deal = $omzet_deal_row->sum('total_harga');

        $potensi_all_row = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_potensi_all = $potensi_all_row->sum('total_harga');

        if ($count_potensi_all == 0) {
            $avg_closing_rate_per_omzet = 0;
        } else {
            $avg_closing_rate_per_omzet = ($count_omzet_deal / $count_potensi_all) * 100;
        }

        $konsumen_deal = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->distinct('no_hp')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_konsumen_deal = $konsumen_deal->count();

        $kontak_masuk = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->distinct('no_hp')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });
        $count_kontak_masuk = $kontak_masuk->count();

        $kontak_masuk_emas = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->distinct('no_hp')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->whereNotIn('sumber', [
                'Relasi',
                'Relasi Customer',
                'Repeat Order'
            ])
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });

        $count_kontak_masuk_emas = $kontak_masuk_emas->count();

        if ($count_kontak_masuk_emas == 0) {
            $avg_closing_rate_per_customer = 0;
        } else {
            $avg_closing_rate_per_customer = $count_konsumen_deal / $count_kontak_masuk_emas * 100;
        }

        $list_customer_repeat = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select(DB::raw("count(tb_orders.id_customer) as cro, nama, SUM(total_harga) as total_harga, sumber, SUM(modal_sales) AS total_modal_sales"))
            ->where('total_harga', '>', '0')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('status_deal', 'DEAL')
            ->groupBy('tb_orders.id_customer', 'sumber')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->orderBy('total_harga', 'DESC')
            ->get();

        // _____________________________________________Follow Up_______________________________________________________________________

        $data_fu = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                    ->where('status_order', 'FU');
                
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->orderBy('total_harga', 'DESC');
        $count_total_fu = $data_fu->count();
        $total_data_fu = $data_fu->get();
        $sum_gt_fu = $data_fu->sum('total_harga');

        $data_fu_emas = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                ->where('status_order', 'FU Emas');
                
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->orderBy('total_harga', 'DESC');
        $count_total_fu_emas = $data_fu_emas->count();
        $total_data_fu_emas = $data_fu_emas->get();
        $sum_gt_emas = $data_fu_emas->sum('total_harga');

        $data_fu_bronze = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                ->where('status_order', 'Bronze');
                
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->orderBy('total_harga', 'DESC');
        $count_total_fu_bronze = $data_fu_bronze->count();
        $total_data_fu_bronze = $data_fu_bronze->get();
        $sum_gt_bronze = $data_fu_bronze->sum('total_harga');

        $data_fu_platinum = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                ->where('status_order', 'Platinum');
                
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->orderBy('total_harga', 'DESC');
        $count_total_fu_platinum = $data_fu_platinum->count();
        $total_data_fu_platinum = $data_fu_platinum->get();
        $sum_gt_platinum = $data_fu_platinum->sum('total_harga');


        $data_fu_ruby = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                ->where('status_order', 'Ruby');
                
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->orderBy('total_harga', 'DESC');
        $count_total_fu_ruby = $data_fu_ruby->count();
        $total_data_fu_ruby = $data_fu_ruby->get();
        $sum_gt_ruby = $data_fu_ruby->sum('total_harga');

        $data_fu_diamond = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Follow Up')
                ->where('status_order', 'Diamond');
                
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->orderBy('total_harga', 'DESC');
        $count_total_fu_diamond = $data_fu_diamond->count();
        $total_data_fu_diamond = $data_fu_diamond->get();
        $sum_gt_diamond = $data_fu_diamond->sum('total_harga');

        $data_fu_deal = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('nama', 'total_harga')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->where(function ($query) {
                $query->where('status_deal', 'Deal');
                
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->orderBy('total_harga', 'DESC');
        $count_total_fu_deal = $data_fu_deal->count();
        $total_data_fu_deal = $data_fu_deal->get();
        $sum_gt_deal = $data_fu_deal->sum('total_harga');

        // _________________________________________________________________________________________________________________
        $i = 0;
        foreach ($lost as $l) {
            $i++;
            $data_lost[$i] = Order::whereNotNull('id_order')
                ->whereMonth('waktu_kontak', $this_month)
                ->whereYear('waktu_kontak', $this_year)
                ->where(function ($query) {
                    if (Auth::user()->roles == 'SALES') {
                        $query->where('id_pic', Auth::user()->id);
                    }
                });
            $data_lost[$i]->where("status_order", $l->lost);
            $data_lost[$i]->get();

            $count_data_lost[$i] = $data_lost[$i]->count();
        }

        // __________________________________________________________________________________________________________________________

        $list_lost_data = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select(['tb_customers.nama', 'status_order', 'total_harga'])
            ->where('status_deal', 'Lost')
            ->where('total_harga', '!=', '0')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('kategori_produksi', 'like', '%pack%')
            ->whereMonth('waktu_kontak', $this_month)
            ->whereYear('waktu_kontak', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->orderBy('tb_orders.updated_at', 'DESC');

        $list_lost = $list_lost_data->get();
        $list_lost_total = $list_lost_data->sum('total_harga');

        //PRODUCTION
        $jenis_kertas = JenisKertas::orderBy('jenis_kertas', 'ASC')->get('jenis_kertas');
        $vendor = Vendor::all()->toArray();

        $total_produksi = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->sum('jumlah_fix');

        $softbox_total = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('jenis_bahan', 'Softbox')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->sum('jumlah_fix');

        $hardbox_total = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('jenis_bahan', 'Hardbox')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->sum('jumlah_fix');

        $corrugated_total = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('jenis_bahan', 'Corrugated Box')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->sum('jumlah_fix');

        $produksi_lainnya = DB::table('tb_spks')
            ->leftJoin('tb_produksis', 'tb_spks.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('jenis_bahan', 'Corrugated Box')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereRaw("SUBSTRING(sko, -1) = 'P'")
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->sum('jumlah_fix');
            
        $spk_total = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereIn('catatan_khusus', ['Tuntas', 'Berjalan'])
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->where(function ($q) {
                $q->where('flag_dummy', 'Produksi Massal')
                ->orWhereRaw("SUBSTRING(sko, -1) = 'P'");
            });
        $count_spk_total = $spk_total->count();

        $spk_tuntas = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('catatan_khusus', 'Tuntas')
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->where(function ($q) {
                $q->where('flag_dummy', 'Produksi Massal')
                ->orWhereRaw("SUBSTRING(sko, -1) = 'P'");
            })
            ->where('catatan_khusus', 'Tuntas');
        $count_spk_tuntas = $spk_tuntas->count();

        $spk_reject = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->where(function ($q) {
                $q->whereRaw("SUBSTRING(sko, -1) = 'R'")
                ->orWhere('flag_dummy', 'Produksi Ulang');
            })
            ->where('catatan_khusus', 'Reject');
        $count_spk_reject = $spk_reject->count();

        $spk_berjalan = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->where('catatan_khusus', 'Berjalan')
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->where(function ($q) {
                $q->where('flag_dummy', 'Produksi Massal')
                ->orWhereRaw("SUBSTRING(sko, -1) = 'P'");
            })
            ->where('catatan_khusus', 'Berjalan');
        $count_spk_berjalan = $spk_berjalan->count();

        // _______________________________________________________________________________________________________

        $spk_total_dummy = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->where(function ($q) {
                $q->whereRaw("SUBSTRING(sko, -1) = 'D'");
            });
        $count_spk_total_dummy = $spk_total_dummy->count();

        $spk_tuntas_dummy = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->where('catatan_khusus', 'Tuntas')
            ->where(function ($q) {
                $q->whereRaw("SUBSTRING(sko, -1) = 'D'");
            });
        $count_spk_tuntas_dummy = $spk_tuntas_dummy->count();

        $spk_berjalan_dummy = DB::table('tb_spks')
            ->leftJoin('tb_orders', 'tb_spks.sko_key', '=', 'tb_orders.sko_key')
            ->leftJoin('tb_faws', 'tb_spks.sko_key', '=', 'tb_faws.sko_key')
            ->whereMonth('tgl_faw', $this_month)
            ->whereYear('tgl_faw', $this_year)
            ->where('catatan_khusus', 'Berjalan')
            ->where(function ($q) {
                $q->whereRaw("SUBSTRING(sko, -1) = 'D'");
            });
        $count_spk_berjalan_dummy = $spk_berjalan_dummy->count();

        // _________________________________________________________________________________________________________________
        $data_jenis_kertas = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('status_deal', 'Deal')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year);
        $x = 0;
        foreach ($jenis_kertas as $jk) {
            $x++;
            $data_jenis_kertas->addSelect(DB::raw(
                'SUM(
                                                CASE
                                                    when jenis_kertas = "' . $jk['jenis_kertas'] . '" then
                                                    jumlah_produk
                                                    end
                                                ) as "' . $jk['jenis_kertas'] . '", get_plano'
            ));
        }
        $data_jk = $data_jenis_kertas->get()->toArray();

        $data_produksi_berjalan = Spk::leftJoin('tb_orders', 'tb_orders.sko_key', '=', 'tb_spks.sko_key')
            ->leftJoin('tb_customers', 'tb_customers.id_customer', '=', 'tb_orders.id_customer')
            ->leftJoin('tb_faws', 'tb_faws.sko_key', '=', 'tb_spks.sko_key')
            ->where('status_deal', 'Deal')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            //  ->whereMonth('tgl_order', $this_month)
            //  ->whereYear('tgl_order', $this_year)
            //  ->where('catatan_khusus','Berjalan')
            ->select(['nama', 'sko', 'tb_faws.tgl_deadline', 'catatan_khusus', 'keterangan_tambahan'])
            ->get();

        $table_detail_deal = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('total_harga', '>', '0')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('status_deal', 'DEAL')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year);

        $total_detail_deal = $table_detail_deal->sum('total_harga');
        $total_modal_deal = $table_detail_deal->sum('modal_sales');

        $gross_margin = 0;
        if ($modal_sales > 0 ){
            $gross_margin = $modal_sales / $total_realisasi * 100;
        }

        $cycle = DB::table('tb_orders')
            ->select(DB::raw('SUM(DATEDIFF(tgl_order, waktu_kontak)) as cycle'))
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('status_deal', 'DEAL')
            ->where('kategori_produksi', 'like', '%pack%')
            ->where('sumber', '!=', 'Repeat Order')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->first()->cycle ?? 0;

        $count_cust_cycle = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where('status_deal', 'DEAL')
            ->where('kategori_produksi', 'like', '%pack%')
            ->where('sumber', '!=', 'Repeat Order')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->count();
        
        $count_cycle = 0;
        if ($cycle > 0) {
            $count_cycle = ceil($cycle / $count_cust_cycle);
        } 

        return view('pages.dashboard.superadmin', compact(
            'count_cycle',
            'gross_margin',
            'pic_sales',
            'lost',
            'total_target',
            'total_realisasi',
            'target_progress',
            'count_price_null',
            'count_prioritas',
            'modal_sales',
            'potensi_all',
            'omzet_deal',
            'omzet_repeat',
            'potensi_followup',
            'potensi_lost',
            'avg_closing_rate_per_omzet',
            'count_kontak_masuk',
            'count_kontak_masuk_emas',
            'avg_closing_rate_per_customer',
            'list_customer_repeat',
            'count_total_fu',
            'total_data_fu',
            'sum_gt_fu',
            'count_total_fu_emas',
            'total_data_fu_emas',
            'sum_gt_emas',
            'count_total_fu_bronze',
            'total_data_fu_bronze',
            'sum_gt_bronze',
            'count_total_fu_platinum',
            'total_data_fu_platinum',
            'sum_gt_platinum',
            'count_total_fu_ruby',
            'total_data_fu_ruby',
            'sum_gt_ruby',
            'count_total_fu_diamond',
            'total_data_fu_diamond',
            'sum_gt_diamond',
            'count_total_fu_deal',
            'total_data_fu_deal',
            'sum_gt_deal',
            'annual_target',
            'annual_realisasi',
            'count_data_lost',
            'sumber',
            'data_sumber',
            'pic_list',
            'data_pic',
            'instansi',
            'data_instansi',
            'tipe_produk',
            'data_tipe_produk',
            'list_lost',
            'total_produksi',
            'softbox_total',
            'hardbox_total',
            'corrugated_total',
            'produksi_lainnya',
            'count_spk_total',
            'count_spk_tuntas',
            'count_spk_reject',
            'count_spk_berjalan',
            'count_spk_total_dummy',
            'count_spk_tuntas_dummy',
            'count_spk_berjalan_dummy',
            'jenis_kertas',
            'data_jk',
            'data_produksi_berjalan',
            'list_lost_total',
            'omzet_other',
            'total_detail_deal',
            'total_modal_deal'
        ));
    }

    public function get_dashboard_spadmin_sales(Request $request)
    {

        $daterange = explode(' - ', $request->daterange_sales);
        $time_start = Carbon::createFromFormat('d/m/Y', $daterange[0])->format('Y-m-d');
        $month_start = date('m', strtotime($time_start));
        $month_start_l = date('n', strtotime($time_start));
        $year_start = date('Y', strtotime($time_start));
        $time_end = Carbon::createFromFormat('d/m/Y', $daterange[1])->format('Y-m-d');
        $month_end = date('m', strtotime($time_end));
        $year_end = date('Y', strtotime($time_end));
        $selected_pic = $request->pic_sales;


        $lost = Lost::orderBy('lost', 'ASC')->get('lost');
        $sumber = Sumber::all()->toArray();
        $pic_list = User::where('roles', 'SALES')->orWhere('roles', 'SALES SPV')->get()->toArray();
        $instansi = TipeInstansi::all()->toArray();
        $tipe_produk = TipeProduk::all()->toArray();
        $this_year = date('Y');
        $this_month = date('m');
        $this_month_l = date('n');
        $last_month = date('m', strtotime($time_start . ' -1 month'));
        $last_2month = date('m', strtotime($time_start . ' -2 month'));

        $logged_id = Auth::user()->id;
        $bulan_ke = array("", "target_01", "target_02", "target_03", "target_04", "target_05", "target_06", "target_07", "target_08", "target_09", "target_10", "target_11", "target_12");
        $this_month_target = $bulan_ke[$month_start_l];
        $bulan_target = date('F', strtotime($time_start));
        $total_target = DB::table('tb_targets')
            ->where('year_target', $year_start)
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->sum($this_month_target);

        if ($year_start > 2022) {
            $total_realisasi = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereNotIn('kategori_produksi', ['Dummy', 'PPN', 'Jasa Desain'])
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->whereMonth('tgl_order', $month_start)
            ->whereYear('tgl_order', $year_start)
            ->sum('total_harga');
        } else {
            $total_realisasi = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('notes', 'NOT LIKE', '%PPN%')
            ->where('notes', 'NOT LIKE', '%jasa desain%')
            ->where('notes', 'NOT LIKE', '%dummy%')
            ->whereNotIn('kategori_produksi', ['Dummy', 'PPN', 'Jasa Desain'])
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->whereMonth('tgl_order', $month_start)
            ->whereYear('tgl_order', $year_start)
            ->sum('total_harga');
        }

        $omzet_deal = DB::table('tb_produksis')
            ->leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where(function ($query) use ($time_start, $time_end) {
                $query->whereBetween('tgl_order', array($time_start, $time_end));
            })
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->where(function ($query) {
                $query->where('status_deal', 'Deal')
                    ->where('flag_dummy', '!=', 'Dummy')
                    ->whereNotIn('kategori_produksi', ['Dummy', 'Jasa Desain']);
            })
            ->sum('total_harga');

        $modal_sales = Produksi::leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->whereNotIn('kategori_produksi', ['Dummy', 'PPN', 'Jasa Desain', 'Lain-lain'])
            ->where(function ($query) use ($time_start, $time_end) {
                $query->whereBetween('tgl_order', array($time_start, $time_end));
            })
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->sum('modal_sales');

        $omzet_repeat = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->where('sumber', 'Repeat Order')
            ->where(function ($query) use ($time_start, $time_end) {
                $query->whereBetween('tgl_order', array($time_start, $time_end));
            })
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->sum('total_harga');

        $potensi_all = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->whereRaw('(status_deal like "Deal" OR status_deal like "Follow Up")')
            ->where(function ($q) {
                $q->where('flag_dummy', 'Produksi Massal')
                    ->orWhere('flag_dummy', 'Produksi Ulang')
                    ->orWhereNull('flag_dummy');
            })
            ->where(function ($query) use ($time_start, $time_end) {
                $query->whereBetween('waktu_kontak', array($time_start, $time_end));
            })
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->sum('total_harga');

        $potensi_followup = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', '=', 'Follow Up')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where(function ($potensi_followup) {
                $potensi_followup->where('flag_dummy', 'Produksi Massal')
                    ->orWhere('flag_dummy', 'Produksi Ulang')
                    ->orWhereNull('flag_dummy');
            })
            ->where(function ($query) use ($time_start, $time_end) {
                $query->whereBetween('waktu_kontak', array($time_start, $time_end));
            })
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->sum('total_harga');

        $potensi_lost = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where('status_deal', '=', 'Lost')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->where(function ($potensi_followup) {
                $potensi_followup->where('flag_dummy', 'Produksi Massal')
                    ->orWhere('flag_dummy', 'Produksi Ulang')
                    ->orWhereNull('flag_dummy');
            })
            ->where(function ($query) use ($time_start, $time_end) {
                $query->whereBetween('waktu_kontak', array($time_start, $time_end));
            })
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->sum('total_harga');

        $annual_target = DB::table('tb_targets')
            ->select(DB::raw('SUM(target_' . $last_2month . ') as last_2month, SUM(target_' . $last_month . ') as last_month, SUM(target_' . $month_start . ') as this_month'))
            ->where('year_target', $year_start)
            ->first();
        $annual_realisasi = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select(DB::raw('SUM(
                                CASE
                                    when MONTH(tgl_order) = ' . $month_start . ' then
                                    total_harga
                                    end
                                ) as this_month,
                                SUM(
                                    CASE
                                        when MONTH(tgl_order) = ' . $last_month . ' then
                                        total_harga
                                        end
                                    ) as last_month,
                                SUM(
                                CASE
                                    when MONTH(tgl_order) = ' . $last_2month . ' then
                                    total_harga
                                    end
                                ) as last_2month'))
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereYear('tgl_order', $year_start)
            ->first();

        $data_sumbers = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year);
        $x = 0;
        foreach ($sumber as $smb) {
            $x++;
            $data_sumbers->addSelect(DB::raw(
                'SUM(
                                                CASE
                                                    when sumber = "' . $smb['sumber'] .
                    '" then
                                                    total_harga
                                                    end
                                                ) as "' . $smb['sumber'] . '"'
            ));
        }
        $data_sumber = $data_sumbers->get()->toArray();

        $data_pics = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year);
        $x = 0;
        foreach ($pic_list as $pic) {
            $x++;
            $data_pics->addSelect(DB::raw(
                'SUM(
                                                CASE
                                                    when id_pic = "' . $pic['id'] .
                    '" then
                                                    total_harga
                                                    end
                                                ) as "' . $pic['name'] . '"'
            ));
        }
        $data_pic = $data_pics->get()->toArray();

        $data_instansis = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year);
        $x = 0;
        foreach ($instansi as $ins) {
            $x++;
            $data_instansis->addSelect(DB::raw(
                'SUM(
                                                CASE
                                                    when tipe_instansi = "' . $ins['tipe_instansi'] .
                '" then
                                                    total_harga
                                                    end
                                                ) as "' . $ins['tipe_instansi'] . '"'
            ));
        }
        $data_instansi = $data_instansis->get()->toArray();

        $data_tipe_produks = Order::leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year);
        $x = 0;
        foreach ($tipe_produk as $tp) {
            $x++;
            $data_tipe_produks->addSelect(DB::raw(
                'SUM(
                                                CASE
                                                    when tipe_produk = "' . $tp['tipe_produk'] .
                    '" then
                                                    total_harga
                                                    end
                                                ) as "' . $tp['tipe_produk'] . '"'
            ));
        }
        $data_tipe_produk = $data_tipe_produks->get()->toArray();

        if ($total_target != 0) {
            $target_div = $total_realisasi / $total_target;
        } else {
            $target_div = 0;
        }
        $target_progress_raw = $target_div * 100;
        if ($target_progress_raw > 100) {
            $target_progress = 100;
        } else {
            $target_progress = $target_progress_raw;
        }

        $price_null = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select(['id_order', 'kode_kustomer', 'tb_kode_orders.kode_order', 'tb_orders.order_key', 'total_harga', 'tgl_order', 'waktu_kontak', 'name', 'nama', 'status_deal', 'status_order'])
            ->where('status_deal', '!=', 'Lost')
            ->where(function ($query) use ($time_start, $time_end) {
                $query->whereBetween('waktu_kontak', array($time_start, $time_end));
            })
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->where(function ($query) {
                $query->Where('status_order', '=', 'FU Emas');
                $query->where('total_harga', '<', '1');
            });
        $count_price_null = $price_null->count();

        $prioritas = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->leftJoin('users', 'tb_orders.id_pic', '=', 'users.id')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->select(['id_order', 'kode_kustomer', 'tb_kode_orders.kode_order', 'tb_orders.order_key', 'total_harga', 'tgl_order', 'waktu_kontak', 'name', 'nama', 'status_deal', 'status_order'])
            ->where(function ($query) use ($time_start, $time_end) {
                $query->whereBetween('waktu_kontak', array($time_start, $time_end));
            })
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->where(function ($query) {
                $query->where('status_order', '=', 'Ruby')
                    ->orWhere('status_order', '=', 'Diamond')
                    ->orWhere('status_order', '=', 'Platinum');
            });
        $count_prioritas = $prioritas->count();

        $omzet_deal_row = DB::table('tb_produksis')
            ->leftJoin('tb_orders', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->where(function ($query) use ($time_start, $time_end) {
                $query->whereBetween('tgl_order', array($time_start, $time_end));
            })
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->select(['id_order'])
        ->where(function ($query) {
            $query->where('status_deal', 'Deal')
            ->where('flag_dummy', '!=', 'Dummy');
        });
        $count_omzet_deal = $omzet_deal_row->count();

        $potensi_all_row = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_produksis.sko_key', '=', 'tb_orders.sko_key')
            ->whereRaw('(status_deal like "Deal" OR status_deal like "Follow Up")')
            ->where(function ($potensi_followup) {
                $potensi_followup->where('flag_dummy', 'Produksi Massal')
                    ->orWhere('flag_dummy', 'Produksi Ulang')
                    ->orWhereNull('flag_dummy');
            })
            ->where(function ($query) use ($time_start, $time_end) {
                $query->whereBetween('tgl_order', array($time_start, $time_end));
            })
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            });
        $count_potensi_all = $potensi_all_row->count();
        if ($count_potensi_all == 0) {
            $avg_closing_rate_per_omzet = 0;
        } else {
            $avg_closing_rate_per_omzet = $count_omzet_deal / $count_potensi_all * 100;
        }

        $konsumen_deal = DB::table('tb_orders')
            ->where(function ($query) use ($time_start, $time_end) {
                $query->whereBetween('waktu_kontak', array($time_start, $time_end));
            })
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            })
            ->where(function ($query) {
                $query->where('status_deal', 'Deal');
            });
        $count_konsumen_deal = $konsumen_deal->count();

        $kontak_masuk = DB::table('tb_orders')
            ->select('no_hp')
            ->distinct()
            ->where(function ($query) use ($time_start, $time_end) {
                $query->whereBetween('waktu_kontak', array($time_start, $time_end));
            })
            ->where(function ($q) use ($selected_pic) {
                if ($selected_pic != 'All') {
                    $q->where('id_pic', $selected_pic);
                }
            });
        $count_kontak_masuk = $kontak_masuk->count();

        $kontak_masuk_emas = DB::table('tb_orders')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select('no_hp')
            ->distinct()
            ->whereMonth('waktu_kontak', $selected_month)
            ->whereYear('waktu_kontak', $selected_year)
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            
            ->where(function ($query) {
                $query->where('sumber', '=', 'Online Lintas')
                    ->orWhere('sumber', '=', 'Online');

                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            });

        $count_kontak_masuk_emas = $kontak_masuk_emas->count();


        if ($count_kontak_masuk == 0) {
            $avg_closing_rate_per_customer = 0;
        } else {
            $avg_closing_rate_per_customer = $count_konsumen_deal / $count_kontak_masuk * 100;
        }

        $list_customer_repeat = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select(DB::raw("count(tb_orders.id_customer) as cro, nama, sum(total_harga) as total_harga, SUM(modal_sales) AS total_modal_sales"))
            ->groupBy('tb_orders.id_customer', 'sumber')
            ->whereMonth('tgl_order', $this_month)
            ->whereYear('tgl_order', $this_year)
            ->where(function ($query) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
            })
            ->orderBy('cro', 'DESC')
            ->get();

        $data = array([
            'total_target' => $total_target,
            'total_realisasi' => $total_realisasi,
            'bulan_target' => $bulan_target,
            'omzet_deal' => $omzet_deal,
            'modal_sales' => $modal_sales,
            'omzet_repeat' => $omzet_repeat,
            'potensi_all' => $potensi_all,
            'potensi_followup' => $potensi_followup,
            'potensi_lost' => $potensi_lost,
            'annual_target' => $annual_target,
            'annual_realisasi' => $annual_realisasi,
            'data_sumber' => $data_sumber,
            'data_pic' => $data_pic,
            'data_instansi' => $data_instansi,
            'data_tipe_produk' => $data_tipe_produk,
            'target_progress' => $target_progress,
            'count_price_null' => $count_price_null,
            'count_prioritas' => $count_prioritas,
            'count_omzet_deal' => $count_omzet_deal,
            'count_potensi_all' => $count_potensi_all,
            'avg_closing_rate_per_omzet' => $avg_closing_rate_per_omzet,
            'count_konsumen_deal' => $count_konsumen_deal,
            'count_kontak_masuk' => $count_kontak_masuk,
            'count_kontak_masuk_emas' => $count_kontak_masuk_emas,
            'avg_closing_rate_per_customer' => $avg_closing_rate_per_customer,
            'list_customer_repeat' => $list_customer_repeat
        ]);

        return Response::json($data, 200);
    }
}