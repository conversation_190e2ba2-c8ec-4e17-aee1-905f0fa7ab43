<?php

namespace App\Http\Controllers;

use App\Models\TipeInstansi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Yajra\DataTables\Facades\DataTables;

class TipeInstansiController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(request()->ajax())
        {
            $query = TipeInstansi::query();
            return DataTables::of($query)
                ->addColumn('action', function($item){
                    return '
                    <div class="row">
                        <div class="col-md-6">
                            <a class="btn btn-sm btn-primary w-100 text-white edit-instansi" data-id="'.$item->id_instansi.'" href="javascript:void(0)">Edit</a>
                        </div>
                        <div class="col-md-6">
                            <a href="javascript:void(0)" class="btn btn-sm btn-danger w-100 text-white delete-link" data-id-destroy="'.$item->id_instansi.'" onclick="deleteConfirmation('.$item->id_instansi.')">Delete</a>
                        </div>
                    </div>
                    ';
                })
                ->rawColumns(['action','roles'])
                ->make();

        }
        return view('pages.superadmin.instansi.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = TipeInstansi::create(['tipe_instansi' => $request->tipe_instansi]);

        return Response::json($data, 200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $instansi = TipeInstansi::where('id_instansi',$id)->first();
        return response()->json($instansi);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        if($request){
            $query = TipeInstansi::updateOrCreate(['id_instansi' => $request->id_instansi],['tipe_instansi' => $request->tipe_instansi_edit]);
        }

        return response()->json($query);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        TipeInstansi::find($id)->delete($id);
        return response()->json([
            'success' => 'Record deleted successfully!'
        ]);
    }
}
