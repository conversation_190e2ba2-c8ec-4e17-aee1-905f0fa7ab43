<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Survey;
use App\Models\SurveyResponses;
use App\Models\Question;
use Illuminate\Support\Facades\Response;
use App\Models\SurveyLink;
use App\Models\Option;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;

class SurveyController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $with = [];
        return view('pages.survey.index', $with);
    }

    public function getDataTableSurvey()
    {
        if(request()->ajax())
        {
            $query = Survey::query();
            return DataTables::of($query)
                ->addColumn('total_question', function($item) {
                   return count($item->questions);
                })
                ->addColumn('action', function($item){
                    return '
                    <div class="row">
                        <div class="col-md-6">
                            <a class="btn btn-sm btn-primary w-100 text-white" data-id="'.$item->id.'" href="'.url('survey/edit').'/'.$item->id.'">Edit</a>
                        </div>
                        <div class="col-md-6">
                            <a href="javascript:void(0)" class="btn btn-sm btn-danger w-100 text-white delete-link" data-id-destroy="'.$item->id.'" onclick="deleteConfirmation('.$item->id.')">Delete</a>
                        </div>
                    </div>
                    ';
                })
                ->rawColumns(['action','roles','options'])
                ->make();

        }
        return view('pages.survey.list-survey');
    }

    public function getDataTableQuestion()
    {
        if(request()->ajax())
        {
            $query = Question::query();
            return DataTables::of($query)
                ->editColumn('question_type', function($item) {
                    return questionType($item->question_type);
                })
                ->addColumn('options', function($item) {
                    $options = '<ul style="margin: 0; padding-left: 20px;">';
                    foreach ($item->options as $option) {
                        $options .= '<li>' . htmlspecialchars($option->option_text) . ' (Bobot: ' . htmlspecialchars($option->bobot) . ')</li>';
                    }
                    $options .= '</ul>';
                    return $options;
                })
                ->addColumn('action', function($item){
                    return '
                    <div class="row">
                        <div class="col-md-6">
                            <a class="btn btn-sm btn-primary w-100 text-white" data-id="'.$item->id.'" href="'.url('survey/question/edit').'/'.$item->id.'">Edit</a>
                        </div>
                        <div class="col-md-6">
                            <a href="javascript:void(0)" class="btn btn-sm btn-danger w-100 text-white delete-link" data-id-destroy="'.$item->id.'" onclick="deleteConfirmation('.$item->id.')">Delete</a>
                        </div>
                    </div>
                    ';
                })
                ->rawColumns(['action','roles','options'])
                ->make();

        }
        return view('pages.survey.question');
    }

    public function editQuestion($id)
    {
        $question = Question::find($id);
        if ($question) {
            return view('pages.survey.question_edit', [
                'question' => $question,
                'options' => $question->options
            ]);
        } else {
            return view('pages.survey.question', [
                'error_message' => 'Question not found'
            ]);
        }
    }

    public function detail(Request $request) {
        $with = [];
        $tab = $request->tab;
        switch ($tab) {
            case 'assign-survey':
                return view('pages.survey.assign-survey', $with);
                break;

            case 'list-survey':
                $question = Question::all();
                $with['questions'] = $question;
                return view('pages.survey.list-survey', $with);
                break;

            case 'question':
                return view('pages.survey.question', $with);
                break;
            
            default:
                return view('pages.survey.responses', $with);
                break;
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function createSurvey(Request $request)
    {
        try {
            $data = Survey::create([
                'title' => $request->title,
                'description' => $request->description,
            ]);
    
            if (request()->has('question_id')) {
                for ($i=0; $i < count($request->question_id); $i++) { 
                    $data->questions()->create([
                        'survey_id' => $data->id,
                        'question_id' => $request->question_id[$i],
                    ]);
                }
                    
            }
    
            return Response::json($data, 200);
        } catch (\Throwable $th) {
            return Response::json([
                'message' => 'Error creating question',
                'error' => $th->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */

    public function createQuestion(Request $request)
    {
        try {
            $data = Question::create([
                'question_text' => $request->question_text,
                'question_type' => $request->question_type,
            ]);
    
            if (request()->has('option_text')) {
                for ($i=0; $i < count($request->option_text); $i++) { 
                    $data->options()->create([
                        'option_text' => $request->option_text[$i],
                        'bobot' => $request->bobot[$i],
                        'sequence' => $request->sequence[$i]
                    ]);
                }
                    
            }
    
            return Response::json($data, 200);
        } catch (\Throwable $th) {
            return Response::json([
                'message' => 'Error creating question',
                'error' => $th->getMessage()
            ], 500);
        }
    }

    public function updateQuestion(Request $request)
    {
        try {
            $question = Question::find($request->id);
            if ($question) {
                $question->update([
                    'question_text' => $request->question_text,
                    'question_type' => $request->question_type,
                ]);
                
                if (request()->has('remove_option_id')) {
                    foreach ($request->remove_option_id as $removeId) {
                        $option = $question->options()->find($removeId);
                        if ($option) {
                            $option->delete();
                        }
                    }
                }
                
                if (request()->has('option_text')) {
                    for ($i=0; $i < count($request->option_text); $i++) { 
                        // Update existing options
                        $question->options()->updateOrCreate(
                            ['id' => $request->option_id[$i]],
                            [
                                'option_text' => $request->option_text[$i],
                                'bobot' => $request->bobot[$i],
                                'sequence' => $request->sequence[$i]
                            ]
                        );
                    }
                }
    
                return Response::json($question, 200);
            } else {
                return Response::json(['message' => 'Question not found'], 404);
            }
        } catch (\Throwable $th) {
            return Response::json([
                'message' => 'Error updating question',
                'error' => $th->getMessage()
            ], 500);
        }
    }

    public function destroyQuestion(Request $request)
    {
        try {
            $question = Question::find($request->id);
            if ($question) {
                if ($question->options) {
                    foreach ($question->options as $option) {
                        $option->delete();
                    }
                }
                $question->delete();
                return Response::json(['message' => 'Question deleted successfully'], 200);
            } else {
                return Response::json(['message' => 'Question not found'], 404);
            }
        } catch (\Throwable $th) {
            return Response::json([
                'message' => 'Error deleting question',
                'error' => $th->getMessage()
            ], 500);
        }
    }

    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function editSurvey($id)
    {   
        $survey = Survey::find($id);
        $questions = Question::all();
        if ($survey) {
            return view('pages.survey.survey_edit', [
                'survey' => $survey,
                'questions' => $survey->questions,
                'data_questions' => $questions,
            ]);
        } else {
            return view('pages.survey.survey', [
                'error_message' => 'Survey not found'
            ]);
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updateSurvey(Request $request, $id)
    {
        try {
            $survey = Survey::find($id);
            if ($survey) {
                $survey->update([
                    'title' => $request->title,
                    'description' => $request->description,
                ]);
                
                if (request()->has('remove_question_id')) {
                    foreach ($request->remove_question_id as $removeId) {
                        $question = $survey->questions()->find($removeId);
                        if ($question) {
                            $question->delete();
                        }
                    }
                }
                
                if (request()->has('survey_question_id')) {
                    for ($i=0; $i < count($request->survey_question_id); $i++) { 
                        // Update existing options
                        $survey->questions()->updateOrCreate(
                            ['id' => $request->survey_question_id[$i]],
                            [
                                'question_id' => $request->question_id[$i]
                            ]
                        );
                    }
                }
    
                return Response::json($survey, 200);
            } else {
                return Response::json(['message' => 'Survey not found'], 404);
            }
        } catch (\Throwable $th) {
            return Response::json([
                'message' => 'Error updating question',
                'error' => $th->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroySurvey($id)
    {
        try {
            $survey = Survey::find($id);
            if ($survey) {
                if ($survey->questions) {
                    foreach ($survey->questions as $question) {
                        $question->delete();
                    }
                }
                $survey->delete();
                return Response::json(['message' => 'Question deleted successfully'], 200);
            } else {
                return Response::json(['message' => 'Question not found'], 404);
            }
        } catch (\Throwable $th) {
            return Response::json([
                'message' => 'Error deleting question',
                'error' => $th->getMessage()
            ], 500);
        }
    }

    public function getAssignSurvey(Request $request)
    {
     
        $status_survey = $request->input('status_survey');
        $fromDate = $request->input('from_date');
        $toDate = $request->input('to_date');

        $fromSubmitDate = $request->input('from_submit_date');
        $toSubmitDate = $request->input('to_submit_date');

        $query = DB::table('tb_customers')
            ->leftJoin('tb_orders', 'tb_customers.id_customer', '=', 'tb_orders.id_customer')
            ->leftJoin('tb_kode_orders', 'tb_orders.order_key', '=', 'tb_kode_orders.order_key')
            ->leftJoin('tb_survey_link', 'tb_orders.id_order', '=', 'tb_survey_link.order_id')
            ->select(['tb_customers.id_customer', 'nama', 'kode_kustomer', 'tb_orders.id_order', 'kode_order', 'sko', 'tgl_order', 'status_deal', 'tb_orders.order_key','tb_survey_link.generate_link', 'tb_survey_link.survey_id as id_survey', 'tb_survey_link.submited_date'])
            ->where('status_deal', 'like', 'Deal')
            ->where('tb_orders.status_order', '=', 'Selesai Produksi')
            ->where('tb_orders.flag_dummy', '=', 'Produksi Massal')
            ->where(function ($query) use ($status_survey, $fromDate, $toDate, $fromSubmitDate, $toSubmitDate) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                }
                // Handle order date range
                $fromDate && $query->where('tb_orders.tgl_order', '>=', $fromDate);
                $toDate && $query->where('tb_orders.tgl_order', '<=', $toDate);

                // Handle submit date range 
                $fromSubmitDate && $query->where('tb_survey_link.submited_date', '>=', $fromSubmitDate);
                $toSubmitDate && $query->where('tb_survey_link.submited_date', '<=', $toSubmitDate);
                
                switch ($status_survey) {
                    case 'ungenerated':
                        $query->whereNUll('tb_survey_link.generate_link');
                        break;
                    case 'unsubmited':
                        $query->whereNotNull('tb_survey_link.generate_link')->whereNull('tb_survey_link.submited_date');
                        break;
                    case 'submited':
                        $query->whereNotNull('tb_survey_link.generate_link')->whereNotNull('tb_survey_link.submited_date');
                        break;
                }
            });


        return DataTables::of($query)
            ->editColumn('kode_order', function ($row) {
                $param = Crypt::encryptString($row->id_order);
                return '<a href="#" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->kode_order . '</a>';
            })
            ->editColumn('sko', function ($row) {
                $param = Crypt::encryptString($row->id_order);
                return '<a href="' . route('crm.detail_order', $param) . '" class="text-gray-800 text-hover-primary mb-1 text-decoration-underline">' . $row->sko . '</a>';
            })
            ->editColumn('nama', function ($row) {
                return '<a href="' . route('crm.detail_customer', $row->kode_kustomer) . '" class="text-gray-800 text-hover-primary mb-1">' . $row->nama . '</a>';
            })
            ->editColumn('tgl_order', function ($row) {
                return $row->tgl_order ? date('d M Y', strtotime($row->tgl_order)) : '-';
            })
            ->editColumn('submited_date', function ($row) {
                return $row->submited_date ? date('d M Y', strtotime($row->submited_date)) : '-';
            })
            ->addColumn('action', function ($row) use ($request){
                $param = Crypt::encryptString($row->id_order);

                if (!isset($row->generate_link)) {
                    if ($request->has('is_report')) {
                        return 'Belum Generate Link';
                    } else {
                        return '<div class="btn-group d-flex">
                                    <button class="btn btn-sm btn-primary w-100 generate-link" data-toggle="tooltip" href="javascript:void(0)" data-order-id="'.$row->id_order.'">Generate Link</button>
                                </div>';
                    }
                } else {
                    $btn = '<div class="btn-group d-flex">';
                    if ($row->submited_date) {
                        $btn .= '<a class="btn btn-sm btn-warning w-100 text-white" data-toggle="tooltip" target="_blank" href="' . route('survey.do_survey', ['id' => Crypt::encryptString($row->id_order), 'is_show' => true]) . '">Lihat Survey</a>';
                    } else {
                        $btn .= '<button type="button" class="btn btn-sm btn-success w-100" data-toggle="tooltip" onclick="copyLink(\'' . $row->generate_link . '\')">Copy Link</button>';
                    }
                    $btn .= '</div>';
                    return $btn;
                }
            })
            ->rawColumns(['sko', 'nama', 'tgl_order', 'action'])
            ->make(true);
    
    }

    public function get_survey(Request $request)
    {
        $input = $request->all();

        if (!empty($input['q'])) {
            $data = Survey::select(['id','title','description'])
                ->where('title', 'LIKE', "%{$input['q']}%")->get();
        } else {
            $data = Survey::select(['id','title','description'])->get();
        }

        $list_survey = [];

        if (count($data) > 0) {
            foreach ($data as $row) {
                $list_survey[] = array(
                    "id" => $row->id,
                    "name" => $row->title . ' - ' . count($row->questions) . ' Questions',
                );
            }
        }
        return Response::json($list_survey, 200);
    }

    public function generateLink(Request $request)
    {
        try {
            $order_id   = $request->input('id_order');
            $survey_id  = $request->input('survey_id');

            $link       = url("survey/do_survey",['order id' => Crypt::encryptString($order_id)]);
            
            $data = SurveyLink::updateOrInsert(
                ['order_id' => $order_id],
                [
                    'survey_id'     => $survey_id,
                    'generate_link' => $link,
                    'created_at'    => now(),
                    'updated_at'    => now()
                ]
            );

            return Response::json(['generate_link' => $link], 200);
        } catch (\Exception $th) {
            return Response::json(['message' => $th->getMessage()], 500);
        }
    }

    public function doSurvey(Request $request, $order_id)
    {
        $order_id = Crypt::decryptString($order_id);
        $survey = SurveyLink::where('order_id', $order_id)->first();
        if ($survey) {
            $data = [
                'survey' => $survey,
                'questions' => $survey->survey->questions
            ];
            if ($request->is_show) {
                $data['is_show'] = true;
            }
            return view('pages.survey.do-survey', $data);
        } else {
            return view('pages.survey.do-survey', [
                'error_message' => 'Survey not found'
            ]);
        }
    }

    public function submitSurvey(Request $request)
    {
        try {
            $survey = SurveyLink::find($request->survey_link_id);
            if ($survey) {
                foreach ($request->question_id as $key => $q) {
                    $data = [];
                    $question = Question::find($q);
                    
                    $data = [
                        'survey_link_id' => $request->survey_link_id,
                        'survey_question_id' => $question['id'],
                        'question_text' => $question['question_text'],
                    ];

                    $bobot = 0;
                    if ($request->question_type[$key] == 'single_choice') {
                        $answer = Option::find($request->answers[$q]);
                        $bobot = $answer['bobot'];
                        $data = array_merge($data, ['bobot' => $bobot, 'option_text' => $answer['option_text']]);
                    }

                    SurveyResponses::create($data);
                }
                // Update the survey link with the submitted date
                $survey->update([
                    'submited_date' => now(),
                    'submited_by' => $request->name
                ]);
                // Redirect to doSurvey method with order_id
                return redirect()->route('survey.do_survey', ['id' => Crypt::encryptString($survey->order_id)])->with('success', 'Survey submitted successfully');
            } else {
                return redirect()->route('survey.do_survey', ['id' => Crypt::encryptString($request->order_id)])->with('error', 'Survey not found');
            }
        } catch (\Throwable $th) {
            return Response::json([
                'message' => 'Error submitting survey',
                'error' => $th->getMessage()
            ], 500);
        }
    }
}
