<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Customer;
use App\Models\SuratJalan;
use App\Models\Order;
use Illuminate\Http\Request;
use App\Models\SuratJalanDetail;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpWord\TemplateProcessor;
use Yajra\DataTables\Facades\DataTables;
use DateTime;
use Illuminate\Support\Facades\Response;

class SuratJalanController extends Controller
{
    public function customers(Request $request)
    {
        $customers = Customer::select('id_customer as id', 'nama', 'nama_instansi', 'no_hp')
            ->when($request->input('q'), function ($query, $keyword) {
                $query->where('nama', 'like', '%' . $keyword . '%');
            })
            ->orderBy('nama', 'asc')
            ->paginate(10);

        return response()->json($customers);
    }

    public function index()
    {
        // $customers = Customer::all();
        return view('pages.surat_jalan.index');
    }

    public function create()
    {
        return view('pages.surat_jalan.create-edit');
    }

    public function edit($id)
    {
        $data = SuratJalan::with(['details', 'customer', 'createdBy'])
            ->where('surat_jalan_key', $id)
            ->firstOrFail();
        return view('pages.surat_jalan.create-edit', compact('data'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'customer_id' => 'required|exists:tb_customers,id_customer',
            'product_name' => 'required|array',
            'product_name.*' => 'required|string|max:255',
            'description' => 'nullable|array',
            'description.*' => 'nullable|string|max:255',
            'qty' => 'required|array',
            'qty.*' => 'required|integer|min:1',
        ]);

        $input = $request->all();
        try {
            DB::transaction(function () use ($input, $request) {
                $customer = Customer::find($input['customer_id']);

                $no_surat_jalan = SuratJalan::generateSuratJalanNumber();

                if ($request->hasFile('lampiran-sign')) {
                    $file = $request->file('lampiran-sign'); 
                    $filenameSimpanSign = upload_file($file, 'suratjalan', 'Surat Jalan');
                } else {
                    $filenameSimpanSign = '';
                }

                $input['sign_lampiran'] = $filenameSimpanSign;

                $datas = [
                    'no_surat_jalan' => $no_surat_jalan['no_surat_jalan'],
                    'no_number' => $no_surat_jalan['no_number'],
                    'customer_id' => $input['customer_id'],
                    'company_id' => $customer->company_id ?? null,
                    'no_po' => $input['no_po'],
                    'delivery_date' => $input['delivery_date'],
                    'created_by_id' => auth()->id(),
                    'sign_lampiran' => $input['sign_lampiran'],
                    'status' => $input['status']
                ];

                if ($input['id'] != null) {
                    unset($datas['no_surat_jalan']);
                }

                $surat_jalan = SuratJalan::updateOrCreate([
                    'surat_jalan_key' => $input['id'] ?? null,
                ], $datas);

                $totalQuantity = 0;
                $totalPrice = 0;

                $collectId = [];
                foreach ($input['product_name'] as $key => $product_name) {
                    if (empty($input['product_name'][$key]) || empty($input['qty'][$key])) {
                        continue;
                    }

                    $detailData = [
                        'product_name' => $product_name ?? "",
                        'description' => $input['description'][$key] ?? "",
                        'quantity' => $input['qty'][$key] ?? 0,
                        'kode_order' => $input['kode_order'][$key] ?? "-",
                        'jumlah_koli' => $input['jumlah_koli'][$key] ?? 0,
                        'surat_jalan_key' => $surat_jalan->surat_jalan_key,
                    ];

                    $surat_jalanDetail = $surat_jalan->details()->updateOrCreate(
                        ['id' => $input['id_product'][$key] ?? null],
                        $detailData
                    );

                    $collectId[] = $surat_jalanDetail->id;
                }

                if (count($collectId) > 0) {
                    SuratJalanDetail::where('surat_jalan_key', $surat_jalan->surat_jalan_key)
                        ->whereNotIn('id', $collectId)->delete();
                }

                foreach ($surat_jalan->details as $detail) {
                    $totalQuantity += $detail->quantity;
                }

                $surat_jalan->update([
                    'total_quantity' => $totalQuantity,
                ]);
            });

            return redirect(route('surat-jalan.index'))
                ->with('success', 'Surat Jalan saved successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to save surat jalan: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function show($id)
    {
        $data = SuratJalan::with(['details', 'customer', 'createdBy'])
            ->where('surat_jalan_key', $id)
            ->firstOrFail();
        return view('pages.surat_jalan.show', compact('data'));
    }

    public function destroy($id)
    {
        try {
            $surat_jalan = SuratJalan::where('surat_jalan_key', $id)->firstOrFail();
            $surat_jalan->details()->delete();
            $surat_jalan->delete();

            return redirect()->route('surat-jalan.index')
                ->with('success', 'Surat Jalan deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->route('surat-jalan.index')
                ->with('error', 'Failed to delete surat jalan: ' . $e->getMessage());
        }
    }

    public function dataTable(Request $request)
    {
        $data = SuratJalan::select(
            'id',
            'surat_jalan_key',
            'company_id',
            'customer_id',
            'no_surat_jalan',
            'total_quantity',
            'delivery_date',
            'created_by_id',
            'created_at',
            'status'
        )->with(['details', 'customer', 'createdBy'])
        ->filter($request)
            ->latest();

        return DataTables::of($data)
            ->addindexColumn()
            ->addColumn('delivery_date', function ($row) {
                return $row->delivery_date ? date('d M Y', strtotime($row->delivery_date)) : '-';
            })
            ->addColumn('created_at', function ($row) {
                return $row->created_at ? date('d M Y H:i', strtotime($row->created_at)) : '-';
            })
            ->addColumn('created_by', function ($row) {
                return $row->createdBy ? $row->createdBy->name : '-';
            })
            ->addColumn('action', function ($row) {
                $editUrl = route('surat-jalan.edit', $row->surat_jalan_key);
                $generateDocUrl = route('surat-jalan.generateSuratJalan', $row->surat_jalan_key);
                $detailUrl = route('surat-jalan.show', $row->surat_jalan_key);

                $generateDocButton = '<a href="' . $generateDocUrl . '" target="_blank" class="btn btn-sm px-4 btn-secondary"><i class="fas fa-file-alt p-0"></i></a>';

                $dropdownMenu = '<div class="btn-group">
                                    <button type="button" class="btn" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end" style="max-width: 80px">';

                $dropdownMenu .= '<li><a class="dropdown-item py-3 px-5" href="' . $detailUrl . '"><i class="fas fa-eye p-0"></i> View</a></li>';

                $dropdownMenu .= '<li><a class="dropdown-item py-3 px-5" href="' . $editUrl . '"><i class="fas fa-edit p-0"></i> Edit</a></li>';
                if (strtoupper($row->status) !== 'SUBMITTED') {
                    $dropdownMenu .= '<li><button class="dropdown-item py-3 px-5 deleteData" data-id="' . $row->surat_jalan_key . '" data-input=\'' . json_encode($row) . '\'><i class="fas fa-trash-alt p-0"></i> Delete</button></li>';
                }

                $dropdownMenu .= '</ul></div>';

                return $generateDocButton . ' ' . $dropdownMenu;
            })
            ->addColumn('status_badge', function ($row) {
                $status = strtoupper($row->status);
                $badgeClass = '';

                switch ($status) {
                    case 'DRAFT':
                        $badgeClass = 'bg-info';
                        break;
                    case 'SUBMITTED':
                        $badgeClass = 'bg-primary';
                        break;
                }

                return '<span class="badge ' . $badgeClass . '">' . $status . '</span>';
            })
            ->addColumn('produk', function ($row) {
                return '<table class="table table-bordered">
                        <thead>
                            <tr class="bg-secondary">
                                <th style="font-size: 12px; white-space: nowrap" class="px-3 py-2">Kode Order</th>
                                <th style="font-size: 12px; white-space: nowrap" class="px-3 py-2">Nama Produk</th>
                                <th style="font-size: 12px; white-space: nowrap" class="px-3 py-2">Quantity</th>
                            </tr>
                        </thead>
                        <tbody>' .
                    collect($row->details)->map(function ($detail) {
                        return '<tr>
                                        <td class="px-3 py-2">' . $detail->kode_order . '</td>
                                        <td class="px-3 py-2">' . $detail->product_name . '</td>
                                        <td class="px-3 py-2">' . $detail->quantity . '</td>
                                    </tr>';
                    })->implode('') .
                    '</tbody>
                    </table>';
            })
            ->rawColumns(['action', 'created_at', 'created_by', 'status_badge','produk','delivery_date'])
            ->smart(true)
            ->make(true);
    }


    public function generateSuratJalan($id)
    {
        $data = SuratJalan::with(['details'])
                            ->where('surat_jalan_key', $id)
                            ->firstOrFail();

        $templateProcessor = new TemplateProcessor(public_path('templates/surat-jalan/template_surat_jalan.docx'));
        $formatted_delivery_date = \Carbon\Carbon::parse($data->delivery_date)->locale('id')->isoFormat('D MMMM YYYY');
        $templateProcessor->setValue('tanggal_pengiriman', $formatted_delivery_date);
        $templateProcessor->setValue('no_surat_jalan', $data->no_surat_jalan);
        $templateProcessor->setValue('nama_customer', $data->customer->nama??"-");
        $templateProcessor->setValue('no_hp_customer', $data->customer->no_hp??"-");
        $templateProcessor->setValue('no_po', $data->no_po??"-");

        $nama_instansi = $data->customer->company->name ?? $data->customer->nama ?? "";
        $templateProcessor->setValue('nama_instansi', $nama_instansi);

        $alamat_instansi = $data->customer->company->address ?? $data->customer->alamat_instansi ?? "";
        $alamat_lengkap = "";
        if (isset($data->customer->company->village)) {
            $alamat_instansi .= ", " . $data->customer->company->village->name;
        }
        if (isset($data->customer->company->subdistrict)) {
            $alamat_lengkap .= $data->customer->company->subdistrict->name;
        }
        if (isset($data->customer->company->district)) {
            $alamat_lengkap .= ", " . $data->customer->company->district->name;
        }
        if (isset($data->customer->company->province)) {
            $alamat_lengkap .= ", " . $data->customer->company->province->name;
        }
        if (isset($data->customer->company->zip_code)) {
            $alamat_lengkap .= ", " . $data->customer->company->zip_code;
        }
        $templateProcessor->setValue('alamat', $alamat_instansi);
        $templateProcessor->setValue('alamat_lengkap', $alamat_lengkap);
        
        $templateProcessor->cloneRow('nama_produk', count($data->details));

        foreach ($data->details as $index => $detail) {
            $rowNumber = $index + 1;
        
            $templateProcessor->setValue('nama_produk#' . $rowNumber, $detail['product_name'] ?? '');
            $templateProcessor->setValue('keterangan#' . $rowNumber, $detail['description'] ?? '');
            $templateProcessor->setValue('quantity#' . $rowNumber, $detail['quantity'] ? number_format($detail['quantity'], 0, '', '.') : '');
            $templateProcessor->setValue('jumlah_koli#' . $rowNumber, $detail['jumlah_koli'] ?? '');
            $templateProcessor->setValue('kode_order#' . $rowNumber, $detail['kode_order'] ?? '');
        }

        $datemark = date('dmy');
        $filename = "Surat Jalan - {$data->no_surat_jalan} - {$nama_instansi} - {$datemark}.docx";
        $templateProcessor->saveAs($filename);

        \PhpOffice\PhpWord\Settings::setOutputEscapingEnabled(true);
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($filename));
        ob_clean();
        flush();
        readfile($filename);
        unlink($filename);
        exit;
    }

    public function getOrderDetail(Request $request)
    {
        $order = Order::with(['tb_produksi:sko_key,nama_produk,harga_produk,jumlah_produk,notes'])
            ->where('sko', $request->input('sko'))
            ->first();

        if (!$order) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        return response()->json($order);
    }
}
