<?php

namespace App\Http\Controllers;

use App\Models\DraftHitungan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Order;
use App\Models\Produksi;
use App\Models\KertasPlano;

class DraftHitunganController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(request()->ajax())
        {
            $query = DraftHitungan::with(['tb_bahans','tb_produksi','tb_orders','tb_orders.tb_customer','tb_orders.user'])->orderBy('created_at', 'DESC')->get();
            return DataTables::of($query)
                ->addColumn('action', function($item){
                    return '
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="">
                            <a class="btn btn-sm btn-primary text-white detail_draft text-center ps-5 pe-3 m-0" data-id="'.$item->id.'" href="javascript:void(0)"><i class="fa fa-eye"></i></a>
                        </div>
                        <div class="">
                            <a href="javascript:void(0)" class="btn btn-sm btn-danger text-white delete-link text-center ps-5 pe-3 m-0" data-id-destroy="'.$item->id.'" onclick="deleteConfirmation('.$item->id.')"><i class="fa fa-trash"></i></a>
                        </div>
                    </div>
                    ';
                })
                ->addColumn('bahan', function($item){
                    return $item->tb_bahans->bahan??'';
                })
                ->addColumn('customer', function($item){
                    return $item->tb_orders->tb_customer->nama??'';
                })
                ->addColumn('dimensi', function($item){
                    return $item->dimensi_p.'x'.$item->dimensi_l.'x'.$item->dimensi_t;
                })
                ->editColumn('harga_modal', function($item){
                    return 'Rp. ' . number_format($item->harga_modal, 0, '', ',');
                })
                ->editColumn('status', function($item){
                    if ($item->status == 1) {
                        return '<span class="badge badge-danger badge-sm">Belum diklaim</span>';
                    } else {
                        return '<span class="badge badge-success badge-sm">Sudah diklaim</span>';
                    }
                })
                ->editColumn('quantity', function($item){
                    return number_format($item->quantity, 0, '', ',');
                })
                ->editColumn('created_at', function($item){
                    return [
                        'display' => date("d M Y",strtotime($item->created_at)),
                        'timestamp' => strtotime($item->created_at)
                    ];
                })
                ->addColumn('pic', function($item){
                    return $item->tb_orders->user->name??'';
                })
                ->rawColumns(['action','dimensi','roles','status'])
                ->make();

        }
        $with['drafts'] = $data = DraftHitungan::with(['tb_bahans'])->where('status', '=', 1)->get();
        return view('pages.superadmin.draft_hitungan.index', $with);
    }

    public function store(Request $request)
    {
        $drafts = [];
        $additional_cost = [];
        $finishing = [];
        $laminasi = [];
        if (count($request->id_draft) > 0) {
            foreach ($request->id_draft as $key => $value) {
                $item = DraftHitungan::find($value);
                $drafts[] = $item;
                if ($item->additional_cost) {
                    $additional_cost[] = json_decode($item->additional_cost);
                }
            }
        }
        if (count($additional_cost) > 0) {
            foreach ($additional_cost as $arr) {
                if (array_key_exists('magnet/tali',$arr)) {
                    if (!in_array('Magnet/Tali', $finishing)){
                        $finishing[] = 'Magnet/Tali';
                    }
                }
                if (array_key_exists('Jendela Mika',$arr)) {
                    if (!in_array('Jendela Mika', $finishing)){
                        $finishing[] = 'Jendela Mika';
                    }
                }
                if (array_key_exists('Lem Lapis',$arr)) {
                    if (!in_array('Lem Lapis', $finishing)){
                        $finishing[] = 'Lem Lapis';
                    }
                }
                if (array_key_exists('Spot UV',$arr)) {
                    if (!in_array('Spot UV', $finishing)){
                        $finishing[] = 'Spot UV';
                    }
                }
                if (array_key_exists('UV Varnish',$arr)) {
                    if (!in_array('UV Varnish', $finishing)){
                        $finishing[] = 'UV Varnish';
                    }
                }
                if (array_key_exists('Klise Hot Stamp',$arr) || array_key_exists('Ongkos Hot Stamp',$arr)) {
                    if (!in_array('Hot Stamp', $finishing)){
                        $finishing[] = 'Hot Stamp';
                    }
                }
                if (array_key_exists('Klise Emboss',$arr) || array_key_exists('Ongkos Emboss',$arr)) {
                    if (!in_array('Emboss', $finishing)){
                        $finishing[] = 'Emboss';
                    }
                }
                if (array_key_exists('Lem Samping Corrugated',$arr) || array_key_exists('Lem Samping Softbox',$arr) || array_key_exists('Lem Samping',$arr)) {
                    if (!in_array('Lem Samping', $finishing)){
                        $finishing[] = 'Lem Samping';
                    }
                }
                if (array_key_exists('Sablon',$arr)) {
                    if (!in_array('Sablon', $finishing)){
                        $finishing[] = 'Sablon';
                    }
                }
                if (array_key_exists('Laminasi Glossy',$arr)) {
                    if (!in_array('Laminasi Glossy', $laminasi)){
                        $laminasi[] = 'Laminasi Glossy';
                    }
                }
            }
            $finishing[] = 'Pond/potong';
        }
        
        $box_utama = collect($drafts)->where('type',1)->first();
        if ($box_utama->id_bahan == 2) {
            $finishing[] = 'Finishing Hardbox';
        }
        $kertas_plano = KertasPlano::select('kertas_plano')->find($box_utama->id_kertas_plano);
        if ($drafts) {
            $produksi = Produksi::find($request->id_produksi);
            if ($produksi) {
                $lp = explode(" x ",$box_utama->luas_permukaan);
                $input = [
                    "jumlah_produk" => str_replace(',','',$request->quantity),
                    "modal_sales" => str_replace(',','',str_replace('Rp ','',$request->harga_modal)),
                    "dp_panjang" => $box_utama->dimensi_p,
                    "dp_lebar" => $box_utama->dimensi_l,
                    "dp_tinggi" => $box_utama->dimensi_t,
                    "lp_panjang" => $lp[0],
                    "lp_lebar" => $lp[1],
                    "isi_kertas" => $kertas_plano->kertas_plano,
                    "jenis_kertas" => $box_utama->tb_jenis_kertas->jenis_kertas,
                    "get_plano" => $request->jumlah_potong,
                    "gramasi" => $box_utama->tb_gramasi->gramasi,
                    "finishing" => implode(",",$finishing),
                    "laminasi" => implode(",",$laminasi)
                ];
                
                $produksi->update($input);

                if ($produksi) {
                    foreach ($drafts as $key => $value) {
                        $draft = DraftHitungan::find($value->id);
                        $update = [
                            "status" => 2,
                            "id_produksi" => $produksi->id_produksi,
                            "order_key" => $produksi->order_key,
                            "sko_key" => $produksi->sko_key
                        ];
                        $draft->update($update);
                    }
                }

                return response()->json([
                    "status_code" => 200,
                    "message" => "success"
                ],200);
            }
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function detail($id)
    {
        $with = [];
        $detail = DraftHitungan::with(['tb_bahans','tb_jenis_kertas','tb_kertas_plano','tb_gramasi','tb_layout'])
        ->find($id);

        $with['data'] = $detail;
        return view('pages.superadmin.draft_hitungan.detail', $with);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        DraftHitungan::find($id)->delete($id);
        return response()->json([
            'success' => 'Record deleted successfully!'
        ]);
    }

    public function show()
    {

    }

    public function getOrderProduct(Request $request)
    {
        $input = $request->all();
        
        if (!empty($input['term'])) {
            $keyword = $input['term'];
            $data = Order::with(['tb_customer','tb_produksi'])
            ->where('status_deal', '=', 'Follow Up')
            ->whereHas('tb_customer', function($q) use ($keyword){
                $q->where('nama', 'LIKE', '%'.$keyword.'%');
            })
            ->get();
        } else {
            $data = Order::with(['tb_customer','tb_produksi'])->where('status_deal', '=', 'Follow Up')->get();
        }
        $list = [];
        
        if (count($data) > 0) {
            foreach ($data as $row) {
                $list[] = array(
                    "id" => $row->tb_produksi->id_produksi,
                    "text" => $row->tb_customer->nama.' ('.$row->tb_produksi->jenis_bahan.')'
                );
            }
        }
        return Response::json($list, 200);
    }

    public function getListDraft(Request $request)
    {
        $input = $request->all();
        
        if (!empty($input['term'])) {
            $keyword = $input['term'];
            $data = DraftHitungan::with(['tb_bahans'])->where('status', '=', 1)
            ->where('description', 'LIKE', '%'.$keyword.'%')
            ->get();
        } else {
            $data = DraftHitungan::with(['tb_bahans'])->where('status', '=', 1)->get();
        }
        $list = [];
        
        if (count($data) > 0) {
            foreach ($data as $row) {
                $list[] = array(
                    "id" => $row->id,
                    "text" => $row->code.' - '.$row->description.' ('.$row->tb_bahans->bahan.')'
                );
            }
        }
        return Response::json($list, 200);
    }

    public function getDraft(Request $request)
    {
        $harga_modal = 0;
        $drafts = [];
        if (count($request->id_draft) > 0) {
            foreach ($request->id_draft as $key => $value) {
                $drafts[] = DraftHitungan::find($value);
            }
        }
        $box_utama = collect($drafts)->where('type',1)->first();

        $harga_modal = collect($drafts)->sum('harga_modal') * $box_utama->quantity;
        
        if ($drafts) {
            $luas = explode(' x ', $box_utama->luas_permukaan);
            return response()->json([
                "p" => $luas[0],
                "l" => $luas[1],
                "jumlah_potong" => $box_utama->jumlah_potong,
                "waste" => $box_utama->waste,
                "quantity" => $box_utama->quantity,
                "harga_modal" => $harga_modal
            ],200);
        }
    }
}
