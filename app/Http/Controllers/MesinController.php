<?php

namespace App\Http\Controllers;

use App\Models\Mesin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Yajra\DataTables\Facades\DataTables;

class MesinController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (request()->ajax()) {
            $query = Mesin::query();
            return DataTables::of($query)
                ->addColumn('action', function ($item) {
                    return '
                    <div class="row">
                        <div class="col-md-6">
                            <a class="btn btn-sm btn-primary w-100 text-white edit-mesin" data-id="' . $item->id_mesin . '" href="javascript:void(0)">Edit</a>
                        </div>
                        <div class="col-md-6">
                            <a href="javascript:void(0)" class="btn btn-sm btn-danger w-100 text-white delete-link" data-id-destroy="' . $item->id_mesin . '" onclick="deleteConfirmation(' . $item->id_mesin . ')">Delete</a>
                        </div>
                    </div>
                    ';
                })
                ->editColumn('koefisien', function ($item) {
                    if (!empty($item->koefisien))
                        return $item->koefisien.' %';
                })
                ->editColumn('mesin', function ($item) {
                    return 'Mesin '.$item->mesin;
                })
                ->rawColumns(['action', 'roles'])
                ->make();
        }
        return view('pages.superadmin.mesin.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = Mesin::create([
            'mesin' => $request->mesin,
            'max_area_print' => $request->max_area_print,
            'min_area_print' => $request->min_area_print,
            'max_size_paper' => $request->max_size_paper,
            'min_size_paper' => $request->min_size_paper,
            'insheet'        => $request->insheet,
            'koefisien'      => $request->koefisien
        ]);

        return Response::json($data, 200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $mesin = Mesin::where('id_mesin', $id)->first();
        return response()->json($mesin);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        if ($request) {
            $query = Mesin::updateOrCreate(
                ['id_mesin' => $request->id_mesin],
                [
                    'mesin' => $request->mesin_edit,
                    'max_area_print' => $request->max_area_print_edit,
                    'min_area_print' => $request->min_area_print_edit,
                    'max_size_paper' => $request->max_size_paper_edit,
                    'min_size_paper' => $request->min_size_paper_edit,
                    'insheet'        => $request->insheet_edit,
                    'koefisien'      => $request->koefisien_edit
                ]
            );
        }

        return response()->json($query);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        Mesin::find($id)->delete($id);
        return response()->json([
            'success' => 'Record deleted successfully!'
        ]);
    }
}
