<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Sumber;
use App\Models\Order;
use App\Models\Target;
use App\Models\Produksi;
use App\Models\SumberTarget;
use App\Models\SalesTarget;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Response;
use Yajra\DataTables\Facades\DataTables;
use Svg\Tag\Rect;

class TargetController extends Controller
{
    public function index($year = null){

        if(empty($year)){
            $tahun = date('y');
        }else{
            $tahun = $year;
        }

        $sumber = Sumber::get();

        $pic = User::where(function($query){
            if(Auth::user()->roles == 'SALES'){
                $query->where('id', Auth::user()->id);
            }else{
                $query->where('roles', 'like','%SALES%');
            }
        })->get();
        $check_bulan = date('m');
        $this_month_n = date('n');
        $bulan_ke = array("", "target_01","target_02","target_03", "target_04", "target_05","target_06","target_07","target_08","target_09","target_10", "target_11","target_12");
        $this_year = date('Y');
        $this_month_year = date('F Y');
        $this_month = $bulan_ke[$this_month_n];

        if (Auth::user()->roles == 'SALES'){
            $total_customer = Order::leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->distinct('no_hp')
            ->where('flag_dummy','=','Produksi Massal')
            ->where('status_deal','Deal')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->whereYear('tgl_order',$this_year)
            ->where('id_pic',Auth::user()->id)
            ->count();
            
            $total_target = DB::table('tb_targets')
                ->where('year_target', $this_year)
                ->where('id_pic', Auth::user()->id)
                ->sum(DB::raw('target_01 + target_02 + target_03 + target_04 + target_05 + target_06 + target_07 + target_08 + target_09 + target_10 + target_11 + target_12'));
            
            $total_realisasi = Produksi::leftJoin('tb_orders','tb_produksis.sko_key','=','tb_orders.sko_key')
                ->where('status_deal', 'Deal')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
                ->whereYear('tgl_order', $this_year)
                ->where('id_pic', Auth::user()->id)
                ->sum('total_harga');

            $annual_target = DB::table('tb_targets')
                ->select(DB::raw('SUM(target_01) as jan, SUM(target_02) as feb, SUM(target_03) as mar, SUM(target_04) as apr, SUM(target_05) as mei, SUM(target_06) as jun, SUM(target_07) as jul, SUM(target_08) as aug, SUM(target_09) as sep, SUM(target_10) as okt, SUM(target_11) as nov, SUM(target_12) as des'))
                ->where('year_target', $this_year)
                ->where('id_pic', Auth::user()->id)
                ->first();

            $annual_realisasi = DB::table('tb_orders')
                ->leftJoin('tb_produksis','tb_orders.sko_key','=','tb_produksis.sko_key')
                ->select(DB::raw('SUM(
                    CASE 
                        when MONTH(tgl_order) = 01 then
                        total_harga
                        end
                    ) as jan,
                    SUM(
                        CASE 
                            when MONTH(tgl_order) = 02 then
                            total_harga
                            end
                        ) as feb,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 03 then
                        total_harga
                        end
                    ) as mar,
                    SUM(
                        CASE 
                            when MONTH(tgl_order) = 04 then
                            total_harga
                            end
                        ) as apr,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 05 then
                        total_harga
                        end
                    ) as mei,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 06 then
                        total_harga
                        end
                    ) as jun,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 07 then
                        total_harga
                        end
                    ) as jul,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 08 then
                        total_harga
                        end
                    ) as aug,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 09 then
                        total_harga
                        end
                    ) as sep,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 10 then
                        total_harga
                        end
                    ) as okt,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 11 then
                        total_harga
                        end
                    ) as nov,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 12 then
                        total_harga
                        end
                    ) as des'))
                ->where('status_deal', 'Deal')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
                ->whereYear('tgl_order', $this_year)
                ->where('id_pic', Auth::user()->id)
                ->first();

            $this_month_year_target = DB::table('tb_targets')
                ->where('id_pic', Auth::user()->id)
                ->where('year_target',$this_year)
                ->sum($this_month);

            $this_month_year_realisasi = Produksi::leftJoin('tb_orders','tb_produksis.sko_key','=','tb_orders.sko_key')
                ->where('status_deal', 'Deal')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
                ->where('id_pic', Auth::user()->id)
                ->whereMonth('tgl_order', $check_bulan)
                ->whereYear('tgl_order', $this_year)
                ->sum('total_harga');

            $list_customer_table = DB::table('tb_orders')
                ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
                ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
                ->where('total_harga', '>', '0')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('status_deal', 'DEAL')
                ->whereYear('tgl_order', $this_year)
                ->where('id_pic', Auth::user()->id)
                ->orderBy('total_harga', 'DESC');
            
            $list_customer_repeat = $list_customer_table->sum('total_harga');
            $total_modal_deal = $list_customer_table->sum('modal_sales');
        } else {
            $total_customer = Order::leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->distinct('no_hp')
            ->where('flag_dummy','=','Produksi Massal')
            ->where('status_deal','Deal')
            ->where('tipe_kontak', '=', 'Bukan Sampah')
            ->whereYear('tgl_order',$this_year)
            ->count();

            $total_target = DB::table('tb_targets')
                ->where('year_target', $this_year)
                ->sum(DB::raw('target_01 + target_02 + target_03 + target_04 + target_05 + target_06 + target_07 + target_08 + target_09 + target_10 + target_11 + target_12'));

            $total_realisasi = Produksi::leftJoin('tb_orders','tb_produksis.sko_key','=','tb_orders.sko_key')
                ->where('status_deal', 'Deal')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
                ->whereYear('tgl_order', $this_year)
                ->sum('total_harga');

            $annual_target = DB::table('tb_targets')
                ->select(DB::raw('SUM(target_01) as jan, SUM(target_02) as feb, SUM(target_03) as mar, SUM(target_04) as apr, SUM(target_05) as mei, SUM(target_06) as jun, SUM(target_07) as jul, SUM(target_08) as aug, SUM(target_09) as sep, SUM(target_10) as okt, SUM(target_11) as nov, SUM(target_12) as des'))
                ->where('year_target', $this_year)
                ->first();

            $annual_realisasi = DB::table('tb_orders')
                ->leftJoin('tb_produksis','tb_orders.sko_key','=','tb_produksis.sko_key')
                ->select(DB::raw('SUM(
                    CASE 
                        when MONTH(tgl_order) = 01 then
                        total_harga
                        end
                    ) as jan,
                    SUM(
                        CASE 
                            when MONTH(tgl_order) = 02 then
                            total_harga
                            end
                        ) as feb,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 03 then
                        total_harga
                        end
                    ) as mar,
                    SUM(
                        CASE 
                            when MONTH(tgl_order) = 04 then
                            total_harga
                            end
                        ) as apr,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 05 then
                        total_harga
                        end
                    ) as mei,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 06 then
                        total_harga
                        end
                    ) as jun,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 07 then
                        total_harga
                        end
                    ) as jul,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 08 then
                        total_harga
                        end
                    ) as aug,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 09 then
                        total_harga
                        end
                    ) as sep,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 10 then
                        total_harga
                        end
                    ) as okt,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 11 then
                        total_harga
                        end
                    ) as nov,
                    SUM(
                    CASE 
                        when MONTH(tgl_order) = 12 then
                        total_harga
                        end
                    ) as des'))
                ->where('status_deal', 'Deal')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
                ->whereYear('tgl_order', $this_year)
                ->first();

            $this_month_year_target = DB::table('tb_targets')
                ->where('year_target',$this_year)
                ->sum($this_month);

            $this_month_year_realisasi = Produksi::leftJoin('tb_orders','tb_produksis.sko_key','=','tb_orders.sko_key')
                ->where('status_deal', 'Deal')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
                ->whereMonth('tgl_order', $check_bulan)
                ->whereYear('tgl_order', $this_year)
                ->sum('total_harga');

            $list_customer_table = DB::table('tb_orders')
                ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
                ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
                ->where('total_harga', '>', '0')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('status_deal', 'DEAL')
                ->whereYear('tgl_order', $this_year)
                ->orderBy('total_harga', 'DESC');

            $list_customer_repeat = $list_customer_table->sum('total_harga');
            $total_modal_deal = $list_customer_table->sum('modal_sales');
        }

        $count_total_customer = $total_customer;

        if ($this_month_year_target != 0){
            $target_div = $this_month_year_realisasi/$this_month_year_target;
        } else {
            $target_div = 0;
        }
        $this_month_year_progress_raw = $target_div*100;

        if ($this_month_year_progress_raw > 100){
            $this_month_year_progress = 100;
        } else {
            $this_month_year_progress = $this_month_year_progress_raw;
        }

        return view('pages.target.index', compact('sumber','pic','count_total_customer','total_target','total_realisasi','this_month_year','this_month_year_target','this_month_year_realisasi', 'annual_target','annual_realisasi','this_month_year_progress', 'list_customer_repeat', 'total_modal_deal'));
    }

    public function get_target(Request $request){
        $sumber =  $request->get('sumber');
        // dd($sumber);
        $roles = Auth::user()->roles;
        $loged_pic = Auth::user()->id;
        $selected_pic = $request->get('pic_target');
        $selected_tahun = $request->get('tahun_target');
        if($roles == 'SUPERADMIN' || $roles == 'SALES SPV'){
            //total customer
            $total_customer = Order::leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
                ->distinct('no_hp')
                ->where('flag_dummy','=','Produksi Massal')
                ->where('status_deal','Deal')
                ->where('tipe_kontak', '=', 'Bukan Sampah')
                ->whereYear('tgl_order',$selected_tahun)
                ->where(function($total_customer) use($selected_pic){
                    if($selected_pic != 'All'){
                        $total_customer->where('id_pic',$selected_pic);
                    }
                })
                ->when($sumber, function ($query) use ($sumber) {
                    return $query->where('sumber', $sumber);
                })
                ->get();
            $count_total_customer = $total_customer->count();

            //total realisasi                    
            $total_realisasi = Produksi::leftJoin('tb_orders','tb_produksis.sko_key','=','tb_orders.sko_key')
                ->where('status_deal', 'Deal')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
                ->whereYear('tgl_order',$selected_tahun)
                ->where(function($total_realisasi) use($selected_pic){
                    if($selected_pic != 'All'){
                        $total_realisasi->where('id_pic',$selected_pic);
                    }
                })
                ->when($sumber, function ($query) use ($sumber) {
                    return $query->where('sumber', $sumber);
                })
                ->sum('total_harga');
            
            if ($sumber) {
                $annual_target = SumberTarget::leftJoin('tb_sumbers', 'tb_sumber_targets.id_sumber', '=', 'tb_sumbers.id_sumber')
                ->select(DB::raw('
                    SUM(CASE WHEN month = 1 THEN target_sumber ELSE 0 END) as jan,
                    SUM(CASE WHEN month = 2 THEN target_sumber ELSE 0 END) as feb,
                    SUM(CASE WHEN month = 3 THEN target_sumber ELSE 0 END) as mar,
                    SUM(CASE WHEN month = 4 THEN target_sumber ELSE 0 END) as apr,
                    SUM(CASE WHEN month = 5 THEN target_sumber ELSE 0 END) as mei,
                    SUM(CASE WHEN month = 6 THEN target_sumber ELSE 0 END) as jun,
                    SUM(CASE WHEN month = 7 THEN target_sumber ELSE 0 END) as jul,
                    SUM(CASE WHEN month = 8 THEN target_sumber ELSE 0 END) as aug,
                    SUM(CASE WHEN month = 9 THEN target_sumber ELSE 0 END) as sep,
                    SUM(CASE WHEN month = 10 THEN target_sumber ELSE 0 END) as okt,
                    SUM(CASE WHEN month = 11 THEN target_sumber ELSE 0 END) as nov,
                    SUM(CASE WHEN month = 12 THEN target_sumber ELSE 0 END) as des
                '))
                ->where('year_target', $selected_tahun)
                ->where(function($annual_target) use ($selected_pic){
                    if($selected_pic != 'All'){
                        $annual_target->where('id_pic', $selected_pic);
                    }
                })
                ->when($sumber, function ($query) use ($sumber) {
                    return $query->where('sumber', $sumber);
                })
                ->first();

                //total_target
                $total_target = SumberTarget::leftJoin('tb_sumbers', 'tb_sumber_targets.id_sumber', '=', 'tb_sumbers.id_sumber')
                ->where('year_target','=',$selected_tahun)
                ->where(function($total_target) use($selected_pic){
                    if($selected_pic != 'All'){
                        $total_target->where('id_pic',$selected_pic);
                    }
                })
                ->when($sumber, function ($query) use ($sumber) {
                    return $query->where('sumber', $sumber);
                })
                ->sum('target_sumber');

            } else {
                //total_target
                $total_target = DB::table('tb_targets')
                ->where('year_target','=',$selected_tahun)
                ->where(function($total_target) use($selected_pic){
                    if($selected_pic != 'All'){
                        $total_target->where('id_pic',$selected_pic);
                    }
                })
                ->sum(DB::raw('target_01 + target_02 + target_03 + target_04 + target_05 + target_06 + target_07 + target_08 + target_09 + target_10 + target_11 + target_12'));
        
                //annual_target
                $annual_target = DB::table('tb_targets')
                ->select(DB::raw('SUM(target_01) as jan, SUM(target_02) as feb, SUM(target_03) as mar, SUM(target_04) as apr, SUM(target_05) as mei, SUM(target_06) as jun, SUM(target_07) as jul, SUM(target_08) as aug, SUM(target_09) as sep, SUM(target_10) as okt, SUM(target_11) as nov, SUM(target_12) as des'))
                ->where('year_target', $selected_tahun)
                ->where(function($annual_target) use ($selected_pic){
                    if($selected_pic != 'All'){
                        $annual_target->where('id_pic', $selected_pic);
                    }
                })
                ->first();
                
            }

            $this_month_year_target = SumberTarget::leftJoin('tb_sumbers', 'tb_sumber_targets.id_sumber', '=', 'tb_sumbers.id_sumber')
            ->where('year_target', $selected_tahun)
            ->where('month',date('n'))
            ->when($sumber, function ($query) use ($sumber) {
                return $query->where('sumber', $sumber);
            })
            ->sum('target_sumber');

            $this_month_year_realisasi = Produksi::leftJoin('tb_orders','tb_produksis.sko_key','=','tb_orders.sko_key')
            ->where('status_deal', 'Deal')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
            ->whereMonth('tgl_order', date('m'))
            ->whereYear('tgl_order', $selected_tahun)
            ->when($sumber, function ($query) use ($sumber) {
                return $query->where('sumber', $sumber);
            })
            ->sum('total_harga');

            if ($this_month_year_target != 0){
                $target_div = $this_month_year_realisasi/$this_month_year_target;
            } else {
                $target_div = 0;
            }

            $this_month_year_progress_raw = $target_div*100;

            if ($this_month_year_progress_raw > 100){
                $this_month_year_progress = 100;
            } else {
                $this_month_year_progress = $this_month_year_progress_raw;
            }

            $annual_realisasi = DB::table('tb_orders')
                ->leftJoin('tb_produksis','tb_orders.sko_key','=','tb_produksis.sko_key')
                ->select(DB::raw('
                    SUM(CASE when MONTH(tgl_order) = 01 then total_harga end) as jan,
                    SUM(CASE when MONTH(tgl_order) = 02 then total_harga end) as feb,
                    SUM(CASE when MONTH(tgl_order) = 03 then total_harga end) as mar,
                    SUM(CASE when MONTH(tgl_order) = 04 then total_harga end) as apr,
                    SUM(CASE when MONTH(tgl_order) = 05 then total_harga end) as mei,
                    SUM(CASE when MONTH(tgl_order) = 06 then total_harga end) as jun,
                    SUM(CASE when MONTH(tgl_order) = 07 then total_harga end) as jul,
                    SUM(CASE when MONTH(tgl_order) = 08 then total_harga end) as aug,
                    SUM(CASE when MONTH(tgl_order) = 09 then total_harga end) as sep,
                    SUM(CASE when MONTH(tgl_order) = 10 then total_harga end) as okt,
                    SUM(CASE when MONTH(tgl_order) = 11 then total_harga end) as nov,
                    SUM(CASE when MONTH(tgl_order) = 12 then total_harga end) as des
                '))
                ->where('status_deal', 'Deal')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
                ->whereYear('tgl_order', $selected_tahun)
                ->where(function($annual_realisasi) use($selected_pic){
                    if($selected_pic != 'All'){
                        $annual_realisasi->where('id_pic', $selected_pic);
                    }
                })
                ->when($sumber, function ($query) use ($sumber) {
                    return $query->where('sumber', $sumber);
                })
                ->first();

            $list_customer_table = DB::table('tb_orders')
                ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
                ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
                ->where('total_harga', '>', '0')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('status_deal', 'DEAL')
                ->whereYear('tgl_order', $selected_tahun)
                ->where(function ($annual_realisasi) use ($selected_pic) {
                    if($selected_pic != 'All'){
                        $annual_realisasi->where('id_pic', $selected_pic);
                    }
                })
                ->when($sumber, function ($query) use ($sumber) {
                    return $query->where('sumber', $sumber);
                });

            $list_customer_repeat = $list_customer_table->sum('total_harga');
            $total_modal_deal = $list_customer_table->sum('modal_sales');
        } else {
            //total customer
            $total_customer = Order::leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
                ->distinct('no_hp')
                ->where('flag_dummy','=','Produksi Massal')
                ->where('status_deal','Deal')
                ->where('tipe_kontak', '=', 'Bukan Sampah')
                ->whereYear('tgl_order',$selected_tahun)
                ->where('id_pic', $loged_pic)
                ->get();
            $count_total_customer = $total_customer->count();

            //total target
            $total_target = DB::table('tb_targets')
                ->where('year_target','=',$selected_tahun)
                ->where('id_pic', $loged_pic)
                ->sum(DB::raw('target_01 + target_02 + target_03 + target_04 + target_05 + target_06 + target_07 + target_08 + target_09 + target_10 + target_11 + target_12'));

            //total realisasi
            $total_realisasi = Produksi::leftJoin('tb_orders','tb_produksis.sko_key','=','tb_orders.sko_key')
                ->where('status_deal', 'Deal')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
                ->whereYear('tgl_order',$selected_tahun)
                ->where('id_pic', $loged_pic)
                ->when($sumber, function ($query) use ($sumber) {
                    return $query->where('sumber', $sumber);
                })
                ->sum('total_harga');

            $annual_target = DB::table('tb_targets')
                ->select(DB::raw('SUM(target_01) as jan, SUM(target_02) as feb, SUM(target_03) as mar, SUM(target_04) as apr, SUM(target_05) as mei, SUM(target_06) as jun, SUM(target_07) as jul, SUM(target_08) as aug, SUM(target_09) as sep, SUM(target_10) as okt, SUM(target_11) as nov, SUM(target_12) as des'))
                ->where('year_target', $selected_tahun)
                ->where('id_pic', $loged_pic)
                ->first();

            $annual_realisasi = DB::table('tb_orders')
                ->leftJoin('tb_produksis','tb_orders.sko_key','=','tb_produksis.sko_key')
                ->select(DB::raw('
                    SUM(CASE when MONTH(tgl_order) = 01 then total_harga end) as jan,
                    SUM(CASE when MONTH(tgl_order) = 02 then total_harga end) as feb,
                    SUM(CASE when MONTH(tgl_order) = 03 then total_harga end) as mar,
                    SUM(CASE when MONTH(tgl_order) = 04 then total_harga end) as apr,
                    SUM(CASE when MONTH(tgl_order) = 05 then total_harga end) as mei,
                    SUM(CASE when MONTH(tgl_order) = 06 then total_harga end) as jun,
                    SUM(CASE when MONTH(tgl_order) = 07 then total_harga end) as jul,
                    SUM(CASE when MONTH(tgl_order) = 08 then total_harga end) as aug,
                    SUM(CASE when MONTH(tgl_order) = 09 then total_harga end) as sep,
                    SUM(CASE when MONTH(tgl_order) = 10 then total_harga end) as okt,
                    SUM(CASE when MONTH(tgl_order) = 11 then total_harga end) as nov,
                    SUM(CASE when MONTH(tgl_order) = 12 then total_harga end) as des
                '))
                ->where('status_deal', 'Deal')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('kategori_produksi', 'NOT LIKE', '%jasa%')
                ->whereYear('tgl_order', $selected_tahun)
                ->where('id_pic', $loged_pic)
                ->when($sumber, function ($query) use ($sumber) {
                    return $query->where('sumber', $sumber);
                })
                ->first();

            $list_customer_table = DB::table('tb_orders')
                ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
                ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
                ->where('total_harga', '>', '0')
                ->where('flag_dummy', 'Produksi Massal')
                ->where('status_deal', 'DEAL')
                ->whereYear('tgl_order', $selected_tahun)
                ->where('id_pic', $loged_pic)
                ->when($sumber, function ($query) use ($sumber) {
                    return $query->where('sumber', $sumber);
                });

                
            $list_customer_repeat = $list_customer_table->sum('total_harga');
            $total_modal_deal = $list_customer_table->sum('modal_sales');
        }

        

        $data = array(
            [
                'total_customer' => $count_total_customer, 
                'total_realisasi' => $total_realisasi, 
                'total_target' => $total_target, 
                'annual_target' => $annual_target, 
                'annual_realisasi' => $annual_realisasi, 
                'selected_pic' => $selected_pic, 
                'this_month_year_target' => $this_month_year_target, 
                'list_customer_repeat' => $list_customer_repeat,
                'total_modal_deal' => $total_modal_deal,
                'this_month_year_realisasi' => $this_month_year_realisasi,
                'this_month_year_progress' => $this_month_year_progress
            ]);
        
        return Response::json($data, 200);
    }

    public function add_target(){
        $pic = User::where('roles','SALES')->orWhere('roles', 'SALES SPV')->get();

        $sumber = Sumber::select(['sumber', 'id_sumber'])->get();
        
        return view('pages.target.form_add_target', compact('pic', 'sumber'));
    }

    public function get_current_target(Request $request){
        $total_target = DB::table('tb_targets')
        ->select(DB::raw('SUM(target_01) as jan, SUM(target_02) as feb, SUM(target_03) as mar, SUM(target_04) as apr, SUM(target_05) as mei, SUM(target_06) as jun, SUM(target_07) as jul, SUM(target_08) as aug, SUM(target_09) as sep, SUM(target_10) as okt, SUM(target_11) as nov, SUM(target_12) as des'))
        ->where('year_target', $request->year_target)
        ->where('id_pic', $request->id_pic)
        ->first();

        $sumber_target = SumberTarget::where('year_target', $request->year_target)
        ->select(['id_sumber', 'month', 'target_sumber'])
        ->where('id_pic', $request->id_pic)
        ->get();

        $sales_target = SalesTarget::where('year', $request->year_target)
        ->select(['user_id', 'year', 'month', 'target_day', 'days'])
        ->where('user_id', $request->id_pic)
        ->get();

        $data = array(
            [
                'total_target' => $total_target, 
                'sumber_target' => $sumber_target,
                'sales_target' => $sales_target
            ]);

        return Response::json($data, 200);
    }

    public function store_target(Request $request){

        // dd($request->all());
        $months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

        foreach ($request->except(['_token', '_method', 'id_pic', 'year_target', 'total_target_01', 'total_target_02', 'total_target_03', 'total_target_04', 'total_target_05', 'total_target_06', 'total_target_07', 'total_target_08', 'total_target_09', 'total_target_10', 'total_target_11', 'total_target_12']) as $key => $value) {
            // Check if the key starts with a numeric value and the value is not null
            if (is_numeric(substr($key, 0, 1)) && $value !== null) {
                // Extract the month and other information
                $month = substr($key, strpos($key, '_') + 1);
                $columnName = (int)substr($key, 0, strpos($key, '_'));
                $month_index = array_search($month, $months);
                // dd($request->id_pic, $month_index+1, $columnName, $value);

                SumberTarget::updateOrCreate(
                    [
                        'id_pic' => $request->id_pic,
                        'year_target' => $request->year_target,
                        'month' => $month_index+1,
                        'id_sumber' => $columnName,
                    ],
                    [
                        'target_sumber' => (float)str_replace(',', '', $value),
                    ]
                );
            }
        }

        $target_01 = (float)str_replace(',', '', $request->total_target_01);
        $target_02 = (float)str_replace(',', '', $request->total_target_02);
        $target_03 = (float)str_replace(',', '', $request->total_target_03);
        $target_04 = (float)str_replace(',', '', $request->total_target_04);
        $target_05 = (float)str_replace(',', '', $request->total_target_05);
        $target_06 = (float)str_replace(',', '', $request->total_target_06);
        $target_07 = (float)str_replace(',', '', $request->total_target_07);
        $target_08 = (float)str_replace(',', '', $request->total_target_08);
        $target_09 = (float)str_replace(',', '', $request->total_target_09);
        $target_10 = (float)str_replace(',', '', $request->total_target_10);
        $target_11 = (float)str_replace(',', '', $request->total_target_11);
        $target_12 = (float)str_replace(',', '', $request->total_target_12);

        //store to SalesTarget
        foreach ($request->except(['_token', '_method', 'id_pic', 'year_target', 'total_target_01', 'total_target_02', 'total_target_03', 'total_target_04', 'total_target_05', 'total_target_06', 'total_target_07', 'total_target_08', 'total_target_09', 'total_target_10', 'total_target_11', 'total_target_12']) as $key => $value) {
            if (is_numeric(substr($key, 0, 1)) && $value !== null) {
                $month = substr($key, strpos($key, '_') + 1);
                $months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
                $month_index = array_search($month, $months);
                $month_index_padded = str_pad($month_index + 1, 2, '0', STR_PAD_LEFT);
                $total_day= $request->input("total_day_{$month_index_padded}");
                $total_per_hari= $request->input("total_per_hari_{$month_index_padded}");
                if ($month_index !== false && $value !== null) {
                    SalesTarget::updateOrCreate(
                        [
                            'user_id' => $request->id_pic,
                            'month' => $month_index_padded,
                            'year' => $request->year_target
                        ],
                        [
                            'user_id' => $request->id_pic,
                            'month' => $month_index_padded,
                            'year' => $request->year_target,
                            'target_revenue' => (float)str_replace(',', '', $value),
                            'days' => $total_day,
                            'target_day' => (float)str_replace(',', '', $total_per_hari),
                        ]
                    );
                }
            }
        }
        $data = Target::updateOrCreate(
            [
                'id_pic' => $request->id_pic,
                'year_target' => $request->year_target,
            ],
            [
                'target_01' => $target_01,
                'target_02' => $target_02,
                'target_03' => $target_03,
                'target_04' => $target_04,
                'target_05' => $target_05,
                'target_06' => $target_06,
                'target_07' => $target_07,
                'target_08' => $target_08,
                'target_09' => $target_09,
                'target_10' => $target_10,
                'target_11' => $target_11,
                'target_12' => $target_12,
            ]
        );

        if($data){
            return redirect()->route('target');
        }
    }

    public function view_detail(){

        $pic = User::where(function($query){
            if(Auth::user()->roles == 'SALES'){
                $query->where('id', Auth::user()->id);
            }else{
                $query->where('roles', 'like','%SALES%');
            }
        })->get();

        $this_year = date('Y');

        if(Auth::user()->roles == 'SUPERADMIN' || Auth::user()->roles == 'SALES SPV'){
            $data_target = Target::select(DB::raw('SUM(target_01) as jan, SUM(target_02) as feb, SUM(target_03) as mar, SUM(target_04) as apr, SUM(target_05) as mei, SUM(target_06) as jun, SUM(target_07) as jul, SUM(target_08) as aug, SUM(target_09) as sep, SUM(target_10) as okt, SUM(target_11) as nov, SUM(target_12) as des'))
                                ->where('year_target',$this_year)
                                ->first();

            $data_order = Order::leftJoin('tb_produksis','tb_orders.sko_key','=','tb_produksis.sko_key')
                ->select(DB::raw('SUM(
                                    CASE 
                                        when MONTH(tgl_order) = 01 then
                                        total_harga
                                        end
                                    ) as jan,
                                    SUM(
                                        CASE 
                                            when MONTH(tgl_order) = 02 then
                                            total_harga
                                            end
                                        ) as feb,
                                    SUM(
                                    CASE 
                                        when MONTH(tgl_order) = 03 then
                                        total_harga
                                        end
                                    ) as mar,
                                    SUM(
                                        CASE 
                                            when MONTH(tgl_order) = 04 then
                                            total_harga
                                            end
                                        ) as apr,
                                    SUM(
                                    CASE 
                                        when MONTH(tgl_order) = 05 then
                                        total_harga
                                        end
                                    ) as mei,
                                    SUM(
                                    CASE 
                                        when MONTH(tgl_order) = 06 then
                                        total_harga
                                        end
                                    ) as jun,
                                    SUM(
                                    CASE 
                                        when MONTH(tgl_order) = 07 then
                                        total_harga
                                        end
                                    ) as jul,
                                    SUM(
                                    CASE 
                                        when MONTH(tgl_order) = 08 then
                                        total_harga
                                        end
                                    ) as aug,
                                    SUM(
                                    CASE 
                                        when MONTH(tgl_order) = 09 then
                                        total_harga
                                        end
                                    ) as sep,
                                    SUM(
                                    CASE 
                                        when MONTH(tgl_order) = 10 then
                                        total_harga
                                        end
                                    ) as okt,
                                    SUM(
                                    CASE 
                                        when MONTH(tgl_order) = 11 then
                                        total_harga
                                        end
                                    ) as nov,
                                    SUM(
                                    CASE 
                                        when MONTH(tgl_order) = 12 then
                                        total_harga
                                        end
                                    ) as des'))
                ->whereYear('tgl_order',$this_year)
                ->where('flag_dummy','!=','Dummy')
                ->where('status_deal','Deal')
                ->first();

            $total_customer_1 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','01')
                                ->get();
            $total_customer_2 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','02')
                                ->get();
            $total_customer_3 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','03')
                                ->get();
            $total_customer_4 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','04')
                                ->get();
            $total_customer_5 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','05')
                                ->get();
            $total_customer_6 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','06')
                                ->get();
            $total_customer_7 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','07')
                                ->get();
            $total_customer_8 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','08')
                                ->get();
            $total_customer_9 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','09')
                                ->get();
            $total_customer_10 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','10')
                                ->get();
            $total_customer_11 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','11')
                                ->get();
            $total_customer_12 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','12')
                                ->get();
        }else{
            $data_target = Target::select(DB::raw('SUM(target_01) as jan, SUM(target_02) as feb, SUM(target_03) as mar, SUM(target_04) as apr, SUM(target_05) as mei, SUM(target_06) as jun, SUM(target_07) as jul, SUM(target_08) as aug, SUM(target_09) as sep, SUM(target_10) as okt, SUM(target_11) as nov, SUM(target_12) as des'))
                                    ->where('year_target',$this_year)
                                    ->where('id_pic',Auth::user()->id)
                                    ->first();

            $data_order = Order::leftJoin('tb_produksis','tb_orders.sko_key','=','tb_produksis.sko_key')
                                ->select(DB::raw('SUM(
                                                    CASE 
                                                        when MONTH(tgl_order) = 01 then
                                                        total_harga
                                                        end
                                                    ) as jan,
                                                    SUM(
                                                        CASE 
                                                            when MONTH(tgl_order) = 02 then
                                                            total_harga
                                                            end
                                                        ) as feb,
                                                    SUM(
                                                    CASE 
                                                        when MONTH(tgl_order) = 03 then
                                                        total_harga
                                                        end
                                                    ) as mar,
                                                    SUM(
                                                        CASE 
                                                            when MONTH(tgl_order) = 04 then
                                                            total_harga
                                                            end
                                                        ) as apr,
                                                    SUM(
                                                    CASE 
                                                        when MONTH(tgl_order) = 05 then
                                                        total_harga
                                                        end
                                                    ) as mei,
                                                    SUM(
                                                    CASE 
                                                        when MONTH(tgl_order) = 06 then
                                                        total_harga
                                                        end
                                                    ) as jun,
                                                    SUM(
                                                    CASE 
                                                        when MONTH(tgl_order) = 07 then
                                                        total_harga
                                                        end
                                                    ) as jul,
                                                    SUM(
                                                    CASE 
                                                        when MONTH(tgl_order) = 08 then
                                                        total_harga
                                                        end
                                                    ) as aug,
                                                    SUM(
                                                    CASE 
                                                        when MONTH(tgl_order) = 09 then
                                                        total_harga
                                                        end
                                                    ) as sep,
                                                    SUM(
                                                    CASE 
                                                        when MONTH(tgl_order) = 10 then
                                                        total_harga
                                                        end
                                                    ) as okt,
                                                    SUM(
                                                    CASE 
                                                        when MONTH(tgl_order) = 11 then
                                                        total_harga
                                                        end
                                                    ) as nov,
                                                    SUM(
                                                    CASE 
                                                        when MONTH(tgl_order) = 12 then
                                                        total_harga
                                                        end
                                                    ) as des'))
                                ->whereYear('tgl_order',$this_year)
                                ->where('id_pic', Auth::user()->id)
                                ->where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->first();

            $total_customer_1 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','01')
                                ->where('id_pic',Auth::user()->id)
                                ->get();
            $total_customer_2 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','02')
                                ->where('id_pic',Auth::user()->id)
                                ->get();
            $total_customer_3 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','03')
                                ->where('id_pic',Auth::user()->id)
                                ->get();
            $total_customer_4 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','04')
                                ->where('id_pic',Auth::user()->id)
                                ->get();
            $total_customer_5 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','05')
                                ->where('id_pic',Auth::user()->id)
                                ->get();
            $total_customer_6 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','06')
                                ->where('id_pic',Auth::user()->id)
                                ->get();
            $total_customer_7 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','07')
                                ->where('id_pic',Auth::user()->id)
                                ->get();
            $total_customer_8 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','08')
                                ->where('id_pic',Auth::user()->id)
                                ->get();
            $total_customer_9 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','09')
                                ->where('id_pic',Auth::user()->id)
                                ->get();
            $total_customer_10 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','10')
                                ->where('id_pic',Auth::user()->id)
                                ->get();
            $total_customer_11 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','11')
                                ->where('id_pic',Auth::user()->id)
                                ->get();
            $total_customer_12 = Order::where('flag_dummy','!=','Dummy')
                                ->where('status_deal','Deal')
                                ->whereYear('tgl_order',$this_year)
                                ->whereMonth('tgl_order','12')
                                ->where('id_pic',Auth::user()->id)
                                ->get();
        }

        $count_total_customer_1 = $total_customer_1->count();
        $count_total_customer_2 = $total_customer_2->count();
        $count_total_customer_3 = $total_customer_3->count();
        $count_total_customer_4 = $total_customer_4->count();
        $count_total_customer_5 = $total_customer_5->count();
        $count_total_customer_6 = $total_customer_6->count();
        $count_total_customer_7 = $total_customer_7->count();
        $count_total_customer_8 = $total_customer_8->count();
        $count_total_customer_9 = $total_customer_9->count();
        $count_total_customer_10 = $total_customer_10->count();
        $count_total_customer_11 = $total_customer_11->count();
        $count_total_customer_12 = $total_customer_12->count();
        
        $margin_1 = $data_order->jan-$data_target->jan;
        $margin_2 = $data_order->feb-$data_target->feb;
        $margin_3 = $data_order->mar-$data_target->mar;
        $margin_4 = $data_order->apr-$data_target->apr;
        $margin_5 = $data_order->mei-$data_target->mei;
        $margin_6 = $data_order->jun-$data_target->jun;
        $margin_7 = $data_order->jul-$data_target->jul;
        $margin_8 = $data_order->aug-$data_target->aug;
        $margin_9 = $data_order->sep-$data_target->sep;
        $margin_10 = $data_order->okt-$data_target->okt;
        $margin_11 = $data_order->nov-$data_target->nov;
        $margin_12 = $data_order->des-$data_target->des;

        return view('pages.target.details', compact('pic',
                                                    'data_target',
                                                    'data_order',
                                                    'count_total_customer_1',
                                                    'count_total_customer_2',
                                                    'count_total_customer_3',
                                                    'count_total_customer_4',
                                                    'count_total_customer_5',
                                                    'count_total_customer_6',
                                                    'count_total_customer_7',
                                                    'count_total_customer_8',
                                                    'count_total_customer_9',
                                                    'count_total_customer_10',
                                                    'count_total_customer_11',
                                                    'count_total_customer_12',
                                                    'margin_1',
                                                    'margin_2',
                                                    'margin_3',
                                                    'margin_4',
                                                    'margin_5',
                                                    'margin_6',
                                                    'margin_7',
                                                    'margin_8',
                                                    'margin_9',
                                                    'margin_10',
                                                    'margin_11',
                                                    'margin_12',
                                                    ));
    }

    public function get_detail(Request $request){
        $roles = Auth::user()->roles;
        $logged_pic = Auth::user()->id;
        $selected_pic = $request->get('pic');
        $this_year = $request->get('tahun_target');
        
        $data_target = Target::select(DB::raw('SUM(target_01) as jan, SUM(target_02) as feb, SUM(target_03) as mar, SUM(target_04) as apr, SUM(target_05) as mei, SUM(target_06) as jun, SUM(target_07) as jul, SUM(target_08) as aug, SUM(target_09) as sep, SUM(target_10) as okt, SUM(target_11) as nov, SUM(target_12) as des'))
        ->where('year_target',$this_year)
        ->where(function($data_target) use($selected_pic){
            if($selected_pic != 'All' && $selected_pic != 'PIC'){
                $data_target->where('id_pic', $selected_pic);
            }
        })
        ->first();
        
        $data_order = Order::leftJoin('tb_produksis','tb_orders.sko_key','=','tb_produksis.sko_key')
        ->select(DB::raw('
            SUM(CASE when MONTH(tgl_order) = 01 then total_harga end) as jan,
            SUM(CASE when MONTH(tgl_order) = 02 then total_harga end) as feb,
            SUM(CASE when MONTH(tgl_order) = 03 then total_harga end) as mar,
            SUM(CASE when MONTH(tgl_order) = 04 then total_harga end) as apr,
            SUM(CASE when MONTH(tgl_order) = 05 then total_harga end) as mei,
            SUM(CASE when MONTH(tgl_order) = 06 then total_harga end) as jun,
            SUM(CASE when MONTH(tgl_order) = 07 then total_harga end) as jul,
            SUM(CASE when MONTH(tgl_order) = 08 then total_harga end) as aug,
            SUM(CASE when MONTH(tgl_order) = 09 then total_harga end) as sep,
            SUM(CASE when MONTH(tgl_order) = 10 then total_harga end) as okt,
            SUM(CASE when MONTH(tgl_order) = 11 then total_harga end) as nov,
            SUM(CASE when MONTH(tgl_order) = 12 then total_harga end) as des
        '))
        ->whereYear('tgl_order',$this_year)
        ->where('flag_dummy','!=','Dummy')
        ->where('status_deal','Deal')
        ->where(function($data_order) use($selected_pic){
            if($selected_pic != 'All' && $selected_pic != 'PIC'){
                $data_order->where('id_pic', $selected_pic);
            }
        })
        ->first();

        $total_customer_1 = Order::where('flag_dummy','!=','Dummy')
        ->where('status_deal','Deal')
        ->where(function($total_customer_1) use($selected_pic){
            if($selected_pic != 'All' && $selected_pic != 'PIC'){
                $total_customer_1->where('id_pic', $selected_pic);
            }
        })
        ->whereYear('tgl_order',$this_year)
        ->whereMonth('tgl_order','01')
        ->get();

        $total_customer_2 = Order::where('flag_dummy','!=','Dummy')
                            ->where('status_deal','Deal')
                            ->where(function($total_customer_2) use($selected_pic){
                                if($selected_pic != 'All' && $selected_pic != 'PIC'){
                                    $total_customer_2->where('id_pic', $selected_pic);
                                }
                            })
                            ->whereYear('tgl_order',$this_year)
                            ->whereMonth('tgl_order','02')
                            ->get();
        $total_customer_3 = Order::where('flag_dummy','!=','Dummy')
                            ->where('status_deal','Deal')
                            ->where(function($total_customer_3) use($selected_pic){
                                if($selected_pic != 'All' && $selected_pic != 'PIC'){
                                    $total_customer_3->where('id_pic', $selected_pic);
                                }
                            })
                            ->whereYear('tgl_order',$this_year)
                            ->whereMonth('tgl_order','03')
                            ->get();
        $total_customer_4 = Order::where('flag_dummy','!=','Dummy')
                            ->where('status_deal','Deal')
                            ->where(function($total_customer_4) use($selected_pic){
                                if($selected_pic != 'All' && $selected_pic != 'PIC'){
                                    $total_customer_4->where('id_pic', $selected_pic);
                                }
                            })
                            ->whereYear('tgl_order',$this_year)
                            ->whereMonth('tgl_order','04')
                            ->get();
        $total_customer_5 = Order::where('flag_dummy','!=','Dummy')
                            ->where('status_deal','Deal')
                            ->where(function($total_customer_5) use($selected_pic){
                                if($selected_pic != 'All' && $selected_pic != 'PIC'){
                                    $total_customer_5->where('id_pic', $selected_pic);
                                }
                            })
                            ->whereYear('tgl_order',$this_year)
                            ->whereMonth('tgl_order','05')
                            ->get();
        $total_customer_6 = Order::where('flag_dummy','!=','Dummy')
                            ->where('status_deal','Deal')
                            ->where(function($total_customer_6) use($selected_pic){
                                if($selected_pic != 'All' && $selected_pic != 'PIC'){
                                    $total_customer_6->where('id_pic', $selected_pic);
                                }
                            })
                            ->whereYear('tgl_order',$this_year)
                            ->whereMonth('tgl_order','06')
                            ->get();
        $total_customer_7 = Order::where('flag_dummy','!=','Dummy')
                            ->where('status_deal','Deal')
                            ->where(function($total_customer_7) use($selected_pic){
                                if($selected_pic != 'All' && $selected_pic != 'PIC'){
                                    $total_customer_7->where('id_pic', $selected_pic);
                                }
                            })
                            ->whereYear('tgl_order',$this_year)
                            ->whereMonth('tgl_order','07')
                            ->get();
        $total_customer_8 = Order::where('flag_dummy','!=','Dummy')
                            ->where('status_deal','Deal')
                            ->where(function($total_customer_8) use($selected_pic){
                                if($selected_pic != 'All' && $selected_pic != 'PIC'){
                                    $total_customer_8->where('id_pic', $selected_pic);
                                }
                            })
                            ->whereYear('tgl_order',$this_year)
                            ->whereMonth('tgl_order','08')
                            ->get();
        $total_customer_9 = Order::where('flag_dummy','!=','Dummy')
                            ->where('status_deal','Deal')
                            ->where(function($total_customer_9) use($selected_pic){
                                if($selected_pic != 'All' && $selected_pic != 'PIC'){
                                    $total_customer_9->where('id_pic', $selected_pic);
                                }
                            })
                            ->whereYear('tgl_order',$this_year)
                            ->whereMonth('tgl_order','09')
                            ->get();
        $total_customer_10 = Order::where('flag_dummy','!=','Dummy')
                            ->where('status_deal','Deal')
                            ->where(function($total_customer_10) use($selected_pic){
                                if($selected_pic != 'All' && $selected_pic != 'PIC'){
                                    $total_customer_10->where('id_pic', $selected_pic);
                                }
                            })
                            ->whereYear('tgl_order',$this_year)
                            ->whereMonth('tgl_order','10')
                            ->get();
        $total_customer_11 = Order::where('flag_dummy','!=','Dummy')
                            ->where('status_deal','Deal')
                            ->where(function($total_customer_11) use($selected_pic){
                                if($selected_pic != 'All' && $selected_pic != 'PIC'){
                                    $total_customer_11->where('id_pic', $selected_pic);
                                }
                            })
                            ->whereYear('tgl_order',$this_year)
                            ->whereMonth('tgl_order','11')
                            ->get();
        $total_customer_12 = Order::where('flag_dummy','!=','Dummy')
                            ->where('status_deal','Deal')
                            ->where(function($total_customer_12) use($selected_pic){
                                if($selected_pic != 'All' && $selected_pic != 'PIC'){
                                    $total_customer_12->where('id_pic', $selected_pic);
                                }
                            })
                            ->whereYear('tgl_order',$this_year)
                            ->whereMonth('tgl_order','12')
                            ->get();

        $count_total_customer_1 = $total_customer_1->count();
        $count_total_customer_2 = $total_customer_2->count();
        $count_total_customer_3 = $total_customer_3->count();
        $count_total_customer_4 = $total_customer_4->count();
        $count_total_customer_5 = $total_customer_5->count();
        $count_total_customer_6 = $total_customer_6->count();
        $count_total_customer_7 = $total_customer_7->count();
        $count_total_customer_8 = $total_customer_8->count();
        $count_total_customer_9 = $total_customer_9->count();
        $count_total_customer_10 = $total_customer_10->count();
        $count_total_customer_11 = $total_customer_11->count();
        $count_total_customer_12 = $total_customer_12->count();

        $margin_1 = $data_order->jan-$data_target->jan;
        $margin_2 = $data_order->feb-$data_target->feb;
        $margin_3 = $data_order->mar-$data_target->mar;
        $margin_4 = $data_order->apr-$data_target->apr;
        $margin_5 = $data_order->mei-$data_target->mei;
        $margin_6 = $data_order->jun-$data_target->jun;
        $margin_7 = $data_order->jul-$data_target->jul;
        $margin_8 = $data_order->aug-$data_target->aug;
        $margin_9 = $data_order->sep-$data_target->sep;
        $margin_10 = $data_order->okt-$data_target->okt;
        $margin_11 = $data_order->nov-$data_target->nov;
        $margin_12 = $data_order->des-$data_target->des;

        $data = array(
            [
                'data_target' => $data_target, 
                'data_order' => $data_order,
                'count_total_customer_1' => $count_total_customer_1,
                'count_total_customer_2' => $count_total_customer_2,
                'count_total_customer_3' => $count_total_customer_3,
                'count_total_customer_4' => $count_total_customer_4,
                'count_total_customer_5' => $count_total_customer_5,
                'count_total_customer_6' => $count_total_customer_6,
                'count_total_customer_7' => $count_total_customer_7,
                'count_total_customer_8' => $count_total_customer_8,
                'count_total_customer_9' => $count_total_customer_9,
                'count_total_customer_10' => $count_total_customer_10,
                'count_total_customer_11' => $count_total_customer_11,
                'count_total_customer_12' => $count_total_customer_12,
                'margin_1' => $margin_1,
                'margin_2' => $margin_2,
                'margin_3' => $margin_3,
                'margin_4' => $margin_4,
                'margin_5' => $margin_5,
                'margin_6' => $margin_6,
                'margin_7' => $margin_7,
                'margin_8' => $margin_8,
                'margin_9' => $margin_9,
                'margin_10' => $margin_10,
                'margin_11' => $margin_11,
                'margin_12' => $margin_12,
            ]);

        return Response::json($data, 200);

    }

    public function edit_target(Request $request){

        $id_pic = $request->get('pic');
        $pic = User::where('id',$id_pic)->first();
        $this_year = date('Y');
        $target = Target::where('id_pic',$id_pic)->where('year_target',$this_year)->first();

        return view('pages.target.form_edit_target', compact('pic','target'));
    }

    public function update_target(Request $request){

        $target_01 = (float)str_replace(',', '', $request->target_01);
        $target_02 = (float)str_replace(',', '', $request->target_02);
        $target_03 = (float)str_replace(',', '', $request->target_03);
        $target_04 = (float)str_replace(',', '', $request->target_04);
        $target_05 = (float)str_replace(',', '', $request->target_05);
        $target_06 = (float)str_replace(',', '', $request->target_06);
        $target_07 = (float)str_replace(',', '', $request->target_07);
        $target_08 = (float)str_replace(',', '', $request->target_08);
        $target_09 = (float)str_replace(',', '', $request->target_09);
        $target_10 = (float)str_replace(',', '', $request->target_10);
        $target_11 = (float)str_replace(',', '', $request->target_11);
        $target_12 = (float)str_replace(',', '', $request->target_12);

        if($request){
            $query = Target::updateOrCreate(['id_target' => $request->id_target],
                                            ['year_target' => $request->year_target,
                                            'target_01' => $target_01,
                                            'target_02' => $target_02,
                                            'target_03' => $target_03,
                                            'target_04' => $target_04,
                                            'target_05' => $target_05,
                                            'target_06' => $target_06,
                                            'target_07' => $target_07,
                                            'target_08' => $target_08,
                                            'target_09' => $target_09,
                                            'target_10' => $target_10,
                                            'target_11' => $target_11,
                                            'target_12' => $target_12
                                            ]);
        }

        if($query){
            return redirect('target')->with('message', [
                'type' => 'Success',
                'text' => 'Target berhasil diubah',
            ]);
        }
    }

    public function table_detail_deals(Request $request)
    {
        $selected_pic = $request->pic_sales;
        $selected_year = $request->year;
        $list_customer_repeat = DB::table('tb_orders')
            ->leftJoin('tb_produksis', 'tb_orders.sko_key', '=', 'tb_produksis.sko_key')
            ->leftJoin('tb_customers', 'tb_orders.id_customer', '=', 'tb_customers.id_customer')
            ->select(DB::raw("count(tb_orders.id_customer) as cro, nama, SUM(total_harga) as total_harga, sumber, tb_customers.grading as grading, SUM(modal_sales) AS total_modal_sales"))
            ->where('total_harga', '>', '0')
            ->where('flag_dummy', 'Produksi Massal')
            ->where('status_deal', 'DEAL')
            ->groupBy('tb_orders.id_customer', 'sumber')
            ->whereYear('tgl_order', $selected_year)
            ->where(function ($query) use ($selected_pic) {
                if (Auth::user()->roles == 'SALES') {
                    $query->where('id_pic', Auth::user()->id);
                } elseif (Auth::user()->roles === 'SALES SPV' || Auth::user()->roles === 'SUPERADMIN') {
                    if ($selected_pic != 'All') {
                        $query->where('id_pic', $selected_pic);
                    }
                }
                
            })
            ->orderBy('total_harga', 'DESC')
            ->get();
        
        return DataTables::of($list_customer_repeat)
            ->editColumn('nama', function ($row) {
                return $row->nama;
            })
            ->addColumn('total_harga', function ($row) {
                return $row->total_harga;
            })
            ->addColumn('cro', function ($row) {
                return $row->cro;
            })
            ->addColumn('sumber', function ($row) {
                return $row->sumber;
            })
            ->addColumn('modal_sales', function ($row) {
                return $row->total_modal_sales;
            })
            ->make(true);
    }
}
