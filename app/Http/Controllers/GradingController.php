<?php

namespace App\Http\Controllers;

use App\Models\Grading;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use Yajra\DataTables\Facades\DataTables;

class GradingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if(request()->ajax())
        {
            $query = Grading::query();
            return DataTables::of($query)
                ->addColumn('action', function($item){
                    return '
                    <div class="row">
                        <div class="col-md-6">
                            <a class="btn btn-sm btn-primary w-100 text-white edit-grading" data-id="'.$item->id_grading.'" href="javascript:void(0)">Edit</a>
                        </div>
                        <div class="col-md-6">
                            <a href="javascript:void(0)" class="btn btn-sm btn-danger w-100 text-white delete-link" data-id-destroy="'.$item->id_grading.'" onclick="deleteConfirmation('.$item->id_grading.')">Delete</a>
                        </div>
                    </div>
                    ';
                })
                ->editColumn('minimum_omzet', function($item){
                    return 'Rp. '.number_format($item->minimum_omzet, 0, ',', '.');
                })
                ->rawColumns(['action','roles'])
                ->make();

        }
        return view('pages.superadmin.grading.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = Grading::create(['grading' => $request->grading, 'minimum_omzet' => $request->minimum_omzet]);

        return Response::json($data, 200);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $grading = Grading::where('id_grading',$id)->first();
        return response()->json($grading);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request)
    {
        if($request){
            $query = Grading::updateOrCreate(['id_grading' => $request->id_grading],['grading' => $request->grading_edit, 'minimum_omzet' => $request->minimum_omzet_edit]);
        }

        return response()->json($query);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        Grading::find($id)->delete($id);
        return response()->json([
            'success' => 'Record deleted successfully!'
        ]);
    }
}
