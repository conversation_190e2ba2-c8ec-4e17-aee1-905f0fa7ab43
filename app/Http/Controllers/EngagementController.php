<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Customer;
use App\Models\Order;
use App\Models\CompanyEngagement;
use Illuminate\Http\Request;
use App\Models\Company;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpWord\TemplateProcessor;
use Yajra\DataTables\Facades\DataTables;
use DateTime;
use Illuminate\Support\Facades\Response;
use Illuminate\Database\Eloquent\Builder;

class EngagementController extends Controller
{
    public function index(Request $request)
    {
        return view('pages.report.index');
    }

    public function engagement(Request $request)
    {
        $engagements_week = CompanyEngagement::whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])
            ->count();
        
        $engagements_month = CompanyEngagement::whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->count();

        $engagements_open = CompanyEngagement::where('is_completed', 0)
            ->count();

        $engagements_closed = CompanyEngagement::where('is_completed', 1)
            ->count();

        $engagements_due_today = CompanyEngagement::whereDate('due_date', Carbon::today())
            ->count();
        $engagements_due_this_week = CompanyEngagement::whereBetween('due_date', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()])
            ->count();


        return view('pages.sales.engagement.index', compact(
            'engagements_week',
            'engagements_month',
            'engagements_open',
            'engagements_closed',
            'engagements_due_today',
            'engagements_due_this_week'
        ));
    }

    public function getDataEngagement(Request $request)
    {
        if(request()->ajax())
        {
            $query = CompanyEngagement::with(['user','company','userAssigned'])->orderBy('due_date', 'asc');
            // if ($request->has('start_date') && $request->has('end_date')) {
            //     $start_date = Carbon::createFromFormat('Y-m-d', $request->start_date)->startOfDay();
            //     $end_date = Carbon::createFromFormat('Y-m-d', $request->end_date)->endOfDay();
            //     $query->whereBetween('tb_orders.created_at', [$start_date, $end_date]);
            // }
            // if ($request->has('id_customer')) {
            //     $query->where('tb_orders.id_customer', $request->id_customer);
            // }
            // if ($request->has('forecast_month')) {
            //     $query->where('tb_orders.forecast_month', $request->forecast_month);
            // }
            if ($request->has('start_due_date') && $request->has('end_due_date') && !empty($request->start_due_date) && !empty($request->end_due_date)) {
                $start_due_date = date('Y-m-d', strtotime($request->start_due_date));
                $end_due_date = date('Y-m-d', strtotime($request->end_due_date));
                $query->whereBetween('due_date', [$start_due_date, $end_due_date]);
            }
            if ($request->has('forecast_status')) {
                $query->where('tb_engagement_recaps.is_completed', $request->forecast_status);
            }
            return DataTables::of($query)
                ->addColumn('pic', function ($row) {
                    return $row->userAssigned ? $row->userAssigned->name : '-';
                })
                ->editColumn('created_at', function ($row) {
                    return $row->created_at ? $row->created_at->format('d-m-Y') : '-';
                })
                ->editColumn('due_date', function ($row) {
                    return $row->due_date ? date('d-m-Y', strtotime($row->due_date)) : '-';
                })
                ->editColumn('is_completed', function ($row) {
                    if ($row->is_completed) {
                        return '<span class="badge badge-success"><i class="fas fa-check-circle pe-1 text-white"></i> Completed</span>';
                    } else {
                        return '<span class="badge badge-danger"><i class="fas fa-times-circle pe-1 text-white"></i> Not Completed</span>';
                    }
                })
                ->addColumn('action', function ($row) {
                    if (!$row->is_completed) {
                        return '<a data-id="'.$row->id.'" class="btn btn-sm btn-primary btn-edit-engagement" onclick="editEngagement('.$row->id.')"><i class="fas fa-edit"></i> Edit</a>
                                <a type="button" class="btn btn-sm btn-info btn-delete-engagement" onclick="deleteEngagement('.$row->id.')"><i class="fas fa-trash"></i> Delete</a>';
                    }
                })
                ->rawColumns(['is_completed','action'])
                ->make();

        }
        return view('pages.sales.engagement.index');
    }

    public function create()
    {
        return view('pages.sales.engagement.create-edit');
    }

    public function edit($id)
    {
        $data = CompanyEngagement::with(['user', 'company', 'userAssigned'])->findOrFail($id);
        return view('pages.sales.engagement.create-edit', compact('data'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required',
            'detail' => 'required',
        ]);


        try {
            $input = $request->all();
            // dd($input);
            DB::transaction(function () use ($input) {
                CompanyEngagement::updateOrCreate(
                    ['id' => $input['id'] ?? null],
                    [
                        'assign_to' => $input['assign_to'] ?? null,
                        'company_id' => $input['company_id'] ?? null,
                        'title' => $input['title'] ?? "",
                        'type' => $input['type'] ?? "",
                        'detail' => $input['detail'] ?? "",
                        'due_date' => $input['due_date'] ?? "",
                        'created_by_id' => auth()->user()->id,
                        'completed_at' => $input['mark_done'] == '1' ? now() : null,
                        'is_completed' => $input['mark_done'] ?? false,
                        'created_by' => auth()->user()->id,
                    ]
                );
            });

            return response()->json([
                'status' => true,
                'message' => 'Activity Task created successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to create Activity Task',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function show($id)
    {
        $data = CompanyEngagement::with(['user', 'company', 'userAssigned'])->findOrFail($id);
        return view('pages.sales.engagement.show', compact('data'));
    }

    public function destroy($id)
    {
        try {
            $data = CompanyEngagement::with(['user', 'company', 'userAssigned'])->findOrFail($id);
            $data->delete();

            return redirect()->route('engagement.index')
                ->with('success', 'Engagement deleted successfully.');
        } catch (\Exception $e) {
            return redirect()->route('engagement.index')
                ->with('error', 'Failed to delete engagement: ' . $e->getMessage());
        }
    }

    public function getPIC(Request $request)
    {
        if ($request->ajax()) {
            try {
                $input = $request->all();

                $data = User::select(['id as id','name as nama'])
                    ->where('roles', 'SALES ENGAGEMENT') // Assuming 'PIC' is the role for the person in charge
                    ->where('status', 1);
                if (!empty($input['q'])) {
                    $searchTerm = $input['q'];

                    // Add the grouped OR conditions for 'nama' and 'no_hp'
                    $data->where(function (Builder $query) use ($searchTerm) {
                        $query->where('name', 'LIKE', "%{$searchTerm}%");
                    });
                }

                $data = $data->get();

                $list_pic = [];

                if (count($data) > 0) {
                    foreach ($data as $row) {
                        $list_pic[] = array(
                            "id" => $row->id,
                            "name" => $row->nama
                        );
                    }
                }
                return Response::json($list_pic, 200);
            } catch (\Exception $th) {
                dd($th->getMessage());
            }
        }
        return response()->json([]);
    }

    public function getCompany(Request $request)
    {
        if ($request->ajax()) {
            try {
                $input = $request->all();

                $data = Company::select(['id as id','name as nama']);
                if (!empty($input['q'])) {
                    $searchTerm = $input['q'];

                    // Add the grouped OR conditions for 'nama' and 'no_hp'
                    $data->where(function (Builder $query) use ($searchTerm) {
                        $query->where('name', 'LIKE', "%{$searchTerm}%");
                    });
                }

                $data = $data->get();

                $list_company = [];

                if (count($data) > 0) {
                    foreach ($data as $row) {
                        $list_company[] = array(
                            "id" => $row->id,
                            "name" => $row->nama
                        );
                    }
                }
                return Response::json($list_company, 200);
            } catch (\Exception $th) {
                dd($th->getMessage());
            }
        }
        return response()->json([]);
    }
}
