<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class IsSalesSuper
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if(Auth::user() && (Auth::user()->roles == 'SUPERADMIN' || Auth::user()->roles == 'SALES' || Auth::user()->roles == 'SALES SPV' || Auth::user()->roles == 'PRODUKSI' || Auth::user()->roles == 'PRODUKSI SPV'))
        {
            return $next($request);
        }
        return redirect()->route('do.logout')->with('status','Anda tidak memiliki hak akses sales!');
    }
}
