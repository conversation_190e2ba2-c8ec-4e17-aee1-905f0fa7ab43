<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;


class ForecastExport implements FromCollection, WithHeadings, ShouldAutoSize
{
    protected $data;

    /**
     * ForecastExport constructor.
     *
     * @param array $data
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * Return the collection of data to be exported.
     *
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return collect($this->data);
    }

    /**
     * Define the headings for the exported file.
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            'Customer',
            'Company',
            'Week',
            'Month',
            'Year',
            'Total Order',
            'Expected Revenue',
            // Add more column headings as needed
        ];
    }
}